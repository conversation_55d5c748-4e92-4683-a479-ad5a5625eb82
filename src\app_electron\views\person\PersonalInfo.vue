<template>
  <div class="personal-info">
    <div class="header">
      <UserIcon :name="userInfo.name" :photo="userInfo.photo" :showname="true" />
    </div>
    <div class="body">
      <div class="item">
        <span class="label">电话</span>
        <span class="value">{{ userInfo.phone }}</span>
      </div>
      <div class="item">
        <span class="label">邮箱</span>
        <span class="value">{{ userInfo.mail }}</span>
      </div>
      <div class="item">
        <span class="label">部门</span>
        <span class="value">{{ userInfo.departmentName }}</span>
      </div>
      <div class="item">
        <span class="label">公司</span>
        <span class="value">{{ userInfo.orgName }}</span>
      </div>
    </div>
    <div class="person_footer flex-center">
      <el-button type="danger" class="logout-button" @click="onLogout" plain>退出登录</el-button>
    </div>
  </div>
  <DiaConfirm1 v-model="isShowDia1" title="无法退出" hint="当前有沟通录制正在进行中，请先结束沟通再退出登录" />
</template>

<script setup>
import UserIcon from '@/components/userIcon.vue';
import DiaConfirm1 from '@/app_electron/components/DiaConfirm1.vue';
const userInfo = ref({ name: '', photo: '' });
const isShowDia1 = ref(null)
const onLogout = () => {
  const meetingInfo = g.appStore.getStore(g.cv.keyMeetingInfo, {})
  if (meetingInfo && meetingInfo.conferenceId) {
    isShowDia1.value = true;
  } else {
    g.electronStore.logout();
  }
};

onMounted(() => {
  userInfo.value = g.appStore.user;
});

defineExpose({
  userInfo
})
</script>

<style scoped lang="scss">
.personal-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .header {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
    width: 100%;

    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-right: 20px;
    }

    .contact-info {
      .name {
        font-size: 24px;
        font-weight: bold;
      }

      .phone,
      .email {
        font-size: 16px;
        color: #666;
      }
    }
  }

  .body {
    width: 100%;

    .item {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px solid #eee;

      .label {
        font-weight: bold;
      }

      .value {
        color: #666;
      }
    }
  }

  .person_footer {
    position: fixed;
    bottom: 20px;
    width: 100%;

    .el-button {
      width: 90%;
    }
  }
}
</style>
