<template>
    <div v-if="!!url">
        <iframe :src="url" frameborder="0"></iframe>
    </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { Base64 } from 'js-base64';
import { ElLoading } from 'element-plus'

const route = useRoute()
let loading;

const url = ref('')

onMounted(() => {
    loading = ElLoading.service({
        lock: false,
        text: '加载中',
    });
    try {
        const url_encode = Base64.decode(route.params.url_encode);
        if (url_encode.indexOf("yxt.com") > -1) {
            url.value = `https://view.officeapps.live.com/op/embed.aspx?src=${url_encode}`
        }
        nextTick(() => {
            loading.close();
        })
    } catch (e) {
        loading.value = false;
    }
})

</script>

<style lang="scss">

iframe {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}
</style>