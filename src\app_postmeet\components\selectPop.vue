<template>
  <div class="select_pop_wrap" :style="pop_style" ref="refSelectPop">
    <el-popover
      placement="bottom-start"
      width="200"
      trigger="click"
      class="sp_pop"
      v-model="visible_menu"
    >
      <template #reference>
        <div>
          <ListIcon></ListIcon>
        </div>
      </template>
      <div class="sp_box">
        <div class="sb_title">添加至</div>
        <ul>
          <li @click="addItem('challenge')">培训诉求</li>
          <li @click="addItem('suggestion')">建议和方案</li>
          <li @click="addItem('plan')">待办</li>
        </ul>
      </div>
    </el-popover>

    <div @click="onEdit">
      <EditIcon></EditIcon>
    </div>

    <el-dialog
      v-model="is_show_edit"
      title="修改字幕"
      width="480px"
      :append-to-body="true"
      :modal-append-to-body="false"
      class="dialog_select_pop_wrap"
    >
      <el-input type="textarea" v-model="text" rows="5" />
      <template #footer class="dialog-footer">
        <el-button @click="is_show_edit = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import ListIcon from "@/app_postmeet/icons/list.vue";
import EditIcon from "@/icons/edit.vue";
import closeIcon from "@/app_postmeet/icons/close.vue";
import { updateSaleSummary } from "@/app_postmeet/tools/api";
export default {
  components: { ListIcon, EditIcon, closeIcon },
  data() {
    return {
      item: {},
      is_show_edit: false,
      timer: null,
      visible_menu: false,
      allow_hide: false,
      pop_style: { display: "none" },
      select_txt: "",
      text: "",
    };
  },
  mounted() {
    this.initListener();
    this.ListenerEditmode();
    this.ListenerShowEdit();
    this.ListAutoHide();
  },
  methods: {
    ListAutoHide() {
      const that = this;
      // 点击弹框以外的地方时隐藏弹框
      document.addEventListener("click", function (event) {
        if (that.$refs.refSelectPop && !that.$refs.refSelectPop.contains(event.target)) {
          if (that.pop_style.display == "flex") {
            if (that.allow_hide) {
              that.hideme();
            }
          }
        }
      });
    },
    ListenerEditmode() {
      g.emitter.on("update_editmode", (isedit) => {
        this.hideme();
        this.is_show_edit = false;
      });
    },
    ListenerShowEdit() {
      g.emitter.on("show_edit_sb_dialog", (item) => {
        this.item = item;
        this.text = item.txt;
        this.is_show_edit = true;
      });
    },
    showMenu() {
      this.visible_menu = true;
    },
    addItem(type) {
      this.visible_menu = false;
      this.hideme();
      if (g.postmeetStore.data.salesMateSummary[type].includes(this.text)) {
        ElMessage.warning("不允许重复添加!");
        return;
      }
      const data = {
        id: -1,
        value: this.text,
      };
      updateSaleSummary(g.postmeetStore.data.confId, type, data).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("添加成功!");
          g.postmeetStore.data.salesMateSummary[type].push(this.text);
          g.emitter.emit("update_sale_overview", "");
        } else {
          ElMessage.error("添加失败!");
        }
      });
    },
    onEdit() {
      this.is_show_edit = true;
      this.hideme();
    },
    hideme() {
      this.pop_style = { display: "none" };
    },
    onCloseEdit() {
      this.is_show_edit = false;
    },
    onConfirm() {
      if (this.text) {
        g.emitter.emit("update_sb_text", [this.item, this.text]);
        this.is_show_edit = false;
      } else {
        ElMessage.error("内容不能为空!");
      }
    },
    initListener() {
      const that = this;
      g.emitter.on("showSbPop", ([e, txt, item]) => {
        this.select_txt = txt;
        this.text = item.txt;
        this.item = item;
        const x = e.pageX - 70;
        const y = e.pageY - 60;
        that.allow_hide = false;
        that.pop_style = { left: x + "px", top: y + "px", display: "flex" };
        that.visible_menu = false;
        that.timer && clearTimeout(that.timer);
        that.timer = setTimeout(() => {
          that.allow_hide = true;
          that.timer && clearTimeout(that.timer);
        }, 500);
      });
    },
  },
};
</script>

<style lang="scss">
.select_pop_wrap {
  position: fixed;
  width: 76px;
  height: 40px;
  display: none;
  background: #ffffff;
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  z-index: 9999;
  flex-direction: row;
  align-items: center;
  font-size: 18px;
  justify-content: center;

  svg {
    cursor: pointer;
    width: 20px;
    height: 20px;
    margin: 0 8px;
  }
}

.el-popover {
  .sp_box {
    .sb_title {
      padding: 4px 9px;
      height: 20px;
      color: #8c8c8c;
      line-height: 20px;
    }

    ul {
      li {
        padding: 4px 9px;
        cursor: pointer;
      }

      li:hover {
        background: #f5f5f5;
      }
    }
  }
}
</style>
