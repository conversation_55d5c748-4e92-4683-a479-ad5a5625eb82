<template>
    <el-select class="audio-input-device" v-model="selectedDevice" placeholder="请选择音频输入设备" @change="onChange">
        <el-option v-for="device in devices" :key="device.deviceId" :label="device.label" :value="device.deviceId" />
    </el-select>
</template>

<script setup>
import { throttle } from '@/js/utils';
const props = defineProps({
    modelValue: String
});
const devices = ref([]);
const selectedDevice = ref(props.modelValue);
const hasRemoveListener = ref(false);

watch(() => props.modelValue, (newVal) => {
    selectedDevice.value = newVal;
    setDefaultDevice()
});

const emit = defineEmits(['update:modelValue']);

const onChange = (value) => {
    emit('update:modelValue', value);
};

const setDefaultDevice = throttle(() => {
    if (devices.value.length > 0) {
        const deviceExists = devices.value.some(device => device.deviceId === selectedDevice.value);
        
        if (!selectedDevice.value || !deviceExists) {
            selectedDevice.value = devices.value[0].deviceId;
            emit('update:modelValue', selectedDevice.value);
        } else {
            console.log('ready has setDefaultDevice', selectedDevice.value)
        }
    } else {
        selectedDevice.value = null
        emit('update:modelValue', null);
    }
}, 1000);

const handleDeviceChange = async () => {
    devices.value = await g.meetStore.getAudioInputs(false);
    setDefaultDevice()
}

const removeListener = () => {
    if (hasRemoveListener.value) {
        return
    }
    hasRemoveListener.value = true
    navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
}

onMounted(async () => {
    devices.value = await g.meetStore.getAudioInputs(false);
    setDefaultDevice()
    // 添加设备变更监听
    if (!hasRemoveListener.value) {
        navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    }
});

defineExpose({
    devices,
    selectedDevice,
    setDefaultDevice,
    removeListener
})

</script>

<style scoped lang="scss">
.audio-input-device {
    width: 100%;
}
</style>
