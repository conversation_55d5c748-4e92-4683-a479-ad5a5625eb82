class WebSocketClient {
    constructor(url, options = {}) {
        this.url = url;
        this.options = {
            reconnectInterval: 3000,
            heartbeatInterval: 30000,
            ...options
        };

        this.ws = null;
        this.heartbeatTimer = null;
        this.messageHandlers = new Map();
        this.statusHandlers = new Map();
        this.isConnected = false;
    }

    // 连接 WebSocket
    connect() {
        try {
            this.ws = new WebSocket(this.url);
            this.bindEvents();
        } catch (error) {
            console.error('WebSocket 连接失败:', error);
            this.reconnect();
        }
    }

    // 绑定 WebSocket 事件
    bindEvents() {
        this.ws.onopen = () => {
            this.isConnected = true;
            this.startHeartbeat();
            this.triggerStatusHandler('connected');
        };

        this.ws.onclose = () => {
            this.isConnected = false;
            this.stopHeartbeat();
            this.triggerStatusHandler('disconnected');
            this.reconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket 错误:', error);
            this.isConnected = false;
            this.triggerStatusHandler('error', error);
        };

        this.ws.onmessage = (event) => {
            console.log('onmessage:', event);
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('消息解析错误:', error);
            }
        };
    }

    // 重连机制
    reconnect() {
        setTimeout(() => {
            // console.log(`尝试重连`);
            this.connect();
        }, this.options.reconnectInterval);
    }

    // 发送消息
    send(type, data) {
        if (!this.isConnected) {
            console.error('WebSocket 未连接,fail to send:', type, data);
            return false;
        }

        try {
            const message = JSON.stringify({ type, data });
            this.ws.send(message);
            return true;
        } catch (error) {
            console.error('发送消息失败:', error);
            return false;
        }
    }

    // 注册消息处理器
    on(type, handler) {
        this.messageHandlers.set(type, handler);
    }

    // 移除消息处理器
    off(type) {
        this.messageHandlers.delete(type);
    }

    // 处理接收到的消息
    handleMessage(message) {
        const { endpoint, data } = message;
        const handler = this.messageHandlers.get(endpoint);

        if (handler) {
            handler(data);
        }
    }

    // 心跳机制
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.send('heartbeat', { timestamp: Date.now() });
        }, this.options.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    // 关闭连接
    close() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    // 添加状态处理器
    onStatus(status, handler) {
        if (!this.statusHandlers.has(status)) {
            this.statusHandlers.set(status, new Set());
        }
        this.statusHandlers.get(status).add(handler);
    }

    // 移除状态处理器
    offStatus(status, handler) {
        if (this.statusHandlers.has(status)) {
            this.statusHandlers.get(status).delete(handler);
        }
    }

    // 触发状态处理器
    triggerStatusHandler(status, data) {
        if (this.statusHandlers.has(status)) {
            this.statusHandlers.get(status).forEach(handler => handler(data));
        }
    }
}

export default WebSocketClient; 