<template>
    <el-dialog v-model="visible" :title="'您有拜访正在进行'" width="316px" :close-on-click-modal="false" :show-close="false">
        <div>拜访主题：{{ ongoingMeetSubject }}，请先结束当前拜访，再开启新拜访。</div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleEndMeet" :loading="loading">
                    结束
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    ongoingMeetSubject: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'end-meet'])

const visible = ref(props.modelValue)
const loading = ref(false)

watch(() => props.modelValue, (newVal) => {
    visible.value = newVal
})

watch(() => visible.value, (newVal) => {
    emit('update:modelValue', newVal)
})

const handleCancel = () => {
    loading.value = false
    visible.value = false
}

const handleEndMeet = async () => {
    try {
        loading.value = true
        emit('end-meet')
    } catch (error) {
        ElMessage.error('结束拜访失败，请稍后重试')
        loading.value = false
    }
}
</script>