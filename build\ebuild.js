const shell = require('shelljs')
const generateVersionJson = require('./generate-version')
const zipDist = require('./generate-zip')

const env = process.env.BUILD_ENV
const onlyZip = process.env.ONLY_ZIP
const buildTime = new Date().getTime() + ''
console.log('Build environment:', env, buildTime)

const envConfig = {
    tf_green: {
        NODE_ENV: 'development',
        APP_APIENV: 'tf-tc-01',//学习平台需要
        mode: 'e_build_tf',
        json5: './build/electron-builder.tf.test.json5'
    },
    tf_sign: {
        NODE_ENV: 'production',
        APP_APIENV: 'tf-tc-01',
        mode: 'e_build_tf',
        json5: './build/electron-builder.tf.prod.json5'
    },
    prod: {
        NODE_ENV: 'production',
        APP_APIENV: 'prod',
        mode: 'e_build_prod',
        json5: './build/electron-builder.prod.json5'
    }
}

if (!envConfig[env]) {
    throw new Error(`Invalid build environment: ${env}`)
}

// 设置环境变量
const setEnv = () => {
    process.env.NODE_ENV = envConfig[env].NODE_ENV
    process.env.VUE_APP_APIENV = envConfig[env].APP_APIENV
}

// 构建主程序
const buildMain = () => {
    console.log('Building main process...')
    const { code } = shell.exec(
        `vite build --mode ${envConfig[env].mode}`
    )
    return code === 0
}

// 主构建流程
const build = async () => {
    try {
        // 1. 设置环境变量
        setEnv()

        // 2. 构建主程序
        if (!buildMain()) {
            throw new Error('Failed to build main process')
        }

        // 3. 生成 version.json
        generateVersionJson(env, buildTime)

        // 4. 生成zip资源包
        await zipDist(env, buildTime)

        if (onlyZip) {
            console.log('Zip completed successfully!')
            return
        }

        // 5. 打包electron应用
        const { code } = shell.exec(
            `electron-builder --config ${envConfig[env].json5}`
        )
        if (code !== 0) {
            throw new Error('Failed to build electron app')
        }

        console.log('Build completed successfully!')
    } catch (err) {
        console.error('Build failed:', err.message)
        process.exit(1)
    }
}

build() 