<template>
    <div>
        <div class="icon" v-show="isShowIcon && showface">
            <component :is="getFaceCom" />
        </div>
        <div :class="`atti_tag ${getStatusClass(status)}`" v-show="status && (!isShowIcon || !showface)">
            {{ status }}
        </div>
    </div>
</template>

<script>
import faceActive from "@/app_postmeet/icons/face/face_active.vue"
import faceMiddle from "@/app_postmeet/icons/face/face_middle.vue"
import faceInactive from "@/app_postmeet/icons/face/face_inactive.vue"
import { markRaw } from "vue";

export default {
    props: ['status', 'showface'],
    data() {
        return {
            facceMap: {
                "积极": markRaw(faceActive),
                "中性": markRaw(faceMiddle),
                "消极": markRaw(faceInactive),
            }
        }
    },
    methods: {
        getStatusClass(status) {
            if (!status) {
                return ''
            }
            if (status.indexOf('中性') > -1) {
                return 'd_gray' //gray
            } else if (status.indexOf('积极') > -1) {
                return 'd_customer' //green
            } else if (status.indexOf('消极') > -1) {
                return 'd_na' //red
            }
        },
    },
    computed: {
        isShowIcon() {
            return Object.keys(this.facceMap).includes(this.status);
        },
        getFaceCom: function () {
            return this.facceMap[this.status];
        },
    }
}

</script>

<style lang="scss">
.icon {
    margin: 4px 0 0 12px;
}

.atti_tag {
    margin: 4px 0 0 12px;
    padding: 0 5px;
    height: 20px;
    border-radius: 2px;
    height: 20px;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 20px;
    text-align: center;
}
</style>