<template>
    <el-autocomplete v-model="localValue" :fetch-suggestions="handleQccSearch" @select="onSelect"
        :placeholder="`请输入${!props.enableQcc ? '' : '并选择'}客户名称`" />
    <div v-if="!hasSelected && localValue && !modelValue && props.enableQcc" class="no-customer-selected">
        请从下拉列表中选择客户
    </div>
</template>

<script setup>
import { searchCompanyAPI } from "@/js/api.js";

const props = defineProps({
    modelValue: {
        type: String,
        default: "",
    },
    enableQcc: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["update:modelValue", "change"]);
const hasSelected = ref(false);
const localValue = ref(props.modelValue);
const lastResult = ref([]);

let lastKeyword = "";

watch(
    () => props.modelValue,
    (val) => {
        localValue.value = val;
    }
);


const onSelect = (item) => {
    localValue.value = item.value;
    hasSelected.value = true;
    emit("update:modelValue", toRaw(localValue.value));
    emit("change", item.value);
};

const reset = (default_value = "") => {
    localValue.value = default_value;
    hasSelected.value = false;
    emit("update:modelValue", default_value);
};

const _searchQcc = (keyword, cb) => {
    searchCompanyAPI(keyword).then((resp) => {
        if (resp.code == 0 && resp.data && resp.data.length > 0) {
            lastKeyword = keyword;
            const result = resp.data.map((x) => {
                return { value: x.name };
            });
            hasSelected.value = false;
            lastResult.value = result;
            cb(result);
        } else {
            hasSelected.value = false;
            cb([]);
        }
    }).catch((error) => {
        hasSelected.value = false;
        console.error("handleQccSearch error", error);
        cb([]);
    });
}



const handleQccSearch = (keyword, cb) => {
    if (!keyword) {
        cb([]);
        return;
    }
    if (lastKeyword !== keyword) {
        if (props.enableQcc) {
            emit("update:modelValue", '');
            emit("change", '');
            _searchQcc(keyword, cb);
        } else {
            if (!props.enableQcc) {
                emit("update:modelValue", toRaw(keyword));
                emit("change", keyword);
            }
            cb([]);
        }
    } else {
        cb(lastResult.value);
    }
};


defineExpose({
    reset,
});
</script>

<style scoped>
.no-customer-selected {
    color: red;
    font-size: 12px;
    margin-top: 5px;
}
</style>
