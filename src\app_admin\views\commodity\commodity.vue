<template>
  <div class="commodity_wrap">
    <div class="left">
      <LeftMenu ref="refMenu" @callback="cbLeft"></LeftMenu>
    </div>
    <div class="right">
      <RightList ref="refRight"></RightList>
    </div>
  </div>
</template>

<script setup>
import LeftMenu from "./LeftMenu.vue";
import RightList from "./RightList.vue";

const refRight = ref();
const refMenu = ref();
const emit = defineEmits(["callback"]);

const cbLeft = (action, data) => {
  if (action === "click") {
    refRight.value.init(data);
  }
};

onMounted(() => {
  refMenu.value.init();
});

defineExpose({
  cbLeft,
  refRight,
  refMenu,
  LeftMenu,
  RightList,
});
</script>

<style lang="scss">
.commodity_wrap {
  display: flex;
  flex-direction: row;
  height: 100%;

  .left {
    width: 250px;
    border-right: 1px solid #e9e9e9;
    height: 100%;
  }

  .right {
    width: calc(100vw - 453px);
    padding: 20px;
  }
}
</style>
