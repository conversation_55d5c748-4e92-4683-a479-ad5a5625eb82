import AppStore from "@/stores/appliation.js";
import CacheStore from "@/stores/cache.js";
import storeAdmin from "@/app_admin/stores/index.js";
import storeClient from "@/app_client/stores/index.js";
import storeElectron from "@/app_electron/stores/index.js";
import storePostmeet from "@/app_postmeet/stores/index.js";
import PlanStore from "@/stores/plan.js";
import storeClipper from "@/app_clipper/stores/index.js";
const createStore = () => {
  const appStore = AppStore();
  const cacheStore = CacheStore();
  const planStore = PlanStore();

  const adminFileStore = storeAdmin.FileStore();
  const usageStore = storeAdmin.UsageStore();
  const saleStore = storeAdmin.SaleStore();

  const clientStore = storeClient.ClientStore();
  const clientFileStore = storeClient.FileStore();
  const clientBoardStore = storeClient.BoardStore();
  const clientBiStore = storeClient.BiStore();
  const electronStore = storeElectron.ElectronStore();
  const meetStore = storeElectron.MeetStore();
  const postmeetStore = storePostmeet.PostMeetStore();

  const clipperStore = storeClipper.ClipperStore();

  return {
    appStore,
    planStore,
    adminFileStore,
    clientFileStore,
    clientBiStore,
    usageStore,
    saleStore,
    cacheStore,
    clientStore,
    electronStore,
    meetStore,
    postmeetStore,
    clipperStore,
    clientBoardStore,
  };
};

export default createStore;
