<template>
    <div class="customer_filter_item">
        <el-select v-model="selected" multiple clearable collapse-tags placeholder="客户标签"
            popper-class="custom_select_label" :max-collapse-tags="1" style="width: 144px" @change="onChange">
            <template #header>
                <el-input v-model="param.searchKey" placeholder="请输入标签名称" class="search_input" @keyup.enter="search"
                    clearable :prefix-icon="Search" @input="onSearchChange">
                </el-input>
            </template>
            <el-option v-for="item in labels" :key="item" :label="item" :value="item" />
        </el-select>
    </div>
</template>

<script setup>
import { getCustomerTags } from "@/app_client/tools/api.js"
import { onMounted } from "vue";
import { Search } from '@element-plus/icons-vue'
import { debounce } from "@/js/utils.js"

const props = defineProps({
    value: {
        type: Array,
        required: true
    }
})

const emit = defineEmits(['update:value', 'reload'])
const param = ref({
    "searchKey": "", //客户名称
    "startTime": "2024-01-01 00:00:00", //开始时间，注间时间格式 YYYY-mm-dd HH24:mi:ss
    "endTime": null, //结束时间
})

const selected = ref(props.value);
const allLabels = ref([])
const labels = ref([])

const search = () => {
    const _param = toRaw(param.value);
    getCustomerTags(_param).then(resp => {
        if (resp.code == 0) {
            allLabels.value = resp.data;
            labels.value = resp.data;
        }
    })
}

const localSearch = () => {
    labels.value = allLabels.value.filter(x => x.indexOf(param.value.searchKey) > -1)
}

const onSearchChange = debounce(localSearch, 80);

const onChange = () => {
    emit('update:value', selected.value);
    emit('reload', '');
}

onMounted(() => {
    search()
})

defineExpose({ selected, onSearchChange, labels, param, search, localSearch })
</script>

<style lang="scss">
.customer_filter_item {
    margin-right: 12px;

    .el-checkbox {
        display: flex;
        height: unset;
    }
}
</style>