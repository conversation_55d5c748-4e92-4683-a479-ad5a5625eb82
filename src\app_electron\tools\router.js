import { getStore } from "@/js/utils";
import { ConstValue } from "@/js/const_value";
const offset = {
    x: 40,
    y: 20,
};
const standard = {
    width: 374,
    height: 720,
    minWidth: 374,
    minHeight: 720,
}

const electronRouter = [
    {
        path: "/electron",
        component: () => import("@/app_electron/components/Layout.vue"),
        redirect: '/electron/home',
        children: [
            {
                path: 'home',
                name: 'electronHome',
                component: () => import("@/app_electron/views/home/<USER>"),
                meta: {
                    title: '绚星销售助手',
                    param: standard
                }
            },
            {
                path: 'person',
                name: 'electronPersonalInfo',
                component: () => import("@/app_electron/views/person/PersonalInfo.vue"),
                meta: {
                    title: '个人主页',
                    param: standard
                }
            },
            {
                path: 'arrange_visit',
                name: 'arrangeVisit',
                component: () => import("@/app_electron/views/arrange_visit/ArrangeVisit.vue"),
                meta: {
                    title: '安排拜访',
                    param: standard,
                    offset
                }
            },
            {
                path: 'setting',
                name: 'setting',
                component: () => import("@/app_electron/views/setting/Setting.vue"),
                meta: {
                    title: '设置',
                    param: {
                        width: 768,
                        height: 600,
                    }
                }
            },
            {
                path: 'meet',
                name: 'meet',
                component: () => import("@/app_electron/views/meet/Meet.vue"),
                meta: {
                    title: '录制',
                    param: standard,
                    offset
                }
            },
            // {
            //     path: 'card_inmeet',
            //     name: 'cardInmeet',
            //     component: () => import("@/app_electron/views/meet/cardmeet/CardInmeet.vue"),
            //     meta: {
            //         title: '工牌录制',
            //         param: standard,
            //         offset
            //     }
            // },
            {
                path: 'login',
                name: 'electronLogin',
                component: () => import("@/app_electron/views/login/Login.vue"),
                meta: {
                    title: '登录',
                    param: standard
                }
            },
            {
                path: 'upgrade',
                name: 'upgrade',
                component: () => import("@/app_electron/views/upgrade/Upgrade.vue"),
                meta: {
                    title: '软件更新',
                    param: {
                        width: 506,
                        height: 436,
                    }
                }
            },
            {
                path: 'download',
                name: 'ElectronDownload',
                component: () => import("@/app_electron/views/upgrade/Download.vue"),
                meta: {
                    title: '软件更新',
                    param: {
                        width: 460,
                        height: 270,
                    }
                }
            },
            {
                path: 'choose_tag',
                name: 'chooseTag',
                component: () => import("@/app_electron/views/choose_tag/ChooseTag.vue"),
                meta: {
                    title: '选择标签',
                    param: {
                        width: 500,
                        height: 300,
                    }
                }
            },
            {
                path: 'choose_salegoods',
                name: 'chooseSaleGoods',
                component: () => import("@/app_electron/views/choose_tag/ChooseSaleGoods.vue"),
                meta: {
                    title: '选择商品',
                    param: {
                        width: 769,
                        height: 600,
                    }
                }
            },
            {
                path: 'visit_detail',
                name: 'visitDetail',
                component: () => import("@/app_electron/views/arrange_visit/VisitDetail.vue"),
                meta: {
                    offset,
                    title: '计划详情',
                    param: standard
                }
            },
            {
                path: 'add_inner_contact',
                name: 'addInnerContact',
                component: () => import("@/app_electron/views/arrange_visit/AddInnerContact.vue"),
                meta: {
                    title: '添加内部参会人',
                    param: {
                        width: 500,
                        height: 270,
                    }
                }
            },
            {
                path: 'add_outer_contact',
                name: 'addOuterContact',
                component: () => import("@/app_electron/views/arrange_visit/AddOuterContact.vue"),
                meta: {
                    title: '添加外部参会人',
                    param: {
                        width: 500,
                        height: 450,
                    }
                }
            },
            {
                path: 'ai_app',
                name: 'aiApp',
                component: () => import("@/app_electron/views/home/<USER>"),
                meta: {
                    title: '智能应用',
                    offset,
                    param: standard
                }
            },
            {
                path: 'dummy_window',
                name: 'dummyWindow',
                component: () => import("@/app_electron/components/DummyWindow.vue"),
                meta: {
                    title: '消息中心',
                    offset,
                    param: standard
                }
            },
            {
                path: 'dummy_cardmeet',
                name: 'dummyCardmeet',
                component: () => import("@/app_electron/components/DummyWindow.vue"),
                meta: {
                    title: '工牌录制',
                    offset,
                    param: standard
                }
            },
            {
                path: 'visit_notification',
                name: 'visitNotification',
                component: () => import("@/app_electron/views/Notie/VisitStartNotie.vue"),
                meta: {
                    title: '拜访通知',
                    param: {
                        width: 400,
                        height: 202,
                        titleBarStyle: 'default',
                        frame: false,
                        transparent: true,
                        ...(() => {
                            const span = 20;
                            const info = getStore(ConstValue.keyMainScreenInfo)
                            return {
                                x: info.width - 400 - span,
                                y: info.y + span,
                            }
                        })()
                    }
                }
            },
            {
                path: 'meeting_summary_notification',
                name: 'visitNotification',
                component: () => import("@/app_electron/views/Notie/MeetingSummaryNotie.vue"),
                meta: {
                    title: '消息通知',
                    param: {
                        width: 400,
                        height: 152,
                        titleBarStyle: 'default',
                        frame: false,
                        transparent: true,
                        ...(() => {
                            const span = 20;
                            const info = getStore(ConstValue.keyMainScreenInfo)
                            return {
                                x: info.width - 400 - span,
                                y: info.height - 202 - span,
                            }
                        })()
                    }
                }
            },
            {
                path: 'meet_setting',
                name: 'meetSetting',
                component: () => import("@/app_electron/views/meet/MeetSetting.vue"),
                meta: {
                    title: '录制设置',
                    param: {
                        width: 374,
                        height: 400
                    }
                }
            },
            {
                path: 'hide_window',
                name: 'hideWindow',
                component: () => import("@/app_electron/components/HideWindow.vue"),
                meta: {
                    title: 'About',
                    param: {
                        width: 500,
                        height: 500
                    }
                }
            },
            {
                path: 'rename_inmeet',
                name: 'renameInmeet',
                component: () => import("@/app_electron/views/meet/RenameInmeet.vue"),
                meta: {
                    title: '发言者身份标注',
                    param: standard,
                    offset
                }
            }
        ]
    }
]

export default electronRouter
