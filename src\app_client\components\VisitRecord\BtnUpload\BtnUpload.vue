<template>
    <div class="visit_btn_upload">
        <el-button type="primary" @click="onShowUpload">上传</el-button>
        <UploadModal ref="refUpload">
            <template #right>
                <div class="vbp_box">
                    <div class="vbp_title">
                        沟通关联客户<span>*</span>
                    </div>
                    <div class="vbp_value">
                        <CustomerNamePicker v-model="param.salesMateCustomerName"
                            v-model:customerId="param.customerId" />
                    </div>
                </div>
                <div class="vbp_box">
                    <div class="vbp_title">
                        主题标签<span>*</span>
                    </div>
                    <div class="vbp_value">
                        <TagPicker v-model="param" ref="refTagPicker" @change="onTagChange" />
                    </div>
                </div>
                <div class="vbp_box">
                    <div class="vbp_title">
                        沟通主题<span>*</span>
                    </div>
                    <div class="vbp_value">
                        <el-input type="text" v-model="param.subject" placeholder="请输入" />
                    </div>
                </div>
                <formRoleNum v-model:value="param.roleNum" />
                <div class="vbp_box">
                    <div class="vbp_title">
                        沟通目标<span>*</span>
                    </div>
                    <div class="vbp_value">
                        <el-input type="textarea" v-model="param.salesGoal" :autosize="{ minRows: 3, maxRows: 6 }"
                            placeholder="请输入" />
                    </div>
                </div>
            </template>
        </UploadModal>
    </div>
</template>

<script setup>
import CustomerNamePicker from "@/components/CustomerNamePicker";
import TagPicker from "@/components/ArrangeVisitCore/TagPicker.vue";
import UploadModal from "@/app_client/components/UploadModal.vue";
import formRoleNum from "./formRoleNum.vue";
import { onMounted } from "vue";
import { uploadVideo } from "@/app_client/tools/api.js"

const refChatWrap = ref();
const refUpload = ref();
const refTagPicker = ref();
const defValue = {
    roleNum: 0,
    fileName: '',
    subject: '',
    salesMateType: 2,//1 interval,2 customer
    salesRelatedType: 1,
    salesMateTags: '',
    salesGoodsCategories: '',
    customerId: '',
    salesMateCustomerName: ''
}
const param = ref({
    ...defValue
})

const _checkParam = () => {
    const { subject, salesGoal, salesMateTags, salesMateCustomerName } = param.value;
    if (!salesMateCustomerName) {
        ElMessage.error('沟通关联客户不可以为空');
        return false;
    }

    if (!salesMateTags) {
        ElMessage.error('主题标签不可以为空');
        return false;
    }

    if (!subject) {
        ElMessage.error('沟通主题不可以为空');
        return false;
    }

    if (!salesGoal) {
        ElMessage.error('沟通目标不可以为空');
        return false;
    }

    return true;
}

const _uploadMethod = (file, onProgress, onSuccess, onFail) => {
    const { subject, roleNum, salesGoal, salesMateType, salesMateTags, customerId, salesMateCustomerName, salesGoodsCategories } = param.value;
    const formData = new FormData();

    formData.append('files', file)
    formData.append('subject', subject)
    formData.append('roleNum', roleNum)
    formData.append('salesGoal', salesGoal)
    formData.append('salesMateType', salesMateType)
    formData.append('customerId', customerId)
    formData.append('salesMateCustomerName', salesMateCustomerName)
    formData.append('salesMateTags', salesMateTags)
    formData.append('salesGoodsCategories', salesGoodsCategories)
    formData.append('meetingType', 7)
    uploadVideo(formData, onProgress, onSuccess, onFail);
}

const _updateTheme = () => {
    if (param.value.salesMateTags && param.value.salesMateCustomerName) {
        if (!param.value.subject || !param.value.conferenceId) {
            param.value.subject = param.value.salesMateCustomerName + ' - ' + param.value.salesMateTags;
        }
    }
}

watch(() => param.value.salesMateCustomerName, (val) => {
    _updateTheme()
}, { immediate: true })

const onTagChange = (val) => {
    _updateTheme()
}

const onShowUpload = () => {
    const cfg = {
        page: 'visit',
        fileTypes: ["mp4", "mp3", "m4a", "aac", "wav"],
        maxSizeMb: 1024,
        fileIcon: 'video',
        uploadMethod: _uploadMethod,
        checkParam: _checkParam
    }
    refUpload.value.show(cfg);
    nextTick(() => {
        param.value = { ...defValue };
        refTagPicker.value.init({ ...defValue })
    })
}

onMounted(() => {
    g.emitter.on('file_setName', (fileName) => {
        param.value.fileName = fileName;
    })
})

onUnmounted(() => {
    g.emitter.off('file_setName');
});

defineExpose({
    formRoleNum, refChatWrap, UploadModal, refUpload, onShowUpload, refTagPicker, TagPicker
})
</script>

<style lang="scss">
.visit_btn_upload {
    .el-dialog__body {
        padding-top: 12px;

        .vbp_box {
            margin-bottom: 12px;

            .vbp_title {
                margin-bottom: 12px;

                span {
                    color: red;
                    margin-left: 4px;
                }
            }
        }
    }
}
</style>
