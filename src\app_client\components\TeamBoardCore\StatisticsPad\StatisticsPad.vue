<template>
  <div class="statistics-pad">
    <AverageBarChart ref="refAverageBarChart" @callback="callback" />
    <div class="sp-right">
      <div v-if="chartType === 'ai_evaluation'">
        <AIEvaluation ref="refAIEvaluation" />
      </div>
      <div v-if="chartType === 'sales_ability_radar'">
        <SalesAbilityRadar ref="refSalesAbilityRadar" />
      </div>
      <div v-if="chartType === 'task_completion'">
        <TaskCompletion ref="refTaskCompletion" />
      </div>
    </div>
  </div>
</template>

<script setup>
import AverageBarChart from "./AverageBarChart.vue";
import AIEvaluation from "./AIEvaluation.vue";
import SalesAbilityRadar from "@/app_client/components/TeamBoardCore/components/SalesAbilityRadar.vue";
import TaskCompletion from './TaskCompletion.vue'
import { getReportDate } from "../misc.js";
import { throttle, getUrlParam, round } from '@/js/utils.js'

const loading = ref(false)
const refAIEvaluation = ref(null)
const refSalesAbilityRadar = ref(null)
const refTaskCompletion = ref(null)
const columnData = ref([]);
const deptData = ref({});
const refAverageBarChart = ref([])
const isEndDept = ref(false)
const analyseUser = ref({ ssoUserId: '' })

const formData = ref({
  dptId: '',
  userIds: []
})
const periodType = computed(() => g.clientBoardStore.periodType);
const chartType = computed(() => {
  return g.clientBoardStore.getRightChartType();
});

const callback = (action, data) => {
  if (action === 'setUser') {
    analyseUser.value = data
    _updateRightUI()
  }
}
const reportDate = getReportDate();
const getSubDeptData = async () => {
  if (!formData.value.dptId) return;
  loading.value = true;
  console.log('getSubDeptData', deptData.value)
  isEndDept.value = !deptData.value.children?.length > 0
  if (isEndDept.value) {
    columnData.value = await g.clientBoardStore.getReportSubUserWrap(formData.value.dptId, periodType.value, reportDate);
  } else {
    columnData.value = await g.clientBoardStore.getReportSubTeamWrap(formData.value.dptId, periodType.value, reportDate);
  }
  nextTick(() => {
    refAverageBarChart.value.init(isEndDept.value, columnData.value, deptData.value, periodType.value);
  })
  loading.value = false;
  _updateRightUI()
};

const setDept = (data) => {
  deptData.value = data;
  formData.value.dptId = data.value;
  isEndDept.value = !deptData.value.children?.length > 0
  getSubDeptData();
};


//销售能力画像
const getAssessmentData = async () => {
  const radarData = await g.clientBoardStore.apiGetAssessment(analyseUser.value.ssoUserId)
  refSalesAbilityRadar.value.init(analyseUser.value, periodType.value, radarData);
}

const _updateRightUI = throttle(() => {
  if (analyseUser.value.ssoUserId) {
    nextTick(() => {
      console.log('chartType.value', chartType.value)
      if (chartType.value === 'sales_ability_radar') {
        getAssessmentData()
      } else if (chartType.value === 'task_completion') {
        refTaskCompletion.value.init(analyseUser.value, periodType.value);
      } else if (chartType.value === 'ai_evaluation') {
        refAIEvaluation.value.init(analyseUser.value, periodType.value);
      } else {
        console.log('callback no dom', chartType.value)
      }
    })
  }
}, 500); // 设置节流时间为500毫秒


watch(() => [periodType.value, chartType.value], () => {
  getSubDeptData();
}, { immediate: true });

const _team_board_region_click = () => {
  getSubDeptData();
}

onMounted(() => {
  g.emitter.on("team_board_region_click", _team_board_region_click);
});

onUnmounted(() => {
  g.emitter.off("team_board_region_click", _team_board_region_click);
});

defineExpose({
  AverageBarChart,
  SalesAbilityRadar,
  refAIEvaluation,
  refSalesAbilityRadar,
  refTaskCompletion,
  TaskCompletion,
  chartType,
  setDept,
  loading
});
</script>

<style lang="scss" scoped>
.statistics-pad {
  display: flex;
  flex-direction: row;
  gap: 16px;

  .sp-right {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: calc(0.5 * (100vw - 220px));
  }
}
</style>