<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="lgriarv-1">
                <stop stop-color="#E9F1FF" offset="0%"></stop>
                <stop stop-color="#F5F9FF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="需求调研报告助手" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="沟通模板-常规文档" transform="translate(-976.000000, -32.000000)">
                <rect fill="#FFFFFF" x="0" y="0" width="1280" height="833"></rect>
                <g id="编组-10" fill="url(#lgriarv-1)">
                    <rect id="矩形" x="0" y="0" width="1280" height="1200"></rect>
                </g>
                <g id="编组-2" transform="translate(280.000000, 24.000000)">
                    <g id="编组-3" transform="translate(688.000000, 0.000000)">
                        <path
                            d="M31.5,0.5 L31.5,31.5 L4,31.5 C3.03350169,31.5 2.15850169,31.1082492 1.52512627,30.4748737 C0.891750844,29.8414983 0.5,28.9664983 0.5,28 L0.5,4 C0.5,3.03350169 0.891750844,2.15850169 1.52512627,1.52512627 C2.15850169,0.891750844 3.03350169,0.5 4,0.5 L31.5,0.5 Z"
                            id="矩形备份" stroke="#D9D9D9" fill="#FFFFFF"
                            transform="translate(16.000000, 16.000000) scale(-1, 1) translate(-16.000000, -16.000000) ">
                        </path>
                        <g id="Icon/16px/code" transform="translate(8.000000, 8.000000)">
                            <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                            <g id="编组"
                                transform="translate(8.000000, 8.000000) rotate(90.000000) translate(-8.000000, -8.000000) translate(4.000000, 5.000000)"
                                fill="#262626">
                                <path
                                    d="M4.33355108,0.597897888 L4.39569401,0.655374228 L7.87993448,4.72247085 C8.05348322,4.91446408 8.03693488,5.20936624 7.84297273,5.38115328 C7.67056192,5.53385286 7.41600649,5.53787698 7.23967831,5.40204307 L7.17753537,5.34456673 L4.00009405,1.61669075 L0.822464628,5.34456673 C0.648915882,5.53655995 0.35098943,5.55294031 0.157027273,5.38115328 C-0.0153835335,5.22845369 -0.0476155207,4.97847795 0.0692391753,4.78996999 L0.120065523,4.72247085 L3.60430599,0.655374228 C3.6874279,0.563418257 3.7990817,0.511747382 3.91417929,0.501740977 L4.00075619,0.502069814 C4.08810103,0.493868997 4.17736592,0.510073806 4.25723417,0.55003793 L4.33355108,0.597897888 Z"
                                    id="形状结合"></path>
                            </g>
                        </g>
                        <g id="Icon/16px/code" transform="translate(8.000000, 8.000000)">
                            <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                            <g id="编组"
                                transform="translate(8.000000, 8.000000) rotate(90.000000) translate(-8.000000, -8.000000) translate(4.000000, 5.000000)"
                                fill="#262626">
                                <path
                                    d="M4.33355108,0.597897888 L4.39569401,0.655374228 L7.87993448,4.72247085 C8.05348322,4.91446408 8.03693488,5.20936624 7.84297273,5.38115328 C7.67056192,5.53385286 7.41600649,5.53787698 7.23967831,5.40204307 L7.17753537,5.34456673 L4.00009405,1.61669075 L0.822464628,5.34456673 C0.648915882,5.53655995 0.35098943,5.55294031 0.157027273,5.38115328 C-0.0153835335,5.22845369 -0.0476155207,4.97847795 0.0692391753,4.78996999 L0.120065523,4.72247085 L3.60430599,0.655374228 C3.6874279,0.563418257 3.7990817,0.511747382 3.91417929,0.501740977 L4.00075619,0.502069814 C4.08810103,0.493868997 4.17736592,0.510073806 4.25723417,0.55003793 L4.33355108,0.597897888 Z"
                                    id="形状结合"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
