<template>
    <div class="visit_btn_export">
        <el-button :type="props.team ? 'primary' : 'default'" @click="onExport">导出数据</el-button>
    </div>
</template>

<script setup>
import { exportCustomerMeets } from "@/app_client/tools/api.js"
import { toRaw } from "vue";
import { now } from "@/js/utils.js"

const props = defineProps({
    param: {
        type: Object,
        required: true
    },
    team: {
        type: Boolean,
        required: false,
        default: false
    }
})

const onExport = () => {
    const data = toRaw(props.param);
    data.filename = '沟通管理_' + now('yyyyMMddhhmmss');
    exportCustomerMeets(props.team, data)
}


defineExpose({ onExport, props })
</script>

<style lang="scss">
.visit_btn_export {}
</style>
