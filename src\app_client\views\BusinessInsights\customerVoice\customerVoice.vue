<template>
    <div class="customerVoice_wrap">
        <div class="cvh_title">总览</div>
        <div class="customerVoice_content">
            <div class="customerVoice_content_top">
                <aiAnalysis :data="cvAlAnalyse" :loading="loadingAiAnalyse" style="margin-bottom: 24px;" />
                <div class="cv_chart2">
                    <div class="cvc_left">
                        <FeedbackChart :colorObject="colorObject" />
                    </div>
                    <!-- <div class="cvc_right">
                        <QuadrantChart :colorObject="colorObject" />
                    </div> -->
                </div>
            </div>
            <div style="page-break-after: always;"></div>
            <checkSource />
            <div style="page-break-after: always;"></div>
            <FAQ />
        </div>
    </div>
</template>
<script setup lang="js">
import FeedbackChart from './components/FeedbackChart.vue';
import QuadrantChart from './components/QuadrantChart.vue';
import checkSource from './checkSource/checkSource.vue';
import aiAnalysis from '../components/aiAnalysis.vue'
import FAQ from './components/FAQ.vue'
const cvAlAnalyse = computed(() => g.clientBiStore.cvAlAnalyse);

// 颜色配置
const colorObject = reactive({
    positive: '#04CCA4',
    negative: '#FF6B3B',
})

const loadingAiAnalyse = ref(false)

const init = async () => {
    loadingAiAnalyse.value = true;
    await g.clientBiStore.getVoiceAnalyse()
    loadingAiAnalyse.value = false;
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

onMounted(async () => {
    init()
});
</script>

<style lang="scss">
.customerVoice_wrap {
    padding: 24px 0;
    background-color: #fff;
    min-height: 100vh;

    .customerVoice_content {
        // padding: 15px;
        display: flex;
        flex-direction: column;
        // gap: 16px;
    }

    .customerVoice_content_top,
    .cv_checkSource {
        padding: 24px 15px;
    }

    .cvh_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }

    .cv_chart2 {
        display: flex;
        justify-content: space-between;
        height: 500px;

        box-sizing: border-box;

        .cvc_left,
        .cvc_right {
            width: 100%;
            height: 460px;
            box-sizing: border-box;
            background: #fff;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
            border-radius: 8px;
        }
    }
}
</style>