import { mergeArrays, timeToSecond } from "./tools.js";

export function getLaCuEvaluate() {
  let list = [];
  const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
  if (ar) {
    const raw = ar.find((x) => x.systemId == 101).report;
    if (raw && raw["客户情绪评估"]) {
      const users = raw["客户情绪评估"].map((x) => x["姓名"]);
      list = users.map((x) => {
        return {
          姓名: x,
          客户反馈: raw["客户反馈"].find((y) => y["姓名"] == x),
          客户侧整体态度: raw["客户情绪评估"].find((y) => y["姓名"] == x),
        };
      });

      const { saleAnalyseList } = g.postmeetStore.data;
      for (let item of list) {
        if (item["客户反馈"]) {
          const a1 = item["客户反馈"]["积极反馈"];
          const a2 = item["客户反馈"]["消极反馈"];
          if (a1.length > 0 || a2.length > 0) {
            saleAnalyseList[item["姓名"]] = [];
            if (a1.length > 0) {
              const sortedA1 = a1.sort((x, y) => (x["时间戳"] > y["时间戳"] ? 1 : -1));
              saleAnalyseList[item["姓名"]].push({
                label: `积极反馈`,
                count: sortedA1.length,
                value: sortedA1,
                type: "attitude",
              });
            }
            if (a2.length > 0) {
              const sortedA2 = a2.sort((x, y) => (x["时间戳"] > y["时间戳"] ? 1 : -1));
              saleAnalyseList[item["姓名"]].push({
                label: `负面反馈`,
                count: sortedA2.length,
                value: sortedA2,
                type: "attitude",
              });
            }
          }
        }
      }
      g.postmeetStore.setValue("saleAnalyseList", saleAnalyseList);
    }
  }
  return list;
}

export function getLaCompetitorStatus() {
  let list = [];
  let list_risk = [];
  const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
  if (ar) {
    const t1 = ar.find((x) => x.systemId == 102);
    if (t1 && t1.report) {
      list = t1.report || [];
    }

    // risk
    const t2 = ar.find((x) => x.systemId == 104);
    if (t2 && t2.report && t2.report["风险项"]) {
      const item = t2.report["风险项"];
      list_risk = [
        {
          id: "high",
          name: "高风险",
          count: item.filter((x) => x["风险等级"] == "高").length,
        },
        {
          id: "middle",
          name: "中风险",
          count: item.filter((x) => x["风险等级"] == "中").length,
        },
        {
          id: "low",
          name: "低风险",
          count: item.filter((x) => x["风险等级"] == "低").length,
        },
      ].filter((x) => x.count > 0);

      const { saleAnalyseList } = g.postmeetStore.data;
      saleAnalyseList["风险项"] = list_risk.map((x) => {
        return {
          label: x.name,
          count: x.count,
          value: item.filter((y) => y["风险等级"] == x.name.replace("风险", "")),
          type: "risk",
        };
      });
      g.postmeetStore.setValue("saleAnalyseList", saleAnalyseList);
    }
  }
  return [list, list_risk];
}

export function getLaCompetitorMenu() {
  let list = [];
  const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
  if (ar) {
    const raw = ar.find((x) => x.systemId == 102).report;
    if (raw) {
      list = raw.map((x) => {
        return {
          count: x["count"],
          name: (x["competitor"] && x["competitor"]["commonName"]) || "",
          data: x,
        };
      });
      const { saleAnalyseList } = g.postmeetStore.data;
      saleAnalyseList["竞争对手"] = list
        .filter((x) => x.count > 0)
        .map((x) => {
          return {
            label: x.name,
            count: x.count,
            value:
              (x.data["competitorReport"] &&
                x.data["competitorReport"]["竞品提及内容的详细分析"]) ||
              "",
            type: "competitor",
          };
        });
      g.postmeetStore.setValue("saleAnalyseList", saleAnalyseList);
    }
  }
  return list;
}

export function setTodoList(list) {
  if (list.length > 0) {
    const { saleAnalyseList } = g.postmeetStore.data;
    saleAnalyseList["待办"] = [
      { label: `共${list.length}项`, count: list.length, value: list, type: "todo" },
    ];
    g.postmeetStore.setValue("saleAnalyseList", saleAnalyseList);
  }
}

export function mergeTodoList(list, new_list) {
  const list_update = new_list.map((x) => {
    const { id, done } = x;
    return {
      id,
      done,
      待办内容: x.todoContent,
      时间戳: x.timestamp,
      原话: x.originalWords,
      执行人: x.executor,
      截止时间: x.deadline,
    };
  });
  list = mergeArrays(list, list_update);
  return list;
}

export function getLaTodoList() {
  let list = [];
  const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
  if (ar) {
    const t1 = ar.find((x) => x.systemId == 103);
    if (t1 && t1.report) {
      list = t1.report["待办事项"] || [];
    }
    const { updatedAsrContent } = g.postmeetStore.data.asrRaw;
    if (updatedAsrContent.hasOwnProperty("Todos")) {
      list = mergeTodoList(list, updatedAsrContent["Todos"]);
    }
    setTodoList(list);
  }
  return list;
}


export function getAnalyseTime(type, row) {
  if (!row) {
    return "";
  }
  let value = "";
  switch (type) {
    case "attitude":
    case "todo":
    case "competitor":
      value = row["时间戳"];
      break;
    case "risk":
      value = (row["来源"] && row["来源"]["时间戳"]) || "";
      break;
  }
  //为了解决分析面板时时间戳问是真实时间，但是在页面上显示的都是相对时间的问题
  let markTime = adjustTime2(value);
  if (markTime < 0) {
    markTime = adjustTime(value);
  }

  const formattedTime = formatTime(markTime);
  return formattedTime;
}

export const adjustTime = (vtime) => {
  const { needCalRealTime } = g.postmeetStore.data.asrRaw;

  let markTime = 0;
  if (!needCalRealTime) {
    markTime = timeToSecond(vtime) + 1;
  } else {
    markTime =
      timeToSecond(vtime) -
      timeToSecond(g.postmeetStore.data.playItem.startTime.slice(10)) +
      1;
  }
  return markTime;
}

export const adjustTime2 = (vtime) => {
  const { needCalRealTime } = g.postmeetStore.data.asrRaw;
  let markTime = 0;
  if (!needCalRealTime) {
    markTime =
      timeToSecond(vtime) -
      timeToSecond(g.postmeetStore.data.recordInfo.startTime.slice(10)) +
      1;
  } else {
    markTime = timeToSecond(vtime) + 1;
  }
  return markTime;
}

//把秒数转化成00:00:00格式
export const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}


export function getH1(type, row) {
  let value = "";
  switch (type) {
    case "attitude":
    case "todo":
      value = row["原话"];
      break;
    case "competitor":
      value = (row["上下文分析"] && row["上下文分析"]["客户原话"]) || "";
      break;
    case "risk":
      value = (row["来源"] && row["来源"]["原话"]) || "";
      break;
  }
  return value;
}

function getSi(type, row) {
  let value = "";
  switch (type) {
    case "todo":
      value = row["序号"] || "";
      break;
  }
  return value;
}

function getTimeBySi(si) {
  let value = "";
  const a = g.postmeetStore.data.subtitles.find((x) => x.si == si);
  if (a) {
    value = a.time;
  }
  return value;
}

export function ajustHeightData(data) {
  let prevHeight = 0;
  const result = [];
  const min_space = 230;

  for (const item of data) {
    if (result.length === 0 || item.height - prevHeight >= min_space) {
      result.push(item);
      prevHeight = item.height;
    } else {
      const newHeight = prevHeight + min_space;
      result.push({ ...item, height: newHeight });
      prevHeight = newHeight;
    }
  }

  return result;
}

export function covertToComments(sbTimeHeights) {
  if (!sbTimeHeights) {
    return;
  }
  const a = toRaw(g.postmeetStore.data.saleAnalyseList);
  let list = [];
  for (let [key, rows] of Object.entries(a)) {
    for (let row of rows) {
      const type = row.type;
      if (!Array.isArray(row.value)) {
        row.value = [row.value];
        row.count = 1;
      }
      for (let item of row.value) {
        let unit = {
          key,
          type,
          label: row.label,
          time: getAnalyseTime(type, item),
          si: getSi(type, item),
          data: item,
        };
        if (unit.si) {
          unit.time = getTimeBySi(unit.si);
        }
        if (unit.time || unit.si) {
          list.push(unit);
        }
      }
    }
  }
  list = list.sort((a, b) => (a.time >= b.time ? 1 : -1));
  g.postmeetStore.setValue("sbComments", list);
}

export function getCommentTimeText() {
  const list = [];
  const rawList = Object.values(g.postmeetStore.data.saleAnalyseList);
  for (let rows of rawList) {
    for (let row of rows) {
      const type = row.type;
      if (!Array.isArray(row.value)) {
        row.value = [row.value];
        row.count = 1;
      }
      for (let item of row.value) {
        const txt = getH1(type, item);
        const time = getAnalyseTime(type, item);
        if (txt && time) {
          let unit = {
            time,
            txt,
          };
          list.push(unit);
        }
      }
    }
  }
  return list;
}

export function mergeSbCommentsToMessages(messages_all) {
  const sbComments = g.postmeetStore.data.sbComments || [];
  if (!sbComments.length) {
    return messages_all;
  }

  // 深拷贝messages_all以避免直接修改原数据
  let mergedMessages = [...messages_all];

  // 按时间对评论进行分组
  const commentsByTime = {};
  for (const comment of sbComments) {
    const commentTime = comment.time;
    if (!commentsByTime[commentTime]) {
      commentsByTime[commentTime] = [];
    }
    commentsByTime[commentTime].push(comment);
  }

  // 遍历每个时间点的评论组，将它们添加到对应的字幕中
  Object.entries(commentsByTime).forEach(([time, comments]) => {
    // 找到时间小于等于评论时间的最后一条字幕
    const targetIndex = mergedMessages.reduce((lastIndex, msg, currentIndex) => {
      if (msg.time <= time) {
        return currentIndex;
      }
      return lastIndex;
    }, -1);

    if (targetIndex !== -1) {
      // 如果找到对应字幕，将所有同一时间点的评论添加到其list属性中
      if (!mergedMessages[targetIndex].list) {
        mergedMessages[targetIndex].list = [];
      }
      // 确保不重复添加相同的评论
      comments.forEach(comment => {
        const isDuplicate = mergedMessages[targetIndex].list.some(
          existing =>
            existing.time === comment.time &&
            existing.type === comment.type &&
            existing.key === comment.key &&
            existing.label === comment.label
        );
        if (!isDuplicate) {
          mergedMessages[targetIndex].list.push(comment);
        }
      });
    }
  });

  return mergedMessages;
}
