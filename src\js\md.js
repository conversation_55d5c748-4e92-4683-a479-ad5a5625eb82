import { marked } from "marked"

export function md2html(markdown_txt) {
    if (!markdown_txt) return '';

    if (typeof markdown_txt != 'string') {
        console.log('md2html markdown_txt is not string', markdown_txt);
        return '';
    }

    // 需要把文本里的 \n    ### 换成\n###，通过正则来去掉\n和###中间的空格
    markdown_txt = markdown_txt.replace(/\n\s+###/g, '\n###');
    markdown_txt = markdown_txt.replace(/\n\s+##/g, '\n##');

    // 创建自定义的renderer
    const renderer = new marked.Renderer();

    // 重写 renderer 中的方法，使链接在新窗口中打开
    renderer.link = function (href, title, text) {
        return `<a href="${href}" target="_blank">${text}</a>`;
    };

    // 设置 marked 使用自定义的 renderer
    marked.setOptions({
        renderer: renderer
    });

    // 渲染 Markdown 文档
    // const markdown = '[Example Link](https://example.com)';
    const html = marked(markdown_txt);
    return html;
}


export function mdToText(markdown) {
    // 替换标题（#、## 等）
    let plainText = markdown.replace(/#+\s/g, '');

    // 替换粗体和斜体（**、*、__、_）
    plainText = plainText.replace(/(\*\*|__)(.*?)\1/g, '$2');
    plainText = plainText.replace(/(\*|_)(.*?)\1/g, '$2');

    // 替换链接格式 [text](url)
    plainText = plainText.replace(/\[(.*?)\]\(.*?\)/g, '$1');

    // 替换图片格式 ![alt text](url)
    plainText = plainText.replace(/!\[(.*?)\]\(.*?\)/g, '$1');

    // 替换代码块和内联代码
    plainText = plainText.replace(/`{1,3}([\s\S]*?)`{1,3}/g, '$1');

    // 替换引用（>）
    plainText = plainText.replace(/^\s*>\s+/gm, '');

    // 替换分隔线（---、***）
    plainText = plainText.replace(/^\s*[-*]{3,}\s*$/gm, '');

    return plainText;
}