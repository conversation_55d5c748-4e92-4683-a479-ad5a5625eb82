<template>
  <div class="task-completion" v-ai-tip="'bottom-right'">
    <div class="task-header">
      <span class="title">任务达成情况</span>
      <span class="target-value">达标值：{{ targetValue }}%</span>
      <!-- <el-dropdown trigger="click">
        <span class="dropdown-link">
          {{ currentType.label }}
          <el-icon class="el-icon--right">
            <arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="task-completion-dropdown">
            <el-dropdown-item v-for="option in typeOptions" :key="option.id" @click="onClickType(option)">
              {{ option.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
</el-dropdown> -->
    </div>

    <div class="vertical-bars-container" v-if="barData.length > 0">
      <div class="vertical-bars">
        <div class="bar-item" v-for="item in barData" :key="item.text">
          <div class="bar-container">
            <div class="vertical-bar" :class="{
              'success': item.percentage >= targetValue,
              'danger': item.percentage < targetValue
            }" :style="{ height: item.percentage + '%' }">
            </div>
          </div>
          <div class="bar-label">
            <div class="percentage">{{ item.percentage }}%</div>
            <el-tooltip :content="item.text" placement="bottom" :disabled="item.text.length <= 6">
              <div class="text">{{ truncateText(item.text, 6) }}</div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <el-empty description="暂无数据" />
    </div>

    <PieChartWithTitle title="任务达成详情" sub-title="点击饼图图例可跳转查看拜访详情" :chart-data="chartData" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import PieChartWithTitle from '@/app_client/components/TeamBoardCore/components/PieChartWithTitle.vue'
import { getAssessment } from '@/app_client/tools/api'
import { getXmTopic } from "@/app_admin/tools/api.js"
import { round, getUrlParam } from '@/js/utils'

// 文本截断函数
const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength) + '...'
}

const targetValue = ref(80)
const userData = ref({})
const periodType = ref('')
const currentType = ref({ label: '', id: 0 })
const typeOptions = ref([])

let achievementNum = ref(0)
let unAchievementNum = ref(0)
let unDetermineNum = ref(0)

const chartData = computed(() => {
  return [
    {
      value: achievementNum.value,
      name: '达成',
      type: 'achievement',
      color: '#67C23A'
    },
    {
      value: unAchievementNum.value,
      name: '未达成',
      type: 'unAchievement',
      color: '#F56C6C'
    },
    {
      value: unDetermineNum.value,
      name: '结果未评估',
      type: 'unDetermine',
      color: '#909399'
    }
  ]
})

const barData = ref([])
//任务达成详情
const getEvaluationDetail = async () => {
  const orgId = g.appStore.user.ssoOrgId;
  const userId = userData.value.ssoUserId;
  const startTime = getUrlParam('startDate');
  const endTime = getUrlParam('endDate');
  const param = {
    // topicId: currentType.value.id
  }
  if (startTime && endTime) {
    param.startTime = startTime
    param.endTime = endTime
  }
  const res = await getAssessment(periodType.value, orgId, userId, 'COMPLETION', param)
  if (res.code === 0 && res.data?.datas) {
    barData.value = res.data.datas.map(item => ({
      text: item.assessmentName,
      percentage: round(item.assessmentValue, 0)
    }))
  }
}

const onClickType = (option) => {
  currentType.value = option
  getEvaluationDetail()
}

const getTaskStandardScore = async () => {
  if (g.clientBoardStore.topicData.length > 0) {
    typeOptions.value = g.clientBoardStore.topicData
    currentType.value = typeOptions.value[0]
    getEvaluationDetail()
    return
  }
  const res = await getXmTopic()
  if (res.code === 0 && res.data.customerTopics) {
    typeOptions.value = res.data.customerTopics
    g.clientBoardStore.topicData = typeOptions.value
    currentType.value = typeOptions.value[0]
    getEvaluationDetail()
  }
}

const init = async (data, type) => {
  userData.value = data
  periodType.value = type
  const totalNum = data.totalVisitCount
  achievementNum.value = data.taskCompletePassCount
  unAchievementNum.value = data.taskCompleteNotPassCount
  unDetermineNum.value = totalNum - achievementNum.value - unAchievementNum.value
  targetValue.value = data.taskStandardScore
  getTaskStandardScore()
}

defineExpose({
  init
})  
</script>

<style scoped lang="scss">
.task-completion {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  width: calc(0.5 * (100vw - 250px));
  border: 1px solid #E4E7ED;

  .task-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-weight: 500;
      margin-right: 12px;
    }

    .target-value {
      color: #999;
      font-size: 14px;
      margin-right: auto;
    }

    .dropdown-link {
      color: #666;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  .vertical-bars-container {
    height: 200px;
    margin: 20px 0;
    overflow-x: auto;
    overflow-y: hidden;

    // 确保滚动条始终显示
    &::-webkit-scrollbar {
      height: 8px;
      background: transparent;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 4px;
      margin: 0 20px; // 与内容左右边距保持一致
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 4px;

      &:hover {
        background: #bfbfbf;
      }

      &:active {
        background: #999999;
      }
    }

    // 对于Firefox浏览器的滚动条样式
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 #f5f5f5;
  }

  .vertical-bars {
    display: flex;
    height: 100%;
    padding: 0 20px;
    min-width: fit-content;
    gap: 20px;

    .bar-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 80px;
      flex-shrink: 0;

      .bar-container {
        height: 160px;
        width: 24px;
        background: #f5f5f5;
        border-radius: 12px 12px 0 0;
        position: relative;
        overflow: hidden;

        .vertical-bar {
          position: absolute;
          bottom: 0;
          width: 100%;
          border-radius: 2px;
          transition: height 0.3s ease;

          &.success {
            background-color: #52C41A;
          }

          &.danger {
            background-color: #F5222D;
          }
        }
      }

      .bar-label {
        margin-top: 8px;
        text-align: center;
        width: 80px;

        .percentage {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }

        .text {
          font-size: 12px;
          color: #999;
          width: 100%;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }
      }
    }
  }

}

.task-completion-dropdown {
  height: 200px;
  overflow-y: auto;
}
</style>