<template>
    <el-dialog v-model="visible" title="消息清空后，不可恢复" width="316px" :close-on-click-modal="false" :show-close="false">
        <div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="loading">
                    结束
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { clearUserMsg } from '@/app_electron/tools/api.js';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    ongoingMeetSubject: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)
const loading = ref(false)

watch(() => props.modelValue, (newVal) => {
    visible.value = newVal
})

watch(() => visible.value, (newVal) => {
    emit('update:modelValue', newVal)
})

const handleCancel = () => {
    visible.value = false
}

const handleConfirm = async () => {
    try {
        loading.value = true
        const resp = await clearUserMsg()
        if (resp.code === 0) {
            ElMessage.success('消息清空成功')
            g.electronStore.sendMessage('main', 'reload_schedule', 2000)
            visible.value = false
        } else {
            ElMessage.error("消息清空失败，请稍后重试")
        }
    } catch (error) {
        ElMessage.error("消息清空失败，请稍后重试")
        loading.value = false
    }
}
</script>