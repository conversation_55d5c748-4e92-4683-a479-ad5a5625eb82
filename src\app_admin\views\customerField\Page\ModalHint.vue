<template>
    <Modal ref="refModal" @callback="cbModal" destroy-on-close>
        <div>
            该字段已被设为查重字段，请先到「客户类型 - 行编辑 - 查重设置」取消勾选后再删除。
        </div>
    </Modal>
</template>

<script setup>
import Modal from '@/components/Modal.vue';
const refModal = ref();
const emit = defineEmits(['callback']);

const show = () => {
    const cfg = {
        width: "480px",
        title: "删除提醒"
    }
    refModal.value.show(cfg);
}

const cbModal = (action) => {
}

defineExpose({ show, cbModal, Modal });
</script>

<style lang="scss">
.el-dialog__body {
    padding: 15px 24px 5px 24px;
}
</style>