<template>
  <div class="select_mm_btn" @click="onShowSelectWin">
    <div class="sb_left">
      <LogoIcon />
    </div>
    <div class="sb_right">录制记录</div>
  </div>

  <el-dialog v-model="dialogVisible" title="选择沟通沟通记录" width="500" :before-close="handleClose" class="select_mm_dialog">
    <el-input v-model="param.searchKey" placeholder="请输入沟通/客户名称搜索" class="keyword_input" :prefix-icon="Search" clearable
      @change="query" />
    <ul v-loading="loading">
      <li v-for="item in list" :key="item.conferenceId">
        <div class="b_left">
          <input type="radio" v-model="rdSelect" :value="item.conferenceId" />
        </div>
        <div class="b_middle">
          <div class="bm_title">
            {{ item.subject }}
          </div>
          <div class="bm_sub">{{ item.startTime }} {{ item.salesMateCustomerName }}</div>
        </div>
        <div class="b_right" @click="onView(item)">
          <ArrowIcon />
        </div>
      </li>
    </ul>

    <div class="pager_box" v-show="totalNum > param.pageSize">
      <el-pagination layout="prev, pager, next" :page-size="5" :total="totalNum" @current-change="onPageChange">
      </el-pagination>
    </div>

    <el-empty :image-size="120" v-if="list.length == 0 && !loading" description="未搜索到沟通" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import LogoIcon from "@/app_client/icons/logo.vue";
import ArrowIcon from "@/app_client/icons/rightArrow.vue";
import WarningIcon from "@/app_client/icons/yellowWarning.vue";
import { Search } from "@element-plus/icons-vue";
import { getMeetingHistory, getCustomerRequirement } from "@/app_client/tools/api.js"
import config from "@/js/config.js"

const emit = defineEmits(["callback"]);
const dialogVisible = ref();
const rdSelect = ref([]);
const list = ref([]);
const totalNum = ref(0)
const loading = ref(false);
let bot_type = ''

const param = ref({
  "searchKey": "", //搜索内容
  "pageSize": 5, //每页记录数
  "pageNumber": 1, //当前页数
})

const handleClose = () => {
  dialogVisible.value = false;
};

const onConfirm = () => {
  const confId = rdSelect.value;
  const item = list.value.find(x => x.conferenceId == confId)
  loading.value = true;
  getCustomerRequirement(confId).then(resp => {
    if (resp.code == 0) {
      item['requirement'] = resp.data
      emit("callback", 'choose_mm', item)
      dialogVisible.value = false;
    }
    loading.value = false;
  })
}

const query = () => {
  loading.value = true;
  getMeetingHistory(param.value).then(resp => {
    if (resp.code == 0) {
      list.value = resp.data.datas;
      totalNum.value = resp.data.totalNum;
    }
    loading.value = false;
  })
}

const onShowSelectWin = () => {
  if (bot_type) {
    dialogVisible.value = true;
    query()
  } else {
    ElMessage({
      message: '请先选择分类',
      type: 'warning',
    })
  }
};

const onPageChange = (page) => {
  param.value['pageNumber'] = page;
  query()
}

const onView = (item) => {
  const param = {
    url: `${config.publicPath}/#/postmeet/record/${item.conferenceId}`,
    title: '录制纪要'
  }
  g.clientStore.openUrl(param)
}

const setBotType = (type) => {
  bot_type = type;
}

defineExpose({
  onShowSelectWin, query, handleClose, dialogVisible,
  LogoIcon, WarningIcon, Search, onConfirm, setBotType,
  rdSelect, ArrowIcon, param, list, loading
});
</script>

<style lang="scss">
.select_mm_btn {
  width: 146px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  cursor: pointer;
  margin-left: 10px;
  margin-bottom: 14px;

  .sb_left {
    width: 24px;
    height: 24px;
    border-radius: 2px;
    margin-right: 6px;
    font-size: 24px;
  }

  .sb_right {
    height: 22px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    color: #595959;
    line-height: 22px;
  }
}

.select_mm_dialog {
  padding: 0;

  .el-dialog__header {
    padding: 16px 24px;
    border-bottom: 1px solid #e9e9e9;
  }

  .el-dialog__body {
    padding: 16px 24px;
    min-height: 300px;

    .keyword_input {
      width: 100%;
    }

    .el-loading-mask {
      margin-top: 115px;
    }

    ul {
      li {
        display: flex;
        flex-direction: row;
        height: 60px;
        align-items: center;
        margin: 4px 0;

        .b_left {
          width: 30px;
        }

        .b_middle {
          flex-grow: 1;

          .bm_title {
            height: 24px;
            font-size: 16px;
            color: #262626;
            line-height: 24px;
          }

          .bm_sub {
            height: 22px;
            font-size: 14px;
            color: #8c8c8c;
            line-height: 22px;
          }

          .bm_warning {
            display: flex;
            flex-direction: row;

            .bmw_icon {
              width: 12px;
              height: 12px;
              margin: 0 3px;
            }

            .bmw_txt {
              height: 18px;
              font-size: 12px;
              color: #fa8c16;
              line-height: 18px;
            }
          }
        }

        .b_right {
          width: 30px;
          cursor: pointer;
        }
      }
    }

    .pager_box {
      display: flex;
      justify-content: flex-end;

      .el-pagination {
        height: 32px;
      }
    }

  }

  .el-dialog__footer {
    padding: 16px 24px;
  }
}
</style>
