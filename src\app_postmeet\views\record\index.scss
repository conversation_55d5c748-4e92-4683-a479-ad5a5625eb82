.index_wrap {
    .index_main {
        height: calc(100vh - 50px);
        overflow: hidden;

        .column_box {
            display: flex;
            flex-direction: row;
            position: fixed;

            .column-left {
                background-color: #fff;
                position: relative;
                overflow-y: auto;
                overflow-x: hidden;
                float: left;
                height: calc(100vh - 42px);

                .mi_box {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    width: 100%;
                    margin-bottom: 12px;

                    .mi_select4 {
                        margin: 14px 12px 0 0;
                        width: 100px;
                    }

                    .el-radio-button__inner {
                        max-width: 150px;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        overflow: hidden;
                    }

                    .mi_right {
                        padding-top: 13px;
                        display: flex;
                        margin-top: 5px;
                        cursor: pointer;
                        margin-right: 34px;
                        height: 20px;

                        .txt {
                            color: #333;
                        }

                        .icon {
                            color: #333;
                            margin: 4px 4px 0 0;
                        }
                    }

                    .mi_right:hover {

                        .icon,
                        .txt {
                            color: #436bff;
                        }
                    }
                }

                .over_btns {
                    display: flex;
                    flex-direction: row;
                }

                div::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                    background-color: #f5f5f5;
                }

                div::-webkit-scrollbar-thumb {
                    border-radius: 4px;
                    background: #e0e0e0;
                    display: none;
                }

                div::-webkit-scrollbar-track {
                    border-radius: 10px;
                    background-color: #fff;
                }

                .resize-save {
                    position: absolute;
                    top: 0;
                    right: 5px;
                    bottom: 0;
                    left: 0;
                    padding: 16px;
                    overflow-x: hidden;

                    .ai_reload {
                        margin-top: 47px;
                    }
                }

                .resize-save-noright {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .resize-bar {
                    height: inherit;
                    resize: horizontal;
                    cursor: ew-resize;
                    cursor: col-resize;
                    opacity: 0;
                    overflow: scroll;
                }

                /* 拖拽线 */
                .resize-line {
                    position: absolute;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    border-right: 1px solid #eee;
                    pointer-events: none;
                }

                .resize-bar:hover~.resize-line,
                .resize-bar:active~.resize-line {
                    border-left: 1px solid #436bff;
                }

                .resize-bar::-webkit-scrollbar {
                    height: inherit;
                }

                /* Firefox只有下面一小块区域可以拉伸 */
                @supports (-moz-user-select: none) {

                    .resize-bar:hover~.resize-line,
                    .resize-bar:active~.resize-line {
                        border-left: 1px solid #436bff;
                    }

                    .resize-bar:hover~.resize-line::after,
                    .resize-bar:active~.resize-line::after {
                        content: '';
                        position: absolute;
                        width: 16px;
                        height: 16px;
                        bottom: 0;
                        right: -8px;
                        background-size: 100% 100%;
                    }
                }

                .drag-button {
                    align-items: center;
                    background-color: #fff;
                    background-size: 100%;
                    border-radius: 20px;
                    box-shadow: -1px 0 0 0 #d3d6db80;
                    display: flex;
                    height: 36px;
                    justify-content: center;
                    right: -5px;
                    pointer-events: none;
                    position: absolute;
                    top: 320px;
                    width: 16px;
                    z-index: 100;
                    overflow: hidden;
                }

                .play_fold_icon {
                    position: fixed;
                    right: 25px;
                    top: 60px;
                    width: 40px;
                    height: 40px;
                    background-color: #fff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }

                .play_fold_icon:hover {
                    color: #436bff;
                }
            }

            .column-right {
                position: relative;
                height: calc(100vh - 73px);
                padding: 16px 16px 0 16px;
                box-sizing: border-box;
                overflow-y: hidden;
                background: #F7F8FC;
            }

        }


        .only_video {
            .video_box {
                margin: 0 auto;
            }
        }
    }

    .loading {
        margin-top: 100px;
    }
}