<template>
    <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="拜访过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="播放器-收起-音频条-交互" transform="translate(-32.000000, -118.000000)">
                <g id="暂停" transform="translate(32.000000, 118.000000)">
                    <circle id="椭圆形" fill="#436BFF" cx="18" cy="18" r="18"></circle>
                    <path
                        d="M14.4997225,11.3652245 L24.4986117,17.1338144 C24.9769927,17.4098035 25.1410639,18.0213414 24.8650748,18.4997225 C24.7772466,18.651958 24.6508472,18.7783574 24.4986117,18.8661856 L14.4997225,24.6347755 C14.0213414,24.9107646 13.4098035,24.7466935 13.1338144,24.2683124 C13.0461491,24.1163592 13,23.9440179 13,23.7685899 L13,12.2314101 C13,11.6791253 13.4477153,11.2314101 14,11.2314101 C14.175428,11.2314101 14.3477693,11.2775592 14.4997225,11.3652245 Z"
                        id="矩形" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>
