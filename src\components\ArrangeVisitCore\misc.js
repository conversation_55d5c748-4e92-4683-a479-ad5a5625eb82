import { nowMNAfter, now } from '@/js/utils'

export const defaultParam = () => {
    return {
        "meeting_type": 7,
        "salesMateType": 2,
        customerId: '',
        "salesMateCustomerName": "",//四川海底捞餐饮股份有限公司
        "salesMateTags": "",
        "subject": "",
        "salesGoal": '',
        "startDate": nowMNAfter('yyyy-MM-dd', 30),
        "startTime": nowMNAfter('hh:mm', 30),
        "endDate": nowMNAfter('yyyy-MM-dd', 30, 60),
        "endTime": nowMNAfter('hh:mm', 30, 60),
        "location": '',
        "description": '',
        "onlineMeetUrl": '',
        "salesGoodsCategories": "",
        "salesRelatedType": 1,//1分类 2商品
        "meetingLevel": 0,
        "participantList": [],
        "fileList": [],//{name,path}
        // 实时数据
        "hostUserId": "",
        "hostUserName": "",
        "duration": 0,
    }
}

// 计算沟通时长,根据 endDt= endDate + endTime, startDt= startDate + startTime,duration=endDt-startDt;
const calcDuration = (param) => {
    const endDt = new Date(`${param.endDate} ${param.endTime}`);
    const startDt = new Date(`${param.startDate} ${param.startTime}`);
    const nowDt = new Date(now());
    if (startDt.getTime() > endDt.getTime() || startDt.getTime() < nowDt.getTime()) {
        return 0;
    }
    const duration = Math.floor((endDt - startDt) / (1000 * 60));
    return duration;
}


export const updateParam = (param) => {
    const { id, name } = g.appStore.user;
    param.duration = calcDuration(param);
    param.hostUserId = id;
    param.hostUserName = name;
    return param;
}

export const checkParam = (param) => {
    if (!param.salesMateCustomerName) {
        ElMessage.error('请输入客户名称')
        return false;
    }
    if (!param.salesMateTags) {
        ElMessage.error('选择主题标签')
        return false;
    }
    if (!param.subject) {
        ElMessage.error('输入沟通主题')
        return false;
    }
    if (!param.salesGoal) {
        ElMessage.error('输入沟通目标')
        return false;
    }
    updateParam(param);
    if (!param.duration || param.duration <= 0) {
        ElMessage.error('计划沟通的开始时间不得晚于当前时间')
        return false;
    }

    return true;
}

export const getParList = (allPars, type) => {
    return allPars.filter(item => item.attendeeType == type) || [];
}