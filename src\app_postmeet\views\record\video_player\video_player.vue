<template>
  <div :class="`video_player_wrap ${!!tag ? 'vpw_has_tag' : 'vpw_has_notag'} ${hasAsr == 'yes' ? 'vpw_hasasr' : 'vpw_noasr'
    } `">
    <div v-show="item && item.url" :style="vbox_style" :class="[
      'video_box',
      {
        audio_box: item.format && item.format.toLowerCase() == 'mp4',
        has_analyse: isShowAnalyse,
      },
    ]" @mousemove="onMouseMove(true)" @mouseleave="onMouseMove(false)">
      <video id="myvideoid" ref="videoComponent" :src="`${item.url}`" width="100%" height="100%" controls
        :class="`fsize_${sb_fontsize} ${hasPlayed ? 'video_started' : ''}`"
        :poster="getOssUrl('postmeet_1.png')"></video>
      <div class="cover_txt" :style="cover_style" v-show="showCoverTxt">
        <div class="subject">{{ getSubject() }}</div>
        <div class="note customerName">{{ item.customerName }}</div>

        <div class="note">开始时间: {{ item.startTime }}</div>
        <div class="note">沟通主题: {{ getTags() }}</div>
        <div class="note">时长: {{ item.videoTime }}</div>
        <div class="note">创建者: {{ item.hostName }}</div>
      </div>
      <div class="markers markers_share">
        <div v-for="mark in marksShare" :key="mark.markTime" class="mark" :style="getMarkStyle(mark)"></div>
      </div>
      <div class="markers markers_split">
        <div v-for="mark in marks_split" :key="mark.markTime" class="mark" :style="getMarkStyle(mark)"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  sharesToMarkers,
  tagsToMarkers,
  formateTime,
  splitsToMarkers,
  addTrack,
} from "@/app_postmeet/tools/tools";
import { getOssUrl } from "@/js/utils.js";

export default {
  name: "VideoComponent",
  data() {
    return {
      item: {},
      enable_subtitle: false,
      cover_style: {},
      mouseOverVideo: false,
      playing: false,
      hasPlayed: false,
      showCoverTxt: true,
      subtitle: "",
      tag: "",
      player: null,
      hideMarkers: false,
      isShowAnalyse: false,
      marksTag: [],
      marksShare: [],
      marks_split: [],
      vbox_style: {},
      isShowRight: true,
      subtitle_style: {},
      hasAsr: "",
      updateSubject: "",
      sb_fontsize: 3,
      store: {},
    };
  },
  mounted() {
    this.ListenerVideoTags();
    this.ListenerSetVideoTag();
    this.ListenerSetPlayItem();
    this.ListenerSetVideoTime();
    this.ListenerVideoListeners();
    this.ListenerSetSubTitle();
    this.ListenerVideoControl();
  },
  methods: {
    getOssUrl,
    setIsShowAnalyse(value) {
      this.isShowAnalyse = value;
    },
    getSubject() {
      return this.updateSubject || this.item.subject;
    },
    getTags() {
      return g.postmeetStore.data.asrRaw.tags;
    },
    formatMk(b) {
      if (b && b.length == 11) {
        return b.substring(0, 3) + " " + b.substring(3, 7) + " " + b.substring(7, 11);
      }
      return b;
    },
    getMarkStyleAlways(mark) {
      let rate = (100 * mark.markTime) / (this.item.duration / 1000);
      rate = Math.min(Math.max(rate, 0), 100);
      return { left: rate + "%" };
    },
    getMarkStyle(mark) {
      if (this.hideMarkers) {
        return { display: "none" };
      } else {
        return this.getMarkStyleAlways(mark);
      }
    },
    resize() {
      this.$nextTick(() => {
        let width = 0;
        let height = 0;
        if (this.hasAsr === "yes") {
          const dom = document.getElementsByClassName("resize-save")[0];
          const ratio = 0.95;
          width = ratio * dom.clientWidth;
          height = ratio * dom.clientHeight;
        } else {
          this.isShowRight = false;
          width = document.body.offsetWidth;
          height = document.body.offsetHeight - 68;
        }
        if (this.item.format.toLowerCase() == "mp4") {
          const whratio1 = 1920 / 1080;
          const whratio2 = width / height;
          if (whratio1 < whratio2) {
            width = height * whratio1;
          } else {
            height = width / whratio1;
          }
        } else {
          height = 50;
        }
        if (width && height && width > 0 && height > 0) {
          if (this.hasAsr == "no") {
            // this.vbox_style = {}
            this.vbox_style = { width: width - 100 + "px", height: height + "px" };
            g.emitter.emit("leftWidth", "100%");
          } else {
            this.vbox_style = { height: height + "px" };
            g.emitter.emit("leftWidth", width + "px");
          }
          if (this.hasAsr == "no") {
            this.cover_style["left"] =
              0.5 * (document.body.offsetWidth - width) + 10 + "px";
          }
        }
      });
    },
    setTime(mark) {
      if (mark && typeof mark.markTime === "number" && Number.isFinite(mark.markTime)) {
        const time = mark.markTime;
        if (time >= 0) {
          this.player.currentTime = time;
          this.hasPlayed = true;
        }
      }
    },
    checkNeedHideMarker() {
      this.hideMarkers = this.playing && !this.mouseOverVideo;
    },
    controlVideo(action) {
      const video = document.getElementById("myvideoid");
      if (action === "pause") {
        if (!video.paused) {
          video.pause();
        }
      } else if (action === "play") {
        if (video.paused) {
          const playPromise = video.play();
          if (playPromise) {
            playPromise
              .then(() => { })
              .catch((e) => {
                console.log("play failed");
              });
          }
        }
      }
    },
    ListenerVideoListeners() {
      this.player = document.getElementById("myvideoid");
      let that = this;
      // 播放中
      this.player.addEventListener("playing", () => {
        g.emitter.emit("videoStatus", "playing");
        g.emitter.emit("editmode_false", "");
        that.playing = true;
        that.hasPlayed = true;
        that.checkNeedHideMarker();
        that.showCoverTxt = false;
      });
      // 暂停开始执行的函数
      this.player.addEventListener("pause", () => {
        g.emitter.emit("videoStatus", "pause");
        that.playing = false;
        that.checkNeedHideMarker();
      });
      this.player.addEventListener("timeupdate", function () {
        if (that.player.currentTime > 0) {
          that.showCoverTxt = false;
        }
        const bt = Math.floor(1000 * (that.item.startInSecord + that.player.currentTime));
        g.emitter.emit("videoTimeUpdate", bt);
        g.emitter.emit(
          "videoTimeUpdateNoStart",
          Math.floor(that.player.currentTime * 1000)
        );
      });
    },
    setHasAsr(hasAsr) {
      this.hasAsr = hasAsr;
      this.resize();
    },
    onMouseMove(status) {
      this.mouseOverVideo = status;
      this.checkNeedHideMarker();
    },
    ListenerVideoTags() {
      g.emitter.on("videoTags", ([tag, videoTags, need_set_time]) => {
        this.tag = tag;
        this.marksTag = tagsToMarkers(this.item, videoTags);
        if (need_set_time) {
          this.setTime(this.marksTag[0]);
        }
      });
    },
    ListenerSetVideoTag() {
      g.emitter.on("setVideoTag", (videoTag) => {
        const marksTag = tagsToMarkers(this.item, [videoTag]);
        this.setTime(marksTag[0]);
      });
    },
    ListenerSetVideoTime() {
      g.emitter.on("setVideoTime", (videoTag) => {
        this.setTime(videoTag);
      });
    },
    ListenerSetPlayItem() {
      g.emitter.on("updateSubject", (subject) => {
        this.updateSubject = subject;
      });
      g.emitter.on("updateVideoSpeed", (speed) => {
        this.player.playbackRate = speed;
      });
      g.emitter.on("updateVideoVolume", (volume) => {
        this.player.volume = volume;
      });
      g.emitter.on("updateVideoTime", (currentTime) => {
        this.player.currentTime = currentTime;
      });
      g.emitter.on("setPlayItem", (item) => {
        this.marksTag = [];
        this.subtitle = "";
        this.item = item;
        this.item["videoTime"] = formateTime(item.duration / 1000);
        this.marksShare = sharesToMarkers(item.shareRecords);
        this.marks_split = splitsToMarkers(item.splitTags);
        this.showCoverTxt = item?.format?.toLowerCase() == "mp4";
      });
    },
    ListenerSetSubTitle() {
      g.emitter.on("setSubTitleAll", (messages) => {
        if (this.item?.format?.toLowerCase() == "mp4") {
          this.$nextTick(() => {
            addTrack("myvideoid", this.item.startInSecord, this.item.title, messages);
          });
        }
      });
    },
    ListenerVideoControl() {
      g.emitter.on("video_control", (action) => {
        this.controlVideo(action);
      });
      g.emitter.on("update_editmode", (isedit) => {
        if (isedit) {
          this.controlVideo("pause");
        }
      });
      g.emitter.on("update_text_trace", (enable) => {
        this._enableTextTrack(enable);
      });
      g.emitter.on("video_sb_zoom", (value) => {
        this.sb_fontsize = value;
      });
    },
    _enableTextTrack(status) {
      const textTracks = this.player.textTracks;
      for (let i = 0; i < textTracks.length; i++) {
        textTracks[i].mode = status ? "showing" : "hidden";
      }
      this.enable_subtitle = status;
    },
    switchTextTrack() {
      this._enableTextTrack(!this.enable_subtitle);
    },
  },
};
</script>

<style lang="scss">
@use './video_player.scss';
</style>
