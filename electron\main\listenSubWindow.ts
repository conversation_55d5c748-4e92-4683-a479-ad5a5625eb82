import { BrowserWindow, ipcMain } from 'electron'
import config from '../utils/config'
import { createContextMenu } from './contextMenu'
import windowManager from './windowManager'
import { getPageSize } from '../utils/windowsSizes'
import { errorWriteLocal } from '../utils/errorWriteLocal'
import { WebResourceManager } from '../utils/webResource'
import { addWindowsEvents } from '../utils/tools'
let hasAddPageReadyListener = false
let childWindow;
let newPageData = null;

export const createSubWindows = (arg) => {
    const objArg = JSON.parse(arg);
    const urlParam = objArg.urlParam;
    const winId = urlParam.id;
    newPageData = objArg.newPageData;

    const existingWindow = windowManager.getWindow(winId);
    if (existingWindow && !existingWindow.window.isDestroyed()) {
        if (winId == config.hideWindowId) {
            existingWindow.window.hide()
            return
        }

        existingWindow.window.show();
        existingWindow.window.focus();
        errorWriteLocal('showSubWindows', winId);
        if (newPageData && existingWindow.window && !existingWindow.window.isDestroyed()) {
            existingWindow.window.webContents.send('page-opened', {
                id: winId,
                data: newPageData
            });
        }
        return;
    }

    errorWriteLocal('createSubWindows', winId);

    const param = getPageSize(winId, windowManager);
    const winParam: any = {
        icon: config.iconPath,
        titleBarStyle: 'hiddenInset',  // only mac使用隐藏式标题栏
        resizable: false,
        maximizable: false,
        show: false,
        webPreferences: {
            webSecurity: false,
            webviewTag: true,
            nodeIntegration: true,
            contextIsolation: false,
            preload: config.preload,
        },
        ...param
    }
    childWindow = new BrowserWindow(winParam)
    childWindow.setMenu(null);
    createContextMenu(childWindow)

    // 加载本地资源
    console.log('urlParam.url', urlParam.url)
    if (urlParam.url.startsWith('http')) {
        childWindow.loadURL(urlParam.url)
    } else {
        const { filePath, isFile } = WebResourceManager.getInstance().getLoadPath(urlParam.url)
        if (isFile) {
            childWindow.loadFile(filePath, { hash: urlParam.url })
        } else {
            childWindow.loadURL(filePath)
        }
    }

    // 将子窗口信息存储到 windowManager
    windowManager.addWindow(childWindow, urlParam.id);

    // 监听窗口从最小化恢复的事件
    addWindowsEvents(childWindow)

    childWindow.once('ready-to-show', () => {
        console.log('ready-to-show', winId)
        if (winId == config.hideWindowId) {
            childWindow.hide()
            return
        }
        childWindow.show()
    })
    //监听窗口的 close事件
    childWindow.on('close', (e) => {
        // if (!windowManager.isWindowCloseable(urlParam.id)) {
        //     e.preventDefault();
        //     childWindow.show()
        //     return;
        // }
        windowManager.removeWindow(urlParam.id)
    })

    if (newPageData) {
        if (!hasAddPageReadyListener) {
            hasAddPageReadyListener = true;
            ipcMain.on('pageReady', (event, id) => {
                if (childWindow && !childWindow.isDestroyed()) {
                    childWindow.webContents.send('page-opened', { id, data: newPageData })
                }
            })
        }
    }
}