<template>
  <div class="visit-detail" v-loading="loading">
    <div class="section flex-col">
      <div class="item">
        <span class="subject">{{ plan.subject }}</span>
        <span v-if="!isTeamPage && plan.notMe" class="visit-tag">协访</span>
      </div>
      <div class="item2">
        <div class="tags">
          <div v-if="plan.salesMateTags">
            <span class="tag" v-for="tag in plan.salesMateTags?.split(',').filter((x) => !!x)">{{ tag }}</span>
          </div>
          <span class="time" v-if="!isElectronUrl">{{ getWebTime() }}</span>
        </div>
        <div class="btns flex-row"
          v-if="!readonly && !plan.completed && plan.status != 'ongoing' && !plan.notMe && !isTeamPage">
          <div class="icon-container" @click="onEdit">
            <DetailEdit />
          </div>
          <div class="icon-container" @click="onDelete">
            <DetailDelete />
          </div>
        </div>
      </div>
    </div>
    <div class="section electron-times" v-if="isElectronUrl">
      <div class="time-container">
        <div class="time-block">
          <div class="time">{{ plan.startDt }}</div>
          <div class="date">{{ getEleDate(plan.scheduleStartDate) }}</div>
        </div>
        <div class="duration">{{ plan.duration }}分钟</div>
        <div class="time-block">
          <div class="time">{{ plan.endDt }}</div>
          <div class="date">{{ getEleDate(plan.scheduleStartDate) }}</div>
        </div>
      </div>
    </div>
    <div class="section flex-row" v-if="!isElectronUrl && !readonly">
      <el-button type="default" @click="onReady">准备</el-button>
      <el-button type="primary" @click="onReview" v-if="plan.showBtnReview">回顾</el-button>
      <el-button type="primary" @click="onStart" v-if="plan.showBtnStart">开始</el-button>
    </div>
    <div class="divider"></div>
    <div class="section">
      <div class="item2">
        <IconPicker iconName="customer" />
        <span class="detail-text">{{ plan.salesMateCustomerName }}</span>
      </div>
    </div>
    <div class="section" v-show="plan.onlineMeetUrl">
      <div class="item company-link">
        <IconPicker iconName="meeting" />
        <div class="detail-text flex-col">
          <el-button type="primary" class="join-button" @click="openMeetingUrl" plain
            :disabled="plan.inProgress || !validateUrl(plan.onlineMeetUrl)">加入沟通</el-button>
          <div class="url-line flex-row">
            <div class="url-text">
              {{ plan.onlineMeetUrl }}
            </div>
            <div class="copy-btn" @click="copyUrl">
              <CopyIcon />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section flex-col">
      <div class="item flex-row" v-if="plan.participantList">
        <IconPicker iconName="participants" />
        <div class="participants flex-col">
          <div class="par_row flex-col">
            <div class="title">内部参会人</div>
            <div class="content">{{ getInnerParticipants() }}</div>
          </div>
          <div class="par_row flex-col" v-if="getOuterParticipants()">
            <div class="title">客户参会人</div>
            <div class="content">{{ getOuterParticipants() }}</div>
          </div>
        </div>
      </div>
      <div class="item" v-if="plan.location">
        <IconPicker iconName="location" />
        <span class="detail-text">{{ plan.location }}</span>
      </div>
      <div class="item" v-if="plan.salesGoal">
        <IconPicker iconName="goal" />
        <span class="detail-text">{{ plan.salesGoal }}</span>
      </div>
      <div class="item" v-if="plan.description">
        <IconPicker iconName="note" />
        <div class="detail-text description-wrapper">
          <div class="description" :class="{ expanded: isExpanded }">
            {{ plan.description }}
          </div>
          <div class="expand-btn" v-if="showExpandBtn" @click="toggleExpand">
            {{ isExpanded ? "收起" : "展开" }}
            <component :is="isExpanded ? ArrowDown : ArrowUp" class="fold-icon" />
          </div>
        </div>
      </div>
      <div class="item" v-if="plan.fileList?.length > 0">
        <IconPicker iconName="attachment" />
        <AttachmentList :attachments="plan.fileList" />
      </div>
    </div>
    <div class="section-btns flex-row" v-if="isElectronUrl">
      <el-button type="default" @click="onReady">准备</el-button>
      <el-button type="primary" @click="onReview" v-if="plan.showBtnReview">回顾</el-button>
      <el-button type="primary" @click="onStart" v-if="plan.showBtnStart">开始</el-button>
    </div>
  </div>
</template>

<script setup>
import IconPicker from "@/components/IconPicker.vue";
import AttachmentList from "./AttachmentList.vue";
import { getScheduleDetail } from "@/js/api";
import {
  updatePlanApiResult,
  formatDate,
  validateUrl,
  jsOpenNewWindow,
} from "@/js/utils";
import DetailEdit from "@/icons/detail_edit.vue";
import DetailDelete from "@/icons/detail_delete.vue";
import ArrowUp from "@/icons/arrow_up.vue";
import ArrowDown from "@/icons/arrow_down.vue";
import CopyIcon from "@/icons/copy.vue";

const emit = defineEmits(["callback"]);
const isElectronUrl = ref(location.href.includes("electron"));
const isTeamPage = ref(location.href.indexOf('/team') > 0);
const loading = ref(false);
const plan = ref({
  participantList: [],
  fileList: [],
  salesMateTags: "",
  scheduleStartDate: "",
  startWeek: "",
  startTime: "",
  endTime: "",
});
const isExpanded = ref(false);
const showExpandBtn = ref(false);
const readonly = ref(false);


// 修改检测逻辑
const checkTextOverflow = () => {
  nextTick(() => {
    const element = document.querySelector(".description");
    if (element) {
      // 检查文本是否超过4行
      const lineHeight = 22; // 行高
      const maxHeight = lineHeight * 4; // 4行的总高度
      showExpandBtn.value = element.scrollHeight > maxHeight;
    }
  });
};

// 在数据加载完成后检查
watch(
  () => plan.value.description,
  () => {
    if (plan.value.description) {
      checkTextOverflow();
    }
  },
  { immediate: true }
);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const getInnerParticipants = () => {
  return plan.value.participantList
    .filter((item) => item.attendeeType == "PARTNER_ATTENDEE")
    .map((item) => item.name)
    .join(", ");
};

const getOuterParticipants = () => {
  return plan.value.participantList
    .filter((item) => item.attendeeType == "CUSTOMER_ATTENDEE")
    .map((item) => item.name)
    .join(", ");
};

// 10月16日 周三 19:00-20:00(GTM+8)
const getWebTime = () => {
  const { startDate, startWeek, startTime, endTime } = plan.value;
  return `${startDate} ${startWeek} ${startTime} - ${endTime}(GTM+8)`;
};

const getEleTime = (time) => {
  if (!time) return ""; // Add this lin
  return time.substring(11, 16);
};

const getEleDate = (time) => {
  return formatDate(time, "yyyy年MM月dd日");
};

const onStart = () => {
  g.electronStore.joinMeeting(toRaw(plan.value));
};

const onReview = () => {
  g.clientStore.viewPlanRecord(toRaw(plan.value));
};

const onReady = () => {
  if (isElectronUrl.value) {
    g.electronStore.openUrl("/prepare/" + plan.value.scheduleId);
  } else {
    const url = "#/prepare/" + plan.value.scheduleId;
    jsOpenNewWindow(url, "_blank");
  }
};

const openMeetingUrl = () => {
  g.electronStore.openUrl(plan.value.onlineMeetUrl);
};

const onEdit = () => {
  const data = toRaw(plan.value);
  if (isElectronUrl.value) {
    g.electronStore.openWin("arrange_visit", data);
    g.electronStore.closeWin("visit_detail");
  } else {
    emit("callback", "edit_plan", data);
  }
};

const onDelete = () => {
  g.planStore.cancelPlan(plan.value.scheduleId).then((res) => {
    if (res) {
      if (isElectronUrl.value) {
        g.electronStore.sendMessage("main", "reload_schedule", 1000);
        g.electronStore.closeWin("visit_detail");
      } else {
        emit("callback", "reload");
      }
    }
  });
};

const copyUrl = () => {
  g.appStore.doCopy(plan.value.onlineMeetUrl, "已复制");
};

const init = (_plan, _readonly = false) => {
  readonly.value = _readonly;
  console.log('view_plan', _plan, _readonly)
  plan.value = _plan;
  loading.value = true;
  getScheduleDetail(_plan.scheduleId)
    .then((res) => {
      if (res.code == 0) {
        plan.value = updatePlanApiResult({ ...plan.value, ...res.data });
        console.log('view_plan2', _plan)
      } else {
        ElMessage.error(res.message);
        emit("callback", "has_error");
      }
    })
    .catch((err) => {
      console.log(err);
      emit("callback", "has_error");
    })
    .finally(() => {
      loading.value = false;
    });
};

// 窗口大小改变时重新检查
onMounted(() => {
  window.addEventListener("resize", checkTextOverflow);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkTextOverflow);
});

defineExpose({
  init,
  getInnerParticipants,
  getOuterParticipants,
  getWebTime,
  onStart,
  onReady,
  openMeetingUrl,
  getEleTime,
  getEleDate,
  onEdit,
  onDelete,
  DetailEdit,
  DetailDelete,
  copyUrl,
  validateUrl,
});
</script>

<style lang="scss">
@import url("./VisitDetail.scss");
</style>
