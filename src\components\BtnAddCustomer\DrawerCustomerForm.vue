<template>
    <el-drawer v-model="dialogVisible" size="640" class="customer_edit_drawer">
        <template #header>
            <div class="vd_title">
                {{ title }}
            </div>
        </template>
        <template #default>
            <div class="customer_edit_form">
                <el-form :model="formData" label-width="120px" label-position="top" ref="formRef" :rules="rules">
                    <el-form-item label="客户名称" required prop="name" class="fi_item">
                        <CustomerNamePickerQcc v-model="formData.name"
                            :enableQcc="customerTypeInfo.enableBusinessRule == 1" />
                    </el-form-item>
                    <el-form-item label="负责人" required prop="ssoUserIds" class="fi_item">
                        <DrawerSelectMixed ref="refUser" @callback="onSelectUser" type="user" :rootDeptId="rootDeptId"
                            :allDept="isSelectAllDept" :queryManagedDepartment="false" />
                    </el-form-item>
                    <el-form-item v-for="field in formFields" :key="field.id"
                        :label="`${field.fieldType == 'Divider' ? '' : field.fieldName}`"
                        :class="`${field.fieldType == 'Divider' ? 'fi_divider' : 'fi_item'}`" :prop="field.id">

                        <div v-if="field.fieldType == 'Divider'" class="divider_title">
                            {{ field.fieldName }}
                        </div>

                        <el-input v-if="field.fieldType == 'String'" v-model="formData[field.id]"
                            :placeholder="field.placeholder" :maxlength="field.maxLength" :minlength="field.minLength"
                            @blur="validateField(field.id)" />

                        <el-input type="textarea" v-if="field.fieldType == 'TextArea'" v-model="formData[field.id]"
                            :placeholder="field.placeholder" :maxlength="field.maxLength" :minlength="field.minLength"
                            @blur="validateField(field.id)" />

                        <el-date-picker v-if="field.fieldType == 'Date'" v-model="formData[field.id]"
                            :format="getDtFormat(field)" :value-format="getDtFormat(field)"
                            :placeholder="field.placeholder" @blur="validateField(field.id)" />

                        <el-date-picker v-if="field.fieldType == 'DateTime'" type="datetime"
                            v-model="formData[field.id]" :placeholder="field.placeholder" :format="getDtFormat(field)"
                            :value-format="getDtFormat(field)" @blur="validateField(field.id)" />

                        <el-input-number v-if="field.fieldType == 'Integer'" v-model="formData[field.id]"
                            :max="field.maxValue == -1 ? Number.MAX_SAFE_INTEGER : field.maxValue" :min="field.minValue"
                            @blur="validateField(field.id)" />

                        <el-select v-if="field.fieldType == 'Select'" v-model="formData[field.id]"
                            :placeholder="field.placeholder" @blur="validateField(field.id)">
                            <el-option v-for="item in field.fieldOptionsArray" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>

                        <el-select v-if="field.fieldType == 'MultiSelect'" v-model="formData[field.id]" multiple
                            :placeholder="field.placeholder" @blur="validateField(field.id)">
                            <el-option v-for="item in field.fieldOptionsArray" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </span>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, toRaw } from "vue";
import { get_rules } from "@/js/utils";
import { getFormFields, createCustomer, updateCustomer, getCustomerInfo } from "@/js/api.js"
import CustomerNamePickerQcc from "@/components/CustomerNamePicker/CustomerNamePickerQcc.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
const { proxy } = getCurrentInstance();
const dialogVisible = ref(false);
const refUser = ref(null);
const rootDeptId = ref(null);
const formRef = ref(null);
const rules = ref({})
const title = ref('')
const domain = g.appStore.user.ssoDomain || '';
//  这里isSelectAllDept应该是一直true,但是pro-phx-ucstable测试机构部门太多，加载太慢，所以暂时先这样
const isSelectAllDept = ref(domain.indexOf("pro-phx-ucstable") < 0);
const customerTypeInfo = ref({ name: '', enableBusinessRule: 0 })
const emit = defineEmits(["reload"]);
const source = location.href.indexOf("admin") > -1 ? 0 : 1;
let customerTypeId = ''
let formCode = ''
let dataId = ''
let editData = {}

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})
const _default_form_data = {
    source,
    "name": "",
    "ssoUserIds": [],
    "customerTypeId": "",
}

const getDtFormat = (field) => {
    if (field.fieldType == 'DateTime') {
        if (field.datetimePrecision == "hour") {
            return 'YYYY-MM-DD HH'
        } else {
            return 'YYYY-MM-DD HH:mm'
        }
    } else {
        return 'YYYY-MM-DD'
    }
}

const formData = ref({
    ..._default_form_data
})

const formFields = ref([]);

const getCustomerTypeList = () => {
    return new Promise((resolve, reject) => {
        getFormFields(formCode).then(res => {
            if (res.code == 0) {
                const fields = res.data;
                fields.forEach(item => {
                    item.fieldId = item.id;
                    item[item.id] = '';
                    if (item.fieldType == 'Select' || item.fieldType == 'MultiSelect') {
                        item.fieldOptionsArray = item.fieldOptions.split(',');
                    }
                });
                formFields.value = [...fields];
                rules.value = {
                    name: [{
                        required: true,
                        message: customerTypeInfo.enableBusinessRule == 0 ? '请输入客户名称' : '',
                        trigger: 'blur'
                    }],
                    ssoUserIds: [{
                        required: true,
                        message: '请选择负责人',
                        trigger: 'blur'
                    }],
                    ...get_rules(fields, '')
                }
                formRef.value.resetFields();
                proxy.$forceUpdate();
                resolve(true);
            } else {
                ElMessage.error(res.message || '获取失败2');
                reject(false);
            }
        })
    })
}

const validateField = (prop) => {
    if (formRef.value) {
        try {
            formRef.value.validateField(prop);
        } catch (error) {
            console.log("validateField error", error);
        }
    }
}

const onSelectUser = (action, data) => {
    formData.value.ssoUserIds = data.users.map(user => user.id);
    validateField('ssoUserIds');
}

const get_data = () => {
    return new Promise((resolve, reject) => {
        if (formRef.value) {
            formRef.value.validate((valid) => {
                if (valid) {
                    const fd = toRaw(formData.value);
                    let data = {
                        source,
                        name: fd.name,
                        ssoUserIds: fd.ssoUserIds,
                        customerTypeId,
                        dynamicFormData: {
                            formCode,
                            fieldValues: dataId ? editData.formData.fieldValues : []
                        }
                    }
                    if (!dataId) {
                        const mySsoId = g.appStore.user.ssoUserId;
                        if (!data.ssoUserIds.includes(mySsoId)) {
                            data.ssoUserIds.push(mySsoId)
                        }
                    }

                    formFields.value.forEach(item => {
                        if (item.fieldType !== 'Divider') {
                            let fieldValue = fd[item.id] || ''
                            const oldField = editData?.formData?.fieldValues.find(x => x.fieldId == item.id) || undefined
                            if (item.fieldType == 'MultiSelect' || item.fieldType == 'Select') {

                                // if (fieldValue) {
                                //     // array to string 
                                //     fieldValue = fieldValue.join(',')
                                // } else {
                                //     fieldValue = ""
                                // }
                            }
                            const newFiledValue = Array.isArray(fieldValue) ? fieldValue.join(',') : fieldValue
                            if (!!oldField) {
                                const oldFieldIndex = data.dynamicFormData.fieldValues.findIndex(x => x.fieldId == item.id)
                                if (oldFieldIndex != -1) {
                                    data.dynamicFormData.fieldValues[oldFieldIndex].fieldValue = newFiledValue
                                }
                            } else {
                                console.log('item2', item, fieldValue);
                                data.dynamicFormData.fieldValues.push({
                                    id: '',
                                    fieldId: item.id,
                                    fieldName: item.fieldName,
                                    fieldValue: newFiledValue,
                                    order: item.sortOrder
                                })
                            }
                        }
                    });
                    resolve(data);
                } else {
                    resolve(false);
                }
            });
        } else {
            ElMessage.error('请先完成表单');
            resolve(false);
        }
    });
}

const handleConfirm = async () => {
    const data = await get_data();
    if (!data) {
        return;
    }
    if (dataId) {
        updateCustomer(dataId, data).then(res => {
            if (res.code == 0) {
                dialogVisible.value = false;
                ElMessage.success('更新成功');
                emit('reload');
            } else {
                ElMessage.error(res.message || '更新失败');
            }
        }).catch(err => {
            console.log("update customer error", err);
            ElMessage.error('更新客户失败');
        })
    } else {
        createCustomer(data).then(res => {
            if (res.code == 0) {
                dialogVisible.value = false;
                ElMessage.success('创建成功');
                emit('reload');
            } else {
                ElMessage.error(res.message || '创建失败');
            }
        }).catch(err => {
            console.log("add customer error", err);
            ElMessage.error('创建客户失败');
        })
    }
}
const _defaultEdit = { formData: { fieldValues: [] } }

const show_add = async (item) => {
    console.log('show_add', item)
    title.value = `创建【${item.name}】`
    customerTypeInfo.value = item;
    const typeid = item.id;
    dataId = ''
    formData.value = {
        ..._default_form_data,
        customerTypeId: typeid
    }
    editData = { ..._defaultEdit }
    customerTypeId = typeid;
    formCode = 'CUSTOMER_TYPE_' + typeid;
    dialogVisible.value = true;
    await getCustomerTypeList();
    nextTick(() => {
        formRef.value.resetFields();
        setTimeout(() => {
            formRef.value.resetFields();
        }, 100);
        refUser.value.init({ users: [] })
    })
}

const show_edit_byid = async (id) => {
    getCustomerInfo(id).then(res => {
        if (res.code == 0) {
            if (res.data.typeId) {
                show_edit(res.data.typeId, res.data)
            } else {
                ElMessage.error('当前客户类型不存在')
                dialogVisible.value = false
            }
        }
    }).catch(err => {
        console.log(err);
        ElMessage.error('获取客户信息失败')
        dialogVisible.value = false
    })
}

const show_edit = async (typeid, data) => {
    title.value = '编辑客户'
    dataId = data.id
    customerTypeInfo.value = { ...data }
    editData = { ..._defaultEdit, ...data }
    customerTypeId = typeid;
    formCode = 'CUSTOMER_TYPE_' + typeid;
    dialogVisible.value = true;
    await getCustomerTypeList();
    const users = []
    const salesSsoUserIds = data?.salesSsoUserIds?.split(',') || [];
    const userNames = data?.salesSsoUserNames?.split(',') || [];
    for (let i = 0; i < salesSsoUserIds.length; i++) {
        users.push({ id: salesSsoUserIds[i], fullname: userNames[i] })
    }
    formData.value = {
        id: data.id,
        source,
        name: data.name,
        ssoUserIds: salesSsoUserIds,
        customerTypeId: data.typeId,
    }
    if (formFields.value.length > 0) {
        for (let i = 0; i < data.formData.fieldValues.length; i++) {
            const field = data.formData.fieldValues[i];
            const filedType = formFields.value.find(item => item.id == field.fieldId).fieldType;
            if (filedType == 'MultiSelect' || filedType == 'Select') {
                formData.value[field.fieldId] = field.fieldValue ? field.fieldValue.split(',') : [];
            } else if (filedType == 'Date' || filedType == 'DateTime') {
                formData.value[field.fieldId] = field.fieldValue;
            } else if (filedType == 'Integer') {
                formData.value[field.fieldId] = parseInt(field.fieldValue);
            } else {
                formData.value[field.fieldId] = field.fieldValue;
            }
        }
    }
    console.log(JSON.stringify(toRaw(formData.value)));
    refUser.value.init({ users })
}

defineExpose({
    show_add,
    show_edit,
    show_edit_byid
})
</script>

<style lang="scss">
.customer_edit_drawer {
    .vd_title {
        color: #262626;
    }

    .customer_edit_form {
        .fi_item {
            margin-left: 24px;

            .mixed_input_box {
                width: 100%;
            }
        }

        .fi_divider {
            margin-top: 40px;
            margin-bottom: 24px;
        }

        .divider_title {
            border-left: 4px solid #436BFF;
            padding-left: 10px;
            line-height: 20px;
        }
    }
}
</style>
