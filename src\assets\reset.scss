 html,
 body,
 h1,
 h2,
 h3,
 h4,
 h5,
 h6,
 ul,
 ol,
 li,
 dl,
 dt,
 dd,
 input,
 img,
 select,
 textarea,
 form,
 fieldset,
 .el-button--primary.is-text iframe {
   margin: 0;
   padding: 0;
   font-family: 'PingFangSC';
 }

 :root {
   --el-card-padding: 24px;
   --el-color-primary: #436BFF;
   --el-menu-active-color: #436BFF;
 }

 a {
   text-decoration: none;
   color: #000000;
 }

 li {
   list-style: none;
 }

 img {
   vertical-align: top;
   border: none;
 }

 html,
 body {
   width: 100%;
   height: 100%;
 }

 #app {
   width: 100%;
   height: 100%;
 }

 input,
 textarea {
   outline: none;
 }


 .el-button.is-text {
   padding: 0;
 }

 .el-dropdown-link {
   outline: none;
 }

 .flex-col {
   display: flex;
   flex-direction: column;
 }

 .flex-row {
   display: flex;
   flex-direction: row;
 }

 .flex-center {
   display: flex;
   justify-content: center;
   align-items: center;
 }

 .column-center {
   display: flex;
   flex-direction: column;
   align-items: center;
 }

 .flex-wrap {
   display: flex;
   flex-direction: row;
   flex-wrap: wrap;
 }

 .justify-between {
   display: flex;
   justify-content: space-between;
 }

 .flex-grow {
   flex-grow: 1;
 }

 .flex-center {
   display: flex;
   justify-content: center;
   align-items: center;
 }

 .pointer {
   cursor: pointer;
 }

 .column_link {
   cursor: pointer;
   color: var(--el-color-primary);
 }

 .flex-grow {
   flex-grow: 1;
 }

 .flex-end {
   display: flex;
   justify-content: flex-end;
 }

 .el-drawer__header {
   height: 56px;
   padding: 0 !important;
   border: 1px solid #E9E9E9;
   font-size: 16px;
   color: #262626;
   margin-bottom: 0 !important;

   .vd_title {
     margin-left: 20px;
   }
 }

 .el-tabs__nav-wrap:after {
   background-color: #fff;
   height: 1px !important;
 }

 .el-tabs__header {
   margin: 0 !important;
 }

 /* 添加全局滚动条样式 */
 .custom-scrollbar {
   overflow-y: auto;
   scroll-behavior: smooth;

   &::-webkit-scrollbar {
     width: 4px;
   }

   &::-webkit-scrollbar-track {
     background: transparent;
   }

   &::-webkit-scrollbar-thumb {
     background: #C0C4CC;
     border-radius: 3px;

     &:hover {
       background: #909399;
     }
   }
 }

 .card_no_border {
   border: none !important;
 }

 .el-card {
   overflow: auto !important;
 }

 .hidden {
   visibility: hidden;
   width: 0;
   height: 0;
 }

 .single-line-ellipsis-2 {
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
   overflow: hidden;
 }

 .single-line-ellipsis {
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
 }

 .van-floating-bubble {
   background: #FFFFFF !important;
   box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.1) !important;
   color: unset !important;

 }

 .van-floating-bubble .el-tooltip__trigger {
   height: 24px;
   width: 24px;
 }

 .hide-scrollbar {
   /* 启用垂直滚动 */
   scrollbar-width: none;
   /* Firefox */
   -ms-overflow-style: none;

 }

 /* Internet Explorer 10+ */
 .hide-scrollbar::-webkit-scrollbar {
   width: 0 !important;
   /* 对于WebKit浏览器 */
 }


 .el-pagination .el-pager li {
   background: #FFFFFF !important;
   border-radius: 4px !important;
   border: 1px solid #D9D9D9 !important;
   color: #595959 !important;

   &.is-active {
     background: #FFFFFF !important;
     color: #436BFF !important;
     border: 1px solid #436BFF !important;
   }

   &:hover {
     background: #F5F7FA !important;
     border: 1px solid #436BFF !important;
   }
 }

 .el-pagination .btn-prev,
 .el-pagination .btn-next {
   background: #FFFFFF !important;
   border-radius: 4px !important;
   border: 1px solid #D9D9D9 !important;
   color: #595959 !important;

   &:hover {
     background: #F5F7FA !important;
     color: #436BFF !important;
   }

   &.disabled {
     background: #F5F7FA !important;
     color: #C0C4CC !important;
     border-color: #E4E7ED !important;
   }
 }

 .el-pagination .el-pagination__total {
   color: #595959;
   font-size: 14px;
 }


 .view-plan-record-dialog {
   /* width: 480px !important; */
   padding: 0;

   .el-message-box__title {
     font-weight: 500;
     font-size: 16px;
     color: #262626;
     padding: 16px 24px 4px;
   }

   .el-message-box__headerbtn {
     top: 8px;
   }

   .el-message-box__container {
     width: 100%;
   }

   .el-message-box__content {
     border-top: 1px solid #E9E9E9;
     border-bottom: 1px solid #E9E9E9;
     padding: 24px;
     box-sizing: border-box;

     p {
       font-size: 14px;
       color: #595959;
       line-height: 22px;
     }

   }

   .el-message-box__btns {
     /* padding: 0; */
     padding: 16px 24px;
   }
 }

 .md_body {
   overflow-x: auto;

   p {
     margin: 8px 0;
     color: #262626;
     font-size: 14px;
     line-height: 27px;
   }

   ol {
     display: block;
     list-style-type: decimal;
     padding-inline-start: 20px;
     unicode-bidi: isolate;
     margin-left: 12px;

     li {
       list-style-type: decimal;
       color: #262626;
       font-size: 14px;
       line-height: 27px;
     }
   }

   ul {
     margin-left: 0;
     line-height: 14px;

     li {
       font-weight: 400;
       list-style: none;
       //  list-style-type: circle;
       margin: 0;
       font-size: 14px;
       color: #595959;
       line-height: 22px;

     }
   }

   h2,
   h3,
   h4 {
     margin: 12px 0;
   }

   h1,
   h2 {
     font-size: 16px !important;
     font-weight: 900;
     color: #262626;
     line-height: 24px;
   }

   h3 {
     font-size: 14px !important;
     font-weight: 600;
     color: #262626;
     line-height: 22px;
   }

   h4 {
     font-size: 14px !important;
   }

   hr {
     background: #E9E9E9;
     border: none;
     height: 1px;
     margin: 16px 0;
     width: 100%;
   }

   table {
     width: 100%;
     table-layout: fixed;
     /*表格宽度*/
     max-width: 65em;
     /*表格最大宽度，避免表格过宽*/
     border: 1px solid #dedede;
     /*表格外边框设置*/
     /*外边距*/
     border-collapse: collapse;
     /*使用单一线条的边框*/
     empty-cells: show;
     /*单元格无内容依旧绘制边框*/

     td {
       height: 35px;
       /*统一每一行的默认高度*/
       border: 1px solid #dedede;
       /*内部边框样式*/
       padding: 0 10px;
       /*内边距*/
     }

     th {
       font-weight: bold;
       /*加粗*/
       text-align: center !important;
       /*内容居中，加上 !important 避免被 Markdown 样式覆盖*/
       background: rgba(158, 188, 226, 0.2);
       /*背景色*/
     }

     tbody tr:nth-child(2n) {
       background: rgba(158, 188, 226, 0.12);
     }

     tr:hover {
       background: #efefef;
     }

     td {
       word-break: break-word;
     }
   }
 }

 .faq_icon_min {
   width: 16px;
   height: 16px;
   margin-right: 2px;
 }