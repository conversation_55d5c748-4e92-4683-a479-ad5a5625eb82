<template>
  <el-config-provider :locale="locale">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </el-config-provider>
</template>

<script setup>
import { ElConfigProvider } from 'element-plus';
import locale from "element-plus/es/locale/lang/zh-cn";


defineExpose({
  ElConfigProvider, locale
})

</script>

<style lang="scss">
@import url("assets/reset.scss");
</style>