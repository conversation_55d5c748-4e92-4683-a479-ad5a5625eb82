<template>
    <el-drawer v-model="is_show" direction="rtl" class="select_file_drawer">
        <template #header>
            <div class="vd_title">
                选择文件
            </div>
        </template>
        <template #default>
            <div class="draw_main flex-row">
                <div class="dm_left flex-column">
                    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
                        <template #_header_left>
                            <el-select v-model="selectCate" class="sel_cate" placeholder="请选择" @change="onSelectChange">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                            <el-checkbox v-model="checkAll" label="选中该分类下所有文件" @change="onChangeCheckAll" />
                        </template>
                    </MyTable>
                </div>
                <div class="dm_right flex-column">
                    <div class="dfr_header flex-row">
                        <div class="dh_left">已选：{{ total_files }}/100 </div>
                        <div class="dh_right" @click="onClear">清空 </div>
                    </div>
                    <div class="bbuttom">
                        <TagLabel ref="refTag" @callback="cbTag" />
                    </div>
                </div>
            </div>

        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="cancelClick">取消</el-button>
                <el-button type="primary" @click="confirmClick">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { getPptDocs } from "@/app_admin/tools/api.js";
import { union, differenceObj } from "@/js/utils.js"
import MyTable from "@/components/Table.vue";
import TagLabel from "./TagLabel.vue";

const total_files = ref(0)
const is_show = ref(false);
const selectCate = ref("")
const options = ref([])
const selectDocs = ref([])
const selectCates = ref([])
const refTable = ref(null);
const refTag = ref();
const checkAll = ref();
let initData = {}
const emit = defineEmits(['callback'])

const datas = reactive({
    tableid: 'template_select_file',
    pk: 'id',
    param: { categoryIds: [] },
    show_search: false,
    need_header: true,
    need_init_load: false,
    enable_checkbox: true,
    form: {},
    urlGet: getPptDocs,
    columns: ["fileName"],
});

const onClear = () => {
    selectDocs.value = [];
    selectCates.value = [];
    total_files.value = 0;
    refTable.value.manualCheck([]);
    refTag.value.handleClear();
}

const cbDatas = (action, _data) => {
    const data = toRaw(_data);
    if (action == "after_search") {
        if (selectDocs.value) {
            const check_ids = selectDocs.value.map(x => x.id)
            nextTick(() => {
                refTable.value.manualCheck(check_ids);
            })
        }
    } else if (action === "check_row") {
        addDocs(data['checked'].map(x => {
            return {
                id: x.id,
                name: x.fileName
            }
        }))

        removeDocs(data['unchecked'].map(x => {
            return {
                id: x.id,
                name: x.fileName
            }
        }))
    }
}

const getPptCategoryData = async () => {
    options.value = await g.adminFileStore.get_ppt_cates();
    selectCate.value = options.value[0].value;
    addDocs(toRaw(selectDocs.value))
    addCates(toRaw(selectCates.value))
    datas.param['categoryIds'] = [selectCate.value]
    refTable.value.search()
}

const addDocs = (docs) => {
    if (docs && docs.length > 0) {
        selectDocs.value = union(toRaw(selectDocs.value), docs);
        refTag.value.updateDoc(toRaw(selectDocs.value))
        _updateTotal();
    }
}

const removeDocs = (docs) => {
    if (docs && docs.length > 0) {
        selectDocs.value = differenceObj(toRaw(selectDocs.value), docs);
        refTag.value.updateDoc(toRaw(selectDocs.value))
        _updateTotal();
    }
}

const addCates = (cates) => {
    if (cates && cates.length > 0) {
        selectCates.value = union(toRaw(selectCates.value), cates);
        refTag.value.updateCate(toRaw(selectCates.value))
        _updateTotal()
        _setCheckAll(toRaw(selectCates.value).map(x => x.id).includes(toRaw(selectCates.value)))
    }
}

const _updateTotal = () => {
    total_files.value = selectDocs.value.length + selectCates.value.length;
}

const show = (_initData) => {
    is_show.value = true;
    selectDocs.value = _initData.docs || [];
    selectCates.value = _initData.categories || [];
    // initData = _initData;
    getPptCategoryData()
}


const cancelClick = () => {
    is_show.value = false;
}

const confirmClick = () => {
    emit('callback', 'confirm', { docs: toRaw(selectDocs.value), categories: toRaw(selectCates.value) })
    is_show.value = false;
}

const onChangeCheckAll = (enable) => {
    const ids = selectCates.value.map(x => x.id)
    const isHave = ids.includes(selectCate.value);
    refTable.value.setCheckable(!enable);
    if (enable) {
        if (!isHave) {
            selectCates.value.push(options.value.filter(x => x.value == selectCate.value).map(x => { return { id: x.value, name: x.label } })[0])
        }
    } else if (isHave) {
        selectCates.value = selectCates.value.filter(x => x.id !== selectCate.value)
        refTag.value.updateCate(selectCates.value);
    }
}

const onSelectChange = (cateId) => {
    _setCheckAll(selectCates.value.map(x => x.value).includes(cateId))
    datas.param['categoryIds'] = [cateId]
    refTable.value.search()
}

const _setCheckAll = (status) => {
    checkAll.value = status;
    refTable.value.setCheckable(!checkAll.value)
}

const cbTag = (action, data) => {
    if (action == "close_doc") {
        selectDocs.value = selectDocs.value.filter(x => x.id !== data.id)
        refTag.value.updateDoc(selectDocs.value);
        const check_ids = selectDocs.value.map(x => x.id)
        refTable.value.manualCheck(check_ids);
    } else if (action == "close_cate") {
        selectCates.value = selectCates.value.filter(x => x.id !== data.id)
        refTag.value.updateCate(selectCates.value);
    }
}

defineExpose({
    refTag, MyTable, is_show, checkAll, selectDocs, initData, selectCates,
    onSelectChange, show, cancelClick, confirmClick, TagLabel
})

</script>

<style lang="scss">
.select_file_drawer {
    width: 960px !important;

    .draw_main {
        .dm_left {
            width: 600px;

            .table_wrap {
                padding: 0;

                .table_box {
                    padding: 0;

                    .sel_cate {
                        margin-right: 10px;
                        width: 200px;
                    }
                }
            }
        }

        .dm_right {
            width: 360px;
            margin-left: 10px;

            .dfr_header {
                margin: 10px 0;
                justify-content: space-between;

                .dh_right {
                    color: var(--el-color-primary);
                    cursor: pointer;
                }
            }

            .bbuttom {
                .el-tag {
                    margin: 7px 7px 7px 0;

                    .el-tag__content {
                        display: flex;
                        flex-direction: row;
                        margin: 7px 7px 7px 0;

                        .tname {
                            margin-top: 4px;
                            margin-left: 4px;
                        }
                    }
                }
            }
        }
    }

    .el-drawer__footer {
        .el-button--primary {
            margin-right: 16px;
        }
    }

}
</style>