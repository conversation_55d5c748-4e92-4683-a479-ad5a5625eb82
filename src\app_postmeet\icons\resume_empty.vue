<template>
    <svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="lgreem-1">
                <stop stop-color="#BFD4FA" offset="0%"></stop>
                <stop stop-color="#DDE7FE" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="lgreem-2">
                <stop stop-color="#BFD4FA" offset="0%"></stop>
                <stop stop-color="#8EB5F5" offset="100%"></stop>
            </linearGradient>
            <pattern id="pattern-3" width="100%" height="100%" patternUnits="objectBoundingBox">
                <use xlink:href="#image-4"></use>
            </pattern>
            <image id="image-4" width="56" height="28"
                xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAcCAYAAAA0u3w+AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAOKADAAQAAAABAAAAHAAAAAA5FuGXAAAES0lEQVRYCZWXWZITSwxF28zwCetgZyyCPbIO+IRgNDpZddLXcpY7WhGypqvhusrNe6fz+fzpYZNzWPzH9N+OwXb9u+fS4h8p/Yl1Hjekb3x0W8Hn3cN/UZ/P9uRptxQQ4z6MhQh9+ODwEbDG+qPQPlYzyTEva0mu1xKXPquMHyDIQSlJLPP6fiEslBi+xMCxgLirNSziIVgJYFN7LXvSz3n6k6CktAD0c0j6cwhOCBi/BHzJB+SKWM7sJO03L9aYmeawiPHwj15RyQHStxHLAkmASclF4rDMSXEetSN1F3XxR5bZ1BAx8wlu6e1TQlrA6d8MyebwwfElJDlJesCKmH9srIF9jCBr864Z+xuUwJGl2Zq+S8mnOhwcBFHr1BBq9kvEv7DGacEfqfOWdvWKckwHM9yci8Dhc4gE7AWLWJekOfM+LckZP5Uc85y9u2P3fEU91gNpyFwOwE8FlyRdgAXnsUnSHIT+7CpJa1j7c1+l5379lSU3CeJ3cuQYjKxsLuV4DmJGaoXzSAnSBzbJQbITzPndL/jNTZmbvq/oTJRzj6g4FqZPLEmOx5cQOHL+3iX3u3Kq5LDUJYSPGOuvLDnE20bgUgKJjUJ9XAFbbK0v9jjs81KJljuekF/Cr4p/lmIhKUHmOaPPdmdBrm7LPDVk5vIJbqVLsRO+atzBDkqLj3Io8zkessiPUgh937WTs1dbsMuxzaeGgD2UfIKCVsSspT0abF4L0Telb/fmL2W/lUoOnFgt0PSJl3I6ne7iVk9wOWiRPPoizGt9TV/WjI+ln0vflxLzZMGJ1VZq5vAPpf5vKHtucE99gn2YcVp8VGKQeFX6rvRD6ddShFiSSdT+nDka6sOcMXaVm/V7T7A3ZqyPTR9SxJJjPvq6VEL+Bis1iHeSzuiz3UPfkU8NmfV8gjO5YS6gbNh9sdhUiWF5KsyHAASx5PlN8teTOsJvCF9NgviIO/RXlhwCdv4uOcAhFBEAiv7KkktljuqxEsSi4CHIX1aepLs5SHWG2NyhX/B5JzkFX3Ij71IAHZg5a9iVchR5rOSwEsMXwwGS5L9gnN0Jite6t1rmDfpaZuS8+a2a1NqgNY/t6gFYNAmmL46ZEJSks8kjEsXS49F9r33mxWGRUecbZgiSDT3OmgM9mBhfTVI+ObHM5QBUgtR4Zd1hXWuve7sFZ2/akfcVzULhrxqyho/mUvzH1L6CDmE5BLXUIbkSMLnPWWkHmcLd2BVBGhEHpG8uF3Zy+dQS5xwsh6C+qs51NxgEDDOQxKz8Tm40Hb2iLloNMufxnWDGYMU5cyyujyTpTDHd0pMYZiKSsmY8a6snSNGGe9bDk9Aq5wzmKpLrlicKHoswT588MSKOnHJDrgrnFcE8SD+tJMjh34uzDx/hECUJerSExDCfnLbPzJieJDr+maAR8YBsuOffI5a1bfr1ZxLDT3LstC7ZJOe91xO3KMkN/z9sTi12WE7MagAAAABJRU5ErkJggg==">
            </image>
        </defs>
        <g id="面试助手" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="客户端-简历预览（默认1064）-空数据" transform="translate(-472.000000, -217.000000)">
                <g id="编组-2" transform="translate(472.000000, 217.000000)">
                    <rect id="矩形" opacity="0.5" x="0" y="0" width="120" height="120"></rect>
                    <g id="编组-4" transform="translate(16.000000, 6.000000)">
                        <path
                            d="M55,34 L97,34 C100.313708,34 103,36.6862915 103,40 L103,102 C103,105.313708 100.313708,108 97,108 L55,108 C50.581722,108 47,104.418278 47,100 L47,42 C47,37.581722 50.581722,34 55,34 Z"
                            id="矩形" fill="url(#lgreem-1)"></path>
                        <path
                            d="M88,102 C88,104.973063 90.1623839,107.441075 93.0001915,107.917074 L93,108 L6,108 C2.6862915,108 4.05812251e-16,105.313708 0,102 L0,6 C-4.05812251e-16,2.6862915 2.6862915,6.08718376e-16 6,0 L82,0 C85.3137085,-6.08718376e-16 88,2.6862915 88,6 Z"
                            id="形状结合" fill="url(#lgreem-2)"></path>
                        <rect id="矩形" fill="#FFFFFF" opacity="0.300000012" x="15" y="64" width="58" height="29" rx="6">
                        </rect>
                        <rect id="矩形" fill="#FFFFFF" x="31" y="74" width="27" height="3" rx="1.5"></rect>
                        <rect id="矩形备份-3" fill="#FFFFFF" x="31" y="81" width="27" height="3" rx="1.5"></rect>
                        <path
                            d="M67,64 C70.3137085,64 73,66.6862915 73,70 L73,87 C73,88.4186701 72.5076348,89.7223422 71.6844634,90.7494571 C71.8887434,90.2052467 72,89.615616 72,89 L72,70 C72,67.2385763 69.7614237,65 67,65 L19,65 C18.3851666,65 17.7962525,65.1109739 17.2521953,65.3139841 C18.2787764,64.4919429 19.5819385,64 21,64 L67,64 Z"
                            id="形状结合" fill="url(#pattern-3)" opacity="0.800000012"></path>
                        <g id="编组" transform="translate(28.000000, 19.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <path
                                d="M25.3333333,21.5384615 C29.0152317,21.5384615 32,24.5519296 32,28.2692308 L32,30.9615385 C32,33.1919192 30.209139,35 28,35 L4,35 C1.790861,35 0,33.1919192 0,30.9615385 L0,28.2692308 C0,24.5519296 2.98476833,21.5384615 6.66666667,21.5384615 L25.3333333,21.5384615 L25.3333333,21.5384615 Z M16,0 C19.3344789,0 22.4156644,1.79602825 24.0829039,4.71153842 C25.7501433,7.62704859 25.7501433,11.2191053 24.0829039,14.1346154 C22.4156644,17.0501256 19.3344789,18.8461538 16,18.8461538 C10.8453424,18.8461538 6.66666681,14.6272985 6.66666681,9.42307692 C6.66666681,4.2188553 10.8453424,0 16,0 L16,0 Z"
                                id="形状"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
