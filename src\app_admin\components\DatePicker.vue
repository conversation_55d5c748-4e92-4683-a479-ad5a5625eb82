<template>
    <el-date-picker v-model="selectRange" :type="dateRangeType" range-separator="~" start-placeholder="开始日期"
        end-placeholder="结束日期" :format="showDateFormat" v-if="['daterange', 'monthrange'].includes(dateRangeType)"
        @change="onChange" />
    <DatePickerEnhanced v-else ref="refQuarterPicker" v-bind="quarterCfg"
        @update:modelValue="quarterCfg.modelValue = $event" @visibleChange="visibleChangeFn($event)" />
</template>
<script setup>
import { getCurrentWeekDay, getLastDayOfMonth, getLastDayOfQuarter } from "@/app_admin/tools/utils.js"
import { formatDate } from "@/js/utils.js"
import DatePickerEnhanced from "datepicker-enhanced"

const dateRangeType = ref('daterange')
const selectRange = ref(['', ''])
const emit = defineEmits(['callback']);
const dataType = ref('');
const showDateFormat = ref('YYYY-MM-DD')


const quarterCfg = ref({
    type: 'quarteryearrange',
    modelValue: ['', ''],
    disabledDate: () => false,
    valueFormat: 'YYYY-MM-DD',
    wantEnd: false,
})

const setRangeType = (type, startTime, endTime) => {
    dataType.value = type;
    selectRange.value = [startTime, endTime]
    if (type == 'week' || type == 'date') {
        showDateFormat.value = 'YYYY-MM-DD'
        dateRangeType.value = 'daterange';
    } else if (type == 'quarter' || type == 'month') {
        if (type == 'quarter') {
            quarterCfg.value.modelValue = [startTime, endTime]
        } else if (type == 'month') {
            showDateFormat.value = 'YYYY-MM'
        }
        dateRangeType.value = type + 'range';
    } else {
        console.error('setRangeType error', type)
    }
}

const visibleChangeFn = (show) => {
    if (!show) {
        const [start, end] = toRaw(quarterCfg.value.modelValue)
        const lastDay = getLastDayOfQuarter(end)
        emit('callback', [start, lastDay])
    }
}

const onChange = (e) => {
    const [start, end] = toRaw(e);
    if (dataType.value == 'week') {
        selectRange.value = [getCurrentWeekDay(start, 1), getCurrentWeekDay(end, 7)]
    } else if (dataType.value == 'month') {
        selectRange.value = [formatDate(start), getLastDayOfMonth(end)]
    } else {
        selectRange.value = [formatDate(start), formatDate(end)]
    }
    emit('callback', toRaw(selectRange.value))
}

defineExpose({
    selectRange,
    dateRangeType,
    DatePickerEnhanced,
    showDateFormat,
    quarterCfg,
    visibleChangeFn,
    setRangeType,
    onChange
})

</script>

<style lang="scss">
.el-date-editor {
    width: 215px !important;
}

.component-datepicker-enhanced {
    display: flex !important;
}
</style>
