<!--
QAList 组件使用说明：
1. 使用 van-list 实现触底加载功能
2. 父组件需要传入以下 props：
   - qaList: 问答列表数据
   - loading: 是否正在加载
   - finished: 是否已加载完所有数据
3. 父组件需要监听 load-more 事件来请求更多数据
4. 示例用法：
   <QAList 
     :qa-list="qaList" 
     :loading="loading" 
     :finished="finished"
     @load-more="handleLoadMore"
   />
-->
<template>
    <div class="qa-list" v-loading="loading">
        <van-list v-if="qaList.length" v-model="listLoading" :finished="finished" finished-text="没有更多了" @load="onLoad"
            :immediate-check="false">
            <div v-for="(qa, index) in qaList" :key="index" class="qa-item">
                <div class="qa-item-content" @click="onDetail(qa)">
                    <p class="qa-item-content-left">
                        <span class="qa-bubble question">
                            <span class="qa-tag customer">问</span>
                            <span class="qa-text-customer">客户</span>
                        </span> <span class="qa-text">{{ qa.question }}</span>
                    </p>
                    <div class="qa-detail-link">详情</div>
                </div>
                <div class="qa-item-content">
                    <TextEllipsis :max-lines="2" :lineHeight="1.6" :text="qa.answer">
                        <span class="qa-bubble answer">
                            <span class="qa-tag partner">答</span>
                            <span class="qa-text-answer">伙伴</span>
                        </span> <span class="qa-text-2">{{ qa.answer }}</span>
                    </TextEllipsis>
                </div>
                <el-divider border-style="double" />
                <div class="qa-footer">
                    <span>客户名称：{{ qa.customerName }}</span>
                    <template v-if="qa.customerName && qa.customerName.length < 10">
                        <span>创建人：{{ qa.createdUser }}</span>
                        <span>{{ qa.createdTime ? qa.createdTime.substring(5, 10) : '' }}</span>
                    </template>
                </div>
                <div class="qa-footer" v-if="qa.customerName && qa.customerName.length >= 10">
                    <span>创建人：{{ qa.createdUser }}</span>
                    <span>{{ qa.createdTime ? qa.createdTime.substring(5, 10) : '' }}</span>
                </div>
            </div>
        </van-list>
        <el-empty v-else style="display: flex;justify-content: flex-start;" :image="getOssUrl('no-data.png', 3)"
            description="暂无数据" />
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import TextEllipsis from './textEllipsis.vue';

const props = defineProps({
    qaList: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    finished: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['load-more'])

const isHide = ref(true)
const listLoading = ref(false)

// 监听loading状态变化
watch(() => props.loading, (newVal) => {
    if (!newVal) {
        listLoading.value = false
    }
})

// 监听finished状态变化
watch(() => props.finished, (newVal) => {
    if (newVal) {
        listLoading.value = false
    }
})

const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 2);
}

const onLoad = () => {
    // 触发加载更多事件
    emit('load-more')
}

const onDetail = (item) => {
    g.clientStore._openMeetRecord(item.conferenceId, 'summary', 'key_points')
}
</script>

<style lang="scss" scoped>
.qa-list {
    padding: 16px;
    background: #fff;

    p {
        margin: 0;
    }

    .qa-item {
        background: #F9FAFC;
        border-radius: 8px;
        // box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
        padding: 16px;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .qa-item-content {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;



            .qa-item-content-left {
                width: calc(100% - 42px);
                // display: flex;
                // flex-direction: row;
                // justify-content: flex-start;
                // align-items: flex-start;
            }

            .qa-item-content-da-left {
                width: 100%;
                // display: flex;
                // flex-direction: row;
                // justify-content: flex-start;
                // align-items: flex-start;
            }
        }

        .qa-detail-link {
            top: 16px;
            right: 20px;
            color: #436BFF;
            font-size: 14px;
            text-decoration: none;
            z-index: 2;
            line-height: 24px;
            cursor: pointer;
        }

        .qa-bubble {
            align-items: center;
            border-radius: 4px;
            display: inline-block;
            // margin-bottom: 10px;
            font-size: 12px;
            // display: flex;
            flex-direction: row;


            .qa-text-customer {
                color: #04CCA4;
                padding: 0px 6px;
            }

            .qa-text-answer {
                color: #436BFF;
                padding: 0px 6px;
            }

            .qa-tag {
                display: inline-block;
                border-radius: 4px;
                font-size: 12px;
                width: 20px;
                height: 20px;
                font-weight: 500;
                text-align: center;
                line-height: 20px;

            }

            &.question {
                background: rgba(4, 204, 164, 0.1);

                .qa-tag.customer {
                    background: #04CCA4;
                    color: #fff;
                }
            }

            &.answer {
                background: #E6EEFF;
                color: #436BFF;

                .qa-tag.partner {
                    background: #436BFF;
                    color: #fff;
                }
            }
        }

        .qa-text {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            width: calc(100% - 80px);
            margin-left: 6px;
            line-height: 24px;
        }

        .qa-text-2 {
            color: #8C8C8C;
            font-size: 14px;
            width: calc(100% - 80px);
            margin-left: 6px;
            line-height: 25px;
        }

        .qa-footer {
            display: flex;
            gap: 12px;
            color: #B0B0B0;
            font-size: 12px;

            box-sizing: border-box;
            word-break: break-all;

            span:not(:last-child)::after {
                content: '';
                display: inline-block;
                width: 1px;
                height: 12px;
                background: #E0E0E0;
                margin-left: 12px;
                vertical-align: middle;
            }
        }
    }

    .el-divider {
        margin: 12px 0;
    }
}
</style>

<style lang="scss">
.custom-tooltip {
    max-width: 500px !important;
    word-break: break-all;
}
</style>