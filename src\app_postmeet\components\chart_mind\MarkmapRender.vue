<template>
  <svg id="myMindSvg" ref="refMarkmap" style="width: 100%;height: 100%;"></svg>
</template>

<script>
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';

export default {
  name: 'MarkdownRenderer',
  props: {
    content: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      mm: null
    }
  },
  mounted() {
    this.renderMarkmap();
  },
  watch: {
    content() {
      this.$nextTick(this.renderMarkmap());
    },
  },
  methods: {
    renderMarkmap() {
      if (!this.content) return;

      if (!this.mm) {
        const dom = this.$refs.refMarkmap;
        dom.innerHTML = '';
        this.mm = Markmap.create(dom, null, this._getContent())
        this.mm.fit();
      } else {
        this.mm.setData(this._getContent())
      }
    },
    _getContent() {
      const transformer = new Transformer();
      const { root } = transformer.transform(this.content);
      return root;
    },
    getDom() {
      return new Promise((resolve) => {
        this.mm.fit().then(() => {
          resolve(this.$refs.refMarkmap);
        })
      })
    }
  },
};
</script>