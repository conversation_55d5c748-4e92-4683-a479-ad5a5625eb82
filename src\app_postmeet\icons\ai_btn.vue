<template>
    <svg width="89px" height="104px" viewBox="0 0 89 104" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="aibtn-1">
                <stop stop-color="#6B1DFF" offset="0%"></stop>
                <stop stop-color="#01BAE6" offset="100%"></stop>
            </linearGradient>
            <circle id="path-2" cx="27" cy="27" r="27"></circle>
            <filter x="-71.3%" y="-67.6%" width="242.6%" height="242.6%" filterUnits="objectBoundingBox" id="filter-3">
                <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
                <feGaussianBlur stdDeviation="12.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
                <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1">
                </feComposite>
                <feColorMatrix
                    values="0 0 0 0 0.119055704   0 0 0 0 0.106531411   0 0 0 0 0.442182458  0 0 0 0.174321528 0"
                    type="matrix" in="shadowBlurOuter1"></feColorMatrix>
            </filter>
            <rect id="path-4" x="0" y="0" width="24" height="24"></rect>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="aibtn-6">
                <stop stop-color="#FFBE07" offset="0%"></stop>
                <stop stop-color="#FF6B00" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="aibtn-7">
                <stop stop-color="#E128FF" offset="0%"></stop>
                <stop stop-color="#FC5850" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="75.2500162%" y1="50%" x2="32.4838846%" y2="36.6540886%" id="aibtn-8">
                <stop stop-color="#36A4FE" offset="0%"></stop>
                <stop stop-color="#A344FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="aibtn-9">
                <stop stop-color="#4CD2F4" offset="0%"></stop>
                <stop stop-color="#8532FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="aibtn-10">
                <stop stop-color="#4CD2F4" offset="0%"></stop>
                <stop stop-color="#8532FF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="b8--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="总结" transform="translate(-1191.000000, -1182.000000)">
                <g id="沟通内容" transform="translate(640.000000, 72.000000)">
                    <g id="AI入口-常态" transform="translate(576.000000, 1133.000000)">
                        <g id="椭圆形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <circle stroke="url(#aibtn-1)" stroke-width="0.84375" stroke-linejoin="square"
                                fill="#FFFFFF" fill-rule="evenodd" cx="27" cy="27" r="26.578125"></circle>
                        </g>
                        <g id="ai备份-3" transform="translate(15.000000, 15.000000)">
                            <mask id="mask-5" fill="white">
                                <use xlink:href="#path-4"></use>
                            </mask>
                            <g id="path-1"></g>
                            <g id="编组" mask="url(#mask-5)" fill-rule="nonzero">
                                <g
                                    transform="translate(12.312196, 12.318218) rotate(69.000000) translate(-12.312196, -12.318218) translate(-0.227249, -2.483358)">
                                    <path
                                        d="M1.40577651,17.697375 L17.1557765,17.697375 C18.6055239,17.697375 19.7807765,18.8726275 19.7807765,20.322375 C19.7807765,21.7721226 18.6055239,22.947375 17.1557765,22.947375 L1.40577651,22.947375 C-0.0439709569,22.947375 -1.21922349,21.7721226 -1.21922349,20.322375 C-1.21922349,18.8726275 -0.0439709569,17.697375 1.40577651,17.697375 Z"
                                        id="矩形备份" fill="url(#aibtn-6)"
                                        transform="translate(9.280776, 20.322375) rotate(45.000000) translate(-9.280776, -20.322375) ">
                                    </path>
                                    <path
                                        d="M19.3786004,11.7681704 C20.8283479,11.7681704 22.0036005,12.9434229 22.0036005,14.3931704 C22.0036005,15.8429179 20.8283479,17.0181703 19.3786004,17.0181703 L4.36456015,17.0173827 C3.49981875,17.1011289 3.16816371,17.4701601 3.36959505,18.1244761 L1.70438284,16.4478505 C1.46821873,16.128643 1.32304616,15.8834638 1.26886511,15.7123131 C1.18090693,15.4344648 1.09575099,15.1797958 1.08061675,15.0273258 C1.0304428,14.8250954 1.00360048,14.6122676 1.00360048,14.3931704 C1.00360048,12.9434229 2.17885302,11.7681704 3.62860048,11.7681704 L19.3786004,11.7681704 Z"
                                        id="形状结合" fill="url(#aibtn-7)"></path>
                                    <path
                                        d="M19.5581257,11.7690916 C19.7059282,11.783629 19.8124157,11.7976222 19.8775885,11.8110711 C20.0855881,11.8539934 20.2323223,11.9082172 20.2323223,11.9082172 C20.3093999,11.9385831 20.3901403,11.9667054 20.4702212,11.9990602 C21.8144038,12.5421452 22.4638216,14.0720777 21.9207365,15.4162602 L19.954052,20.2839754 C19.410967,21.6281578 17.8810345,22.2775756 16.5368521,21.7344907 C15.1926695,21.1914057 14.5432518,19.6614732 15.0863368,18.3172908 L16.3938254,15.081333 C16.4326514,14.9600985 16.477018,14.830756 16.5270482,14.6932991 C16.9860151,13.4322982 16.7219303,12.4651334 15.7347943,11.7918047 L19.5581257,11.7690916 Z"
                                        id="形状结合" fill="url(#aibtn-8)"></path>
                                    <path
                                        d="M13.7115063,4.50235032 L21.9615063,4.50235032 C23.4112537,4.50235032 24.5865063,5.67760286 24.5865063,7.12735032 C24.5865063,8.57709779 23.4112537,9.75235032 21.9615063,9.75235032 L13.7115063,9.75235032 C12.2617588,9.75235032 11.0865063,8.57709779 11.0865063,7.12735032 C11.0865063,5.67760286 12.2617588,4.50235032 13.7115063,4.50235032 Z"
                                        id="矩形备份-3" fill="url(#aibtn-9)"
                                        transform="translate(17.836506, 7.127350) rotate(21.000000) translate(-17.836506, -7.127350) ">
                                    </path>
                                    <path
                                        d="M8.10393046,0.766364487 C9.55367794,0.766364487 10.7289305,1.94161703 10.7289305,3.39136449 C10.7289305,4.84111196 9.55367794,6.01636449 8.10393046,6.01636449 C6.65418299,6.01636449 5.47893046,4.84111196 5.47893046,3.39136449 C5.47893046,1.94161703 6.65418299,0.766364487 8.10393046,0.766364487 Z"
                                        id="矩形备份-4" fill="url(#aibtn-10)"
                                        transform="translate(8.103930, 3.391364) rotate(21.000000) translate(-8.103930, -3.391364) ">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
