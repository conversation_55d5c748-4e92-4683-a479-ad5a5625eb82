<template>
	<div class="login-box">
		<div class="login-phone-email" v-if="step === 1">
			<div class="login-header">
				<h3>{{ title }}</h3>
				<div class="login-title">
					<p :class="loginType === 1 ? 'active' : ''" @click="errMsg = ''; loginType = 1">手机号</p>
					<p :class="loginType === 1 ? '' : 'active'" @click="errMsg = ''; loginType = 2">邮箱</p>
				</div>
				<div class="phone-box" v-if="loginType === 1">
					<div class="phone-title">+86</div>
					<div class="phone-input">
						<input type="text" class="input-placeholder" placeholder="请输入你的手机号" v-model="phone"
							autocomplete="off">
					</div>
				</div>
				<div class="email-box" v-if="loginType === 2">
					<input type="text" class="input-placeholder" placeholder="请输入邮箱" v-model="email" autocomplete="off">
				</div>
				<span style="color: red; text-align: left; display:block; width: 100%; font-size: 14px;"
					v-if="errMsg">{{ errMsg }}</span>
				<div class="login-next" @click="getCode">下一步</div>
				<div class="login-agree">
					<input type="checkbox" v-model="isAgree" />
					<span>我已阅读并同意<a href="https://www.x-mate.com/postmeet-xmate-h5/#/privacyAgreement"
							target="_blank">隐私协议</a></span>
				</div>
			</div>
			<div class="login-more-box">
				<p class="login-more-line"><span>更多方式</span></p>
				<div class="login-more-btn" @click="setStep(2)"><span>SSO 登录</span></div>
			</div>
		</div>
		<sso @back="setStep" v-else-if="step === 2" :ssoDomain="defaultSSODomain" :productCode="productCode"
			:deviceType="deviceType" :apiConfig="apiConfig" @targetOrgInfo="orgInfo"></sso>
		<id-code @back="setStep" @next="showList" @sendCodeAgain="getCode" @targetOrgInfo="orgInfo" :errMsg="errMsg"
			:phone="phone" :email="email" :type="loginType" :country-code="countryCode" :apiConfig="apiConfig"
			:deviceType="deviceType" :productCode="productCode" :waitTime="waitTime" v-else-if="step === 3"></id-code>
		<org-list @back="setStep" @targetOrgInfo="orgInfo" :list="list" :phone="phone" :email="email" :type="loginType"
			:country-code="countryCode" :code="code" :apiConfig="apiConfig" :deviceType="deviceType"
			:productCode="productCode" v-else-if="step === 4"></org-list>
	</div>
</template>
<script>
import sso from './sso.vue'
import idCode from './idCode.vue'
import orgList from './orgList.vue'
import api from './api.js'
export default {
	name: 'LoginCom',
	components: {
		sso,
		idCode,
		orgList
	},
	computed: {
		_captchaId() {
			return this.captchaId || (window.feConfig?.common.captchaId) || '2025627896'
		}
	},
	props: {
		privacyLink: {
			type: String,
			default: import.meta.env.VITE_H5_PRIVACY_LINK
		},
		// 短信验证码、邮箱验证码、机构列表等 接口的集群 必须与 接收验证码的机构（验证码发送需要绑定机构）保持一致
		apiConfig: {
			type: String,
			default: window.feConfig?.common?.apiBaseUrl || ''
		},
		// 产品要素
		productCode: {
			type: String,
			default: 'apaas_platform'
		},
		// 1 PC 2 H5 3 IOS 4 Android
		deviceType: {
			type: Number,
			default: 1
		},
		captchaId: String,
		defaultLoginType: {
			type: Number,
			default: 1
		},
		title: {
			type: String,
			default: ''
		},
		defaultSSODomain: String,
		waitTime: {
			type: Number,
			default: 60
		}
	},
	data() {
		return {
			isAgree: false,
			loginType: this.defaultLoginType, // 1 手机号 2 邮箱
			step: 1,
			phone: '',
			email: '',
			errMsg: '',
			countryCode: '+86',
			list: [],
			url: 'https://turing.captcha.qcloud.com/TCaptcha.js',
			code: ''
		}
	},
	methods: {
		orgInfo(org) {
			this.$emit('targetOrgInfo', org)
		},
		showList(res, code) {
			this.code = code
			this.list = res
			this.setStep(4)
		},
		getCode(c) {
			if (typeof c !== 'function') {
				c = null
			}
			if (!this.isAgree) {
				return alert('请先同意用户协议')
			}
			if (this.loginType === 1) {
				if (/^1[3456789]\d{9}$/.test(this.phone)) {
					this.openCaptcha(this.smsSend, c)
				} else {
					this.errMsg = '请输入正确的手机号'
				}
			} else if (this.loginType === 2) {
				if (/.*@.*/.test(this.email)) {
					this.openCaptcha(this.emailSend, c)
				} else {
					this.errMsg = '请输入正确的邮箱'
				}
			}
		},
		smsSend(r, c) {
			api({
				baseURL: this.apiConfig,
				headers: {
					source: this.deviceType == 1 ? 501 : 506
				}
			}).post('/core/apaas/sms/send', {
				"phone": this.phone,
				"countryCode": this.countryCode,
				"randstr": r.randstr,
				"ticket": r.ticket,
				"factorCode": this.productCode
			}).then(res => {
				console.log(res)
				this.setStep(3)
				this.errMsg = ''
				c && c()
			}).catch(err => {
				this.errMsg = err.response ? err.response.data?.error.message : err.message
			})
		},
		emailSend(r, c) {
			api({
				baseURL: this.apiConfig,
				headers: {
					source: this.deviceType == 1 ? 501 : 506
				}
			}).post('/core/apaas/mail/send', {
				"email": this.email,
				"randstr": r.randstr,
				"ticket": r.ticket,
				"factorCode": this.productCode
			}).then(res => {
				console.log(res)
				this.setStep(3)
				this.errMsg = ''
				c && c()
			}).catch(err => {
				this.errMsg = err.response ? err.response.data?.error.message : err.message
			})
		},
		openCaptcha(callback, c) {
			this.creatScript(() => {
				new window.TencentCaptcha(this._captchaId, (res) => {
					if (res.ret === 0) {
						callback && callback(res, c)
					} else {
						alert('验证失败')
					}
				}).show()
			})
		},
		setStep(step) {
			this.step = step
			this.$emit('stepChange', step)
		},
		creatScript(callback) {
			if (window.TencentCaptcha) return callback && callback()
			const script = document.createElement('script')
			script.onload = () => {
				callback && callback()
			}
			script.onreadystatechange = function () {
				if (this.readyState === 'loaded' || this.readyState === 'complete') {
					callback && callback()
				}
			}
			script.setAttribute('type', 'text/javascript')
			script.setAttribute('src', this.url)
			document.body.appendChild(script)
		}
	},
	created() {
		this.$emit('stepChange', this.step)
	}
}
</script>
<style lang="scss" scoped>
.login-box {
	position: relative;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
}

.login-phone-email {
	display: flex;
	height: 100%;
	width: 100%;
	flex-direction: column;

	h3 {
		font-size: 22px;
		text-align: left;
	}
}

.login-header {
	flex: 1;

	h3 {
		margin-top: 0;
	}
}

.login-title {
	overflow: hidden;

	p {
		font-size: 14px;
		float: left;
		margin-right: 30px;
		line-height: 24px;
		cursor: pointer;
		height: 28px;
	}

	p.active {
		color: #3370ff;
		position: relative;
	}

	.active:after {
		position: absolute;
		bottom: 0;
		left: 0;
		display: block;
		width: 100%;
		height: 2px;
		content: "";
		background-color: #3370ff;
		border-radius: 2px 2px 0 0;
	}
}

.login-agree {
	overflow: hidden;
	text-align: left;
	font-size: 14px;
	line-height: 14px;
	margin: 24px 0px;
	color: #595959;

	input {
		padding: 0;
		margin: 0;
		float: left;
		width: 14px;
		height: 14px;
		margin-right: 4px;
	}

	span {
		float: left;

		a {
			text-decoration: none;
			margin-left: 4px;
			color: #436BFF;
			cursor: pointer;
		}
	}
}

.phone-box {
	width: 100%;
	display: flex;

	.phone-title {
		font-size: 16px;
		width: 80px;
		float: left;
		box-sizing: border-box;
		justify-content: center;
		min-width: 80px;
		height: 40px;
		line-height: 40px;
		padding: 0 8px;
		border: 1px solid #d0d3d6;
		border-radius: 6px 0 0 6px;
		flex-shrink: 1;
		align-items: center;
		-webkit-box-pack: center;
	}

	.phone-input {
		flex: 1;
		float: left;
		z-index: 0;
		box-sizing: border-box;
		display: flex;
		flex-grow: 1;
		align-items: center;
		justify-content: center;
		height: 40px;
		border: 1px solid #d0d3d6;
		border-left-color: transparent;
		border-radius: 0 6px 6px 0;
		transform: translateX(-1px);

		input {
			width: 100%;
			height: 24px;
			padding: 0 10px;
			font-size: 16px;
			line-height: 24px;
			background: transparent;
			border: none;
			outline: none;
		}
	}

	div.active {
		border-color: #3370ff;
	}
}

.email-box {
	width: 100%;
	flex: 1;
	z-index: 0;
	box-sizing: border-box;
	height: 40px;
	border: 1px solid #d0d3d6;
	border-radius: 6px;
	transform: translateX(-1px);
	display: flex;
	flex-grow: 1;
	align-items: center;
	justify-content: center;

	input {
		width: 100%;
		height: 24px;
		padding: 0 10px;
		font-size: 16px;
		line-height: 24px;
		background: transparent;
		border: none;
		outline: none;
	}
}

.input-placeholder::placeholder {
	color: #BFBFBF;
	font-size: 14px;
}

.login-next {
	text-align: center;
	margin-top: 24px;
	width: 100%;
	color: #fff;
	background: #3370ff;
	border-color: #3370ff;
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	border-radius: 6px;
	min-width: 96px;
	cursor: pointer;

	&:hover {
		color: #fff;
		background: #4e83fd;
		border-color: #4e83fd;
	}
}

.login-more-box {
	position: relative;

	.login-more-line {
		margin-top: 24px;
		margin-bottom: 24px;
		color: #8f959e;
		position: relative;
		font-size: 14px;
		line-height: 22px;
		font-weight: 400;
		border-top-width: 0;
		white-space: nowrap;
		text-align: center;
		display: flex;
		clear: both;
		width: 100%;
		min-width: 100%;
		border-top-color: rgba(31, 35, 41, .15);

		&:before {
			margin: 0;
			color: #8f959e;
			white-space: nowrap;
			text-align: center;
			position: relative;
			top: 50%;
			width: 50%;
			border-top: 1px solid transparent;
			border-top-color: inherit;
			transform: translateY(50%);
			box-sizing: border-box;
			content: "";
		}

		&:after {
			position: relative;
			top: 50%;
			width: 50%;
			border-top: 1px solid transparent;
			border-top-color: inherit;
			transform: translateY(50%);
			box-sizing: border-box;
			content: "";
		}

		span {
			display: inline-block;
			padding: 0 1em;
			box-sizing: border-box;
		}
	}

	.login-more-btn {
		box-sizing: border-box;
		font-weight: 400;
		border-radius: 20px;
		height: 40px;
		line-height: 24px;
		padding: 7px 15px;
		font-size: 16px;
		min-width: 96px;
		text-align: center;
		cursor: pointer;
		user-select: none;
		outline: none;
		border: 1px solid #d0d3d6;

		span {
			padding-left: 30px;
			display: inline-block;
			background: url('https://stc.yxt.com/assets/5432b5a8/e5e2832b/sso2x.png') no-repeat;
			background-size: 20px 20px;
			background-position: 0 2px;
		}

		&:hover {
			color: #1f2329;
			background: #f2f3f5;
			border-color: #d0d3d6;
		}
	}
}
</style>