<template>
  <div class="radar_chart_wrap">
    <div v-show="hasData">
      <div ref="refChart" class="Chart"></div>
      <div class="score-legend" v-if="showLegend && hasData">
        <div class="legend-item">
          <span class="dot red"></span>
          <span class="legend-text">60分以下</span>
        </div>
        <div class="legend-item">
          <span class="dot orange"></span>
          <span class="legend-text">60 - 80分</span>
        </div>
        <div class="legend-item">
          <span class="dot blue"></span>
          <span class="legend-text">80 - 90分</span>
        </div>
        <div class="legend-item">
          <span class="dot green"></span>
          <span class="legend-text">90分以上</span>
        </div>
      </div>
    </div>
    <div class="no_data" v-if="!hasData">
      <el-empty description="暂无数据"></el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onUnmounted, onMounted } from 'vue'
import echarts from "@/js/echarts"

const hasData = ref(false)
const refChart = ref(null)

const props = defineProps({
  showLegend: {
    type: Boolean,
    default: true
  }
})

const getScoreColor = (score) => {
  if (score >= 90) return "#52C41A" // 绿色
  if (score >= 80) return "#436BFF" // 蓝色
  if (score >= 60) return "#FA8C16" // 橙色
  return "#FF4D4F" // 红色
}

const clear = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
}

let chartInstance = null;
let resizeObserver = null;

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  // 使用ResizeObserver监听容器尺寸变化
  resizeObserver = new ResizeObserver(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });

  if (refChart.value) {
    resizeObserver.observe(refChart.value);
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  clear();
})

const init = async (data, perfect = 100) => {
  if (data.length === 0) {
    hasData.value = false
    return
  }

  hasData.value = true
  const option = {
    radar: {
      shape: "circle",
      indicator: data.map((item) => ({
        name: item.label.trim() + " " + item.value + "分",
        max: perfect,
        color: getScoreColor(item.value),
      })),
      axisName: {
        color: "#333",
      },
    },
    series: [
      {
        type: "radar",
        data: [
          {
            value: data.map((item) => parseFloat(item.value)),
            name: "Score",
            itemStyle: {
              color: "#436bff",
            },
            areaStyle: {
              color: "rgba(64, 158, 255, 0.2)",
            },
            lineStyle: {
              color: "#436bff",
            },
          },
        ],
      },
    ],
  }

  await nextTick()
  if (chartInstance) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(refChart.value, "light")
  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

defineExpose({
  init,
  clear
})
</script>

<style lang="scss" scoped>
.radar_chart_wrap {
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: flex;
  flex-direction: column;

  .no_data {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .Chart {
    flex: 1;
    min-height: 300px;
    width: 100%;
  }

  .score-legend {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 20px;

    .legend-item {
      display: flex;
      align-items: center;

      .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .legend-text {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
      }

      .red {
        background-color: #ff4d4f;
      }

      .orange {
        background-color: #fa8c16;
      }

      .blue {
        background-color: #436bff;
      }

      .green {
        background-color: #52c41a;
      }
    }
  }
}
</style>
