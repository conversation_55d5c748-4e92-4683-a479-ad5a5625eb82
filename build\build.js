const path = require('path')
const shell = require('shelljs')

// VUE_APP_APIENV 是jenkins打包注入的
const env = process.env.VUE_APP_APIENV || 'tf';//tf-tc-01,di-hw-01
console.log('env:', env)
// const vitePath = path.join(__dirname, '../node_modules/.bin/vite')
const vitePath = './node_modules/.bin/vite'

const { code } = shell.exec(
  `cross-env NODE_ENV=production VUE_APP_APIENV=${env} ${vitePath} build --mode ${env}`
)

if (code !== 0) {
  shell.echo('Error: build failed')
  shell.exit(1)
}
