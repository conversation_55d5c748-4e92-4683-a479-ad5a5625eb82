.downlod_wrap {
  position: relative;
  width: 100%;
  height: 100vh;

  .pointer {
    cursor: pointer;
  }

  .download_page {
    box-sizing: border-box;
    overflow-x: hidden;
    z-index: 1;
    width: 100%;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 1);
    justify-content: flex-start;
    padding-top: 0;
    font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
      Arial, PingFang SC-Light;

    button {
      margin: 0;
      padding: 0;
      border: 1px solid transparent;
      outline: none;
      background-color: transparent;
    }

    button:active {
      opacity: 0.6;
    }

    .flex-col {
      display: flex;
      flex-direction: column;
    }

    .flex-row {
      display: flex;
      flex-direction: row;
    }


    .box1 {
      z-index: auto;

      .main1 {
        z-index: 57;
        height: 52px;
        background-color: rgba(255, 255, 255, 0.5);
        width: 100%;
        justify-content: center;
        align-items: flex-start;
        padding-left: 363px;

        .main2 {
          z-index: auto;
          cursor: pointer;
          justify-content: space-between;

          .label1 {
            z-index: 59;
            width: 37px;
            height: 25px;
          }

          .pic1 {
            width: 160px;
            height: 26px;
          }
        }
      }

      .main3 {
        z-index: 2;
        background: url(https://stc.yxt.com/ufd/989eac/bg_blue.png) 100% repeat-x;
        width: 100%;
        justify-content: flex-start;
        padding-top: 48px;
        align-items: center;
        position: relative;

        .wrap1 {
          z-index: auto;
          width: 892px;
          height: 389px;

          .word1 {
            z-index: 54;
            width: 432px;
            height: 67px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 48px;
            font-family: PingFangSC-Semibold;
            white-space: nowrap;
            line-height: 67px;
            text-align: center;
            align-self: center;
          }

          .txt1 {
            z-index: 55;
            width: 730px;
            height: 28px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 20px;
            white-space: nowrap;
            line-height: 28px;
            text-align: center;
            align-self: center;
            margin-top: 16px;
          }

          .txt12 {
            font-size: 20px;
            text-align: center;
            color: rgba(51, 51, 51, 1);
            align-self: center;
            align-self: center;
          }

          .mod1 {
            z-index: auto;
            width: 892px;
            height: 215px;
            margin-top: 48px;
            justify-content: space-between;

            .section1 {
              z-index: auto;
              width: 148px;
              height: 215px;

              .wrap2 {
                z-index: auto;
                width: 148px;
                height: 215px;

                img.qr {
                  height: 148px;
                }

                .layer1 {
                  cursor: pointer;
                  z-index: 45;
                  height: 148px;
                  border-radius: 8px;
                  background-color: rgba(255, 255, 255, 1);
                  box-shadow: 0px 0px 10px 0px rgba(19, 24, 51, 0.05);
                  width: 148px;

                  .outer1 {
                    z-index: auto;
                    width: 74px;
                    height: 60px;
                    margin-left: 74px;
                    justify-content: space-between;

                    .outer2 {
                      z-index: 48;
                      width: 11px;
                      height: 12px;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_8.png) 100% no-repeat;
                      margin-top: 48px;
                    }

                    .outer3 {
                      z-index: 52;
                      height: 52px;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_15.png) 100% no-repeat;
                      width: 52px;
                      justify-content: flex-start;
                      align-items: flex-end;
                      padding: 6px 6px 0 0;

                      .icon1 {
                        z-index: 53;
                        width: 20px;
                        height: 19px;
                      }
                    }
                  }

                  .outer4 {
                    z-index: auto;
                    width: 44px;
                    height: 40px;
                    margin-left: 52px;

                    .mod2 {
                      z-index: 47;
                      width: 44px;
                      height: 40px;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_16.png) 100% no-repeat;
                    }
                  }
                }

                .info1 {
                  z-index: 49;
                  width: 94px;
                  height: 28px;
                  display: block;
                  overflow-wrap: break-word;
                  color: rgba(38, 38, 38, 1);
                  font-size: 20px;
                  white-space: nowrap;
                  line-height: 28px;
                  text-align: center;
                  align-self: center;
                  margin-top: 14px;
                }
              }
            }

            .section2 {
              z-index: auto;
              width: 148px;
              height: 215px;

              .group1 {
                z-index: auto;
                width: 148px;
                height: 215px;

                img.qr {
                  height: 148px;
                }

                .section3 {
                  cursor: pointer;
                  z-index: 28;
                  height: 148px;
                  border-radius: 8px;
                  background-color: rgba(255, 255, 255, 1);
                  box-shadow: 0px 0px 10px 0px rgba(19, 24, 51, 0.05);
                  width: 148px;
                  justify-content: flex-start;
                  align-items: flex-end;
                  position: relative;

                  .wrap3 {
                    z-index: 30;
                    height: 52px;
                    background: url(https://stc.yxt.com/ufd/989eac/dl_15.png) 100% no-repeat;
                    width: 52px;
                    justify-content: flex-start;
                    align-items: flex-end;
                    padding: 6px 6px 0 0;

                    .icon2 {
                      z-index: 31;
                      width: 20px;
                      height: 19px;
                    }
                  }

                  .wrap4 {
                    z-index: 35;
                    height: 64px;
                    border-radius: 50%;
                    background: url(https://stc.yxt.com/ufd/989eac/dl_12.png) 100% no-repeat;
                    width: 64px;
                    position: absolute;
                    left: 42px;
                    top: 42px;

                    .group2 {
                      z-index: 36;
                      position: relative;
                      width: 64px;
                      height: 64px;
                      border-radius: 50%;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_13.png) 100% no-repeat;

                      .section4 {
                        z-index: 37;
                        position: absolute;
                        left: 15px;
                        top: 13px;
                        width: 35px;
                        height: 40px;
                        background: url(https://stc.yxt.com/ufd/989eac/dl_14.png) 0px 0px no-repeat;
                      }
                    }
                  }
                }

                .txt2 {
                  z-index: 32;
                  width: 132px;
                  height: 28px;
                  display: block;
                  overflow-wrap: break-word;
                  color: rgba(38, 38, 38, 1);
                  font-size: 20px;
                  white-space: nowrap;
                  line-height: 28px;
                  text-align: center;
                  align-self: center;
                  margin-top: 14px;
                }
              }
            }

            .section5 {
              z-index: auto;
              width: 148px;
              height: 215px;


              .box2 {
                z-index: auto;
                width: 148px;
                height: 215px;
                position: absolute;

                .box3 {
                  cursor: pointer;
                  z-index: 17;
                  height: 148px;
                  border-radius: 8px;
                  background-color: rgba(255, 255, 255, 1);
                  box-shadow: 0px 0px 10px 0px rgba(19, 24, 51, 0.05);
                  width: 148px;
                  justify-content: center;
                  align-items: center;

                  .mac_note {
                    position: absolute;
                    top: 0;
                    left: 0;
                    background-color: #0c6;
                    color: #fff;
                    border-radius: 8px 3px;
                    padding: 2px 6px;
                  }

                  .pic2 {
                    z-index: 20;
                    width: 64px;
                    height: 64px;
                  }
                }

                .info2 {
                  z-index: 18;
                  width: 128px;
                  height: 28px;
                  display: block;
                  overflow-wrap: break-word;
                  color: rgba(38, 38, 38, 1);
                  font-size: 20px;
                  white-space: nowrap;
                  line-height: 28px;
                  text-align: center;
                  align-self: center;
                  margin-top: 14px;
                }
              }
            }

            .section6 {
              z-index: auto;
              width: 148px;
              height: 215px;

              .mod3 {
                z-index: auto;
                width: 148px;
                height: 215px;

                .layer2 {
                  z-index: 6;
                  height: 148px;
                  border-radius: 8px;
                  background-color: rgba(255, 255, 255, 1);
                  box-shadow: 0px 0px 10px 0px rgba(19, 24, 51, 0.05);
                  width: 148px;
                  justify-content: center;
                  align-items: center;

                  .btn5 {
                    width: 116px;
                    height: 36px;
                    text-align: center;
                    line-height: 36px;
                    border-radius: 4px;
                    margin-top: 9px 0;
                    cursor: pointer;
                    background: #3878D1;
                    color: #FFFFFF;
                  }


                  .btn52 {
                    margin-top: 18px;
                  }

                  .box4 {
                    z-index: 8;
                    height: 64px;
                    border-radius: 50%;
                    background: url(https://stc.yxt.com/ufd/989eac/dl_7.png) 100% no-repeat;
                    width: 64px;
                    justify-content: center;
                    align-items: center;

                    .mod4 {
                      z-index: 9;
                      width: 32px;
                      height: 32px;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_9.png) 0px 0px no-repeat;
                    }

                    .mod5 {
                      z-index: 9;
                      width: 32px;
                      height: 32px;
                      background: url(https://stc.yxt.com/ufd/989eac/dl_9.png) 0px 0px no-repeat;
                    }
                  }
                }

                .info3 {
                  z-index: 14;
                  width: 144px;
                  height: 28px;
                  display: block;
                  overflow-wrap: break-word;
                  color: rgba(38, 38, 38, 1);
                  font-size: 20px;
                  white-space: nowrap;
                  line-height: 28px;
                  text-align: center;
                  margin-top: 14px;
                }
              }
            }
          }

          .info4 {
            z-index: 50;
            width: 72px;
            height: 24px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 16px;
            white-space: nowrap;
            line-height: 24px;
            text-align: center;
            align-self: flex-start;
            margin: -1px 0 0 38px;
          }

          .info5 {
            z-index: 33;
            width: 84px;
            height: 24px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 16px;
            white-space: nowrap;
            line-height: 24px;
            text-align: center;
            align-self: flex-start;
            margin: 0 0 0 35px;
          }

          .word2 {
            z-index: 15;
            width: 92px;
            height: 24px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 16px;
            white-space: nowrap;
            line-height: 24px;
            text-align: center;
            align-self: flex-end;
            margin: 0px 28px 0 0;
          }

          .txt3 {
            z-index: 26;
            width: 90px;
            height: 24px;
            display: block;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 16px;
            white-space: nowrap;
            line-height: 24px;
            text-align: center;
            margin-left: 30px;
          }
        }

        .word3 {
          z-index: 25;
          position: absolute;
          left: 840px;
          top: 393px;
          width: 90px;
          height: 24px;
          display: block;
          overflow-wrap: break-word;
          color: rgba(140, 140, 140, 1);
          font-size: 16px;
          white-space: nowrap;
          line-height: 24px;
          text-align: center;
        }
      }

      .word4 {
        z-index: 3;
        width: 338px;
        height: 20px;
        display: block;
        overflow-wrap: break-word;
        color: rgba(89, 89, 89, 1);
        font-size: 12px;
        white-space: nowrap;
        line-height: 20px;
        text-align: center;
        align-self: center;
        margin-top: 620px;
        margin-bottom: 48px;
      }
    }

    .box5 {
      z-index: 84;
      position: absolute;
      top: 540px;
      width: 913px;
      height: 530px;
      margin: 0 auto;
      left: 0;
      right: 0;
    }
  }
}