<template>
  <el-drawer v-model="isShow" @callback="cbModal" modal-class="new-folder-modal" direction="rtl">
    <template #header>
      <div class="vd_title">
        新建文件夹
      </div>
    </template>
    <template #default>
      <el-form :model="form" label-position="top">
        <div class="form_title">
          基本信息
        </div>
        <el-form-item label="名称">
          <el-input v-model="form.name" ref="nameRef" placeholder="请输入名称" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="所属文件夹">
          <el-input v-model="parentName" readonly disabled></el-input>
        </el-form-item>
        <div class="form_title">
          权限设置
        </div>
        <el-form-item label="权限设置">
          <el-checkbox v-model="inheritParent" :disabled="form.parentId == '0'">继承父文件夹权限</el-checkbox>
          <div class="permission-note">注：勾选后使用父文件夹权限覆盖当前文件夹权限，不允许修改</div>
          <div class="permission-note">（父文件夹是指根目录下的一级文件夹）</div>
        </el-form-item>
        <el-form-item label="文件夹管理员" v-if="!inheritParent">
          <DrawerSelectMixed ref="adminListRef" @callback="cbAdminList" type="user" />
        </el-form-item>
        <el-form-item label="浏览权限" v-if="!inheritParent">
          <div class="view-permission flex-col">
            <el-radio-group v-model="viewPermission" :disabled="form.inheritParentPermissions"
              @change="viewPermissionChange">
              <el-radio value="all">所有人可见</el-radio>
              <el-radio value="partial">仅部分人可见</el-radio>
            </el-radio-group>
            <div v-if="viewPermission === 'partial'">
              <DrawerSelectMixed ref="userListRef" @callback="cbUserList" type="mixed" />
            </div>
          </div>
        </el-form-item>

      </el-form>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">取消</el-button>
        <el-button type="primary" @click="confirmClick">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { createClipLibCategory, updateClipLibCategory } from "@/app_client/tools/api";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import { getClipFolderPermission } from "@/js/api";
import { ElMessage } from 'element-plus';

const defaultUserList = { users: [], depts: [] };
const emit = defineEmits(["confirm"]);
const parentName = ref("");
const nameRef = ref(null);
const folderId = ref(null);
const pageMode = ref("add");
const isShow = ref(false);
const adminList = ref(defaultUserList);
const userList = ref(defaultUserList);
const viewPermission = ref('all');
const inheritParent = ref(false);
const folderInfo = ref({});
const adminListRef = ref(null);
const userListRef = ref(null);
const form = ref({
  "name": "",
  "parentId": "",
  "permission": {
    "sourceId": "",
    "inheritParent": 0,
    "permissionMaps": []
  }
});
// 是否继承父文件的权限配置：0-否，1-是
// 权限类型：0-案例库浏览权限，1-案例库管理员
// sourceType	授权对象类型：0-部门，1-人员
const cbAdminList = (action, data) => {
  // cbAdminList confirm {
  // 	"users": [{
  // 		"deptId": "4566d8c5-920f-4458-9d9d-58a2d5c9eac1",
  // 		"deptName": "zqdept_01",
  // 		"fullname": "zq009",
  // 		"id": "d5ddee28-cee1-411e-aa57-5820627c0dab",
  // 		"imgUrl": "",
  // 		"positionName": "zq岗位02",
  // 		"username": "zq009",
  // 		"_index": 1,
  // 		"is_checkable_": true
  // 	}],
  // 	"depts": [{
  // 		"value": "308b2ad7-9b9a-47ac-af4e-94e0fce1cc1c",
  // 		"label": "zqdept_03",
  // 		"pName": "zqdept_01",
  // 		"pid": "4566d8c5-920f-4458-9d9d-58a2d5c9eac1"
  // 	}]
  // }
  if (action == "confirm") {
    adminList.value = data;
  }
}

const cbUserList = (action, data) => {
  if (action == "confirm") {
    userList.value = data;
  }
}

// Add watch to handle permission changes
const getData = () => {
  // Reset permissionMaps
  form.value.permission.permissionMaps = [];
  form.value.permission.inheritParent = inheritParent.value ? 1 : 0;
  if (inheritParent.value) {
    form.value.permission.permissionMaps = [];
  } else {
    for (let i = 0; i < adminList.value.depts.length; i++) {
      form.value.permission.permissionMaps.push({
        permissionId: "",
        permissionType: 1, // Admin type
        targetId: adminList.value.depts[i].value,
        sourceType: 0,
        targetName: adminList.value.depts[i].label
      });
    }

    for (let i = 0; i < adminList.value.users.length; i++) {
      form.value.permission.permissionMaps.push({
        permissionId: "",
        permissionType: 1, // Admin type
        targetId: adminList.value.users[i].id,
        sourceType: 1,
        targetName: adminList.value.users[i].fullname
      });
    }

    if (viewPermission.value === 'partial') {
      if ((userList.value?.users?.length || 0) + (userList.value?.depts?.length || 0) === 0) {
        ElMessage.error('请选择可见用户或部门');
        return;
      }
      // Add user permissions
      userList.value.users.forEach(user => {
        form.value.permission.permissionMaps.push({
          permissionId: "",
          permissionType: 0, // User type
          targetId: user.id,
          sourceType: 1,
          targetName: user.fullname
        });
      })

      userList.value.depts.forEach(dept => {
        form.value.permission.permissionMaps.push({
          permissionId: "",
          permissionType: 0, // User type
          targetId: dept.value,
          sourceType: 0,
          targetName: dept.label
        });
      })

    } else {
      form.value.permission.permissionMaps.push({
        permissionId: "",
        permissionType: 0, // User type
        targetId: 'ALL',
        sourceType: 1,
        targetName: '所有人'
      })
    }
  }

  const data = toRaw(form.value);
  return data;
};

const cancelClick = () => {
  isShow.value = false;
};

const confirmClick = () => {
  cbModal('confirm');
};

const _add_top = async () => {
  try {
    const formData = getData();
    if (!formData) return;
    const node = await createClipLibCategory(formData);
    ElMessage.success("创建成功");
    emit("callback", "add_ok", node);
    isShow.value = false;
    _reset();
  } catch (error) {
    console.log(error)
    ElMessage.error("创建失败");
  }
};

const _add_sub = async () => {
  try {
    const formData = getData();
    if (!formData) return;
    const node = await createClipLibCategory(formData);
    ElMessage.success("创建成功");
    emit("callback", "add_ok", node);
    isShow.value = false;
    form.value.name = ""; // 重置表单
    _reset();
  } catch (error) {
    console.log(error)
    ElMessage.error("创建失败");
  }
};

const _edit = async () => {
  try {
    const formData = getData();
    if (!formData) return;
    const res = await updateClipLibCategory(folderId.value, formData);
    ElMessage.success("编辑成功");
    emit("callback", "refresh");
    isShow.value = false;
    _reset();
  } catch (error) {
    console.log(error);
    ElMessage.error("创建失败");
  }
};

const cbModal = async (action, data) => {
  if (action == "confirm") {
    if (!form.value.name) {
      ElMessage.warning("请输入文件夹名称");
      return;
    }
    if (pageMode.value == "add_top") {
      _add_top();
    } else if (pageMode.value == "add_sub") {
      _add_sub();
    } else if (pageMode.value == "edit") {  // 编辑
      _edit();
    } else {
      console.error("未知操作", pageMode.value);
    }
  } else {
    isShow.value = false;
  }
};

const open = (title) => {
  isShow.value = true;
};

const _reset = () => {
  form.value.name = "";
  userList.value = defaultUserList;
  adminList.value = defaultUserList;
  viewPermission.value = 'all';
  inheritParent.value = false;
  adminListRef.value?.reset();
  userListRef.value?.reset();
};

const show_add_top = () => {
  pageMode.value = "add_top";
  form.value.parentId = "0";
  parentName.value = "根目录";
  _reset();
  open("新建文件夹");
};

const show_add_sub = (data) => {
  pageMode.value = "add_sub";
  form.value.parentId = data.id;
  parentName.value = data.name;
  _reset();
  open("新建子文件夹");
};

const get_permission = async (id) => {
  try {
    const res = await getClipFolderPermission(id);

    if (res.code === 0 && res.data) {
      const { admins, permissionBase, permissionData } = res.data;

      // 设置继承权限状态
      inheritParent.value = permissionBase.inheritParent === 1;
      form.value.permission.inheritParent = permissionBase.inheritParent;
      form.value.permission.sourceId = permissionBase.sourceId;

      // 设置管理员列表
      adminList.value.users = admins.filter(admin => admin.sourceType === 1).map(x => {
        return {
          id: x.targetId,
          fullname: x.targetName || '未知用户'  // 建议后端返回targetName
        }
      });

      adminList.value.depts = admins.filter(admin => admin.sourceType === 0).map((x) => {
        return {
          value: x.targetId,
          label: x.targetName || '未知部门'  // 建议后端返回targetName
        };
      });

      // 设置查看权限
      if (permissionData.length === 1 && permissionData[0].targetId === 'ALL') {
        viewPermission.value = 'all';
        userList.value = [];
      } else {
        viewPermission.value = 'partial';
        // 设置用户列表
        userList.value.users = permissionData.filter(user => user.sourceType === 1).map((user) => {
          return {
            id: user.targetId,
            fullname: user.targetName || '未知用户'
          };
        });
        userList.value.depts = permissionData.filter(user => user.sourceType === 0).map((user) => {
          return {
            value: user.targetId,
            label: user.targetName || '未知部门'
          };
        });
      }

      // 更新 form 中的 permissionMaps
      form.value.permission.permissionMaps = [
        ...admins.map(admin => ({
          permissionId: admin.permissionId,
          permissionType: 1,
          targetId: admin.targetId,
          sourceType: 1,
          targetName: admin.targetName
        })),
        ...permissionData.map(user => ({
          permissionId: user.permissionId,
          permissionType: 0,
          targetId: user.targetId,
          sourceType: 1,
          targetName: user.targetName
        }))
      ];
      nextTick(() => {
        adminListRef.value.init(adminList.value);
        if (viewPermission.value === 'partial') {
          userListRef.value.init(userList.value);
        }
      })
    }
  } catch (error) {
    console.error('获取权限信息失败:', error);
    ElMessage.error('获取权限信息失败');
  }
};

const viewPermissionChange = () => {
  userList.value = [];
  nextTick(() => {
    if (viewPermission.value === 'partial') {
      userListRef.value.init({ users: [], depts: [] });
    }
  })
}

const show_edit = async (data) => {
  folderInfo.value = data;
  pageMode.value = "edit";
  form.value.name = data.name;
  form.value.parentId = data.parentId;
  if (data.parentId == "0") {
    parentName.value = "根目录";
  } else {
    parentName.value = data.parentName;
  }

  folderId.value = data.id;
  open("编辑文件夹");

  // 等待权限数据加载
  await get_permission(data.id);
};

// 对外暴露打开方法
defineExpose({
  open,
  show_add_sub,
  show_add_top,
  nameRef,
  form,
  adminList,
  userList,
  viewPermission,
  inheritParent,
  DrawerSelectMixed,
  show_edit,
  cbModal,
  isShow,
});
</script>

<style lang="scss">
.new-folder-modal {

  .el-drawer {
    .el-drawer__header {
      margin-bottom: 12px !important;
    }

    .el-form {
      padding: 0 4px;

      .mixed_input_box {
        width: 400px !important;
      }

      .form_title {
        font-size: 16px;
        border-left: 4px solid #436BFF;
        padding-left: 12px;
        margin-bottom: 12px;
      }

      .el-form-item {
        margin: 12px 0;
      }

      .permission-note {
        color: #999;
        font-size: 12px;
        margin-top: 4px;
        line-height: 22px;
      }
    }
  }

}
</style>
