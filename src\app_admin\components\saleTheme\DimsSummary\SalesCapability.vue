<template>
  <div class="sales-capability">
    <div class="example_pic">评估维度</div>
    <div class="sales-capability-content">
      <MyTable ref="refTable" :cfg="datas">

        <template v-if="data.systemId == 206" #col_task_name="{ row }">
          {{ row['name'] }}
        </template>
        <template v-if="data.systemId == 205" #col_ability_name="{ row }">
          {{ row['name'] }}
        </template>
        <template #col_behavior="{ row }">
          <div v-for="(item, index) in row['details']" :key="index">
            {{ item }}
          </div>
        </template>
      </MyTable>
    </div>
    <DrawerSalesCapability v-if="drawerSalesCapability" ref="refDrawerSalesCapability" @callback="onCallback"
      :systemId="systemId" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import DrawerSalesCapability from "./DrawerSalesCapability.vue";
import { nextTick } from "vue";

const props = defineProps(["data"]);
const emit = defineEmits(["update:data", "callback"]);

const tableData = ref([]);
const refTable = ref(null);
const refDrawerSalesCapability = ref(null);
const drawerSalesCapability = ref(false)

const datas = reactive({
  tableid: '',
  param: {},
  need_header: false,
  need_init_load: false,
  form: {},
  columns: props.data.systemId == 206 ? ['task_name', "behavior"] : ['ability_name', "behavior"],
  template: props.data.systemId == 206 ? ['task_name', "behavior"] : ['ability_name', "behavior"],
  modal_type: "link",
  search_txt: "查询",
  show_btn_add: false,
  show_search: false,
  show_search_btn: true,
  show_link_column: false,
  show_pager: false,

});
const systemId = computed(() => {
  return props.data.systemId;
});

const showEditor = (data) => {
  drawerSalesCapability.value = true
  nextTick(() => {
    refDrawerSalesCapability.value.show(data);

  })
};

const cancel = () => {
  refDrawerSalesCapability && refDrawerSalesCapability.value.cancel();
  drawerSalesCapability.value = false
};

const onCallback = (type, data) => {
  console.log("onCallback3", type, data.length, data);
  if (type == "reload") {
    emit("update:data", data);
    tableData.value = data;
    init();
    emit("callback", type, data);

  }
};

const init = () => {
  refTable.value.update_data(tableData.value, tableData.value.length);
};

watch(
  () => props.data,
  (newVal) => {
    tableData.value = JSON.parse(newVal.content);
    init();
  }
);

onMounted(() => {
  tableData.value = JSON.parse(props.data.content || "[]");
  init();
});

defineExpose({
  datas,
  refTable,
  MyTable,
  onCallback,
  refDrawerSalesCapability,
  DrawerSalesCapability,
  showEditor,
  cancel,
});
</script>

<style lang="scss" scoped>
.sales-capability {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .table_wrap {
    padding: 6px;
  }
}
</style>
