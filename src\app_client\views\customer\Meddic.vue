<template>
    <div class="meddic-container" @click="onClick">
        <div v-for="(letter, index) in letters" :key="index" class="meddic-item" :class="[letter.status]">
            {{ letter.text }}
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
const props = defineProps(['row']);
const emit = defineEmits(['callback']);
const letters = ref([
    { text: 'M', status: 'inactive', type: "METRICS" },
    { text: 'E', status: 'inactive', type: "ECONOMIC_BUYER" },
    { text: 'D', status: 'inactive', type: "DECISION_CRITERIA" },
    { text: 'D', status: 'inactive', type: "DECISION_PROCESS" },
    { text: 'I', status: 'inactive', type: "IDENTIFY_PAIN" },
    { text: 'C', status: 'inactive', type: "CHAMPION" }
])

const statusMap = {
    'NOT_FILLED': 'inactive',
    'FULL': 'active',
    'INSUFFICIENT': 'light'
}

const updateLettersStatus = (meddicList) => {
    if (!meddicList) return;

    letters.value = letters.value.map(letter => {
        const item = meddicList.find(item => item.meddicType === letter.type);
        return {
            ...letter,
            status: item ? statusMap[item.meddicStatus] : 'inactive'
        };
    });
}

// 初始化时更新状态
if (props.row?.meddicList) {
    updateLettersStatus(props.row.meddicList);
}

const onClick = () => {
    emit("callback", props.row, "meddic");
};

watch(() => props.row?.meddicList, (newVal) => {
    updateLettersStatus(newVal);
}, { immediate: true })

defineExpose({
    letters
})
</script>

<style lang="scss" scoped>
.meddic-container {
    display: flex;
    gap: 8px;
    padding: 10px;
}

.meddic-item {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;

    // 深蓝色状态
    &.active {
        background-color: #4169E1;
        color: white;
    }

    // 白色状态
    &.inactive {
        background-color: white;
        color: #666;
        border: 1px solid #ddd;
    }

    // 浅蓝色状态
    &.light {
        background-color: #94B2FF;
        color: white;
    }
}
</style>