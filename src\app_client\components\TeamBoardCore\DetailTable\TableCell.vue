<template>
    <span :class="{ 'highlight-warning': isLowerThanOverview(scope, column) }" @click="handleViewDetail">
        {{ formattedValue }}
    </span>
</template>

<script setup>
import { categoryInfo, StatisColumnTypes, isLowerThanAvg, formatValue } from "../misc.js";
import { toPercent, jsOpenNewWindow, getUrlParam } from "@/js/utils.js";
const props = defineProps({
    scope: {
        type: Object,
        required: true
    },
    column: {
        type: String,
        required: true
    },
    overviewData: {
        type: Object,
        required: true
    },
    valueType: {
        type: String,
        default: 'normal' // 可以是 'normal', 'percent', 'time'
    }
});

const handleViewDetail = () => {
    const category = StatisColumnTypes.find(x => x.field === props.column).category;
    const pageUrl = categoryInfo[category].url
    const splitor = pageUrl.includes('?') ? '&' : '?';
    const deptId = props.scope.row.deptId;
    let ssoUserIdStr = '';
    if (props.scope.row?.ssoUserId) {
        const { ssoUserId, userName } = props.scope.row;
        ssoUserIdStr = `&ssoUser=${ssoUserId},${userName}`;
    }
    const periodType = g.clientBoardStore.periodType.replace('ly', '');
    const startDate = getUrlParam('startDate');
    const endDate = getUrlParam('endDate');
    let url = `${g.config.publicPath}/#${pageUrl}${splitor}periodType=${periodType}&deptId=${deptId}${ssoUserIdStr}`;
    if (startDate && endDate) {
        url += `&startDate=${startDate}&endDate=${endDate}`;
    }
    jsOpenNewWindow(url)
}

const formattedValue = computed(() => {
    const value = props.scope.row[props.column];
    if (props.valueType === 'percent') {
        return toPercent(value);
    }
    const round = props.valueType === 'average' ? 2 : 0
    return formatValue(value, round);
});

const isLowerThanOverview = (scope, field) => {
    if (!scope || !scope.row || Object.keys(scope.row).length === 0) {
        return false;
    }
    return isLowerThanAvg(props.overviewData, scope.row, field);
};
</script>

<style lang="scss"  scoped>
.highlight-warning {
    color: #f56c6c;
    display: block;
    background-color: #fef0f0;
    height: 31px;
    margin: 0;
    padding: 7px 8px 0 8px;
    text-align: center;
}
</style>