<template>
  <div class="overview">
    <template v-if="shouldShowTimeBox(topicLabResult)">
      <div class="header">
        <switchShowTopic ref="refShowTopic" @callback="cbShowTopic"></switchShowTopic>
        <div class="blank"></div>
        <div class="oss_icon" v-if="!isEdit">
          <el-icon @click="onCopy" class="copy-icon">
            <CopyIcon />
          </el-icon>
          <el-icon @click="onEdit" class="edit-icon" v-if="isHost && enable_edit">
            <EditIcon />
          </el-icon>
        </div>
        <div class="edit-actions" v-else>
          <span class="action-btn" @click="onCancelEdit">取消</span>
          <span class="action-btn confirm" @click="onConfirmEdit">完成编辑</span>
        </div>
      </div>
      <ul :style="ov_style" :class="'topic ' + (hasBt ? 'has_bt' : '')">
        <li v-for="(item, index) in topicLabResult" :key="item.bt + item.value"
          :class="currBt === item.bt ? 'active ' : ''">
          <div class="ov_main">
            <div class="time" @click="onMouseEnter(true, item)"> {{ topicTime(item.bt) }}</div>
            <div-edit v-model="item.value" @onclick="clickLi(item, 'topicLabResult')"
              @callback="(e) => onCbTopic(item, e)" :edit="isEdit" :auto-focus="isEdit && index === 0">
            </div-edit>
            <div class="flexgrow"></div>
          </div>
          <div class="ov_summary" v-if="ovDetailShows.includes(item.time)">
            <div-edit v-model="item.summary" @callback="(e) => onCbTopicSummary(item, e)" :edit="isEdit">
            </div-edit>
          </div>
        </li>
      </ul>
    </template>
    <el-empty v-else>
      <template #text class="description">暂无数据</template>
    </el-empty>
  </div>
</template>
<script>
import folding from '@/app_postmeet/components/folding.vue'
import DivEdit from "@/app_postmeet/components/div_edit.vue"
import ArrowUp from "@/icons/arrow_up.vue"
import ArrowDown from "@/icons/arrow_down.vue"
import switchShowTopic from './switchShowTopic.vue'
import { fillMarkArray, getTopicTime } from '@/app_postmeet/tools/tools'
import CopyIcon from "@/icons/copy.vue"
import EditIcon from "@/icons/edit.vue"
const defaultHeight = 'auto'

export default {
  components: { folding, DivEdit, ArrowUp, ArrowDown, switchShowTopic, CopyIcon, EditIcon },
  name: 'Overview',
  data() {
    return {
      currBt: -1,
      hasBt: false,
      startTime: '',
      playItem: {},
      isHost: false,
      topicLabResult: [],
      hasAnyResult: false,
      enable_edit: false,
      ov_style: {},
      isEdit: false,
      ovDetailShows: []
    }
  },
  methods: {
    topicTime(bt) {
      return getTopicTime(this.startTime, bt, this.playItem.startTime)
    },
    shouldShowTimeBox(items) {
      return items.length > 0;
    },
    onCbTopicSummary(item, newValue) {
      item.summary = newValue
      console.log('onCbTopicSummary', item, newValue)
    },
    onCbTopic(item, newValue) {
      item.value = newValue
      console.log('onCbTopic', item, newValue)
    },
    init() {
      this.playItem = g.postmeetStore.getCurrRecord()
      const { topicLabResult } = g.postmeetStore.data
      this.startTime = this.playItem.startTime
      this.topicLabResult = topicLabResult
      this.hasAnyResult = this.topicLabResult.length > 0;
      this.isHost = !g.postmeetStore.isReadonly();
      if (this.topicLabResult.length > 6) {
        this.ov_style['height'] = defaultHeight
      }
      if (this.hasAnyResult) {
        this.checkBt(this.topicLabResult)
      }
      this.enable_edit = g.postmeetStore.data.enable_edit
    },
    checkBt(lists) {
      if (!this.hasBt && lists.length > 0) {
        if (lists[0].hasOwnProperty('bt')) {
          this.hasBt = true
        }
      }
    },
    cbShow(sname, status) {
      if (!status) {
        this[sname] = { overflow: 'hidden', height: defaultHeight }
      } else {
        this[sname] = {}
      }
    },
    sendVideoTags(item, need_set_time) {
      if (item) {
        const nbs = fillMarkArray(item.bt, item.et, this.playItem.duration)
        const items = []
        for (let n of nbs) {
          items.push({ bt: n, et: n + 1000, pd: 0, txt: '-' })
        }
        g.emitter.emit('videoTags', [item.value, items, need_set_time])
      }
    },
    onMouseEnter(status, item) {
      if (status) {
        this.sendVideoTags(item, true)
      } else if (JSON.stringify(this.clickedItem) != "{}") {
        this.sendVideoTags(this.clickedItem, true)
      }
    },
    cbShowTopic(isshow) {
      if (isshow) {
        this.ovDetailShows = this.topicLabResult.map(x => x.time)
        this.$refs.refTopicLab && this.$refs.refTopicLab.onClick(true)
      } else {
        this.ovDetailShows = []
      }
    },
    clickLi(item, type) {
      this.$emit('callback', 'clickLi', { item, type })
    },
    onCopy() {
      const content = this.topicLabResult
        .filter(item => this.shouldShowTime(item))
        .map(item => {
          const time = this.topicTime(item.bt);
          const summary = item.summary ? `\n摘要：${item.summary}` : '';
          return `时间：${time}\n主题：${item.value}${summary}\n`;
        })
        .join('\n');

      g.appStore.doCopy(content, '已复制');
    },
    onEdit() {
      this.isEdit = true
    },
    onCancelEdit() {
      this.isEdit = false
      this.init() // 重新加载数据，取消编辑的更改
    },
    onConfirmEdit() {
      this.isEdit = false
      // {
      // 		"bt":30,
      // 		"et":137100,
      // 		"id":1,
      // 		"pd":0,
      // 		"summary":"一名身穿白袍的美人跟随200凉州铁骑进城，她询问一位算卦老人被铁骑护送的人的身份，得知此人是北凉王的长子，世子殿下。美人为此感到惊讶，自言自语表达对这位公子哥的疑惑和评价。",
      // 		"value":"雪中悍刀行：白袍美人追查北凉铁骑护送的公子哥"
      // 	},
      g.postmeetStore.data.topicLabResult = this.topicLabResult
      const data = this.topicLabResult.map(item => {
        return {
          bt: item.bt,
          et: item.et,
          id: item.id,
          pd: item.pd,
          summary: item.summary,
          value: item.value
        }
      })
      g.emitter.emit('setMdTypeEditorValue', { type: 'SEGMENTED_SUMMARY', md: data });
    }
  }
}
</script>

<style lang='scss'>
@use './style.scss';
;

.overview {
  .header {
    .oss_icon {
      .el-icon {
        cursor: pointer;
        font-size: 14px;
        color: #595959;
        margin-right: 10px;
        margin-top: 15px;
      }
    }

    .edit-actions {
      display: flex;
      gap: 12px;
      margin-right: 10px;

      .action-btn {
        cursor: pointer;
        font-size: 14px;
        color: #436BFF;

        &.confirm {}

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>