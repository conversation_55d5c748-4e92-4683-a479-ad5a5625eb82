import { join, dirname, basename } from 'path'
import { exec } from 'child_process'
import { app, shell } from 'electron'
import { unlink, rename, stat } from 'fs/promises'
import { errorWriteLocal } from './errorWriteLocal'
import { uploadRecordFile } from './uploadRecordFile'

interface FfmpegConfig {
    cmd: string;
    tempOutputFileName: string;
    finalOutputFileName: string;
}

// 获取ffmpeg运行配置
const getFfmpegConfig = (videoPath: string, isVideo: boolean): FfmpegConfig => {
    let ffmpegPath: string
    if (process.env.NODE_ENV === 'development') {
        ffmpegPath = join(app.getAppPath(), `build/extraResources/${process.platform === 'win32' ? 'win/ffmpeg.exe' : 'mac/ffmpeg'}`)
    } else {

        if (process.platform === 'win32') {
            ffmpegPath = join(dirname(app.getPath('exe')), `resources/extraResources/win/ffmpeg.exe`)
        } else {
            ffmpegPath = join(dirname(app.getPath('exe')), `../Resources/build/extraResources/mac/ffmpeg`)
        }
    }

    // errorWriteLocal('ffmpegPath', ffmpegPath)

    const inputFileName = basename(videoPath);

    const pre = 'novaguide_'

    let tempOutputFileName = pre + inputFileName
    let finalOutputFileName = inputFileName
    let cmd: string

    //ffmpeg -y -i "1.mp4" -c copy "output.mp4"
    //ffmpeg -y -i input.mp4 -vn -c:a copy output.m4a
    if (isVideo) {
        finalOutputFileName = inputFileName
        cmd = `"${ffmpegPath}" -y -i "${inputFileName}" -c copy "${tempOutputFileName}"`
    } else {
        finalOutputFileName = inputFileName.replace(pre, '').replace('.mp4', '.m4a')
        cmd = `"${ffmpegPath}" -y -i "${inputFileName}" -vn -c:a copy "${tempOutputFileName}"`
    }

    return {
        cmd,
        tempOutputFileName,
        finalOutputFileName
    }
}

// 处理录制视频
export const ffmpegFixDuration = async (videoPath: string, isVideo: boolean, confId: string = '') => {
    try {
        errorWriteLocal("process-recording videoPath", videoPath, confId)

        // 添加文件大小检查
        const stats = await stat(videoPath)
        if (stats.size === 0) {
            errorWriteLocal("process-recording skip: file size is 0")
            await unlink(videoPath) // 删除空文件
            return null
        }

        const videoDir = dirname(videoPath)
        const { cmd, tempOutputFileName, finalOutputFileName } = getFfmpegConfig(videoPath, isVideo)

        return new Promise((resolve, reject) => {
            const options = {
                cwd: videoDir
            }

            exec(cmd, options, async (error, stdout, stderr) => {
                if (error) {
                    errorWriteLocal("process-recording error:", error)
                    errorWriteLocal("process-recording stderr:", stderr)
                    reject(error)
                    return
                }

                if (stdout) {
                    errorWriteLocal("process-recording stdout:", stdout)
                }

                try {
                    if (!isVideo) {
                        // 对于音频文件，将临时mp4重命名为mp3
                        await rename(
                            join(videoDir, tempOutputFileName),
                            join(videoDir, finalOutputFileName)
                        )
                    }

                    const outputPath = join(videoDir, finalOutputFileName)
                    errorWriteLocal("process-recording output path:", outputPath)

                    // 删除原文件
                    await unlink(videoPath)

                    if (confId) {
                        // 上传录制文件
                        await uploadRecordFile(confId, outputPath)
                    }

                    // 打开目录
                    shell.openPath(videoDir)

                    errorWriteLocal("process-recording success:", videoDir)
                    resolve(outputPath)
                } catch (err) {
                    errorWriteLocal('ffmpeg process error1:', err)
                    reject(err)
                }
            })
        })
    } catch (error) {
        errorWriteLocal('ffmpeg process error2:', error)
        throw error
    }
}
