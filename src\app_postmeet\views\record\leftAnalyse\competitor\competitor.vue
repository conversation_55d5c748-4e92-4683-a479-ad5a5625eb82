<template>
  <div class="la_competitor_wrap">
    <laComMenu @callback="cbMenu" />
    <div class="ri_main" v-if="tag">
      <div class="ri_t1" v-show="getTagValue('客户关注点', false)">客户关注点</div>
      <ol v-show="getTagValue('客户关注点', false)">
        <li>
          <div class="ri_t1">主要关注方面</div>
          <ul v-if="tag['competitorReport']" class="la_competitor_ul">
            <li v-for="(value, key) in getCustomerFocusPoints('主要关注方面')">
              {{ value[1] }}
            </li>
          </ul>
        </li>
        <li>
          <div class="ri_t1">潜在需求或痛点</div>
          <ul v-if="tag['competitorReport']" class="la_competitor_ul">
            <li v-for="(value, key) in getCustomerFocusPoints('潜在需求或痛点')">
              {{ value[1] }}
            </li>
          </ul>
        </li>
      </ol>
      <!-- <div class="ri_t1" v-show="getTagValue('竞争对手分析', false)">
                竞争对手分析
            </div> -->
      <MdViewer :md="getTagValue('竞争对手分析', '')" />
      <div class="ri_t1" v-show="getTagValue('应对策略建议', []).length > 0">
        应对策略建议
      </div>
      <ol v-if="tag['competitorReport']" class="creport">
        <li v-for="value in getTagValue('应对策略建议', [])">
          {{ formatListItem(value) }}
        </li>
      </ol>
    </div>
    <el-empty description="未提及竞争对手" v-else> </el-empty>
  </div>
</template>
<script>
import laComMenu from "./laComMenu.vue";
import MdViewer from "@/components/Markdown/MdViewer.vue"

export default {
  components: {
    laComMenu,
    MdViewer,
  },
  data() {
    return {
      tag: {},
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    init() {
      const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
      if (ar) {
        this.list = ar.find((x) => x.systemId == 102).report;
      }
    },
    handleClick() {
      this.$emit("callback", this.activeName);
    },
    cbMenu(tag) {
      this.tag = (tag && tag.data) || "";
    },
    getTagValue(tag, def_value) {
      return this.tag?.competitorReport?.[tag] || def_value;
    },
    getCustomerFocusPoints(category) {
      return Object.entries(this.tag?.competitorReport?.["客户关注点"]?.[category] || {});
    },
    formatListItem(value) {
      return value.replace(/^\d+\.\s*/, "").trim();
    },
  },
};
</script>

<style lang="scss">
.la_competitor_wrap {
  .ri_main {
    width: 94%;
    background: #f9fafc;
    border-radius: 8px;
    padding: 20px;

    .la_competitor_ul {
      margin-left: 24px;
      list-style-type: decimal;
      li {
        list-style-type: decimal;
      }
    }

    .creport {
      li {
        margin: 8px 0;

        .cr_title {
          font-weight: 600;
        }
      }
    }

    .ri_t1 {
      font-weight: 600;
      margin: 12px 0;
    }
  }
}
</style>
