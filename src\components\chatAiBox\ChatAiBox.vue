<template>
  <div class="chat_wrap common_chat_wrap">
    <div class="back_pic1"></div>
    <div class="back_pic2"></div>
    <div class="center_box">
      <div class="msgs_list">
        <component :is="msg.my ? MsgMy : MsgAi" :data="msg" v-for="msg in msgs">
        </component>
        <div class="btn_re_answer" v-if="!isAnswering && msgs.length > 1">
          <BtnReAnswer ref="refBtnReAnswer" @callback="onReAnswer" />
        </div>
      </div>
      <ChatInput ref="refInput" @callback="cbInput" />
    </div>
  </div>
</template>

<script setup>
import { initConversation, askChatGpt, abortRequest } from "@/js/streamRequest.js";
import MsgMy from "@/components/chatCoze/msg_my.vue";
import MsgAi from "@/components/chatCoze/msg_ai.vue";
import ChatInput from "@/components/chatCoze/chatInput/chatInput.vue";
import BtnReAnswer from "@/components/chatCoze/BtnReAnswer.vue";

const msgs = ref([]);
const isAnswering = ref(false);
const refBtnReAnswer = ref();
const refInput = ref();

let myImg = "";
// let key_xwc_chats = ''
let new_msgs = [];
let updateing = false;
let app_config = {};
let query_param = {};
let chatListDom;
let lastQuestion = "";

const cbInput = (action, data) => {
  if (action === "stop_answer") {
    abortRequest();
    const lastMsg = msgs.value[msgs.value.length - 1].message;
    if (lastMsg == "") {
      msgs.value[msgs.value.length - 1].message = "已停止回答";
    }
  } else if (action == "clear") {
    msgs.value = [];
    if (app_config.first_param) {
      askChatGpt(app_config.first_param).then((resp) => {
        query_param = app_config.first_param;
      });
    } else {
      addHelloMsg();
    }

    // localStorage.setItem(key_xwc_chats, "[]")
  } else if (action == "send") {
    msgs.value.push({
      my: true,
      photo: myImg,
      message: data,
    });
    msgs.value.push({
      my: false,
      message: ``,
    });
    query_param["modelUserContent"] = data;
    isAnswering.value = true;
    askChatGpt(query_param).then((resp) => {
      isAnswering.value = false;
      refInput.value.setIsAnswering(false);
    });
    autoScroll();
  }
};

const onReAnswer = () => {
  if (lastQuestion == "") {
    if (msgs.value.length > 1) {
      lastQuestion = msgs.value[msgs.value.length - 2].message.replace("\n", "");
    }
  }
  refInput.value.setQuestion(lastQuestion);
  refInput.value.onSend();
};

const autoScroll = () => {
  nextTick(() => {
    chatListDom.scrollTop = chatListDom.scrollHeight;
  });
};

const keyEnter = (event, ctrl) => {
  onSend();
};

const updateMsg = () => {
  if (updateing) {
    return;
  }
  const animateText = (line) => {
    return new Promise((resove) => {
      let currentIndex = 0;
      const interval = setInterval(() => {
        if (msgs.value.length == 0) {
          return;
        }
        msgs.value[msgs.value.length - 1].message += line.slice(
          currentIndex,
          currentIndex + 1
        );
        currentIndex++;
        if (currentIndex >= line.length) {
          clearInterval(interval);
          resove();
        }
      }, 30);
    });
  };

  const fn = async () => {
    while (new_msgs.length > 0) {
      updateing = true;
      await animateText(new_msgs.shift());
      autoScroll();
      if (new_msgs.length == 0) {
        updateing = false;
        setTimeout(() => {
          updateMsg();
        }, 200);
        // localStorage.setItem(key_xwc_chats, JSON.stringify(msgs.value))
      }
    }
  };
  fn();
};

const addHelloMsg = () => {
  msgs.value.push({
    my: false,
    message: app_config.hello_txt,
  });
};
const init = (cfg) => {
  const { photo } = g.appStore.user;
  app_config = g.clientStore.getAppConfig(cfg.appid);
  if (app_config.title) {
    window.document.title = app_config.title;
    // key_xwc_chats = `xwc_chats_${cfg.appid}`;
    // let savemsg = JSON.parse(localStorage.getItem(key_xwc_chats) || '[]')
    let savemsg = [];
    if (app_config.first_param) {
      initConversation();
      askChatGpt(app_config.first_param).then((resp) => {
        query_param = app_config.first_param;
      });
    } else if (savemsg.length > 0) {
      const isOldFormat = !!savemsg[0].txt;
      if (isOldFormat) {
        // localStorage.removeItem(key_xwc_chats)
        savemsg = [];
      }
    }
    msgs.value = savemsg;
    myImg = photo || "";
    refInput.value.init({ show_clear: true, show_stop: true });
    if (msgs.value.length == 0) {
      addHelloMsg();
    }

    chatListDom = document.getElementsByClassName("msgs_list")[0];
    autoScroll();
  } else {
    refInput.value.setDisableApp(true);
    g.emitter.emit("show_msg_box", {
      message: "无权限访问此应用",
      time: 3,
      need_icon: true,
    });
  }
};

onMounted(() => {
  initConversation();
  g.emitter.on("gpt_resp", (data) => {
    new_msgs.push(data);
    updateMsg();
  });
});

onUnmounted(() => {
  g.emitter.off("gpt_resp");
});

defineExpose({
  init,
  refBtnReAnswer,
  msgs,
  MsgMy,
  MsgAi,
  cbInput,
  keyEnter,
  onReAnswer,
  BtnReAnswer,
});
</script>

<style lang="scss">
@import url("../chatCoze/chat.scss");
</style>
