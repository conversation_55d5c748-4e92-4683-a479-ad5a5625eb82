<template>
    <div class="sale_right_list_wrap">
        <el-tabs v-model="rdValue" class="rd_header" @tab-change="onMenuType" v-loading="loading">
            <el-tab-pane label="沟通模板" name="communication" v-if="props.type === 'before_visit'">
                <DimsComm ref="refDimComm" @callback="cbFunc" />
            </el-tab-pane>
            <el-tab-pane label="总结" name="SUMMARY" v-if="props.type === 'after_visit'">
                <DimsSummary ref="refDimSummary" dtype="SUMMARY" @callback="cbFunc" />
            </el-tab-pane>
            <el-tab-pane label="分析" name="ANALYSIS" v-if="props.type === 'after_visit' && currData.customer">
                <DimsSummary ref="refDimAnalyse" dtype="ANALYSIS" @callback="cbFunc" />
            </el-tab-pane>
            <el-tab-pane label="辅导" name="COUNSELLING" v-if="props.type === 'after_visit' && currData.customer">
                <DimsSummary ref="refDimCouns" dtype="COUNSELLING" @callback="cbFunc" />
            </el-tab-pane>
        </el-tabs>
    </div>
    <sortDimemsion ref="refSortDimemsion" @callback="cbFunc" />
    <StandardDialog ref="refStandardDialog" @callback="cbFunc" />
</template>

<script setup>

import sortDimemsion from "./sortDimemsion.vue";
import DimsComm from "./DimsComm";
import DimsSummary from "./DimsSummary";
import StandardDialog from "./StandardDialog.vue";
const props = defineProps(['type']);
const emit = defineEmits(['callback']);
const currData = ref({});
const rdValue = ref(props.type === 'before_visit' ? 'communication' : 'SUMMARY')
const refDimComm = ref()
const refSortDimemsion = ref()
const refStandardDialog = ref()
const refDimSummary = ref()
const refDimAnalyse = ref()
const refDimCouns = ref()
// dimensionType: 维度类型, SUMMARY: 总结 ANALYSIS:分析 COUNSELLING:辅导
const loading = ref(false);

const onMenuType = () => {
    g.saleStore.setDimensionType(rdValue.value)
    nextTick(() => {
        switch (rdValue.value) {
            case "communication":
                refDimComm.value.init();
                break;
            case "SUMMARY":
                refDimSummary.value && refDimSummary.value.init();
                break;
            case "ANALYSIS":
                refDimAnalyse.value.init();
                break;
            case "COUNSELLING":
                refDimCouns.value.init();
                break;
        }
    })
}


const cbFunc = (action, data) => {
    switch (action) {
        case "sort":
            for (let i = 0; i < data.length; i++) {
                data[i]['order'] = i + 1;
            }
            refSortDimemsion.value.show(currData.value.id, rdValue.value, data)
            break;
        case "reload":
            query()
            break;
        case "edit_standard":
            refStandardDialog.value.show(data)
            break;
    }
}


const query = () => {
    loading.value = true;
    g.saleStore.getTopDetail().then(data => {
        onMenuType()
    }).finally(() => {
        loading.value = false;
    })
}

const init = (item) => {
    currData.value = item;
    g.saleStore.setXmItem(item)
    query()
}

defineExpose({
    init, cbFunc, sortDimemsion, DimsComm, rdValue, onMenuType,
    refDimComm, DimsSummary, refDimSummary, refSortDimemsion,
    refDimAnalyse, refDimCouns, StandardDialog, refStandardDialog
})

</script>

<style lang="scss">
.sale_right_list_wrap {
    padding: 0;
    height: calc(100vh - 76px);
    overflow-y: auto;
    border-left: 1px solid #E9E9E9;

    .rd_header {
        font-weight: 500;
        font-size: 16px;
        color: #262626;
        line-height: 26px;
    }
}
</style>