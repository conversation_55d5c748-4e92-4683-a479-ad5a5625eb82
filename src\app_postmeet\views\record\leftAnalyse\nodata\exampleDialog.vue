<template>
  <el-dialog
    title="生成内容示意图"
    v-model="isShow"
    width="624px"
    :append-to-body="true"
    :modal-append-to-body="false"
    class="analyse_example_wrap"
  >
    <div class="cd_main">
      <img :src="getOssUrl('analyse_pic.png')" />
    </div>
    <template #footer class="dialog-footer">
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { getOssUrl } from "@/js/utils";

const isShow = ref(false);

const show = () => {
  isShow.value = true;
};

const onCancel = () => {
  isShow.value = false;
};

const onConfirm = () => {
  isShow.value = false;
};

defineExpose({
  show,
});
</script>

<style lang="scss">
.analyse_example_wrap {
  .cd_main {
    img {
      width: 576px;
    }
  }
}
</style>
