<template>
    <div class="stop_btn" @click="onStopAnswer">
        <div class="sb_left"> </div>
        <div class="sb_right">
            停止回答
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits(['callback'])

const onStopAnswer = () => {
    emit('callback', 'stop_answer')
}

defineExpose({ onStopAnswer })

</script>

<style lang="scss">
.stop_btn {
    width: 100px;
    height: 32px;
    border-radius: 16px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    cursor: pointer;

    .sb_left {
        width: 10px;
        height: 10px;
        background: #436BFF;
        border-radius: 2px;
        margin-right: 6px;
    }

    .sb_right {
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 22px;
    }
}
</style>