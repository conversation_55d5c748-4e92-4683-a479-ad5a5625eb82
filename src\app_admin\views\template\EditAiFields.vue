<template>
    <el-drawer v-model="is_show" direction="rtl" class="edit_ai_field_wrap">
        <template #header>
            <div class="vd_title">
                添加字段
            </div>
        </template>
        <template #default>
            <el-form ref="refForm" :model="form" label-width="auto" label-position="top" size="default" :rules="rules">
                <el-form-item label="字段名称" prop="label">
                    <el-input v-model="form.label" type="text" maxlength="20" />
                </el-form-item>
                <el-form-item label="字段类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择" style="width: 240px">
                        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="字段长度" prop="length">
                    <el-input-number v-model="form.length" :min="1" :max="200" controls-position="right" />
                </el-form-item>
                <el-form-item label="提示文案" prop="reminder">
                    <el-input v-model="form.reminder" type="text" />
                </el-form-item>
                <el-form-item label="字段必填" prop="required">
                    <el-radio-group v-model="form.required">
                        <el-radio :value="true">必填</el-radio>
                        <el-radio :value="false">选填</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">保存</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { createTemplateField, updateTemplateField } from "@/app_admin/tools/api.js"
import { apiHintWrap } from "@/app_admin/tools/utils.js"
import { useRoute } from 'vue-router'

const route = useRoute();
const is_show = ref(false)
const title = ref('')
const form = ref({});
const mode = ref('')
const refForm = ref()
const emit = defineEmits(['callback'])

const templateId = route.params.id;

const typeOptions = ref([
    {
        value: 'INPUT',
        label: '单行文本',
    },
    {
        value: 'MULTI_INPUT',
        label: '多行文本',
    }
])

const onCancel = () => {
    is_show.value = false;
}

const show_edit = (e) => {
    mode.value = 'edit'
    form.value = e;
    is_show.value = true;
}

const show_add = () => {
    mode.value = 'add'
    form.value = { required: true };
    is_show.value = true;
}


const rules = reactive({
    label: [
        { required: true, message: '请输入字段名称', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请输入应用场景', trigger: 'blur' },
    ],
    length: [
        { required: true, message: '请输入字段长度', trigger: 'blur' },
    ],
    reminder: [
        { required: true, message: '请输入提示文案', trigger: 'blur' },
    ],
})

const _add = (param) => {
    apiHintWrap(createTemplateField(templateId, param), '添加').then(({ status, resp }) => {
        if (status) {
            emit('callback', 'reload')
            is_show.value = false;
        }
    })
}

const _update = (param) => {
    const fieldId = param.id;
    apiHintWrap(updateTemplateField(templateId, fieldId, param), '更新').then(({ status, resp }) => {
        if (status) {
            emit('callback', 'reload')
            is_show.value = false;
        }
    })

}

const onConfirm = () => {
    if (!refForm.value) return;
    refForm.value.validate((valid, fields) => {
        if (valid) {
            const raw = toRaw(form.value);
            mode.value == 'add' ? _add(raw) : _update(raw);
        }
    })
}

defineExpose({
    refForm, is_show, form, title, mode, typeOptions, rules,
    show_edit, show_add, onCancel, onConfirm,
})

</script>

<style lang="scss">
.edit_ai_field_wrap {
    .vd_main {
        width: 480px;
        margin-left: 26px;
        overflow-y: auto;

        .r_title {
            height: 22px;
            font-size: 14px;
            color: #262626;
            line-height: 22px;
            margin: 16px 0 8px 0;
        }
    }
}
</style>