<template>
    <div :class="`group_9 flex-col ${props.choosed == 3 ? 'active' : ''}`" @click="onClick(3)">
        <span class="text_30">发言人总结</span>
        <dashline />
        <div class="marginl7">
            <page :id="3" />
            <div class="image-wrapper_1 flex-row" v-if="props.choosed">
                <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
            </div>
        </div>
    </div>
</template>

<script setup>
import dashline from './dashline.vue';
import page from '../pages/page.vue';
import { r_png } from './image.js';
const props = defineProps(['choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])

const onClick = (value) => {
    if (props.enableChoose) {
        emit('update:choosed', value)
    }
}
defineExpose({ onClick, page, dashline })
</script>
