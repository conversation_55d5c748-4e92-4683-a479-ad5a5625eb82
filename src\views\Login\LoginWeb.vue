<template>
    <div class='login flex-col'>
        <div class="login_bk" :style="bk_style">
        </div>
        <div class="login_main flex-row">
            <div class="login_left flex-col">
                <img :src="logo_svg" alt="logo" class="login_left_logo">
                <div class="login_left_title_desc">
                    Voice driven Intelligence, sales-led future.
                </div>
                <div class="login_left_title">
                    声启智慧，销领未来
                </div>
                <div class="login_left_desc">
                    每次拜访都是进步，每个客户都是机遇
                </div>
            </div>
            <div class="login_right">
                <div class="login_form_box">
                    <Login productCode="nova_guide" factorCode="nova_guide" :defaultLoginType="1"
                        @targetOrgInfo="onTargetOrgInfo" :apiConfig="comCfg.ssoApiBase" :deviceType="1"
                        :defaultSSODomain="comCfg.defaultSSODomain" title="欢迎使用绚星销售助手">
                    </Login>
                </div>
            </div>
        </div>
        <div class="login_footer">
            <div class="login_footer_text">
                Copyright©{{ new Date().getFullYear() }} x-mate.com All rights reserved 江苏绚星智慧科技有限公司 版权所有 <a
                    href="https://beian.miit.gov.cn/" target="_blank" class="icp_link">苏ICP备14034064号-103</a>
                <a href="#/download" target="_blank" class="download_link">应用下载</a>
            </div>
        </div>
    </div>
</template>

<script setup>
import Login from "./LoginWebCore.vue"
import { getOssUrl, isPC } from '@/js/utils';
import { onMounted } from "vue";

const comCfg = ref({});
const logo_svg = ref(getOssUrl('logo2.svg'));
const bk_style = ref({ background: `url('${getOssUrl('login_bg.png')}') no-repeat center center` });

const onTargetOrgInfo = (res) => {
    console.log('onTargetOrgInfo', res);
    // {origin: 'pro-phx-ucstable.yunxuetang.com.cn', cluster: 'tf-ali', orgId: '8eee1504-1ded-4b63-a89e-ba9031135015', loginType: 1, code: '70febde9-6c46-4785-99f7-0f04d346f396'}
    // {origin: '机构域名', cluster: '平台集群', orgId: '机构id', loginType: '1:手机； 2:邮箱； 3:sso', code: '登录code，只有手机号、邮箱登录才有'}
    let url = ''
    let ssoReturnUrl = g.config.ssoReturnUrl
    if (res.loginType == 3) {
        url = `https://${res.origin}/login.html?platform=1&redirectUrl=${ssoReturnUrl}&productCode=${g.config.productCode}`;
    } else {
        url = `${ssoReturnUrl}?domain=${res.origin}&code=${res.code}&e=${res.cluster}`
    }
    g.appStore.setStore('orgInfo', res)
    console.log('url', url);
    window.open(url, '_self')
}


onMounted(() => {
    if (!isPC()) {
        g.appStore.toMobileDownload()
    }
    if (g.appStore.isLogin) {
        g.router.push({ path: g.appStore.home_page })
    } else {
        const _cfg = { ...g.config }
        const host = location.host;
        // meet?.yxt.com 是固定域名，不参与动态域名逻辑
        if ((host.indexOf('127.0.0.1') == -1 && host.indexOf('localhost') == -1) && host.indexOf('x-mate.com') == -1 && host.indexOf('yxt.com') == -1) {
            _cfg['defaultSSODomain'] = location.host
        }
        comCfg.value = _cfg
    }
})

defineExpose({
    Login, comCfg, onTargetOrgInfo
})

</script>

<style scoped lang='scss'>
.login {
    height: 100%;
    min-height: 830px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(180deg, #F0F5FF 0%, #E6EFFF 100%);
    display: flex;

    .login_bk {
        position: fixed;
        left: 0;
        bottom: 0;
        z-index: 1;
        width: 1229px;
        height: 286px;
        background-size: cover;
    }

    .login_main {
        .login_left {
            width: 662px;
            height: 100%;
            z-index: 2;
            padding: 60px 0 60px 60px;

            .login_left_logo {
                width: 246.2px;
                height: 40px;
            }

            .login_left_title_desc {
                height: 28px;
                font-weight: 300;
                font-size: 20px;
                color: #757575;
                margin-top: 20%;
            }

            .login_left_title {
                height: 84px;
                font-weight: 600;
                font-size: 60px;
                color: #262626;
                line-height: 84px;
                margin-top: 8px;
            }

            .login_left_desc {
                height: 32px;
                font-size: 24px;
                color: #595959;
                line-height: 32px;
                margin-top: 36px;
            }
        }


        .login_right {
            width: calc(100% - 662px);
            height: 100%;
            z-index: 2;
            display: flex;
            justify-content: center;
            padding: 60px 60px 60px 0;

            .login_form_box {
                background-color: #fff;
                margin-top: 80px;
                padding: 24px;
                width: 444px;
                height: 550px;

                box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                border: 1px solid #DEE0E3;
                cursor: pointer;
                font-family: Avenir, Helvetica, Arial, sans-serif;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-align: center;
                color: #2c3e50;
                margin-top: 60px;
            }
        }
    }

    .login_footer {
        width: 100%;
        text-align: center;
        z-index: 2;
        bottom: 24px;
        position: absolute;
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #595959;
        line-height: 20px;
        font-style: normal;

        .icp_link {
            color: #595959;
        }

        .download_link {
            color: #436BFF;
            margin-left: 12px;
        }
    }
}
</style>