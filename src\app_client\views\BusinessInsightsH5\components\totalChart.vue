<template>
    <div class="competitiveRadar_total_chart" v-loading="loading">
        <p class="title">{{ props.setting.title }}</p>
        <div class="chart-container" v-if="data.length" ref="chartDom"></div>
        <el-empty v-else style="margin: 0 auto;" :image="getOssUrl('no-data.png', 3)" description="暂无数据" />
        <slot v-if="data.length" name="bottom"></slot>
    </div>
</template>

<script setup lang="js">
import echarts from '@/js/echarts'
import { checkDomReady } from "@/app_client/tools/utils.js"
import { getOssUrl } from "@/js/utils.js";

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    metrics: {
        type: Array,
        default: () => []
    },
    setting: {
        type: Object,
        default: () => ({
        })
    },
    loading: {
        type: <PERSON>olean,
        default: false
    }
})
const chart = ref(null)
const chartDom = ref(null)

// 计算合适的x轴最大值
const calculateMaxValue = (data) => {
    if (!data || data.length === 0) return 100

    // 计算所有数据中的最大值
    const maxValue = Math.max(...data.map(item =>
        props.metrics.reduce((sum, metric) => sum + (item[metric.key] || 0), 0)
    ))

    // 取最大值的整10倍数
    return Math.ceil(maxValue / 10) * 10
}

// 初始化图表
const initChart = () => {
    if (chart.value) {
        chart.value.dispose()
        chart.value = null
    }
    if (props.data.length === 0 || !checkDomReady(chartDom)) return
    chart.value = markRaw(echarts.init(chartDom.value))
    const categories = props.data.map(item => item.name)
    // 预处理数据，找出每个平台最后一个非零值的索引
    const lastNonZeroIndices = props.data.map(platformData => {
        let lastIndex = -1;
        for (let i = props.metrics.length - 1; i >= 0; i--) {
            if (platformData[props.metrics[i].key] > 0) {
                lastIndex = i;
                break;
            }
        }
        return lastIndex;
    });
    const seriesData = props.metrics.map((metric, idx) => ({
        name: metric.name,
        type: 'bar',
        stack: 'total',
        barWidth: 12,
        itemStyle: {
            borderRadius: [0, 0, 0, 0],
            color: metric.color
        },
        label: { show: false },
        data: props.data.map((item, dataIndex) => ({
            value: item[metric.key],
            itemStyle: {
                borderRadius: lastNonZeroIndices[dataIndex] === idx ? [0, 2, 2, 0] : [0, 0, 0, 0]
            }
        }))
    }))

    const maxValue = calculateMaxValue(props.data)
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#eee',
            borderWidth: 1,
            textStyle: {
                color: '#8C8C8C',
                fontSize: 12
            },
            padding: [10, 5],
            formatter: function (params) {
                if (!params || params.length === 0) return '';
                const platform = params[0].axisValue;
                let result = `<div style="color: #8C8C8C; font-size: 14px; width: 160px;padding-left: 12px">
                    ${platform}</div>`;
                params.forEach((param, idx) => {
                    if (param.value === 0) return
                    result += `
                        <div style="display: inline-block; width: 45%; min-width: 40px; vertical-align: top;padding: 12px 0 8px 12px;">
                            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${param.color}; margin-right: 8px; vertical-align: middle;"></span>
                            <span style="color: #757575; font-size: 14px; vertical-align: middle;">${param.seriesName}</span>
                            <div style="color: #262626; font-size: 14px; font-weight: bold; margin-top: 2px; padding-left: 28px;">${param.value}</div>
                        </div>
                        ${idx % 2 === 1 ? '<br/>' : ''}
                    `;
                });
                return result;
            }
        },
        legend: {
            data: props.metrics.map(item => item.name),
            top: 0,
            left: 'left',
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 24,
            textStyle: {
                fontSize: 12,
                color: '#8A8B8E',
                fontWeight: 400
            },
        },
        grid: {
            left: props.setting.width || 64,
            right: 20,
            top: 44,
            bottom: 48
        },
        xAxis: {
            type: 'value',
            max: maxValue,
            axisLabel: {
                formatter: function (value) {
                    return value === maxValue ? value + '（条）' : value;
                },
                color: '#8A8B8E',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: '#e0e0e0'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: categories,
            inverse: true,
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
                color: '#595959',
                fontSize: 12,
                overflow: 'break',
                interval: 0,
                width: 80,
                formatter: function (value) {
                    // 如果文本长度超过5个字符，则换行显示
                    // if (value.length > 5) {
                    //     return value.split('').reduce((prev, curr, index) => {
                    //         if (index % 5 === 0 && index !== 0) {
                    //             return prev + '\n' + curr;
                    //         }
                    //         return prev + curr;
                    //     }, '');
                    // }
                    return value;
                }
            }
        },
        series: seriesData.map(series => ({
            ...series,
        }))
    }
    if (chart.value) {
        chart.value.setOption(option)
    }
}

// 监听数据变化并重绘
watch(() => props.data, () => {
    nextTick(() => {
        initChart()
    })
}, { deep: true })

onMounted(() => {
    initChart()
    // window.addEventListener('resize', () => {
    //     chart.value?.resize()
    // })
})
// onUnmounted(() => {
//     window.removeEventListener('resize', () => {
//         chart.value?.resize()
//     })
//     if (chart.value) {
//         chart.value.dispose()
//         chart.value = null
//     }
// })
</script>


<style lang="scss" scoped>
.competitiveRadar_total_chart {
    background: #fff;
    box-sizing: border-box;
    // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    width: 100%;
    height: 100%;

    .title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        margin: 12px 0;
    }

    .chart-container {
        height: calc(100% - 30px);
        width: 100%;
    }
}
</style>