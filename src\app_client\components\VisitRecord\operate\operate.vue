<template>
    <el-dropdown @command="handleCommand">
        <span class="vt_more_btn">
            <el-icon>
                <More />
            </el-icon>
        </span>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item command="download"
                    v-if="props.row.recordingPaths && props.row.recordingPaths.indexOf('http') > -1">下载</el-dropdown-item>
                <el-dropdown-item command="share">分享</el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script setup>
import { getShareConfig } from "@/app_client/tools/api"
import { More } from '@element-plus/icons-vue'
import { downloadFile } from '@/js/utils'
const props = defineProps(['row'])
const handleCommand = (action) => {
    const row = toRaw(props.row)
    if (action === 'download') {
        const recordingPaths = row.recordingPaths.split(',').filter(x => !!x);
        if (recordingPaths.length > 0) {
            const url = recordingPaths[0]
            downloadFile(url, row.recordingName)
        }
    }
    else if (action === 'share') {
        getShareConfig(row.conferenceId).then((resp) => {
            if (resp.code == 0) {
                const { shareLink, sharePassword } = resp.data;
                let message = `录制分享链接：${shareLink} `
                if (sharePassword) {
                    message += `\r\n密码：${sharePassword}`
                }
                g.appStore.doCopy(message, '分享链接已复制到剪切板！')
            }
        })
    }
}

defineExpose({
    handleCommand
})
</script>

<style lang="scss">
.vt_more_btn {
    margin: 8px 0 0 8px;
    cursor: pointer;
}
</style>
