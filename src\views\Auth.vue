<template>
    <div class="auth-loading" v-loading="true" loading-text="加载中...">
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(async () => {
    // 获取 # 后面的部分
    const hashString = window.location.hash.substring(1) // 去掉 # 
    // 从 /auth 后面提取查询参数
    const queryString = hashString.substring(hashString.indexOf('?'))
    const urlParams = new URLSearchParams(queryString)

    let id = urlParams.get('id')
    const to = urlParams.get('to') || '/'
    try {
        if (!id || id === 'null') {
            throw new Error('No token provided')
        }
        let token = 'Bearer2 ' + id
        // 重新加载用户信息
        if (await g.appStore.reloadUserInfo(token)) {
            // 跳转到目标页面
            console.log('router to page', to)
            if (to.indexOf('http') == -1) {
                location.href = `/#${to}`
            } else {
                location.href = to
            }
        } else {
            console.log('fail to router')
        }
    } catch (error) {
        console.error('Auth failed:', error)
    }
})
</script>

<style lang="scss" scoped>
.auth-loading {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
