<template>
    <div v-ai-tip="'center'"
        :class="`chat_footer ${config.topicType == 'PRODUCT_RECOMMENDATION' ? 'chat_footer_commend' : ''}`">
        <div class="foot_center">
            <div class="btn_stop_box">
                <slot name="input_top" />
                <BtnStopAnswer ref="refBtnStopAnswer" @callback="onStopAnswer" v-if="isAnswering && config.show_stop" />
            </div>
            <div class="footer_bottom flex-row">
                <BtnClear @callback="onClear" v-show="config.show_clear && (!isPhone || isPhone && !inputQuestion)" />
                <div :class="`input_border ${isPhone && !inputQuestion ? 'no_input' : 'has_input'}`">
                    <div class="fb_left">
                        <slot name="input_inside_top" />
                        <textarea v-model="inputQuestion" :rows="rows" class="chat_input lb-scrollbar"
                            :placeholder="inputPlaceholder" @keyup.ctrl.enter.exact="keyCtrlEnter($event)"
                            @keyup.enter.exact="keyEnter($event, 'enter')" id="myTextarea"
                            :disabled="disableApp"> </textarea>
                    </div>
                    <div class="chat_btn chat_send_btn" v-if="inputQuestion.length > 0" @click="onSend">
                        <SendIcon />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import SendIcon from "@/icons/send.vue"
import BtnStopAnswer from "./BtnStopAnswer.vue"
import BtnClear from "./BtnClear.vue";

import { replaceLastNewline } from "@/js/utils.js"
import { onMounted } from "vue";

const emit = defineEmits(['callback'])

const rows = ref(1)
const refBtnStopAnswer = ref();


const isPhone = ref(window.innerWidth < 960);
const inputQuestion = ref('');
const isAnswering = ref(false);
const disableApp = ref(false);
const inputPlaceholder = ref(isPhone.value ? '请向我提问相关问题' : '请向我提问，Enter发送，Ctrl+ Enter换行')
const config = ref({})
let hasMeetingInfo = false;

watch([inputQuestion], ([newValue], [prevValue]) => {
    const maxCharactersPerLine = isPhone.value ? 17 : 50;

    const regex = /\n/g;
    let count = (newValue.match(regex) || []).length + 1;
    const a1 = parseInt(newValue.length / maxCharactersPerLine + '') + 1;
    count = Math.max(count, a1);
    rows.value = Math.min(5, count);
})

const init = (data) => {
    config.value = data;
}

const setQuestion = (question) => {
    inputQuestion.value = question;
}
const setIsAnswering = (status) => {
    isAnswering.value = status
}
const setDisableApp = (v) => {
    disableApp.value = v;
}

const onStopAnswer = () => {
    isAnswering.value = false;
    emit('callback', 'stop_answer')
}

const keyEnter = (event, ctrl) => {
    onSend();
}

const onClear = () => {
    emit('callback', 'clear')
}

const keyCtrlEnter = (event) => {
    inputQuestion.value = inputQuestion.value + '\n'
}

const addListener = () => {
    g.emitter.on('setHasMeetingInfo', status => {
        hasMeetingInfo = status;
    })
}

const onSend = () => {
    if (disableApp.value) {
        return
    }
    let trimtxt = inputQuestion.value;
    if (trimtxt.length == 0 && !hasMeetingInfo) {
        return;
    }
    if (isAnswering.value) {
        g.emitter.emit('show_msg_box', { message: '暂时不能发送，请稍后再试', time: 3, need_icon: true })
        return
    }
    isAnswering.value = true;

    trimtxt = replaceLastNewline(trimtxt)
    emit('callback', 'send', trimtxt)
    inputQuestion.value = ''
}

onMounted(() => {
    addListener()
})

onUnmounted(() => {
    g.emitter.off('setHasMeetingInfo');
})

defineExpose({
    init, rows, setIsAnswering, inputPlaceholder, onStopAnswer, onClear,
    SendIcon, onSend, BtnClear, setDisableApp, setQuestion
})
</script>

<style lang="scss">
@import url("chatInput.scss");
</style>
