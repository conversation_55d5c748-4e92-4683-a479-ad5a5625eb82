{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "com.yunxuetang.novaguidemac.test", "asar": true, "productName": "绚星销售助手test", "directories": {"output": "release/test/${version}"}, "files": ["dist", "dist-electron"], "protocols": [{"name": "com.yunxuetang.novaguidemac.test", "schemes": ["yxtnovaguidetest"]}], "mac": {"icon": "build/favicon.icns", "target": {"target": "default", "arch": ["universal"]}, "artifactName": "${productName}-Mac-${version}-Installer.${ext}", "extraResources": ["./build/extraResources/mac.zip"], "publish": [{"provider": "generic", "url": "https://meetcdn.yxt.com/dev-download-novaguide/mac/"}], "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "hardenedRuntime": true, "gatekeeperAssess": false}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/favicon.ico", "artifactName": "${productName}-Windows-${version}-Installer.${ext}", "publish": [{"provider": "generic", "url": "https://meetcdn.yxt.com/dev-download-novaguide/windows/"}], "verifyUpdateCodeSignature": false, "extraResources": [{"from": "build/extraResources/win", "to": "extraResources/win", "filter": ["**/*"]}], "signDlls": false}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false, "createDesktopShortcut": true, "installerHeaderIcon": "public/favicon.ico", "installerIcon": "public/favicon.ico", "uninstallerIcon": "public/favicon.ico", "include": "build/urlProtoco-test.nsh"}, "linux": {"target": ["AppImage"], "artifactName": "${productName}-Linux-${version}.${ext}"}}