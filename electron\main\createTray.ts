import { Tray, Menu, app, nativeTheme } from 'electron';
import path from 'path';
import { startMainWindow } from './createMainWindow';
import config from '../utils/config'
import windowManager from './windowManager'
import { getStore, setStore, keyClientId } from '../utils/store'
import { getClientId } from '../utils/tools'
let tray: Tray | null = null;

const getTrayIconPath = () => {
  let iconPath = "";
  if (config.isMac) {
    iconPath = nativeTheme.shouldUseDarkColors ? "mactray_dark.png" : "mactray_light.png";
  } else {
    iconPath = 'tray.png';
  }
  let imgPath = 'dist'
  //如果是开发环境，则使用public目录下的icon
  if (process.env.NODE_ENV === 'development') {
    imgPath = 'public'
  }
  return path.join(app.getAppPath(), imgPath, iconPath)
}

export function onTrayClick() {
  const mainWindow = windowManager.getWindow();
  if (mainWindow && !mainWindow.window.isDestroyed()) {
    mainWindow.window.show()
  } else {
    startMainWindow()
  }
}

export function createTray() {
  app.whenReady().then(() => {
    const iconPath = getTrayIconPath();
    tray = new Tray(iconPath);

    // 添加左键点击事件
    tray.on('click', onTrayClick);

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '打开主窗口', click: onTrayClick,
      },
      { label: '退出', click: () => app.quit() }
    ]);

    tray.setToolTip('绚星销售助手');
    tray.setContextMenu(contextMenu);
  })
}

export function setClientId() {
  const clientId = getStore(keyClientId)
  if (!clientId) {
    const clientId = getClientId()
    setStore(keyClientId, clientId)
  }
}
