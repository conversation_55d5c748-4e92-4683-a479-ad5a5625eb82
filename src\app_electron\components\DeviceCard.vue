<template>
    <div class="device-card">
        <div class="device-card-header">
            <img :src="getAssetUrl('meet_device.png')" alt="">
            <div>
                <div class="device-row">
                    <span class="device-title">智能工牌</span>
                    <span :class="`device-status ${localDevice.online ? 'online' : 'offline'}`">{{ localDevice.online ?
                        '在线' : '离线' }}</span>
                </div>
                <div class="device-serial">序列号：{{ localDevice.snCode }}</div>
            </div>
        </div>
        <BatteryIndicator :battery-per="localDevice.batteryPer" textSide="top" />
    </div>
</template>

<script setup>
import { getAssetUrl } from '@/js/utils';
import BatteryIndicator from '@/app_electron/components/BatteryIndicator.vue';
const emit = defineEmits(['callback']);
let timer = null;
let loading = false;
const localDevice = ref({
    batteryPer: -1,
    online: 1,
    snCode: ''
});

const getStatus = async () => {
    if (loading) return;
    loading = true;
    localDevice.value = await g.meetStore.getCardDeviceStatus()
    emit('callback', toRaw(localDevice.value));
    loading = false;
    if (timer) {
        clearInterval(timer)
    }
    timer = setTimeout(() => {
        getStatus()
    }, 1000 * 60 * 1) // 每1分钟更新一次状态
}

onUnmounted(() => {
    timer && clearInterval(timer)
})

defineExpose({
    getStatus
});

</script>

<style scoped lang="scss">
// 设备卡片
.device-card {
    width: 100%;
    padding: 22px 20px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .device-card-header {
        display: flex;
        flex-direction: row;

        img {
            width: 40px;
            height: 40px;
            margin-right: 15px;
        }
    }

    .device-row {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-bottom: 6px;

        .device-title {
            font-weight: 600;
            color: #222;
        }

        .device-status {
            font-size: 13px;
            border-radius: 4px;
            padding: 2px 8px;
            margin-left: 10px;

            &.online {
                background: rgba(4, 204, 164, 0.1);
                color: #04CCA4;
            }

            &.offline {
                background: rgba(245, 34, 45, 0.1);
                color: #F5222D;
            }
        }
    }

    .device-serial {
        font-size: 13px;
        color: #888;
    }
}
</style>