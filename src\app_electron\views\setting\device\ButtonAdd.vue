<template>
    <el-button type="primary" class="bind-button" @click="onAdd">添加设备</el-button>
    <!-- 添加工牌设备弹框 -->
    <el-dialog v-model="showBindDialog" title="添加工牌设备" width="430px" :show-close="false" modal-class="bind-dialog-add">
        <div class="dialog-content">
            <div class="input-label">请输入工牌上的SN序列号：</div>
            <el-input v-model="snNumber" placeholder="SN:" class="sn-input" :formatter="(value) => `SN: ${value}`"
                :parser="(value) => value.replace(/\SN:\s?|(,*)/g, '')" />
            <div class="help-text">
                <div class="text">SN序列号可在工牌背面或包装盒上找到</div>

                <el-popover placement="bottom-end" :width="350" trigger="hover">
                    <template #reference>
                        <el-link type="primary" :underline="true">如何查找序列号？</el-link>
                    </template>
                    <div>
                        <img :src="getOssUrl('device_sn.jpg')" class="device-sn-img" />
                    </div>
                </el-popover>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showBindDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmBind" :disabled="!isValidSN"
                    :loading="submitLoading">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { bindDevice } from '@/app_electron/tools/api.js';
import { getOssUrl } from '@/js/utils.js';

const showBindDialog = ref(false);
const snNumber = ref('');
const submitLoading = ref(false);
let forceBind = 0;

const emit = defineEmits(['callback']);

const onAdd = () => {
    showBindDialog.value = true;
}

const isValidSN = computed(() => {
    return snNumber.value.length > 4;
})

const _openConfirm = () => {
    ElMessageBox.confirm(
        '该工牌已被其他用户绑定，是否要覆盖',
        '警告',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            forceBind = 1;
            confirmBind();
        })
        .catch(() => {
        })
}


// 确认绑定
const confirmBind = () => {
    const param = {
        code: snNumber.value,
        type: "AISPEECH",
        forceBind
    }
    submitLoading.value = true;
    bindDevice(param).then(async (res) => {
        if (res.code === 0) {
            showBindDialog.value = false;
            ElMessage.success('设备添加成功');
            if (await g.appStore.reflashUserInfo()) {
                emit('callback', 'add', snNumber.value);
                snNumber.value = '';
            } else {
                ElMessage.error('绑定失败，请稍后再试');
            }
        } else if (res.code == 3010) {
            _openConfirm();
        } else {
            ElMessage.error(res.message);
        }
    }).catch(() => {
        ElMessage.error('绑定失败，请稍后再试');
    }).finally(() => {
        submitLoading.value = false;
    })
};
defineExpose({
})

</script>

<style lang="scss" scoped>
.bind-dialog-add {
    width: 500px;

    .dialog-content {
        padding: 0 20px;

        .input-label {
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        }

        .sn-input {
            margin-bottom: 8px;
        }

        .help-text {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;

            .text {
                color: #999;
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        gap: 12px;
        padding-top: 8px;
    }

    .el-dialog__header {
        margin-right: 0;
        text-align: center;
    }
}

.device-sn-img {
    width: 100%;
    height: 100%;
}
</style>