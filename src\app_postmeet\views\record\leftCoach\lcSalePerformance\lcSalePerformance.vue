<template>
  <div class="la_sale_perform_wrap flex-column">
    <div class="la_header flex-row">
      <div class="la_title">销售表现</div>
      <div :class="`las ${getStatusClass(evaluation['整体等级'])}`">
        {{ evaluation["整体等级"] }}
      </div>
    </div>

    <div class="la_box flex-column">
      <div class="la_tit">整体评价</div>
      <div class="las">
        {{ evaluation["整体评价"] }}
      </div>
    </div>

    <div class="la_box flex-column">
      <div class="la_tit">改进建议</div>
      <div class="las">
        <ol>
          <li class="tag" v-for="item in evaluation['改进建议']">
            {{ item }}
          </li>
        </ol>
      </div>
    </div>

    <div class="la_line"></div>

    <div class="md_boxs">
      <div class="md_box" v-for="item in list">
        <div class="mdb_title">
          {{ item.name }}：<span :class="getColor(item['evaluation']['评估等级'])">{{
            item["evaluation"]["评估等级"]
          }}</span>
        </div>
        <MdViewer :md="fixMd(item)" />
      </div>
    </div>
  </div>
</template>

<script>
import { getStatusClass } from "../coach.js";
import MdViewer from "@/components/Markdown/MdViewer.vue"

export default {
  components: { MdViewer },
  data() {
    return {
      evaluation: "",
      list: [],
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    getStatusClass,
    init() {
      const ar = g.postmeetStore.data.saleReport.salesCounsellingReports;
      if (ar) {
        const item = ar.find((x) => x.systemId == 200).report;
        this.evaluation = item.find((x) => x.name == "整体评估")["evaluation"];
        this.list = item.filter((x) => x.name != "整体评估");
      }
    },
    getColor(status) {
      const map = {
        优秀: "t_customer",
        达到预期: "t_internal",
        需改进: "t_na",
      };
      if (Object.keys(map)) {
        return map[status];
      }
      return "";
    },
    fixMd(item) {
      return item["evaluation"]["内容"].replace(new RegExp(" \n###", "gm"), " \n\n###");
    },
  },
};
</script>

<style lang="scss">
@use '../coach.scss';

.la_sale_perform_wrap {
  position: relative;

  .la_header {
    margin: 12px 0 0 0;
    padding: 20px;
    background: #f1f3fb;
    border-radius: 8px 8px 0px 0px;

    .la_title {
      font-weight: 500;
      color: #262626;
      line-height: 22px;
      margin: 4px 12px 2px 0;
      font-size: 16px;
    }

    .las {
      height: 30px;
      font-weight: 600;
      font-size: 20px;
      line-height: 30px;
    }

    .status_nopass {
      color: #ff4d4f;
    }
  }

  .la_box {
    padding: 20px;
    background: #f9fafc;

    .la_tit {
      height: 24px;
      font-weight: 600;
      font-size: 14px;
      color: #262626;
      line-height: 24px;
      margin-bottom: 12px;
    }
  }

  .la_line {
    height: 1px;
    background: #e1e5f7;
    margin: 0 20px;
  }

  .md_boxs {
    padding-top: 12px;
    background: #f9fafc;

    .md_box {
      padding: 4px 20px;
      background: #f9fafc;
      border-radius: 0 0 8px 8px;

      .mdb_title {
        font-weight: 600;
      }
    }
  }
}
</style>
