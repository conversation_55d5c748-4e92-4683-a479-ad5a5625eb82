<template>
    <div class="eb_fs">
        <div class="eb_ck flex-row">
            <input type="checkbox" v-model="selectHys['0']" value="0" @change="onChange" />
            <div class="eb_lab">
                客户关注点
            </div>
        </div>
        <div class="eb_ck flex-row">
            <input type="checkbox" v-model="selectHys['1']" value="1" @change="onChange" />
            <div class="eb_lab">
                客户行业
            </div>
        </div>
        <div class="eb_ck flex-row" v-for="item in HyList">
            <input type="checkbox" v-model="selectHys[item.id]" :value="item.id" @change="onChange" />
            <div class="eb_lab">
                {{ item.label }}
            </div>
            <div class="btn" @click="onAction('edit', item)">编辑</div>
            <div class="btn" @click="onAction('delete', item)">删除</div>
        </div>
        <div class="add_btn btn" @click="onAction('add', '')">
            + 添加字段
        </div>
    </div>
    <EditAiFields ref="refEdit" @callback="cbEdit" />
</template>

<script setup>
import EditAiFields from './EditAiFields.vue';
import { confirmDelete } from "@/js/utils.js"
import { getTemplateField, deleteTemplateField } from "@/app_admin/tools/api.js"

const emit = defineEmits(['callback'])
const refEdit = ref();
const selectHys = ref({ '0': true, '1': true });
const HyList = ref([])
let currItem = {};
let templateId = ''

const onAction = (action, item) => {
    const data = { ...item }
    switch (action) {
        case "edit":
            refEdit.value.show_edit(data);
            break;
        case "add":
            refEdit.value.show_add()
            break;
        case "delete":
            confirmDelete(data.label, (status) => {
                if (status) {
                    const fieldId = item.id;
                    deleteTemplateField(templateId, fieldId).then(resp => {
                        if (resp.code == 0) {
                            _queryHy()
                        }
                    })
                }
            })
            break;
    }
}

const cbEdit = (action) => {
    if (action == 'reload') {
        _queryHy()
    }
}

const _queryHy = () => {
    getTemplateField(templateId).then(resp => {
        if (resp.code == 0) {
            HyList.value = resp.data;
            selectHys.value = {
                "0": false,
                "1": false,
            }
            for (let i = 0; i < HyList.value.length; i++) {
                const item = HyList.value[i];
                selectHys.value[item.id] = false;
            }
            for (let i = 0; i < currItem.selectedFields.length; i++) {
                const id = currItem.selectedFields[i];
                selectHys.value[id] = true;
            }
        }
    })
}

const setValue = (tid, item) => {
    templateId = tid;
    currItem = item;
    _queryHy()
}

const onChange = () => {
    const obj = toRaw(selectHys.value);
    const list = Object.keys(obj).filter(key => obj[key] && key !== undefined);
    emit('callback', 'update', list)
}


defineExpose({
    selectHys, EditAiFields, refEdit, cbEdit, HyList, onAction, onChange, setValue
})

</script>

<style lang="scss">
.eb_fs {
    .eb_ck {
        margin: 12px 0;

        .eb_lab {
            margin-left: 12px;
        }
    }

    .btn {
        margin-left: 12px;
        color: #436BFF;
        cursor: pointer;
    }

    .add_btn {
        margin-bottom: 16px;
    }
}
</style>