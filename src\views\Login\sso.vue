<template>
  <div class="login-sso-box">
    <div class="login-box">
      <div class="back" @click="$emit('back', 1)" v-if="!isMobile">返回</div>
      <h2>SSO 登录</h2>
      <p class="login-tip">请输入企业域名来告诉我们你要登录的企业</p>
      <el-autocomplete v-model="domain" :fetch-suggestions="querySearch" placeholder="请输入企业域名" class="input-placeholder"
        clearable @select="handleSelect" />
      <div class="errMsg" v-if="errMsg">{{ errMsg }}</div>
    </div>
    <div class="next-btn" @click="next">下一步</div>
  </div>
</template>

<script setup>
import api from "./api.js";
import { getClientType } from "@/js/utils.js";

const props = defineProps({
  ssoDomain: String,
  productCode: String,
  deviceType: Number,
  apiConfig: String,
});

const emit = defineEmits(["back", "targetOrgInfo"]);

const domain = ref(props.ssoDomain || "");
const isMobile = getClientType() == "mobile";
const errMsg = ref("");

// 历史记录相关
const STORAGE_KEY = "sso_domain_history";
const getHistoryDomains = () => {
  const history = localStorage.getItem(STORAGE_KEY);
  return history ? JSON.parse(history) : [];
};

const saveHistoryDomain = (domain) => {
  let history = getHistoryDomains();
  // 去重
  if (!history.includes(domain)) {
    history.unshift(domain);
    // 最多保存10条记录
    history = history.slice(0, 10);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
  }
};

const querySearch = (queryString, cb) => {
  const history = getHistoryDomains();
  let results = [];
  if (queryString) {
    results = history.filter((domain) =>
      domain.toLowerCase().includes(queryString.toLowerCase())
    );
    if (!queryString.includes(".")) {
      const tempd = `${queryString}.yunxuetang.cn`;
      if (!results.includes(tempd)) {
        results.push(tempd);
      }
    }
  } else {
    results = history;
  }
  cb(results.map((item) => ({ value: item })));
};

const handleSelect = (item) => {
  errMsg.value = ''
  domain.value = item.value;
};

const next = () => {
  domain.value = domain.value.trim();
  if (!domain.value) {
    alert("请输入企业域名");
    return;
  }

  api({
    baseURL: props.apiConfig,
    headers: {
      source: props.deviceType == 1 ? 501 : 506,
    },
  })
    .get(`/core/org/orgname?domain=${domain.value}`)
    .then(({ data }) => {
      // 保存成功登录的域名
      saveHistoryDomain(domain.value);
      emit("targetOrgInfo", {
        origin: domain.value,
        cluster: data.platEnv,
        orgId: data.orgId,
        loginType: 3,
      });
    })
    .catch((err) => {
      const msg = JSON.stringify(err?.response?.data?.error?.message) || '';
      if (!!feConfig?.common) {
        errMsg.value = "机构信息错误 " + msg;
      } else {
        errMsg.value = "网络错误";
      }
    });
};
</script>

<style lang="scss" scoped>
.login-sso-box {
  text-align: left;
  background: #fff;
  position: relative;
  height: 100%;
}

.back {
  cursor: pointer;
  color: #000;
  position: relative;
  font-size: 14px;
  padding: 4px;
  border-radius: 6px;

  &:before {
    content: "";
    display: inline-block;
    width: 7px;
    height: 7px;
    border-top: 1px solid #000;
    border-right: 1px solid #000;
    transform: rotate(-135deg);
    margin-right: 6px;
  }

  &:hover {
    color: #1f2329;
    background: rgba(31, 35, 41, 0.1);
  }
}

h2 {
  font-size: 22px;
  color: #262626;
  margin: 12px 0;
}

.login-tip {
  font-size: 14px;
  color: #646a73;
  margin-top: 8px;
  line-height: 20px;
  min-height: 40px;
  white-space: pre-wrap;
}

.login-box input {
  display: block;
  border: 1px solid #d0d3d6;
  box-sizing: border-box;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  margin-top: 10px;
  font-size: 14px;
  width: 100%;

  &:focus {
    border-color: #3370ff;
  }

  &:focus-visible {
    outline: none;
  }
}

.next-btn {
  bottom: 64px;
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  background: #3370ff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 36px;

  &:hover {
    color: #fff;
    background: #4e83fd;
    border-color: #4e83fd;
  }
}

.input-placeholder::placeholder {
  color: #bfbfbf;
  font-size: 14px;
}

.errMsg {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: 4px;
}

// 添加 autocomplete 相关样式
:deep(.el-input__wrapper) {
  margin-top: 10px;
  padding: 1px 15px;
  box-shadow: 0 0 0 1px #d0d3d6 inset;

  &.is-focus {
    box-shadow: 0 0 0 1px #3370ff inset;
  }
}

:deep(.el-input__inner) {
  height: 38px;
  font-size: 14px;

  &::placeholder {
    color: #bfbfbf;
    font-size: 14px;
  }
}
</style>
