<template>
    <div class="meet-container">
        <el-tag type="primary" id="msg_status">{{ meetStatus }}</el-tag>
        <br />
        <el-button @click="createMeeting" :disabled="meetStatus == 'start'" id="start_meet">Start live meet</el-button>
        <el-button @click="endMeeting" :disabled="meetStatus !== 'start'" id="end_meet">End live meet</el-button>
        <br />
        <el-tag type="danger" v-show="errorMsg" id="msg_error">{{ errorMsg }}</el-tag>
        <!-- <el-button @click="downloadAudioGetTrack">get audio track</el-button> -->
    </div>
</template>

<script setup>
import { createLiveMeet, endLiveMeet } from '@/app_electron/tools/api'
import { Room, RoomEvent, Track } from 'livekit-client';

let room = null
let meetingInfo = {}
let conferenceId = ''
let participantId = ''
const meetStatus = ref('wait')
const errorMsg = ref('')

const _boradcastWsStatus = (status, e) => {
    console.log('boradcastWsStatus', status, e)
}


const _OnError = (type, e) => {
    console.log('OnError', type, e)
}

const _handleDataReceived = (data) => {
    console.log('handleDataReceived', data)
}

const _disconnectRoom = () => {
    console.log('begin disconnectRoom')
    if (room) {
        room
            .off(RoomEvent.DataReceived, _handleDataReceived)
            .off(RoomEvent.Connected, _boradcastWsStatus)
            .off(RoomEvent.Reconnected, _boradcastWsStatus)
            .off(RoomEvent.Disconnected, _boradcastWsStatus)
            .off(RoomEvent.Reconnecting, _boradcastWsStatus)
            .off(RoomEvent.SignalReconnecting, _boradcastWsStatus)
        room.disconnect()
        room = null
    }
}

const downloadAudioGetTrack = async () => {
    return new Promise(async (resolve) => {
        try {
            const url = `${import.meta.env.VITE_DOWNLOAD_PATH}test_audio.mp3`
            console.log('downloadAudioGetTrack url', url)
            const response = await fetch(url);
            const blob = await response.blob();
            const audioContext = new AudioContext(
                { sampleRate: 48000, latencyHint: 'interactive' }
            );
            const arrayBuffer = await blob.arrayBuffer();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

            // 创建AudioBufferSourceNode
            const audioSource = audioContext.createBufferSource();
            audioSource.buffer = audioBuffer;

            // 创建MediaStreamDestination
            const streamDestination = audioContext.createMediaStreamDestination();

            // 连接音频节点
            audioSource.connect(streamDestination);
            audioSource.loop = true;
            audioSource.start();

            // 获取音频轨道
            const audioTrack = streamDestination.stream.getAudioTracks()[0];
            console.log('audioTrack', audioTrack)
            resolve(audioTrack)
        } catch (err) {
            console.error('downloadAudioGetTrack error', err)
            errorMsg.value = '获取音频文件失败'
            resolve(null)
        }
    })
}



const _publishMixedAudioTrack = async () => {
    return new Promise(async (resolve) => {
        try {
            console.log('begin publishMixedAudioTrack')
            const mixedAudioTrack = await downloadAudioGetTrack();
            console.log('mixedAudioTrack', mixedAudioTrack)
            if (room?.localParticipant) {
                const res = await room.localParticipant.publishTrack(mixedAudioTrack, {
                    name: 'mixedAudioTrack',
                    simulcast: true,
                    source: Track.Source.Microphone,
                });
                console.log('Mixed audio track published', res);
            } else {
                errorMsg.value = '房间或本地参与者未准备好发布音频轨道'
                console.error('Room or localParticipant not ready for publishing');
            }
            resolve(true);
        } catch (err) {
            console.error('publishMixedAudioTrack error', err)
            errorMsg.value = '推流文件失败'
            resolve(false)
        }
    });
}

const _initRoom = async () => {
    return new Promise(async (resolve) => {
        try {
            const { wsUrl, token } = meetingInfo
            console.log('begin initRoom')
            _disconnectRoom()
            room = new Room({
                adaptiveStream: true,
                dynacast: true,
                reconnectPolicy: {
                    nextRetryDelayInMs: (context) => {
                        return 3000;
                    }
                }
            });

            room
                .on(RoomEvent.DataReceived, _handleDataReceived)
                .on(RoomEvent.Connected, () => _boradcastWsStatus('connected'))
                .on(RoomEvent.Reconnected, () => _boradcastWsStatus('reconnected'))
                .on(RoomEvent.Disconnected, (e) => _boradcastWsStatus('disconnected', e))
                .on(RoomEvent.Reconnecting, () => _boradcastWsStatus('reconnecting'))
                .on(RoomEvent.SignalReconnecting, () => _boradcastWsStatus('reconnecting'))
                .on(RoomEvent.MediaDevicesError, (e) => _OnError('mediaDevicesError', e))
                .on(RoomEvent.TrackMuted, (e) => _OnError('TrackMuted', e))
                .on(RoomEvent.TrackSubscriptionFailed, (e) => _OnError('TrackSubscriptionFailed', e))
                .on(RoomEvent.EncryptionError, (e) => _OnError('encryptionError', e))
                .on(RoomEvent.LocalAudioSilenceDetected, (e) => _OnError('LocalAudioSilenceDetected', e))
                .on(RoomEvent.ConnectionQualityChanged, (quality) => {
                    console.log('ConnectionQualityChanged', quality)
                })
                .on(RoomEvent.SignalConnected, () => {
                    console.log('SignalConnected')
                })
                .on(RoomEvent.ParticipantConnected, () => {
                    console.log('ParticipantConnected')
                })

            await room.prepareConnection(wsUrl, token)
            await room.connect(wsUrl, token)

            console.log('room connected')
            resolve(true)
        } catch (err) {
            console.error('initRoom error', err)
            errorMsg.value = '连接房间失败'
            resolve(false)
        }
    })
}



const createMeeting = () => {
    return new Promise(async (resolve) => {
        try {
            const data = {
                upcomingId: '',
                scheduleId: ''
            }
            meetStatus.value = 'loading'
            createLiveMeet(data).then(async resp => {
                meetingInfo = resp;
                console.log('createLiveMeet resp', JSON.stringify(resp))
                //{
                // 	"code": 1653,
                // 	"message": "您有沟通还在进行中，不能开始新的沟通",
                // 	"data": {
                // 		"conferenceId": "1920304610107514880",
                // 		"hostUserId": "ad38f561-a66f-4b9f-b468-b0447bd8a327",
                // 		"hostName": "wumx_xmate01",
                // 		"subject": "wumx_xmate01的即时录制",
                // 		"meetingKey": "54271552048",
                // 		"inProgress": false,
                // 		"completed": false
                // 	},
                // 	"reason": "S6:2025-05-08 10:56:59"
                // }
                //{
                // 	"code": 0,
                // 	"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6I",
                // 	"wsUrl": "wss://api-xxrtc-tf.yunxuetang.com.cn",
                // 	"participantId": "1920312849033445376",
                // 	"room": {
                // 		"conferenceId": "1920312848446242816",
                // 		"subject": "wumx_xmate01的即时录制",
                // 		"meetingKey": "46134632919"
                // 	}
                // }
                if (resp.code == 0) {
                    let status = await _initRoom()
                    status && (status = await _publishMixedAudioTrack())
                    if (status) {
                        meetStatus.value = 'start'
                    } else {
                        meetStatus.value = 'error'
                    }
                    conferenceId = resp.room.conferenceId
                    participantId = resp.participantId
                    resolve(status)
                } else if (resp.code == 1653) {
                    conferenceId = resp.data.conferenceId
                    participantId = resp.data.hostUserId
                    await endMeeting()
                    resolve(await createMeeting())
                } else {
                    meetStatus.value = 'error'
                    errorMsg.value = "创建会议失败" + resp
                    resolve(false)
                }
            })
        } catch (err) {
            console.error('createMeeting error', err)
            meetStatus.value = 'error'
            errorMsg.value = "创建会议失败"
            resolve(false)
        }
    })
}

const endMeeting = () => {
    return new Promise((resolve) => {
        endLiveMeet(conferenceId, { operator: participantId }).then(async (res) => {
            console.log("endLiveMeet result", res)
            const status = res.code == 0
            if (status) {
                _disconnectRoom()
            }
        }).catch(err => {
            console.error('endMeeting error1', err)
        }).finally(() => {
            meetStatus.value = 'end'
            resolve(true)
        })
    })
}

</script>

<style lang="scss">
.meet-container {
    height: 100%;
    padding: 24px;

    .el-tag {
        margin-bottom: 24px;
    }
}
</style>