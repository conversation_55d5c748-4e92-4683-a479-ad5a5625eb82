<template>
    <div class="tbks flex-row">
        <div class="tb">
            <img :src="getOssUrl('template1.png')" />
            <div class="tbt">炽热暖阳</div>
        </div>
        <div class="tb">
            <img :src="getOssUrl('template2.png')" />
            <div class="tbt">清逸天蓝</div>
        </div>
        <div class="tb">
            <img :src="getOssUrl('template3.png')" />
            <div class="tbt">橙光黑影</div>
        </div>
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js"

defineExpose({
    getOssUrl
})  

</script>
<style lang="scss">
.tbks {
    .tb {
        margin: 16px 16px 0 0;

        img {
            width: 128px;
            height: 72px;
        }
    }

    .tbt {
        width: 102px;
        background: #FFFFFF;
        border-radius: 0px 0px 4px 4px;
        border: 1px solid #E9E9E9;
        padding: 4px 12px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 20px;
    }

}
</style>