<template>
  <div class="process-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>

      <template #col_processName="{ row }">
        <div class="process-name">{{ row.processName }}</div>
      </template>

      <template #col_communicationSteps="{ row }">
        <div class="communication-steps">
          <div v-for="(line, index) in (row.communicationSteps || '').split('\n')" :key="index" class="step-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_type="{ row }">
        <div class="type">{{ row.type }}</div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getProcessListData, deleteProcess } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';

const refTable = ref();

const tableConfig = reactive({
  tableid: 'process_list',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "流程名称",
  delete_hint_column: 'processName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["processName", "communicationSteps", "type", "creator", "createTime"],
  template: ["processName", "communicationSteps"],
  urlGet: getProcessListData,
  urlDelete: deleteProcess
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  ElMessage.info('创建功能待开发');
};

const handleEdit = (row) => {
  ElMessage.info(`编辑功能待开发: ${row.processName}`);
};

const handleDelete = (row) => {
  confirmDelete(row.processName, (status) => {
    if (status) {
      deleteProcess(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.process-table {
  padding: 24px 0;

  .process-name {
    font-weight: 500;
    color: #333;
  }

  .communication-steps {
    max-width: 400px;

    .step-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .type {
    color: #333;
    font-weight: 500;
  }
}
</style>