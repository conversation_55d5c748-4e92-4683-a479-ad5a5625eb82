<template>
    <el-select v-if="records.length > 1" v-model="activeName" size="small" class="record-select"
        @change="handleSelectChange">
        <el-option v-for="(item, index) in records" :key="item.id" :label="'录制文件' + (index + 1)" :value="index" />
    </el-select>
</template>

<script setup>
const records = ref([])
const activeName = ref('')
const handleSelectChange = () => {
    const tab = { index: activeName.value, label: '录制文件' + (activeName.value + 1) }
    g.emitter.emit('set_play_record_tab', tab);
}


const init = () => {
    records.value = g.postmeetStore.data.recordInfo.recordList || []
    if (records.value.length > 1) {
        activeName.value = 0
    }
}

onMounted(() => {
    g.emitter.on("after_update_store", () => {
        init();
    });
})

</script>

<style lang="scss" scoped>
.record-select {
    margin-left: 12px;
    width: 120px;
}

:deep(.el-input__wrapper) {
    background: transparent;
    box-shadow: none !important;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
}

:deep(.el-select__caret) {
    color: #4E5969;
}

:deep(.el-input__inner) {
    color: #4E5969;
    font-size: 14px;
}
</style>
