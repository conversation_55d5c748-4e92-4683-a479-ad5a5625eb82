<template>
    <div>
        <div class="mixed_input_box" @click="onClick">
            <div v-if="props.enableClose && allSelected.length > 0" class="close_tags">
                <el-tag v-for="tag in allSelected.slice(0, props.maxTagCount)" :key="tag.id" closable type="info"
                    @close="onClose(tag)">
                    <el-tooltip effect="dark" :content="tag.fullname || tag.label" :show-after="500">
                        {{ tag.fullname || tag.label }}
                    </el-tooltip>
                </el-tag>
                <el-tag v-if="allSelected.length > props.maxTagCount" type="info">
                    +{{ allSelected.length - props.maxTagCount }}
                </el-tag>
            </div>
            <div class="mib_hint" v-else>
                {{ hint }}
            </div>
            <div class="mixed_input_box_icon">
                <el-icon v-if="selectedUsers.length > 0 || selectedDepts.length > 0" @click="OnReset">
                    <CircleClose />
                </el-icon>
                <el-icon v-else>
                    <MoreFilled />
                </el-icon>
            </div>
        </div>
        <el-drawer v-model="isShow" direction="rtl" class="mixed_select_wrap" append-to-body>
            <template #header>
                <div class="vd_title">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane v-if="showUserTab" label="选择成员" name="user"></el-tab-pane>
                        <el-tab-pane v-if="showDeptTab" label="选择部门" name="dept"></el-tab-pane>
                    </el-tabs>
                </div>
            </template>
            <template #default>
                <div class="mixed_select_body">
                    <div class="ms_left">
                        <UserSelectLeft v-if="activeTab === 'user'" :enable_checkbox_1="maxSelect == 1"
                            :selectedUsers="selectedUsers" ref="refUserLeft" :allDept="props.allDept"
                            :queryManagedDepartment="props.queryManagedDepartment"
                            @update:selectedUsers="handleUserUpdate" />
                        <DeptSelectLeft v-else ref="refDeptLeft" :selectedNodes="selectedDepts"
                            :mustSubDept="props.mustSubDept" @update:selectedNodes="handleDeptUpdate" />
                    </div>
                    <div class="ms_right">
                        <div class="ms_right_main">
                            <template v-if="selectedUsers.length">
                                <div class="section_header">
                                    <div class="section_title">已选人员：{{ selectedUsers.length }} 人</div>
                                    <div class="section_btn" @click="onClearAll('user')">清空</div>
                                </div>
                                <div class="tag_list">
                                    <el-tag v-for="user in selectedUsers" :key="user.id" @close="handleUserClose(user)"
                                        closable type="info">
                                        {{ user.fullname }}
                                    </el-tag>
                                </div>
                            </template>
                            <template v-if="noDisabledDepts.length">
                                <div class="section_header">
                                    <div class="section_title">已选部门：{{ noDisabledDepts.length }} 个</div>
                                    <div class="section_btn" @click="onClearAll('dept')">清空</div>
                                </div>
                                <div class="tag_list">
                                    <el-tag v-for="dept in noDisabledDepts" :key="dept.value"
                                        @close="handleDeptClose(dept)" closable type="info">
                                        {{ dept.label }}
                                    </el-tag>
                                </div>
                            </template>
                            <div v-if="maxSelect > 0" class="max_note">
                                注： 最多选择 {{ props.maxSelect }} 个成员/部门
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #footer>
                <div style="flex: auto">
                    <el-button @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="onConfirm"
                        :disabled="!(props.maxSelect == -1 || (selectedUsers.length + noDisabledDepts.length) <= props.maxSelect)">确定</el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>

<script setup>
import UserSelectLeft from './components/UserSelectLeft.vue'
import DeptSelectLeft from './components/DeptSelectLeft.vue'
import { getUrlParam, removeURLParams } from '@/js/utils.js';
import { MoreFilled, CircleClose } from "@element-plus/icons-vue"

const props = defineProps({
    type: {
        type: String,
        default: 'mixed',
        validator: (value) => ['user', 'dept', 'mixed'].includes(value)
    },
    rootDeptId: {
        type: String,
        default: ''
    },
    allDept: {
        type: Boolean,
        default: false
    },
    queryManagedDepartment: {
        type: Boolean,
        default: true
    },
    enableClose: {
        type: Boolean,
        default: false
    },
    maxTagCount: {
        type: Number,
        default: 1
    },
    maxSelect: {
        type: Number,
        default: -1
    },
    mustSubDept: {
        // 是否必须选择子部门
        type: Boolean,
        default: false
    }
})

const getUrlUsers = () => {
    try {
        const urlUsers = [];
        const ssoUser = getUrlParam('ssoUser', '');
        if (ssoUser) {
            const [id, fullname] = ssoUser.split(',');
            urlUsers.push({ id, fullname });
        }
        return urlUsers;
    } catch (e) {
        return []
    }
}

const emit = defineEmits(['callback'])
const isShow = ref(false)
const activeTab = ref(props.type === 'dept' ? 'dept' : 'user')
const selectedUsers = ref(getUrlUsers())
const selectedDepts = ref([])
const refUserLeft = ref()
const refDeptLeft = ref()

// Add computed properties to control tab visibility
const showUserTab = computed(() => props.type === 'user' || props.type === 'mixed')
const showDeptTab = computed(() => props.type === 'dept' || props.type === 'mixed')

const noDisabledDepts = computed(() => {
    if (props.mustSubDept) {
        return selectedDepts.value.filter(x => !x.disabled)
    }
    return selectedDepts.value
})

const allSelected = computed(() => {
    return [...selectedUsers.value, ...noDisabledDepts.value]
})

const getDefaultHint = computed(() => {
    if (props.type === 'user') return '选择成员'
    if (props.type === 'dept') return '选择部门'
    return '选择成员/部门'
})

const hint = ref(getDefaultHint.value)

const onClose = (tag) => {
    if (!!tag.ssoUserId) {
        selectedUsers.value = selectedUsers.value.filter(x => x.id !== tag.id)
        // 更新子组件的选中状态
        if (refUserLeft.value) {
            refUserLeft.value.refTable.manualCheck(selectedUsers.value.map(x => x.id))
        }
    } else {
        selectedDepts.value = selectedDepts.value.filter(x => x.value !== tag.value)
        // 更新子组件的选中状态
        if (refDeptLeft.value) {
            refDeptLeft.value.refTree.setCheckedKeys(selectedDepts.value.map(x => x.value))
        }
    }
    _emitData()
    updateHint()
}

// Modify init to respect type restrictions
const init = (data = {}) => {
    //data={users:[{id,fullname]}],depts:[{label,value}]}
    if (showUserTab.value) {
        selectedUsers.value = data.users || [];
    }
    if (showDeptTab.value) {
        selectedDepts.value = data.depts || [];
    }
    updateHint()
}

const onClick = () => {
    isShow.value = true
    nextTick(() => {
        refDeptLeft.value && refDeptLeft.value.refTree.setCheckedKeys([])
        if (props.type === 'user' || props.type === 'mixed') {
            refUserLeft.value?.setDeptId(props.rootDeptId)
        }
        // 设置选中状态
        if (selectedUsers.value.length && refUserLeft.value) {
            refUserLeft.value.refTable.manualCheck(selectedUsers.value.map(x => x.id))
        }
        if (selectedDepts.value.length && refDeptLeft.value) {
            refDeptLeft.value.refTree.setCheckedKeys(selectedDepts.value.map(x => x.value))
        }
    })
}

const updateHint = () => {
    const userCount = selectedUsers.value.length
    const deptCount = noDisabledDepts.value.length
    if (props.type === 'user') {
        hint.value = userCount ? `已选${userCount}人` : getDefaultHint.value
    } else if (props.type === 'dept') {
        hint.value = deptCount ? `已选${deptCount}部门` : getDefaultHint.value
    } else {
        hint.value = userCount || deptCount
            ? `已选${userCount}人/${deptCount}部门`
            : getDefaultHint.value
    }
}

const handleUserClose = (user) => {
    selectedUsers.value = selectedUsers.value.filter(x => x.id !== user.id)
    if (activeTab.value === 'user') {
        refUserLeft.value.refTable.manualCheck(selectedUsers.value.map(x => x.id))
    }
    updateHint()
}

const handleDeptClose = (dept) => {
    selectedDepts.value = selectedDepts.value.filter(x => x.value !== dept.value)
    if (activeTab.value === 'dept') {
        refDeptLeft.value.refTree.setCheckedKeys(selectedDepts.value.map(x => x.value))
    }
    updateHint()
}

const onClearAll = (type) => {
    if (type == 'user') {
        selectedUsers.value = []
        refUserLeft.value?.reset()
    } else {
        selectedDepts.value = []
        refDeptLeft.value?.reset()
    }
    updateHint()
}

// 处理用户选择更新，当maxSelect为1时实现单选逻辑
const handleUserUpdate = (users) => {
    if (props.maxSelect === 1) {
        // 当maxSelect为1时，只保留最新选择的用户
        selectedUsers.value = users.length > 0 ? [users[users.length - 1]] : []
        // 更新子组件的选中状态
        if (refUserLeft.value) {
            refUserLeft.value.refTable.manualCheck(selectedUsers.value.map(x => x.id))
        }
    } else {
        selectedUsers.value = users
    }
    updateHint()
}

// 处理部门选择更新，当maxSelect为1时实现单选逻辑
const handleDeptUpdate = (depts) => {
    if (props.maxSelect === 1) {
        // 当maxSelect为1时，只保留最新选择的部门
        selectedDepts.value = depts.length > 0 ? [depts[depts.length - 1]] : []
        // 更新子组件的选中状态
        if (refDeptLeft.value) {
            refDeptLeft.value.refTree.setCheckedKeys(selectedDepts.value.map(x => x.value))
        }
    } else {
        selectedDepts.value = depts
    }
    updateHint()
}

const onCancel = () => {
    isShow.value = false
    // emit('callback', 'cancel', { users: [], depts: [] })
}

const _emitData = () => {
    const result = {
        users: toRaw(selectedUsers.value),
        depts: toRaw(noDisabledDepts.value)
    }
    emit('callback', 'confirm', result);
}

const onConfirm = () => {
    isShow.value = false
    _emitData()
    updateHint()
}

const OnReset = (event) => {
    // 阻止事件冒泡
    event.preventDefault();
    event.stopPropagation();
    removeURLParams(['ssoUser'])
    reset()
}

const reset = (need_emit = true) => {
    const _userUsers = getUrlUsers()
    if (_userUsers.length > 0) {
        return
    }
    // 重置时也恢复到默认值
    const resetValue = { users: [], depts: [] }
    init(resetValue)
    selectedUsers.value = []
    selectedDepts.value = []
    refUserLeft.value?.reset()
    refDeptLeft.value?.reset()
    updateHint()
    if (need_emit) {
        emit('callback', 'confim', resetValue)
    }
}

onMounted(() => {
    if (selectedUsers.value.length > 0 || selectedDepts.value.length > 0) {
        const result = {
            users: toRaw(selectedUsers.value),
            depts: toRaw(selectedDepts.value)
        }
        emit('callback', 'init', result)
    }
    updateHint()
})

defineExpose({
    isShow,
    selectedUsers,
    selectedDepts,
    onClick,
    reset,
    init
})
</script>

<style lang="scss">
.mixed_input_box {
    width: 200px;
    height: 32px;
    border-radius: 5px;
    border: 1px solid #e9e9e9;
    padding: 0 10px;
    text-align: right;
    cursor: pointer;
    color: #a8abb2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .close_tags {
        .el-tag {
            margin: 0 4px;

            .el-tag__content {
                width: fit-content;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .mib_hint {
        width: 80%;
        text-align: left;
    }

    .mixed_input_box_icon {
        font-size: 14px;
        color: #8c8c8c;
    }
}

.mixed_select_wrap {
    width: 960px !important;

    .el-drawer__header {
        margin-bottom: 0 !important;
        border-bottom: 1px solid #e9e9e9;

        .vd_title {
            .el-tabs {
                margin-top: 17px;
            }
        }
    }

    .el-drawer__body {
        padding: 0;

        .mixed_select_body {
            height: calc(100vh - 133px);
            display: flex;

            .ms_left {
                width: 70%;
                border-right: 1px solid #e9e9e9;

                .us_left {
                    width: 90%;
                }
            }

            .ms_right {
                width: 30%;
                padding: 20px;

                .ms_right_main {
                    height: calc(100vh - 201px);
                    overflow-y: auto;

                    .section_header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 10px 0;

                        .section_title {
                            color: #666;
                        }

                        .section_btn {
                            color: #436BFF;
                            cursor: pointer;
                            font-size: 14px;
                        }
                    }

                    .tag_list {
                        margin-bottom: 16px;

                        .el-tag {
                            margin: 4px;
                        }
                    }
                }

                .max_note {
                    font-size: 12px;
                    color: #8c8c8c;
                }
            }
        }

    }


    .el-drawer__footer {
        border-top: 1px solid #e9e9e9;
        padding: 20px;
    }
}
</style>