import { defineStore } from 'pinia'
import { anyhas, convertDataFormat } from "@/app_admin/tools/utils.js"
import { getLandingReport } from "@/app_admin/tools/api.js"

export default defineStore('admin_usage', {
    state: () => ({
        apps_key: "dt_applications",
    }),
    actions: {
        getLandingData(param, allApplications) {
            return new Promise((resolve) => {
                const { applications, departmentIds, reportType, timeType, searchKey, pageNumber, pageSize, startTime, endTime } = param;
                getLandingReport(param).then(resp => {
                    if (resp.code == 0) {
                        let cache = convertDataFormat(resp.data.datas)
                        cache = cache.filter(x => x.accessCount > 0)
                        if (departmentIds.length == 0 && applications.length == allApplications.length && searchKey == '') {
                            // sessionStorage.setItem(key, JSON.stringify(cache))
                        }
                        // searchFn(cache)
                        resolve({ data: cache, count: cache.length })
                    }
                })
            })
        }
    }

})
