<template>
    <div class="visit_detail_wrap custom-scrollbar">
        <VisitDetail ref="refVisitDetail" @callback="onCallback" />
    </div>
</template>

<script setup>
import VisitDetail from '@/components/VisitDetail/VisitDetail.vue'
const refVisitDetail = ref()
const emit = defineEmits(['callback'])

const onCallback = (type, data) => {
    if (type == 'has_error') {
        setTimeout(() => {
            g.electronStore.closeWin('visit_detail')
        }, 2000)
    }
}

onMounted(() => {
    g.electronStore.waitPageReady().then(data => {
        refVisitDetail.value.init(data);
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

defineExpose({
    VisitDetail, refVisitDetail
})

</script>

<style lang="scss">
.visit_detail_wrap {
    padding: 16px;
}

.mac-content {
    .visit_detail_wrap {
        height: calc(100vh - 142px);
    }
}

.win-content {
    .visit_detail_wrap {
        height: calc(100vh - 100px);
    }
}
</style>
