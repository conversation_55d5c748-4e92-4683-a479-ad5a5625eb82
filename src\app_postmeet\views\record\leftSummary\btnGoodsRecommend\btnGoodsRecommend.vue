<template>
  <div class="btn_goods_recommend">
    <el-button type="default" :disabled="need_regen" @click="onClick" class="btn_regen">生成商品推荐</el-button>

    <el-dialog title="选择推荐商品分类" v-model="isShow" width="600px" :append-to-body="true" :modal-append-to-body="false"
      class="goods_recomm_modal_wrap">
      <div class="cd_main">
        <div class="cdm_title">
          商品分类
        </div>
        <div class="cdm_list">
          <el-radio v-model="rdSelect" :label="item.name" v-for="item in list" :key="item.name">{{ item.name
            }}</el-radio>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button type="default" @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import editIcon from "@/icons/edit.vue"
import { getSalesConfigure } from "@/js/api.js"

export default {
  name: 'btnGoodsRecommend',
  components: { editIcon },
  data() {
    return {
      rdSelect: '',
      list: [],
      isShow: false,
      need_regen: false,
    }
  },
  methods: {
    onClick() {
      const { salesGoodsCategories, salesRelatedType } = g.postmeetStore.data.asrRaw;
      const hasMutli = salesGoodsCategories == 1 && salesGoodsCategories.indexOf(",") > -1;
      const noCates = salesGoodsCategories !== 1 || !salesGoodsCategories;
      const needDialog = !salesGoodsCategories || hasMutli;
      if (noCates) {
        this._showDialog();
      } else if (hasMutli) {
        this.list = salesGoodsCategories.split(',').filter(x => !!x).map(x => { return { name: x } });
        this.isShow = true;
      } else {
        this.rdSelect = salesGoodsCategories;
        this._goPostmeet();
      }
    },
    _showDialog() {
      this.isShow = true;
      getSalesConfigure().then(resp => {
        if (resp.code == 0) {
          this.list = resp.data.categories
        }
      })
    },
    _goPostmeet() {
      const { customerName } = g.postmeetStore.data.asrRaw;
      const { subject } = g.postmeetStore.data.recordInfo;
      const url = `${g.config.publicPath}/#/recommend2?appid=product_recommender_c&confId=${g.postmeetStore.data.confId}&category=${this.rdSelect}&subject=${subject}&customerName=${customerName}`;
      window.open(url)
    },
    onCancel() {
      this.isShow = false;
    },
    onConfirm() {
      this.isShow = false;
      this._goPostmeet()
    }
  }
}
</script>

<style lang='scss'>
.btn_goods_recommend {
  margin: 12px;

  .btn_regen {
    height: 32px;
    border: 1px solid #D9D9D9;
    line-height: 0px;
  }
}

.goods_recomm_modal_wrap {
  .el-dialog__body {
    padding: 5px 20px;

    .cd_main {
      display: flex;
      flex-direction: column;

      .cdm_title {
        margin: 8px 0;
      }

      .cdm_list {
        .el-radio {
          margin: 10px 10px 10px 0;
        }
      }
    }
  }
}
</style>
