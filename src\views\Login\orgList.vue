<template>
	<div class="org-list-box">
		<div class="login-box">
			<div class="back" @click="$emit('back', 1)" v-if="!isMobile">返回</div>
			<h2>你可进入以下企业</h2>
			<p class="login-tip">{{ phone || email }} 已在以下企业或组织绑定了账号，你可进入以下任一企业或组织</p>
		</div>
		<div class="list-box">
			<div class="list-content">
				<div @click="goLogin(item)" class="list-item" v-for="item in list" :key="item.orgId">
					<div class="item-header" :style="{ 'backgroundImage': `url(${item.orgLogo})` }"></div>
					<div class="item-content">
						<span class="tenant-name">{{ item.orgName }}</span>
						<span class="user-name"><span class="user-name-text">{{ item.fullname }}</span></span>
					</div>
					<div class="item-footer">
						<span class="universe-icon icon-arrow">
							<svg width="1em" height="1em" viewBox="0 0 24 24" fill="none"
								xmlns="http://www.w3.org/2000/svg" data-icon="RightBoldOutlined">
								<path
									d="m7.586 20.486.707.707a1 1 0 0 0 1.414 0l7.778-7.778a2 2 0 0 0 0-2.829L9.707 2.808a1 1 0 0 0-1.414 0l-.707.707a1 1 0 0 0 0 1.414l7.07 7.072-7.07 7.07a1 1 0 0 0 0 1.415Z"
									fill="currentColor"></path>
							</svg>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import api from './api.js'
import { getClientType } from '@/js/utils.js'

export default {
	name: 'orgList',
	data() {
		return {
			isMobile: getClientType() == 'mobile'
		}
	},
	props: {
		deviceType: Number,
		apiConfig: String,
		productCode: String,
		list: Array,
		phone: String,
		type: Number,
		email: String,
		countryCode: String,
		code: String,
		returnUrl: String
	},
	methods: {
		goLogin(org) {
			if (this.type === 1) {
				api({
					baseURL: this.apiConfig,
					headers: {
						source: this.deviceType == 1 ? 501 : 506
					}
				}).post('/core/apaas/sms/code', {
					"phone": this.phone,
					"countryCode": this.countryCode,
					"code": this.code,
					"orgId": org.orgId,
					"factorCode": this.productCode
				}).then(res => {
					this.redirect(res.data, org)
				}).catch(err => {
					console.log(err)
				})
			} else if (this.type === 2) {
				api({
					baseURL: this.apiConfig,
					headers: {
						source: this.deviceType == 1 ? 501 : 506
					}
				}).post('/core/apaas/mail/code', {
					"email": this.email,
					"code": this.code,
					"orgId": org.orgId,
					"factorCode": this.productCode
				}).then(res => {
					this.redirect(res.data, org)
				}).catch(err => {
					console.log(err)
				})
			}
		},
		redirect({ loginCode }, org) {
			this.$emit('targetOrgInfo', {
				origin: org.domain,
				cluster: org.cluster,
				orgId: org.orgId,
				loginType: this.type,
				code: loginCode
			})
		}
	}
}
</script>
<style scoped lang="scss">
.org-list-box {
	text-align: left;
	height: 100%;
	position: relative;
	background: #fff;
}

.back {
	cursor: pointer;
	color: #000;
	position: relative;
	font-size: 14px;
	display: block;
	padding: 4px;
	border-radius: 6px;

	&:before {
		content: '';
		display: inline-block;
		width: 7px;
		height: 7px;
		border-top: 1px solid #000;
		border-right: 1px solid #000;
		transform: rotate(-135deg);
		margin-right: 6px;
	}

	&:hover {
		color: #1f2329;
		background: rgba(31, 35, 41, .1);
	}
}

h2 {
	font-size: 22px;
	color: #262626;
	margin: 12px 0;
}

.login-tip {
	font-size: 14px;
	color: #646a73;
	margin-top: 8px;
	line-height: 20px;
	min-height: 40px;
	white-space: pre-wrap;
}

.list-box {
	flex: 1;
	overflow-y: auto;

	.list-content {
		box-sizing: border-box;
		color: #1f2329;
		font-size: 14px;
		font-variant: tabular-nums;
		line-height: 1.5715;
		list-style: none;
		margin: 10px 0 24px;
		flex: 1;
		overflow-wrap: break-word;

		.list-item {
			position: relative;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: center;
			width: 100%;
			height: 72px;
			padding: 0 15px;
			font-size: 14px;
			font-weight: 500;
			color: #1f2329;
			border: .5px solid #dee0e3;
			border-radius: 10px;
			transition: background-color .3s;
			margin-top: 14px;

			&:hover {
				background-color: rgba(31, 35, 41, .08);
			}

			.item-header {
				display: flex;
				flex-shrink: 0;
				align-items: center;
				width: 48px;
				height: 48px;
				overflow: hidden;
				background-repeat: no-repeat;
				background-position: 50%;
				background-size: 44px 44px;
				border-radius: 8px;
			}

			.item-content {
				display: flex;
				flex: 1;
				flex-direction: column;
				padding-right: 4px;
				padding-left: 15px;
				overflow: hidden;
				transition: color .3s;

				.tenant-name {
					overflow: hidden;
					font-size: 16px;
					font-weight: 600;
					line-height: 24px;
					color: #1f2329;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.user-name {
					display: flex;
					flex-direction: row;
					align-items: center;
					height: 20px;
					overflow: hidden;
					font-size: 14px;
					line-height: 20px;
					color: #646a73;
					text-overflow: ellipsis;
					word-break: break-all;
					white-space: nowrap;

					.user-name-text {
						flex-shrink: 1;
						margin-right: 4px;
						overflow: hidden;
						font-weight: 400;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}

			.item-footer {
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				height: 100%;
				overflow: hidden;

				.icon-arrow {
					font-size: 20px;
					color: #8f959e;
				}

				.universe-icon {
					display: inline-block;
					font-style: normal;
					line-height: 0;
					text-align: center;
					text-transform: none;
					text-rendering: optimizeLegibility;
				}
			}
		}
	}
}
</style>