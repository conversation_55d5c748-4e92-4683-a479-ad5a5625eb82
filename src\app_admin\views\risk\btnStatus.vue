<template>
    <div class="risk_btn_status" @click="onClick">
        {{ props.data[props.ik] ? '禁用' : '启用' }}
    </div>
</template>

<script setup>
import { updateRiskSetting } from "@/app_admin/tools/api.js"
const props = defineProps(['ik', 'data'])
const emit = defineEmits(['callback']);

const item = ref({
    count: 1,
    enable: true
})

const init = (_item) => {
    item.value = _item;
}

const onClick = () => {
    const status = props.data[props.ik];
    if (status) {
        _onDisable()
    } else {
        _onEnable()
    }
}

const _updateApi = (target) => {
    const param = {}
    param[props.ik] = target;
    updateRiskSetting(param).then(resp => {
        if (resp.code == 0) {
            ElMessage({
                type: 'success',
                message: target ? '已启用' : '已禁用',
            })
            emit('callback', props.ik, target);
        } else {
            ElMessage({
                type: 'error',
                message: '操作失败',
            })
        }
    })
}

const _onEnable = () => {
    _updateApi(true)
}

const _onDisable = () => {
    ElMessageBox.confirm(
        '确定要禁用吗？',
        '提醒',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        _updateApi(false)
    }).catch(() => { })
}

defineExpose({
    init, onClick
})
</script>

<style lang='scss'>
.risk_btn_status {
    color: #436BFF;
    cursor: pointer;
}
</style>
