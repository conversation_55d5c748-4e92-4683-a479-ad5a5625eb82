<template>
    <TeamLayout @callback="onCallback">
        <Customer ref="refCustomer" :team="true" />
    </TeamLayout>
</template>

<script setup>
import TeamLayout from "@/app_client/components/TeamLayout.vue"
import Customer from "@/app_client/views/customer/Customer.vue"

const refCustomer = ref();

const onCallback = (action, data) => {
    if (action === 'updateDept') {
        refCustomer.value.setDeptIds([data.value]);
    }
}

defineExpose({
    Customer, refCustomer
})
</script>

<style lang="scss"></style>
