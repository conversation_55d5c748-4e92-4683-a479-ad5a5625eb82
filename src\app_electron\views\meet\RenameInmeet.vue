<template>
  <div class="rename-inmeet-container">
    <RenameSpeaker ref="refRenameSpeaker" @callback="onCallback" />
  </div>
</template>

<script setup>
import RenameSpeaker from '@/components/RenameSpeaker/RenameSpeaker.vue'
const emit = defineEmits(['callback'])
const isQuickMeeting = ref(false)
const refRenameSpeaker = ref(null)

const onCallback = (action, data) => {
  if (action == 'close') {
    g.electronStore.closeWin('rename_inmeet')
  } else if (action == 'submit') {
    g.electronStore.sendMessage('meet', 'rename-speaker', data)
  }
}

const AddListener = () => {
  g.ipcRenderer.on('forward_message', (_, { action, data }) => {
    console.log('forward_message', action, data)
    if (action == 'rename-speaker-status') {
      if (data) {
        ElMessage.success('身份标注成功')
        setTimeout(() => {
          g.electronStore.closeWin('rename_inmeet')
        }, 1000)
      } else {
        ElMessage.error('身份标注失败')
      }
    }
  })
}

onMounted(() => {
  g.electronStore.waitPageReady().then(msg => {
    //msg : speaker,name,confId
    isQuickMeeting.value = msg.isQuickMeeting
    const param = { 'ui': msg.speaker, 'name': msg.name }
    refRenameSpeaker.value.init_meet(param, msg.confId, msg.isQuickMeeting)
    AddListener()
  }).catch(err => {
    console.error('rename_inmeet waitPageReady error', err)
  })
})

</script>

<style lang="scss">
.rename-inmeet-container {
  .diabody_create_wrap {
    .footer {
      position: absolute;
      bottom: 0;
      width: calc(100vw - 40px);
    }
  }
}
</style>