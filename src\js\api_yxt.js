import { getHttp } from "@/js/request.js";
import { getSsoBase<PERSON><PERSON> } from '@/js/utils.js'

const _http = getHttp(getSsoBaseApi(), localStorage.getItem('token'))

export const getYxtAuthCode = (data) => _http.post('core/auth/third/url', data)

// 要素列表
export const getYxtFuncPoints = (orgId) => _http.get(`core/factors/${orgId}/func-points`)


// core/product/all
export const getYxtProductAll = () => _http.get('core/product/all')