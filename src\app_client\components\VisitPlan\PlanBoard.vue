<template>
    <div class="plan-board">
        <div class="plan-stats">
            <div class="stat stat-total">
                <div class="label">计划总数</div>
                <div class="value">{{ planInfos.totalNum }}</div>
            </div>
            <div class="stat stat-finish">
                <div class="label">已完成</div>
                <div class="value">{{ planInfos.completedNum }}</div>
            </div>
            <div class="stat stat-unfinish">
                <div class="label">未完成</div>
                <div class="value">{{ planInfos.unfinishNum }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
const defaulValue = { totalNum: 0, completedNum: 0, unfinishNum: 0 }
const planInfos = ref(defaulValue)

const init = (data) => {
    planInfos.value = { ...defaulValue, ...data }
    if (planInfos.value.totalNum >= 0 && planInfos.value.completedNum >= 0) {
        planInfos.value.unfinishNum = planInfos.value.totalNum - planInfos.value.completedNum
    } else {
        planInfos.value.unfinishNum = 0
    }
}

defineExpose({
    init
})
</script>

<style lang="scss" scoped>
.plan-board {
    .plan-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
    }

    .stat {
        width: 30%;
        padding: 20px 30px;
        height: 58px;
        border-radius: 8px;
    }

    .stat-total {
        background: #F5F8FF;
        border: 1px solid #E7EEFE;
    }

    .stat-finish {
        background: #F2FCF8;
        border: 1px solid #E1F7F0;
        margin: 0 20px;
    }

    .stat-unfinish {
        background: #FFFAF6;
        border-radius: 8px;
        border: 1px solid #FCEFE2;
    }

    .value {
        width: 27px;
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 28px;
        color: #262626;
        line-height: 32px;
        text-align: left;
        font-style: normal;
    }

    .label {
        color: #888;
        margin-bottom: 8px;
    }
}
</style>
