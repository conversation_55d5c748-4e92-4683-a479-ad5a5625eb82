<template>
  <div class="msg_ai msg_line">
    <div>
      <div
        class="mbody"
        v-if="props.data.message"
        v-html="md2html(props.data.message)"
      ></div>
      <div class="mbody mbody_tk" v-if="!props.data.message">
        正在思考 <span class="thinking">{{ thinking }}</span>
      </div>
      <div
        v-if="props.data.questions && props.data.questions.length > 0"
        class="question_box"
      >
        <div class="q_hint">你可以继续问我：</div>
        <div class="q_list">
          <div
            v-for="item in props.data.questions"
            class="q_answer"
            @click="onClick(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
    <div class="right_top_btn" v-if="props.data.message" @click="onCopy">
      <CopyIcon />
    </div>
  </div>
</template>

<script setup>
import AiIcon from "@/icons/ai.vue";
import CopyIcon from "@/icons/copy.vue";
import { md2html } from "@/js/md.js";


const emit = defineEmits(["callback"]);
const props = defineProps(["data"]);
const thinking = ref("");

const onCopy = () => {
  const message = props.data.message.replace(/<br\s*\/?>/g, "\n");
  g.appStore.doCopy(message, "已复制");
};

const onClick = (data) => {
  emit("callback", "send_input", data);
};

onMounted(() => {
  const timer = setInterval(() => {
    if (props.data.message) {
      clearInterval(timer);
    } else {
      thinking.value += ".";
      if (thinking.value.length > 4) {
        thinking.value = ".";
      }
    }
  }, 200);
});

defineExpose({ props, CopyIcon, thinking, AiIcon });
</script>

<style lang="scss">
.msg_ai {
  justify-content: flex-start;
  position: relative;

  .mbody_tk {
    width: 150px !important;
    padding: 12px 16px !important;
  }

  .right_top_btn {
    display: none;
    height: 28px;
    border-radius: 4px;
    position: absolute;
    padding: 8px 14px;
    top: -1px;
    right: -3px;
    z-index: 3;
    cursor: pointer;

    svg:hover {
      color: #436bff;
    }
  }
}

.msg_ai:hover {
  .right_top_btn {
    display: block;
  }
}
</style>
