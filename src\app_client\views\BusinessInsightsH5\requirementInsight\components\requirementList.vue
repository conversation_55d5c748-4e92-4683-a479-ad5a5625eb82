<template>
    <div class="requirement-list-h5">
        <titleMenu>
            <template #title>
                用户需求
            </template>
        </titleMenu>
        <div class="requirement-list" v-loading="loading">
            <van-list v-model="loading" :finished="finished" :finished-text="reqList.length > 0 ? '没有更多了' : ''"
                @load="handleLoadMore">
                <div v-for="item in reqList" :key="item.value" class="requirement-list-item">
                    <div class="requirement-list-item-title">
                        <span class="title-1"> {{ item.demand }}</span> <span class="title-2"
                            @click="onShowActionSheet(item)">详情</span>
                    </div>
                    <div class="requirement-list-item-value">
                        <!-- <div class="priority-tag" :class="{
                            'priority-high': item.priority === 'HIGH',
                            'priority-medium': item.priority === 'MEDIUM',
                            'priority-low': item.priority === 'LOW'
                        }">
                            {{ item.priority === 'HIGH' ? '高频需求' : item.priority === 'MEDIUM' ? '中频需求' : '低频需求' }}
                        </div> -->
                        <div class="value-1">
                            {{ item.classify }}
                        </div>

                    </div>
                    <div class="requirement-list-item-bottom">
                        <div class="bottom-1">
                            建议
                        </div>
                        <div class="bottom-2">
                            {{ item.suggestion }}
                        </div>
                    </div>
                </div>
            </van-list>
            <el-empty v-if="reqList.length == 0" :image="getOssUrl('no-data.png', 3)">
            </el-empty>
        </div>
        <requirementActionSheet ref="refActionSheet" v-if="isShowActionSheet" @closed="isShowActionSheet = false" />
    </div>
</template>
<script setup>
import requirementActionSheet from './requirementActionSheet.vue';
import titleMenu from "../../components/titleMenu.vue";
import { getOssUrl } from "@/js/utils.js";
const reqList = ref([]);
const isShowActionSheet = ref(false)
const refActionSheet = ref(null)
const loading = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = 10;

const queryData = async (pageNum) => {
    try {
        loading.value = true
        const param = {
            pageNumber: pageNum || page.value,
            pageSize: pageSize,
        }
        const response = await g.clientBiStore.getReqTabelData(param) || {}
        const list = response.data.datas || []
        if (pageNum === 1) {
            reqList.value = list
        } else {
            reqList.value.push(...list)
        }
        // 判断是否还有更多数据
        if (list.length < pageSize) {
            finished.value = true
        }
        page.value = pageNum || page.value

    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false
        // checkAndLoadMore()

    }
}

// 处理加载更多
const handleLoadMore = () => {
    if (!loading.value && !finished.value) {
        queryData(page.value + 1)
    }
}
const onShowActionSheet = (item) => {
    isShowActionSheet.value = true
    nextTick(() => {
        refActionSheet.value.showSheet(item)
    })
};


watch(() => g.clientBiStore.periodType, () => {

    page.value = 1
    finished.value = false
    queryData(1)
}, { immediate: true })


</script>
<style lang="scss" scoped>
.requirement-list-h5 {
    padding: 20px 0;
    position: relative;
    z-index: 1;

    .requirement-warp-title {

        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .title-text {
            font-weight: 600;
            font-size: 18px;
            color: #262626;
            line-height: 26px;
            padding-left: 8px;
            position: relative;

            &::before {
                content: '';
                display: inline-block;
                position: absolute;
                width: 5px;
                height: 20px;
                background: #436BFF;
                border-radius: 3px;
                left: -11px;
                top: 2px;
            }
        }

        .van-dropdown-menu {
            width: 130px;
        }


        :deep(.van-dropdown-menu__bar) {
            height: 26px;
            background: transparent;
            box-shadow: unset;
        }

    }

    .requirement-list {
        padding-top: 12px;
        // overflow: scroll;
        // height: calc(100vh - 128px);
        // box-sizing: border-box;

        .el-empty {
            background: #fff;
            border-radius: 6px;
        }

        .requirement-list-item {
            display: flex;
            flex-direction: column;
            background: #FFFFFF;
            border-radius: 8px;
            padding: 16px 18px;
            margin-bottom: 12px;

            .requirement-list-item-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                // align-items: center;

                .title-1 {
                    font-size: 16px;
                    color: #262626;
                    line-height: 24px;
                    font-weight: 700;
                    display: inline-block;
                    width: calc(100% - 44px);
                    box-sizing: border-box;
                }

                .title-2 {
                    font-weight: 400;
                    font-size: 14px;
                    color: #436BFF;
                    line-height: 22px;
                }
            }

            .requirement-list-item-value {
                margin-top: 5px;

                .value-1 {
                    background: #F0F6FE;
                    border-radius: 2px;
                    font-size: 12px;
                    color: #436BFF;
                    line-height: 18px;
                    padding: 1px 6px;
                    // margin-left: 8px;
                    display: inline-block;
                }

                .priority-tag {
                    display: inline-block;
                    padding: 1px 6px;
                    border-radius: 2px;
                    font-size: 12px;
                    text-align: center;
                    line-height: 18px;
                }

                .priority-high {
                    background: rgba(255, 17, 29, 0.08);
                    color: #FF111D;
                }

                .priority-medium {
                    background: rgba(250, 140, 22, 0.08);
                    color: #FA8C16;
                }

                .priority-low {
                    background: rgba(246, 189, 22, 0.08);
                    color: #F6BD16;
                }
            }

            .requirement-list-item-bottom {
                margin-top: 12px;
                display: flex;
                flex-direction: row;

                .bottom-1 {
                    font-size: 14px;
                    color: #8C8C8C;
                    line-height: 22px;
                }

                .bottom-2 {
                    width: calc(100% - 40px);
                    font-size: 14px;
                    color: #262626;
                    line-height: 22px;
                    margin-left: 12px;
                    word-break: break-all;
                }
            }
        }
    }
}
</style>