<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>绚星销售助手</title>
  <link rel="icon" href="https://stc.yxt.com/ufd/989eac/favicon.ico" />
  <style>
    * {
      padding: 0;
      margin: 0;
    }

    .ball-clip-rotate {
      height: 100vh;
      display: flex;
      /* line-height: 100%; */
      align-items: center;
      flex-direction: column;
      justify-content: center;
    }

    .ball-clip-rotate>div {
      background-color: #fff;
      width: 15px;
      height: 15px;
      border-radius: 100%;
      margin: 2px;
      -webkit-animation-fill-mode: both;
      animation-fill-mode: both;
      border: 3px solid #436bff;
      border-bottom-color: transparent;
      height: 25px;
      width: 25px;
      background: transparent !important;
      display: inline-block;
      -webkit-animation: rotate 0.9s 0s linear infinite;
      animation: rotate 0.9s 0s linear infinite;
    }

    @keyframes rotate {
      0% {
        -webkit-transform: rotate(0deg) scale(1);
        transform: rotate(0deg) scale(1);
      }

      100% {
        -webkit-transform: rotate(360deg) scale(1);
        transform: rotate(360deg) scale(1);
      }
    }

    /* 把id 为error的样式，在页面居中显示 */
    #message {
      display: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
    }

    #error {
      color: red;
    }

    #back {
      color: #436bff;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <div class="loader">
    <div class="loader-inner ball-clip-rotate">
      <div></div>
      <p>加载中...</p>
    </div>
  </div>
  <div id="message">
    <span id="error">
      登录失败，请稍后重试！
    </span>
    <br />
    <span id="back">返回登录</span>
  </div>

</body>
<script>
  const e = GetQueryString("e");
  // 添加在脚本最开始处
  if (location.pathname === '/novaguide/') {
    const currentUrl = new URL(window.location.href);
    const siteEnv = e.indexOf('prod') > -1 ? 'www' : 'test';
    const newUrl = new URL(`https://${siteEnv}.x-mate.com/thirdLogin.html`);

    // 保持原有的查询参数
    currentUrl.searchParams.forEach((value, key) => {
      newUrl.searchParams.append(key, value);
    });
    window.location.href = newUrl.toString();
  } else {

    const code = GetQueryString("code");
    const toPage = GetQueryString("page") || '/client/visit';
    const isNative = GetQueryString("isNative");
    const localOrgInfo = JSON.parse(localStorage.getItem('yxtlm_orgInfo') || '{"origin": ""}');
    const domain = GetQueryString("origin") || GetQueryString("domain") || localOrgInfo.origin;
    const userAgent = navigator.userAgent;
    const isElectron = /(Electron)/i.test(userAgent);
    const isAndroid = /(Android)/i.test(userAgent);
    const productCode = 'nova_guide';
    const isiOS = /(iPhone|iPad|iPod|iOS)/i.test(userAgent) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    // console.log('isElectron', isElectron, 'isiOS', isiOS, 'isAndroid', isAndroid, userAgent)
    console.log('code', code, 'e', e, 'domain', domain, 'userAgent', userAgent)
    console.log('url', location.href)
    if (code) {
      let xmlhttp = null;
      if (window.XMLHttpRequest) {
        xmlhttp = new XMLHttpRequest();
      } else if (window.ActiveXObject) {
        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
      }
      let api_base = ''
      if (e.indexOf('prod') > -1) {
        api_base = 'api-phx.yunxuetang.cn/xmate'
      } else {
        api_base = 'api-phx-tf-tc.yunxuetang.com.cn/xmate'
      }
      const url = `https://${api_base}/rest/learning/login`;
      //提交方式 get / post
      if (xmlhttp != null) {
        //判断对象是否存在
        //如果是 post 请求，可在此处设置请求头信息
        xmlhttp.open("post", url, true);
        xmlhttp.setRequestHeader("Content-type", "application/json");
        if (domain) {
          xmlhttp.setRequestHeader("yxt-orgdomain", domain);
        }

        xmlhttp.send(
          JSON.stringify({
            code, e, domain, productCode
          })
        );
        const { origin, pathname } = location;
        const base_url = `${origin}${pathname.replace('/thirdLogin.html', '')}`
        xmlhttp.onreadystatechange = function () {
          //判断响应成功，XMLHttpRequest 对象的 readystate 的属性值为 4 , status 值为： 200；
          //readystate 五个状态：
          // 0 初始化 1 服务器连接已建立  2 请求已接收  3 请求处理中  4 请求已完成，响应已就绪
          if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {

            const res = JSON.parse(xmlhttp.responseText);
            console.log('login response', xmlhttp.responseText)
            if (res.code == 0) {

              if (isElectron) {
                const ipcRenderer = window.nodeRequire('electron').ipcRenderer;
                ipcRenderer.send("ssoLogin", res);
              } else if (isNative) {
                window.flutter_inappwebview.callHandler('ssoLogin', xmlhttp.responseText);
              } else {
                //web
                localStorage.setItem("yxtlm_userInfo", JSON.stringify(res.data))
                localStorage.setItem("yxtlm_yxtUserInfo", res.data.userInfo)
                const yxtUser = JSON.parse(res.data.userInfo);
                setStoreEveryKey(yxtUser.userInfo)
                const toUrlAfterLogin = localStorage.getItem('yxtlm_toUrlAfterLogin') || `${base_url}/#${toPage}`;
                localStorage.removeItem('yxtlm_toUrlAfterLogin')
                location.href = toUrlAfterLogin;
              }
            } else {
              console.log('login error', res)

              // alert('登录失败！')
              // // 隐藏样式为 loader的div，显示id为 message的div
              // document.getElementsByClassName("loader")[0].style.display = "none";
              // document.getElementById("message").style.display = "block";
              let error_id = 'err_login'
              if (res.code == 400401) {
                error_id = 'err_noaccess'
              }
              location.href = `${base_url}/#error?id=${error_id}`;
            }
          }
        };
      } else {
        alert("Your browser does not support XMLHTTP.");
      }
    }
  }
  const setStoreEveryKey = (obj) => {
    for (let key in obj) {
      let value = obj[key];
      // 如果value是对象，则转为JSON字符串
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      localStorage.setItem(key, obj[key], value)
    }
  }

  function GetQueryString(name) {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return '';
  }


  const btn_back = document.getElementById('back')
  btn_back.addEventListener('click', () => {
    const curentUrl = location.href;
    const loginUrl = curentUrl.split('?')[0].replace('thirdLogin.html', '');
    location.href = loginUrl
  })

</script>

</html>