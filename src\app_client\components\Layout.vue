<template>
    <div class="navigator_wrap flex-row" @click="onLayoutClick">
        <div :class="['left flex-col', isCollapse ? 'left-mini' : 'left-full']">
            <LeftSlider @collapse="onCollapse" />
        </div>
        <div :class="['router_wrap', isCollapse ? 'router-mini' : 'router-full']">
            <router-view v-slot="{ Component }">
                <keep-alive>
                    <component :is="Component" />
                </keep-alive>
            </router-view>
        </div>
        <div class="bk_color">
        </div>
        <AiApps />
    </div>
    <UploadingBox />
</template>
<script setup>
import config from "@/js/config.js"
import { onMounted, toRaw } from "vue"
import LeftIcon from "@/app_client/icons/left_menu"
import LeftSlider from "./LeftSlider.vue"
import UploadingBox from "@/app_client/components/UploadingBox"
import { useRoute } from "vue-router"
import AiApps from "./AiApps.vue"

const route = useRoute()
const currId = ref('sale')
const isCollapse = ref(false)

// 监听路由变化
watch(() => route.path, (newPath) => {
    document.title = g.appStore.getTitle(newPath)
}, { immediate: true })

const goYxtAi = (item) => {
    if (item.open_inner) {
        currId.value = item.id;
        if (item.url == '/app') {
            g.router.push(`/app/${item.id}`);
        } else {
            g.router.push(item.url);
        }
    } else {
        g.clientStore.openUrl(toRaw(item))
    }
}

const onLayoutClick = (e) => {
    g.emitter.emit('onLayoutClick', e)
}

const onCollapse = (value) => {
    isCollapse.value = value
}

onMounted(() => {
    g.appStore.reloadUserInfo().then((status) => {
        if (!status) {
            g.appStore.logout()
        }
    })
})

defineExpose({
    UploadingBox, currId, config, goYxtAi, LeftSlider, LeftIcon, AiApps
})

</script>
<style lang="scss">
.navigator_wrap {
    height: 100%;
    background-color: #F3F4F7;

    .bk_color {
        position: fixed;
        z-index: -1;
        top: 0;
        left: 280px;
        width: 829px;
        height: 130px;
        background: linear-gradient(96deg, rgba(205, 235, 255, 0.48) 0%, rgba(255, 207, 237, 0.39) 100%);
        filter: blur(32px);
        overflow: hidden;
    }

    .left {
        background-color: #161E54;
        align-items: center;


        .active {
            background: #E3E9FF;

            svg,
            span {
                color: #436BFF;
            }
        }

    }

    .left-mini {
        width: 64px;
    }

    .left-full {
        width: 210px;
    }

    .router_wrap {
        height: 100%;
        overflow-y: auto;
    }

    .router-mini {
        width: calc(100vw - 66px);
    }

    .router-full {
        width: calc(100vw - 210px);
    }

    /* 隐藏滚动条 */
    div::-webkit-scrollbar {
        display: none;
    }
}
</style>