<template>
  <div v-if="url">
    <video id="videoPlayer" class="video-js vjs-default-skin" controls preload="auto" playsinline></video>
  </div>
  <div v-else></div>
</template>

<script>
import { getPlayInfoAPI } from "@/api/api";
import videojs from "video.js";
import "video.js/dist/video-js.css";

export default {
  name: "videoPlayView",
  emits: ["playEnd", "playStart"],
  props: {
    fileId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      url: null,
      playbackData: [],
    };
  },

  mounted() {
    this.loadPlaybackProgress();
  },

  beforeUnmount() {
    if (this.player) {
      this.savePlaybackProgress();
      this.player.dispose();
    }
  },

  watch: {
    fileId(newFileId) {
      this.savePlaybackProgress();
      this.fetchPlayInfo(newFileId);
    },
  },

  methods: {
    fetchPlayInfo(fileId) {
      if (!fileId) return;
      getPlayInfoAPI(fileId).then((res) => {
        if (res.code !== undefined && res.code !== 0) {
          showToast(res.message);
          return;
        }
        let arr = res;
        const videoSource = arr[0];
        this.url = videoSource.url;
        setTimeout(() => {
          let alreadyInitPlayer = this.player != undefined;

          if (alreadyInitPlayer) {
            this.player.src({ src: this.url, type: "application/x-mpegURL" });
            this.player.load();
            this.loadPlaybackProgress();
          } else {
            this.initPlayer(this.url);
          }

          this.setListener();
        }, 100);
      });
    },

    initPlayer() {
      this.player = videojs("videoPlayer", {
        controls: true,
        autoplay: true,
        preload: "auto",
        src: this.url,
        fluid: true,
        sources: [
          {
            src: this.url,
            type: "application/x-mpegURL",
          },
        ],
      });

      this.player.on("timeupdate", () => {
        this.updatePlaybackProgress();
      });
    },

    setListener() {
      // 监听视频开始播放事件
      this.player.on("play", () => {
        this.$emit("playStart");
      });

      // 监听视频播放完成事件
      this.player.on("ended", () => {
        this.$emit("playEnd");
      });
    },

    updatePlaybackProgress() {
      const currentData = this.playbackData.find((data) => data.fileId === this.fileId);
      if (currentData) {
        currentData.currentTime = this.player.currentTime();
      } else {
        this.playbackData.push({
          fileId: this.fileId,
          currentTime: this.player.currentTime(),
        });
      }
    },

    loadPlaybackProgress() {
      const savedData = localStorage.getItem(`videoProgress`);
      if (savedData) {
        this.playbackData = JSON.parse(savedData);
        const currentData = this.playbackData.find((data) => data.fileId === this.fileId);
        if (currentData) {
          this.player.currentTime(currentData.currentTime);
        }
      }
    },

    savePlaybackProgress() {
      localStorage.setItem(`videoProgress`, JSON.stringify(this.playbackData));
    },
  },
};
</script>

<style lang="scss">
.video-js {
  width: 100%;
  height: 240px;
}
</style>
