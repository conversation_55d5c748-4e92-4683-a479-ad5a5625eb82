<template>
    <ChooseSaleGoodsContent ref="contentRef" @callback="onContentCallback" />
</template>

<script setup>
import ChooseSaleGoodsContent from '@/components/SalesRelated/ChooseSaleGoodsContent.vue'

const contentRef = ref(null)

const setChecked = (data) => {
    contentRef.value.setChecked(data)
}

const onContentCallback = (action, data) => {
    console.log('onContentCallback', action, toRaw(data))
    if (!action) {
        return
    }
    if (action === 'cancel') {
        g.electronStore.closeWin('choose_salegoods')
    } else if (action === 'confirm') {
        g.electronStore.sendMessage('choose_tag', 'confirm', toRaw(data))
        g.electronStore.closeWin('choose_salegoods')
    }
}

onMounted(() => {
    g.electronStore.waitPageReady().then(param => {
        console.log('param', param)
        contentRef.value.init(param)
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

defineExpose({
    setChecked, contentRef, onContentCallback
})
</script>

<style lang="scss">
.dialog_choose_salegoods {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .el-dialog__body {
        width: 749px;
    }
}
</style>
