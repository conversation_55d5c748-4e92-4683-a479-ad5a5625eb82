import { formatDate, deepCopy } from "@/js/utils.js";

export const timeAddEt = (startTime, bt, format = "hh:mm:ss") => {
  const a = new Date(startTime).getTime();
  const res = formatDate(new Date(a + bt), format);
  return res;
};

export const getTopicTime = (startTime, bt, videoStartTime) => {
  const a = timeAddEt(startTime, bt, "yyyy-MM-dd hh:mm:ss");
  const b = timeDiffSecond(a, videoStartTime);
  return formateTime2(b);
};

export const isShouldShowTime = (startTime, bt, duration, videoStartTime) => {
  const a = timeAddEt(startTime, bt, "yyyy-MM-dd hh:mm:ss");
  const b = timeDiffSecond(a, videoStartTime);
  return b >= 0 && b < duration / 1000;
};

export function formateTime2(time) {
  const minute = parseInt(time / 60);
  const second = Math.ceil(time % 60);
  const _minute = (minute < 10 ? "0" : "") + minute;
  let _second = second > 59 ? 59 : second;
  _second = (second < 10 ? "0" : "") + _second;
  return `${_minute}:${_second}`;
}

export const timeDiffSecond = (startTime, endTime) => {
  return (new Date(startTime) - new Date(endTime)) / 1000;
};

export const parseRecordsTag = (shareRecords, startTime, startInSecord) => {
  let data = [];
  if (shareRecords) {
    const _func = (time) => {
      return timeDiffSecond(time, startTime) - startInSecord;
    };
    data = shareRecords.map((x) => {
      return {
        startTime: _func(x.startTime),
        endTime: _func(x.endTime),
      };
    });
  }
  return data;
};

export const tagsToMarkers = (recordInfo, videoTags) => {
  const startI = recordInfo.startInSecord * 1000;
  const endI = startI + recordInfo.duration;
  const currTags = videoTags.filter(
    (x) => startI <= x.bt - x.pd && x.bt - x.pd <= endI
  );
  const offseSecond = 0;
  const marksTag = currTags.map((x) => {
    return {
      markTime: (x.bt - startI - x.pd) / 1000 + offseSecond,
      markDesc: x.txt,
      bt: x.bt,
      et: x.et,
      pd: x.pd,
    };
  });
  return marksTag;
};

export const sharesToMarkers = (shareRecords) => {
  let marksShare = [];
  let lastTime = 0;
  // 两次共享点最小间隔。过小的话，认为是一个点
  const minShareSecond = 5;
  const pushMark = (markTime) => {
    if (markTime - lastTime >= minShareSecond) {
      marksShare.push({ markTime });
      lastTime = markTime;
    }
  };

  for (let record of shareRecords) {
    pushMark(record.startTime);
    pushMark(record.endTime);
  }
  return marksShare;
};

export const splitsToMarkers = (splitsRecords) => {
  let marks = [];
  let lastTime = 0;
  const minShareSecond = 5;
  const pushMark = (markTime) => {
    // 两次点最小间隔。过小的话，认为是一个点
    if (markTime - lastTime >= minShareSecond) {
      marks.push({ markTime });
      lastTime = markTime;
    }
  };

  for (let time of splitsRecords) {
    pushMark(time);
  }
  return marks;
};

export const convertOv = (data, key, startTime) => {
  if (data.hasOwnProperty(key + "s")) {
    const currData = data[key + "s"];
    if (emptyobj(currData)) {
      return [];
    } else {
      let i = 0;
      let data2 = currData.map((x) => {
        i++;
        const cv = x;
        cv["index"] = x.id || i;
        cv["time"] = timeAddEt(startTime, x.bt);
        return cv;
      });
      data2.sort((a, b) => (a.bt - b.bt > 0 ? 1 : -1));
      return data2;
    }
  } else if (data.hasOwnProperty(key)) {
    return data[key].map((x) => {
      return {
        value: x,
      };
    });
  } else {
    return [];
  }
};
export function isHas(a, b) {
  if (a && b) {
    return a.toLowerCase().includes(b.toLowerCase());
  } else {
    return false;
  }
}

export function isEq(a, b) {
  if (a && b) {
    return a.toLowerCase() === b.toLowerCase();
  } else {
    return false;
  }
}

export function toReplace(a, s, t) {
  return a.replace(new RegExp(s, "ig"), t);
}

export function addSeqTxt(data) {
  let result = [];
  for (let i = 1; i < data.length + 1; i++) {
    result.push(`${i}. ${data[i - 1]}`);
  }
  return result.join("\n");
}

export function downloadTxt(filename, text) {
  var element = document.createElement("a");
  element.setAttribute(
    "href",
    "data:text/plain;charset=utf-8," + encodeURIComponent(text)
  );
  element.setAttribute("download", filename);

  element.style.display = "none";
  document.body.appendChild(element);

  element.click();

  document.body.removeChild(element);
}

export function formateTime(time) {
  const hours = parseInt(time / 3600);
  const minute = parseInt((time / 60) % 60);
  const second = Math.ceil(time % 60);
  const formatSecond = second > 59 ? 59 : second;

  const _hour = hours > 0 ? `${hours}小时` : "";
  const _minute = minute > 0 ? minute + "分钟" : "";
  const _second = _minute ? "" : formatSecond + "秒";
  return `${_hour}${_minute}${_second}`;
}

export function calcUserSpeedTime(AsrResultList, UidInfo, minTime = 60000) {
  const result = {};
  if (!AsrResultList || !Array.isArray(AsrResultList)) {
    return [];
  }
  for (let row of AsrResultList) {
    const name = UidInfo[row["ui"]].name;
    if (!(name in result)) {
      result[name] = 0;
    }
    result[name] += row.et - row.bt;
  }
  let nameValue = [];
  for (let key of Object.keys(result)) {
    const min = Math.round(result[key] / minTime);
    if (min > 0) {
      const row = result[key];

      nameValue.push({
        name: key,
        value: min,
        // bt: row.bt,
        // et: row.et,
        // pd: row.pd
      });
    }
  }
  nameValue.sort((a, b) => {
    if (a.value > b.value) return -1;
    else if (a.value < b.value) return 1;
    else return 0;
  });
  return nameValue;
}

export function addTrack(videoId, offset, title, titles) {
  const video = document.getElementById(videoId);
  const defaultStatus = "hidden"; //showing
  if (video) {
    const tracks = video.textTracks;
    let hasFound = false;
    for (let i = 0; i < tracks.length; i++) {
      if (tracks[i].label == title) {
        tracks[i].mode = defaultStatus;
        hasFound = true;
      } else {
        tracks[i].mode = "hidden";
      }
    }

    if (!hasFound) {
      const subtitles = video.addTextTrack("subtitles", title, "en");
      for (var i = 0; i < titles.length; i++) {
        var item = titles[i];
        if (!item.et) {
          continue;
        }
        const start_time = item.bt / 1000 - offset - item.pd / 1000;
        const end_time = item.et / 1000 - offset - item.pd / 1000;
        const show_txt = item.txt ? item.name + ":" + item.txt : "";
        var cue = new VTTCue(start_time, end_time, show_txt);
        cue.line = 90;
        subtitles.addCue(cue);
      }
      subtitles.mode = defaultStatus;
    }
  } else {
    console.log("add track fail:no video ele");
  }
}

export function getTotoFromAsrSummary(asrSummary, st) {
  let todos = asrSummary.filter((x) => !!x.todo);
  return todos.map((x) => {
    return {
      value: x.todo,
      bt: x.bt,
      time: timeAddEt(st, x.bt),
      index: x.index,
    };
  });
}

export function getQueryString(name, defval = "") {
  const url = window.location.href;
  const data = url2json(url);
  if (data.hasOwnProperty(name)) {
    return data[name];
  }
  return defval;
}

export const url2json = (url) => {
  if (url.indexOf("?") > -1) {
    url = url.split("?")[1];
  }
  const arr = url.split("&");
  let res = {};
  for (let i = 0, len = arr.length; i < len; i++) {
    if (arr[i].indexOf("=") > -1) {
      let stra = arr[i].split("=");
      res[stra[0]] = decodeURI(arr[i].substr(arr[i].indexOf("=") + 1));
    } else {
      res[arr[i]] = "";
    }
  }
  return res;
};

export const loadScript = (src) => {
  return new window.Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.async = true;
    script.defer = true;
    script.src = src;
    script.onload = () => resolve();
    script.onerror = () => reject();
    document.head.appendChild(script);
  });
};

export const getfeConfigUrl = () => {
  const rundev = localStorage.getItem("xx_run_env") || "dev";
  if (process.env.VUE_APP_RUN_ENV == "meetdev") {
    return `https://api-fecenter.yunxuetang.com.cn/feConfig/runtime/appname/meetbosspc/env/${rundev}/nodeenv/production`;
  } else {
    return `https://stc.yxt.com/dynamic/feconfig/runtime/meetbosspc/${rundev}/index.js`;
  }
};

export const countKeyWords = (input) => {
  let keyWordsCount = {};
  input.forEach((element) => {
    if (element.hasOwnProperty("keyWords") && !!element.keyWords) {
      element.keyWords.forEach((keyWord) => {
        if (keyWordsCount[keyWord]) {
          keyWordsCount[keyWord]++;
        } else {
          keyWordsCount[keyWord] = 1;
        }
      });
    }
  });
  let result = [];
  for (let key in keyWordsCount) {
    result.push({ value: keyWordsCount[key], name: key });
  }
  return result;
};

export function countKeyWords_Asr(keywords, messages) {
  let results = [];
  if (keywords) {
    let result = {};
    for (let i = 0; i < keywords.length; i++) {
      result[keywords[i]] = 0;
    }
    for (let message of messages) {
      for (let keyword of keywords) {
        if (message.txt.includes(keyword)) {
          let count = (message.txt.match(new RegExp(keyword, "g")) || [])
            .length;
          result[keyword] += count;
        }
      }
    }
    for (let key in result) {
      results.push({ value: result[key], name: key });
    }
  }
  return results;
}

export function fillMarkArray(a, b, duration) {
  const arr = [a];
  const distance = (2000 * (b - a)) / duration; // 计算间距，取最小间距为10
  let current = a + distance;
  while (current < b) {
    arr.push(current);
    current += distance;
  }
  arr.push(b);
  return arr;
}

export function mergeArrays(a, b, ida = "id", idb = "id", value = "value") {
  const result = [];
  a.forEach((objA) => {
    const objB = b.find((b) => b[idb] === objA[ida]);
    if (objB) {
      // if (objB[value]) {
      //   objA[value] = objB[value];
      // }
      // result.push(objA);
      result.push({ ...objA, ...objB });
    } else {
      result.push(objA);
    }
  });
  b.forEach((objB) => {
    const objA = a.find((a) => a[ida] === objB[idb]);
    if (!objA) {
      result.push(objB);
    }
  });
  return result;
}

export function randomString(length = 4) {
  var result = "";
  let chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  for (var i = length; i > 0; --i)
    result += chars[Math.floor(Math.random() * chars.length)];
  return result;
}

export function difference(arr1, arr2) {
  return arr1.filter((x) => !arr2.includes(x));
}

export function differenceObj(arr1, arr2, id = "id") {
  const uids = new Set(arr2.map((item) => item[id]));
  return arr1.filter((item) => !uids.has(item[id]));
}

export function union(arr1, arr2, id = "id") {
  return [...arr1, ...differenceObj(arr2, arr1, id)];
}

export function parseAsrRaw(data) {
  for (let col2 of ["asrExt", "asrSummary"]) {
    if (data.hasOwnProperty(col2) && data[col2] != "") {
      data[col2] = JSON.parse(data[col2]);
    } else {
      if (["asrSummary"].includes(col2)) {
        data[col2] = [];
      } else {
        data[col2] = {};
      }
    }
  }
  data["updatedAsrContent"] = deepCopy(data["asrUpdatedContent"]);
  data["asrContent"] = deepCopy(data["asrRecognition"]);
  data["asrContent"]["UidInfo"] = renameRegUid(data["asrContent"]["UidInfo"]);

  for (let col1 of ["asr", "recognitionContent"]) {
    delete data[col1];
  }
  let i = -1;
  const asrSummary = data.asrSummary.map((x) => {
    i += 1;
    return {
      index: i,
      value: x.value,
      bt: x.startTime,
      todo: x.todo || "",
      keyWords: x.keyWords || "",
    };
  });
  data["asrSummary"] = asrSummary;
  delete data["recordInfo"];
  delete data["asrUpdatedContent"];
  return data;
}

export function renameRegUid(da) {
  if (!da) {
    return {};
  }
  let uuidName = {};
  for (let key of Object.keys(da)) {
    if (!da[key].record_id) {
      uuidName[da[key].uuid] = da[key].name;
    }
  }

  for (let key of Object.keys(da)) {
    if (da[key].record_id) {
      const uuid = da[key].uuid.split(",")[0];
      da[key].name = (uuidName[uuid] || "说话人") + da[key].name;
    }
  }
  return da;
}

export function darray2dict(data, id = "id", value = "value") {
  const result = {};
  data.forEach((item) => {
    result[item[id]] = item[value];
  });
  return result;
}

export function emptyobj(obj) {
  return !emptyobj || JSON.stringify(obj) == "{}";
}

export function array2ListText(data, name = "") {
  let arr = [];
  function fn2(data) {
    let txt = "";
    for (let i = 1; i < data.length + 1; i++) {
      txt += `${i}. ${data[i - 1]}\n`;
    }
    return txt;
  }
  if (data) {
    let temp = name ? `【${name}】\n` : "";
    temp += fn2(data);
    if (name) {
      temp += "\n";
    }
    arr.push(temp);
  }
  return arr.join("\n");
}

export function saveUrlAndLogin(router) {
  /**
   * token不存在前一个页面是需要身份页面，记录当前页面地址
   * 当客户登录完成后，会跳转到指定地址
   */
  const href = window.location.href.split("#")[1];
  // Vue3 路由匹配方式修改
  const routeMatch = router.resolve(href);
  if (routeMatch.name === "record") {
    localStorage.setItem("gourl_postmeet", window.location.href);
  }
  const isLocalDev =
    location.host.indexOf("localhost") > -1 ||
    location.host.indexOf("172.17.") > -1;
  if (isLocalDev) {
    console.log("please copy store from premeet");
  } else {
    console.log("try to login", g.config.premeet);
    // window.location.href = g.config.premeet
  }
}

export function findMinimumValue(arr) {
  if (arr.length === 0) {
    return undefined; // 数组为空，返回 undefined
  }

  let minimum = arr[0]; // 假设第一个元素为最小值

  for (let i = 1; i < arr.length; i++) {
    if (arr[i] <= minimum) {
      minimum = arr[i]; // 如果当前元素小于最小值，则更新最小值
    }
  }

  return minimum; // 返回最小值
}

//对象深合并
export function deepMerge(obj1, obj2) {
  for (let key in obj2) {
    // 如果target(也就是obj1[key])存在，且是对象的话再去调用deepMerge，否则就是obj1[key]里面没这个对象，需要与obj2[key]合并
    obj1[key] =
      obj1[key] && obj1[key].toString() === "[object Object]"
        ? deepMerge(obj1[key], obj2[key])
        : (obj1[key] = obj2[key]);
  }
  return obj1;
}

export const convert2TextPolish = (subtitles, TextPolish) => {
  let status = true;

  function fn(x, y) {
    if (x.SentenceIds.length > 0) {
      if (typeof x.SentenceIds[0] == "number") {
        return x.SentenceIds.includes(y.osi);
      } else {
        // `${ParagraphId}_${osi}`
        return x.SentenceIds.includes(y.posi);
      }
    } else {
      return false;
    }
  }

  const TextPolishs = TextPolish.map((x) => {
    const items = subtitles.filter((y) => fn(x, y));
    if (items.length > 0) {
      const txt = items.map((x) => x.txt).join("");
      const { name, osi, pd, si, time, ui } = items[0];
      return {
        name,
        osi,
        pd,
        si,
        time,
        ui,
        txt,
        bt: x.Start,
        et: x.End,
        polish: x.FormalParagraphText,
        osi_list: items.map((x) => x.osi),
      };
    } else {
      status = false;
      // console.log('x', x.SentenceIds)
    }
  });
  return [status, TextPolishs];
};

export function sum(arr) {
  if (Array.isArray(arr) && arr.length > 0) {
    return arr.reduce((prev, cur) => prev + cur);
  } else {
    return 0;
  }
}

export function generateMarkdown(data) {
  function formatNode(node, level) {
    let markdown = "";
    markdown += "  ".repeat(level) + "- " + node.name + "\n";
    if (node.children && node.children.length > 0) {
      for (let child of node.children) {
        markdown += formatNode(child, level + 1);
      }
    }
    return markdown;
  }

  let markdown = "";
  for (let node of data) {
    markdown += formatNode(node, 0);
  }
  return markdown;
}

export function newPolishToOld(input) {
  const output = {
    AsrResultList: [],
    TextPolish: [],
  };

  for (const item of input) {
    const { ParagraphId, ui, TextPolish, AsrList } = item;
    if (!Array.isArray(AsrList) || AsrList.length == 0) {
      continue;
    }

    // 处理 AsrList
    for (const asr of AsrList) {
      const { bt, et, pd, txt, si, osi } = asr;
      output.AsrResultList.push({
        bt,
        psi: `${ParagraphId}_${si}`,
        posi: `${ParagraphId}_${osi}`,
        si,
        osi,
        et,
        pd,
        txt,
        ui,
      });
    }

    // 处理 TextPolish
    output.TextPolish.push({
      ParagraphId,
      ui,
      FormalParagraphText: TextPolish,
      SentenceIds: AsrList.map((x) => ParagraphId + "_" + x.osi),
      Start: AsrList[0].bt,
      End: AsrList.length > 0 ? AsrList[AsrList.length - 1].et : 0,
      Pd: 0,
    });
  }

  return output;
}

export function replaceWithChinesePunctuation(str) {
  const englishPunctuationRegex = /[,.!?]/g;
  const punctuationMap = {
    ",": "，",
    ".": "。",
    "!": "！",
    "?": "？",
  };
  return str.replace(englishPunctuationRegex, (match) => punctuationMap[match]);
}

export function mergeSameNameData(data) {
  const result = {};

  for (const item of data) {
    const { name, markers, percent, detail, type } = item;
    const key = `${name}${detail.id}${type}`;
    if (!result[key]) {
      result[key] = { name, markers: [], percent: 0, detail, type };
    }
    result[key].markers.push(...markers);
    result[key].percent += percent;
  }

  return Object.values(result);
}

export function timeToSecond(vtime) {
  const [h, m, s] = vtime.split(":");
  return 3600 * parseInt(h) + parseInt(m) * 60 + parseInt(s);
}

export const meetApi2history = (raw) => {
  let history = [];
  for (let i = 0; i < raw.length; i++) {
    const { ai, message, id } = raw[i];
    if (!message) {
      continue;
    }
    let row = {};
    if (ai) {
      let item = {};
      if (message.indexOf("{") == 0) {
        item = { message: "``` \n" + message + "\n```" };
      } else {
        item = { message };
      }
      if (item.message) {
        row = {
          id,
          message: item.message,
          my: false,
          questions: i == raw.length - 1 ? item.questions : [],
        };
        history.push(row);
      }
    } else {
      row = {
        id,
        message,
        my: true,
      };
      history.push(row);
    }
  }
  return history;
};

export function calculateTaskScore(completedCount, totalCount) {
  if (totalCount === 0) {
    return 0;
  }
  return Math.round((completedCount / totalCount) * 100);
}
