<template>
  <vue-cal :time-from="0 * 60" :time-to="24 * 60" :time-step="30" :disable-views="['years', 'year', 'month']"
    active-view="week" :events="events" :on-event-click="onEventClick" :locale="locale" :minute-interval="30"
    class="vuecal--week-view" ref="refVisitTimeChart">
    <template #header-title>
      <!-- 移除默认标题 -->
    </template>

    <template #weekday-heading="{ heading, view }">
      <div class="custom-heading">
        <div class="week-name">{{ heading.label }}</div>
        <div class="day-number" :class="{ 'current-day': heading.today }">
          {{ new Date(heading.date).getDate() }}
        </div>
      </div>
    </template>

    <template #event="{ event }">
      <div class="event-item" :style="{ backgroundColor: event.bgcolor }">
        <div class="event-title line2">{{ event.subject }}</div>
        <div class="event-txt">{{ event.startTime }}-{{ event.endTime }}</div>
        <div class="event-txt line2">{{ event.salesMateCustomerName }}</div>
        <div class="event-txt line2">{{ event.location }}</div>
      </div>
    </template>
  </vue-cal>
</template>

<script setup>
import VueCal from 'vue-cal'
import 'vue-cal/dist/vuecal.css'

const refVisitTimeChart = ref();

const emit = defineEmits(['eventClick'])

const locale = {
  weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
  months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
}
// {startTime: '2024-11-17 00:00:00', endTime: '2024-11-23 23:59:59', completed: ''}
const param = ref({ startTime: '', endTime: '', completed: false })
const events = ref([])

const onEventClick = (event) => {
  emit('callback', 'visit_detail', event)
}

const init = (data, _param) => {
  param.value = _param
  if (param.value.startTime) {
    refVisitTimeChart.value.view.startDate = new Date(param.value.startTime)
  }

  events.value = data.map(x => {
    const startTime = x.scheduleStartTime || x.startTime;
    const endTime = x.scheduleEndTime || x.endTime;
    const item = {
      ...x,
      start: startTime.substring(0, 16),
      end: endTime.substring(0, 16),
      startTime: startTime.substring(11, 16),
      endTime: endTime.substring(11, 16),
      bgcolor: x.completed ? '#EDFDF6' : '#FFEAD8',
      class: 'meeting'
    }
    return item
  })
}

defineExpose({
  init, refVisitTimeChart
})
</script>

<style lang="scss" scoped>
.vuecal--week-view {
  height: 800px;

  :deep(.vuecal__cell-content) {
    padding: 0;
  }

  :deep(.vuecal__time-column) {
    width: 60px;
  }
}

.event-item {
  padding: 8px;
  height: 100%;
  border-radius: 4px;
  cursor: pointer;

  .event-title {
    height: 36px;
    font-size: 12px;
    color: #262626;
    line-height: 18px;
    text-align: left;
  }

  .line2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .event-txt {
    font-size: 12px;
    color: #8C8C8C;
    text-align: left;
  }
}

:deep(.vuecal__menu) {
  display: none !important;
}

:deep(.vuecal__header) {
  background-color: #fff;
}

:deep(.vuecal__title-bar) {
  display: none;
}

:deep(.vuecal__heading) {
  font-weight: normal;
  height: 60px;
  padding: 8px 0;
  background: none !important;  /* Remove blue background */
}

:deep(.vuecal__flex--split) {
  &.vuecal__flex--active {
    background-color: #e6f7ff !important;  /* Keep blue background for selected date */
  }
}

.custom-heading {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;

  .week-name {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .day-number {
    font-size: 16px;
    color: #333;

    &.current-day {
      color: #436BFF;
    }
  }
}
</style>