import { app, BrowserWindow, shell } from 'electron'
import config from '../utils/config'
import { createContextMenu } from './contextMenu'
import windowManager from './windowManager'
import { WebResourceManager } from '../utils/webResource'
import { addWindowsEvents } from '../utils/tools'

function createMainWindow() {
    let mainWin = windowManager.getWindow();
    if (mainWin && !mainWin.window.isDestroyed()) {
        if (mainWin.window.isMinimized()) {
            mainWin.window.restore()
        }
        mainWin.window.focus()
        mainWin.window.show()
        console.log('mainWin.window.isDestroyed()', mainWin.window.isDestroyed())
        return;
    }
    // win 不可以改动窗口大小
    let win = new BrowserWindow({
        icon: config.iconPath,
        width: 374,
        height: 720,
        minWidth: 374,
        minHeight: 720,
        titleBarStyle: 'hiddenInset',  // only mac使用隐藏式标题栏
        resizable: false,
        maximizable: false,
        webPreferences: {
            webSecurity: false,
            webviewTag: true,
            nodeIntegration: true,
            contextIsolation: false,
            preload: config.preload,
            // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
            // nodeIntegration: true,

            // Consider using contextBridge.exposeInMainWorld
            // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
            // contextIsolation: false,
        },
    })
    win.setMenu(null);
    win.webContents.userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/********* Electron/28.0.0"

    // 加载本地资源
    const webResourceManager = WebResourceManager.getInstance()
    const { filePath, isFile } = webResourceManager.getLoadPath()
    if (isFile) {
        win.loadFile(filePath)
    } else {
        win.loadURL(filePath)
    }

    // 检查热更新
    webResourceManager.checkAndApplyHotUpdate(win)

    createContextMenu(win)

    // Make all links open with the browser, not with the application
    win.webContents.setWindowOpenHandler(({ url }) => {
        if (url.startsWith('https:')) shell.openExternal(url)
        return { action: 'deny' }
    })

    // 监听窗口从最小化恢复的事件
    addWindowsEvents(win)
    // win.webContents.on('will-navigate', (event, url) => { }) #344
    //监听窗口的 close事件
    win.on('close', () => {
        windowManager.removeWindow('main')
    })
    // 将主窗口信息存储到 windowManager
    windowManager.addWindow(win);
}

export const startMainWindow = () => {
    if (windowManager.getWindow('login')) {
        console.log('login window is exist')
        return
    }
    app.whenReady().then(createMainWindow)
}
