<template>
    <el-dialog title="消息提醒" v-model="isShow" width="400px" :append-to-body="true" :modal-append-to-body="true"
        class="share_modal_wrap">
        <div class="cd_main">
            {{ hint }}
        </div>
        <template #footer class="dialog-footer">
            <el-button type="primary" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>

export default {
    data() {
        return {
            type: '',
            hint: '',
            isShow: false,
        }
    },
    methods: {
        show(type, hint) {
            this.type = type;
            this.hint = hint;
            this.isShow = true;
        },
        onCancel() {
            this.$emit('callback', this.type, 'close')
            this.isShow = false;
        },
        onConfirm() {
            this.$emit('callback', this.type, 'confirm')
            this.isShow = false;
        },
    }
}
</script>