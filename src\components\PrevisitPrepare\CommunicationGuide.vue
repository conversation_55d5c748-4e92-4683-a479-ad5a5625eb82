<template>
  <div class="communication-guide prepare_wrap" v-if="templates.length > 0" v-ai-tip="'bottom-right'">
    <PrepareHeader title="沟通指引" />
    <div class="guide-card">
      <div class="pdf-section">
        <div class="pdf-item" v-for="template in templates" :key="template.templateId">
          <img :src="getOssUrl('doc.png')" alt="pdf" />
          <a :href="template.templateDownloadUrl" target="_blank">{{ template.templateName }}</a>
        </div>
      </div>

      <div class="question-section" v-show="false">
        <div class="section-header">
          <span>需求探寻问题示例</span>
          <span class="refresh-btn">
            <i class="icon-refresh"></i>
            换一换
          </span>
        </div>
        <div class="question-list">
          <div class="question-item">
            <i class="dot"></i>
            <span>你们的目标是什么？</span>
          </div>
          <div class="question-item">
            <i class="dot"></i>
            <span>在这个时间点，你们发起这个项目的原因是什么？</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PrepareHeader from './PrepareHeader.vue'
import { getOssUrl } from '@/js/utils'

const templates = ref([])

const init = (data) => {
  if (data?.guidelines?.templates) {
    templates.value = data.guidelines.templates
  }
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.communication-guide {

  .guide-card {
    background: #f8f9fe;
    border-radius: 8px;
    padding: 16px;
  }

  .pdf-section {
    .pdf-item {
      display: flex;
      align-items: center;
      padding: 12px;
      background: #fff;
      border-radius: 4px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      a {
        color: #333;
        text-decoration: none;

        &:hover {
          color: #4080FF;
        }
      }
    }
  }

  .question-section {
    margin-top: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .refresh-btn {
        color: #4080FF;
        cursor: pointer;
        display: flex;
        align-items: center;

        .icon-refresh {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }

    .question-list {
      .question-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #4080FF;
          margin-top: 8px;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
