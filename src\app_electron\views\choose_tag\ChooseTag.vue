<template>
  <div class="choose_tag_wrap">
    <div class="ct_main">
      <SalesRelatedComponent ref="refSalesRelated" v-model="param" @callback="cbSalesRelated" />
    </div>
    <div class="ct_footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import SalesRelatedComponent from "@/components/SalesRelated/SalesRelatedComponent.vue"

const selectedTag = ref(null);
const param = ref({});
const refSalesRelated = ref(null)
let saleGoodsHintList = []

const cbSalesRelated = (action, data) => {
  if (action == 'saleGoodsHintList') {
    saleGoodsHintList = toRaw(data)
  }
}

const handleClose = () => {
  g.electronStore.closeWin('choose_tag')
};

const onConfirm = () => {
  let data = []
  if (param.value.salesRelatedType == 1) {
    data = param.value.salesGoodsCategories.split(',').map(x => ({ id: x, label: x }))
  } else if (saleGoodsHintList.length > 0) {
    data = saleGoodsHintList.map((label, index) => {
      const goodsData = refSalesRelated.value.saleGoodsData[index];
      return {
        id: goodsData?.id || label,
        label: label
      }
    })
  }

  g.electronStore.sendMessage('arrange_visit', 'choosed', {
    list: data,
    type: param.value.salesRelatedType
  })
  handleClose();
}


onMounted(() => {
  g.electronStore.waitPageReady().then(data => {
    const { salesRelatedType, salesGoodsCategories, currItem, tagList, selectedTags } = data;
    param.value = { salesRelatedType, salesGoodsCategories, currItem };
    refSalesRelated.value.setCurrItem(currItem);
    refSalesRelated.value.setTags(tagList);
    refSalesRelated.value.setModelValue(toRaw(param.value))
    refSalesRelated.value.setSelectedTags(selectedTags);

    g.ipcRenderer.on('forward_message', (_, { action, data }) => {
      if (action == 'confirm') {
        refSalesRelated.value.saleGoodsData = data
      }
    })
  }).catch(err => {
    console.error('meet waitPageReady error', err)
  })
})


defineExpose({
  handleClose, onConfirm, param, selectedTag, SalesRelatedComponent, refSalesRelated
});
</script>

<style lang="scss">
.choose_tag_wrap {
  padding: 12px 20px;

  .ct_main {
    max-height: 400px;
    overflow-y: auto;
  }

  .ct_footer {
    text-align: right;
    margin: 20px 0;
  }
}
</style>
