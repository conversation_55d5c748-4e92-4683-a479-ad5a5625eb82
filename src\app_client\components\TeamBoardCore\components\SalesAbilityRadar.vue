<template>
  <div class="sales-ability-radar" v-ai-tip="'bottom-right'">
    <div class="radar-header">
      <div class="title">销售能力画像</div>
      <div class="target-score">达标值：{{ abilityStandardScore }}分</div>
    </div>
    <RadarChart ref="radarChartRef" class="radar-chart" :showLegend="false" />
    <PieChartWithTitle title="能力评估详情" sub-title="点击饼图图例可跳转查看拜访详情" :chart-data="chartData" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import PieChartWithTitle from '@/app_client/components/TeamBoardCore/components/PieChartWithTitle.vue'
import RadarChart from '@/components/RadarChart.vue'
const radarChartRef = ref(null)
const userData = ref({})
const periodType = ref('')
const abilityStandardScore = ref(80)
let achievementNum = ref(0)
let unAchievementNum = ref(0)
let unDetermineNum = ref(0)


const chartData = computed(() => {
  return [
    {
      value: achievementNum.value,
      name: '达成',
      type: 'achievement',
      color: '#67C23A'
    },
    {
      value: unAchievementNum.value,
      name: '未达成',
      type: 'unAchievement',
      color: '#F56C6C'
    },
    {
      value: unDetermineNum.value,
      name: '结果未评估',
      type: 'unDetermine',
      color: '#909399'
    }
  ]
})


const init = (data, type, chart_data) => {
  const totalNum = data.totalVisitCount
  achievementNum.value = data.salesAbilityPassCount
  unAchievementNum.value = data.salesAbilityNotPassCount
  unDetermineNum.value = totalNum - achievementNum.value - unAchievementNum.value
  abilityStandardScore.value = data.abilityStandardScore
  userData.value = data
  periodType.value = type
  radarChartRef.value.init(chart_data)
}

defineExpose({
  init
})  
</script>

<style lang="scss" scoped>
.sales-ability-radar {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #E4E7ED;

  .radar-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #1D2129;
    }

    .target-score {
      font-size: 14px;
      margin-left: 12px;
      color: #86909C;
    }
  }

  .radar-chart {
    height: 300px;
    width: 100%;
  }


  .evaluation-list {
    .evaluation-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #E5E6EB;
      cursor: pointer;

      &:hover {
        background-color: #F2F3F5;
      }

      .item-left {
        display: flex;
        flex-direction: column;

        .date {
          color: #86909C;
          font-size: 14px;
          width: 160px;
        }

        .company-score {
          display: flex;

          .company {
            color: #1D2129;
            font-size: 14px;
            margin: 0 24px 0 0;
          }

          .score {
            font-size: 14px;
            font-weight: 500;

            &.passed {
              color: #4080FF;
            }

            &.failed {
              color: rgb(100, 68, 37);
            }
          }
        }
      }

      .arrow-right {
        color: #C9CDD4;
        font-size: 16px;
      }
    }
  }
}
</style>