<template>
  <div class="example_pic">示例图</div>
  <img :src="getImg" class="page_img" alt="img" />
</template>

<script setup>
import { getOssUrl } from "@/js/utils";
const props = defineProps(["id", 'img']);

const getImg = computed(() => {
  let img = '';
  if (props.img) {
    img = props.img;
  } else {
    img = props.id == 4 ? 'salesmethod_normal' : props.id;
  }
  return getOssUrl(img + '.png');
});

defineExpose({ getOssUrl, getImg });
</script>

<style lang="scss">
.example_pic {
  width: 52px;
  height: 20px;
  background: #f5f5f5;
  border-radius: 2px;
  font-size: 12px;
  color: #595959;
  line-height: 20px;
  text-align: center;
  margin-left: 7px;
  padding: 0 2px;
}
</style>
