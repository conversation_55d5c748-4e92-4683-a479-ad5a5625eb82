
export const handleTwMsg = (asrResult, data) => {
    const seq = data.Data.seq
    const existingIndex = asrResult.findIndex(item =>
        item.MsgType === 'TwMsg' && item.Data.seq === seq
    )

    if (existingIndex === -1) {
        asrResult.push(data)
    } else {
        data.Data.showTime = asrResult[existingIndex].Data.showTime
        asrResult[existingIndex] = data
    }

    return [...asrResult]
}

export const handleSummaryMsg = (asrResult, data) => {
    const lastMsg = asrResult.filter(item => item.MsgType === 'TwMsg').pop()
    data.Data.seq = lastMsg ? lastMsg.Data.seq + 0.5 : 0.5

    const insertIndex = asrResult.findIndex(item =>
        (item.MsgType === 'TwMsg' && item.Data.seq > data.Data.seq) ||
        (item.MsgType === 'TwSummary' && item.Data.seq > data.Data.seq)
    )

    if (insertIndex === -1) {
        if (data.Data.summary_content) {
            asrResult.push(data)
        }
    } else {
        asrResult.splice(insertIndex, 0, data)
    }

    return [...asrResult]
}
