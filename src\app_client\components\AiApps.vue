<template>
  <div class="ai_apps_wrap">
    <div class="ai_apps_icon" @click="onClick" v-if="isShowAiApps && isShow">
      <img :src="getOssUrl('ai_circle2.png')" alt="" />
      <img class="ai_app_close" :src="getOssUrl('ai_app_close.png')" alt="" @click="onHide" />
    </div>

    <div class="ai_app_harf" v-show="!isShow">
      <img :src="getOssUrl('ai_app_harf.png')" alt="" @click="onShow" />
    </div>

    <div class="tg_box" v-if="isShowDialog">
      <div class="head flex-row">
        <div class="ai_title">智能应用</div>
        <img class="ai_header_bg" :src="getOssUrl('ai_header_bg.png')" alt="" />
        <div class="close_icon" @click="onClick">
          <img :src="getOssUrl('close.svg')" />
        </div>
      </div>
      <AppList ref="refFuncList" />
    </div>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import AppList from "@/components/AppList.vue";
import { nextTick } from "vue";
const isShowDialog = ref(false);
const isShowAiApps = ref(false);

const lists = ref({ data: [] });
const refFuncList = ref(null);
const isShow = ref(true);

const onClick = () => {
  isShowDialog.value = !isShowDialog.value;
  if (isShowDialog.value) {
    nextTick(() => {
      refFuncList.value.init(lists.value.data);
    });
  }
};
const init = () => {
  isShowAiApps.value = g.appStore.getFuncStatus("sales_ai_apps_icon");
  if (isShowAiApps.value) {
    lists.value = g.clientStore.getMenuList();
  }
};

const onHide = (e) => {
  e.preventDefault();
  e.stopPropagation();
  isShow.value = false;
};

const onShow = () => {
  isShow.value = true;
};

onMounted(() => {
  init();

  // update_func_points
  g.emitter.on("update_func_points", (list) => {
    init();
  });
});

onUnmounted(() => {
  g.emitter.off("update_func_points");
});

defineExpose({
  onClick,
  isShowDialog,
  AppList,
  isShowAiApps,
});
</script>

<style lang="scss" scoped>
.ai_apps_wrap {
  .ai_apps_icon {
    width: 96px;
    height: 86px;
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: 6;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }

    .ai_app_close {
      width: 22px;
      height: 22px;
      position: absolute;
      right: 12px;
      visibility: hidden;
    }

    &:hover {
      .ai_app_close {
        visibility: visible;
      }
    }
  }

  .ai_app_harf {
    position: fixed;
    width: 61px;
    height: 86px;
    right: -28px;
    bottom: 152px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .tg_box {
    width: 380px;
    height: 384px;
    position: fixed;
    right: 105px;
    bottom: 20px;
    z-index: 10;
    background: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
    border-radius: 8px;

    .head {
      height: 60px;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;

      .ai_header_bg {
        position: absolute;
        width: 380px;
        height: 61px;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .close_icon {
        z-index: 2;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }

    .app_list_wrap {
      height: 315px;
    }
  }
}
</style>
