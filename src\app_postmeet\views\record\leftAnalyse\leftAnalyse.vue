<template>
    <div class="left_analyse_wrap" v-ai-tip="'center'">
        <div v-if="isShowSetup">
            <noData />
        </div>
        <lessData v-else-if="!hasSaleData" />
        <div v-else>
            <btnRadio ref="refTags" :active-id="activeId" :tags="tags" @callback="cbMenu" />
            <div class="left_main" v-if="tags">
                <MdViewer :md="tags[activeId].data.report" v-if="tags[activeId].diys" />
                <component :is="tags[activeId].page" v-else />
            </div>
        </div>
    </div>
</template>

<script>
import laOverview from "./overview/overview.vue";
import LaAttitude from "./attitude/attitude.vue";
import laRisk from './risk/risk.vue';
import laCompetitor from "./competitor/competitor.vue";
import laTodo from './todo/todo.vue';
import btnRadio from '@/app_postmeet/components/btnRadio.vue';
import noData from "./nodata/nodata.vue";
import MdViewer from "@/components/Markdown/MdViewer.vue"
import lessData from "@/app_postmeet/components/nodata.vue"
import { markRaw } from "vue";
import { getUrlParam, removeURLParams } from '@/js/utils'

export default {
    components: {
        btnRadio, noData, MdViewer, lessData
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
        g.emitter.on("update_analyse_menu", (tag) => {
            this.cbMenu(tag)
        })
    },
    data() {
        return {
            isLoadingReport: false,
            isShowSetup: false,
            activeId: '',
            hasSaleData: false,
            tags: {},
            // 系统预设组件映射
            systemComponents: {
                100: { page: markRaw(laOverview), key: 'overview' },
                101: { page: markRaw(LaAttitude), key: 'attitude' },
                102: { page: markRaw(laCompetitor), key: 'competitor' },
                103: { page: markRaw(laTodo), key: 'todo' },
                104: { page: markRaw(laRisk), key: 'risk' }
            }
        }
    },
    methods: {
        init() {
            this.isLoadingReport = g.postmeetStore.data.isLoadingReport;
            this.isShowSetup = g.postmeetStore.getIsShowSetup();
            const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
            if (!this.isShowSetup && ar) {
                this.hasSaleData = true;
                this.buildTagsFromAr(ar);
            }
            this.$nextTick(() => {
                this.autoSelectTag()
            })
        },
        autoSelectTag() {
            // 检查URL中是否有menu1参数，如果有则自动切换到对应tab
            const menuParam = getUrlParam('menu2')
            const tagsName = Object.keys(this.tags);
            if (menuParam && tagsName.includes(menuParam)) {
                this.activeId = menuParam
                removeURLParams('menu2')
            }
        },
        buildTagsFromAr(ar) {
            // 重置tags对象
            this.tags = {};

            // 按ar数组的顺序构建tags
            ar.forEach((dim, index) => {
                let tagKey, tagConfig;

                if (dim.systemId === 0) {
                    // 自定义维度，使用dim.id作为key
                    tagKey = dim.id;
                    tagConfig = {
                        name: dim.name,
                        show: true,
                        disabled: false,
                        diys: true,
                        data: dim
                    };
                } else {
                    // 系统预设维度，根据systemId获取对应的组件
                    const systemComponent = this.systemComponents[dim.systemId];
                    if (systemComponent) {
                        tagKey = systemComponent.key;
                        tagConfig = {
                            page: systemComponent.page,
                            name: dim.name, // 使用ar中的name
                            show: true,
                            disabled: false,
                            systemId: dim.systemId
                        };
                    }
                }

                if (tagKey && tagConfig) {
                    this.tags[tagKey] = tagConfig;

                    // 设置第一个为默认激活项
                    if (index === 0) {
                        this.activeId = tagKey;
                    }
                }
            });

            // 更新组件
            this.$nextTick(() => {
                if (this.$refs.refTags) {
                    this.$refs.refTags.setTags(this.tags);
                }
            });
        },
        cbMenu(tag) {
            this.activeId = tag;
        }
    }
}

</script>

<style lang="scss">
.left_analyse_wrap {
    margin-bottom: 20px;
}
</style>