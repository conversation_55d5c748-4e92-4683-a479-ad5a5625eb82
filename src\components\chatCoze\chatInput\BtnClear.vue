<template>
    <div class="chat_btn chat_clear_btn" @click="onClear">
        <ClearIcon />
        <div class="tooltip">
            清除聊天记录
        </div>
    </div>
</template>

<script setup>
import ClearIcon from "@/icons/clear.vue"
const emit = defineEmits(['callback'])

const onClear = () => {
    emit('callback', 'clear')
}

defineExpose({ onClear })

</script>

<style lang="scss">
.chat_clear_btn {
    width: 52px;
    height: 52px;
    background-color: #fff;

    .tooltip {
        display: none;
        width: 93px;
        height: 28px;
        background: #FFFFFF;
        box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.12);
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #262626;
        line-height: 10px;
        position: absolute;
        padding: 10px;
        margin-bottom: 104px;
        border-radius: 3px;
    }
}

.chat_clear_btn:hover {
    color: #436BFF;

    .tooltip {
        display: block;
    }
}
</style>