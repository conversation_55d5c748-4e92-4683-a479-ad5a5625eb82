<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close>
    <div class="dialog-content">
      <div class="left-list">
        <div class="field-group">
          <div class="field-item divider move" draggable="true" @dragstart="handleDragStart($event, 'Divider')">
            分组标题
          </div>
          <div v-for="field in availableFields.filter(x => x.isShow)" :key="field.id" class="field-item move"
            draggable="true" @dragstart="handleDragStart($event, field)">
            {{ field.fieldName }}
          </div>
        </div>
      </div>
      <div class="right-content" @dragover.prevent @drop="handleDrop">
        <el-alert title="从左侧拖拽字段或区块分割线到指定位置" type="info" show-icon />

        <div v-for="(field, index) in formFields" :key="field.id" class="form-field"
          :class="{ 'is-fixed': field.fixed, 'is-divider': field.fieldType === 'Divider' }" draggable="true"
          @dragstart="handleFormDragStart($event, index)" @dragover.prevent @drop="handleFormDrop($event, index)">
          <div class="field-content">
            <div :class="`field-label-container ${!field.fixed ? 'move' : ''}`">
              <el-icon v-if="!field.fixed">
                <dragIcon />
              </el-icon>
              <div v-if="field.fieldType === 'Divider'">
                <el-input v-model="field.fieldName" placeholder="请输入分组标题" class="divider-title-input"
                  v-if="field.inedit" @change="onChangeDividerTitle(field)" />
                <div class="divider-title-label" v-else>
                  <span class="field-label"> {{ field.fieldName }}</span>
                  <el-icon @click="editDividerTitle(field)" class="edit-icon">
                    <EditIcon />
                  </el-icon>
                </div>
              </div>
              <span class="field-label" v-else> {{ field.fieldName }}</span>
            </div>
            <div class="field-actions">
              <el-checkbox v-if="!field.fixed && field.fieldType != 'Divider'" v-model="field.isRequired"
                :true-value="1" :false-value="0" @change="onChangeFieldRequired(field)">必填</el-checkbox>
              <el-icon v-if="!field.fixed" @click="removeField(index)" class="icon_del">
                <Delete />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
  <ModalHint ref="refModalHint" />
</template>

<script setup>
import { getFormFields } from "@/js/api.js"
import { updateFormField, deleteFormField, addFormField } from "@/app_admin/tools/api.js";
import Modal from "@/components/Modal.vue";
import dragIcon from "@/icons/drag.vue";
import EditIcon from "@/icons/edit.vue";
import { Delete } from "@element-plus/icons-vue";
import ModalHint from "./ModalHint.vue";

const refModal = ref();
const refModalHint = ref()
const title = ref("");
const emit = defineEmits(["callback"]);
let formCode = "";
let pageData = ref({})
let isAddingNewField = false; // 添加标志位


// 固定字段
const fixedFields = [
  { fieldName: '客户名称', fixed: true, id: 'CUSTOMER_NAME' },
  { fieldName: '负责人', fixed: true, id: 'CUSTOMER_LEADER' },
];

const availableFields = ref([]);
const formFields = ref([...fixedFields]);

const cfg = {
  width: "800px",
  hide_footer: true,
};

const getFormFieldsList = () => {
  return new Promise((resolve, reject) => {
    getFormFields('CUSTOMER_TPL').then(res => {
      if (res.code == 0) {
        availableFields.value = res.data.map(x => ({ ...x, isShow: true }));
        resolve(true);
      } else {
        ElMessage.error(res.msg || '获取失败');
        reject(false);
      }
    })
  })
}

const getCustomerTypeSearch = () => {
  return new Promise((resolve, reject) => {
    getFormFields(formCode).then(res => {
      if (res.code == 0) {
        formFields.value = [...fixedFields];
        formFields.value.push(...res.data);
        const sourceIds = res.data.map(x => x.sourceId);
        availableFields.value = availableFields.value.map(x => ({ ...x, isShow: !sourceIds.includes(x.id) }));
        resolve(true);
      } else {
        ElMessage.error(res.msg || '获取失败');
        reject(false);
      }
    })
  })
}

const editDividerTitle = (field) => {
  field.inedit = true;
}

const show_edit = async (id, data) => {
  formCode = 'CUSTOMER_TYPE_' + id;
  pageData.value = { ...data };
  cfg["title"] = pageData.value.name + "创建与详情页配置";
  refModal.value.show(cfg);
  await getFormFieldsList();
  await getCustomerTypeSearch();
};

const cbModal = (action) => {
  if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  refModal.value.hide();
};

// 拖拽处理方法
const handleDragStart = (event, field) => {
  event.dataTransfer.setData('field', JSON.stringify(field));
  event.dataTransfer.setData('type', 'new');
};

const handleFormDragStart = (event, index) => {
  const field = formFields.value[index];
  if (fixedFields.some(f => f.id === field.id)) {
    event.preventDefault();
    ElMessage.warning('不能移动固定字段');
    return;
  }
  event.dataTransfer.setData('index', index);
  event.dataTransfer.setData('type', 'reorder');
};

// 公共的reorder方法
const handleReorder = (sourceIndex, targetIndex) => {
  console.log('上下移动 handleReorder', sourceIndex, targetIndex);
  // 检查目标位置是否在固定字段之前
  const isBeforeFixedFields = targetIndex < fixedFields.length;
  if (isBeforeFixedFields) {
    // ElMessage.warning('不能在固定字段之前添加新元素');
    // return;
  }

  const sourceField = formFields.value[sourceIndex];
  if (fixedFields.some(f => f.id === sourceField.id)) {
    // ElMessage.warning('不能移动固定字段');
    return;
  };

  const [movedField] = formFields.value.splice(sourceIndex, 1);
  formFields.value.splice(targetIndex, 0, movedField);

  // 更新所有字段的sortOrder
  const updatePromises = formFields.value.map((field, index) => {
    if (!field.id || fixedFields.some(f => f.id === field.id)) return null;
    const updatedField = { ...field, sortOrder: index };
    return updateFormField(field.id, updatedField);
  }).filter(Boolean);

  Promise.all(updatePromises).then(responses => {
    const hasError = responses.some(res => res.code !== 0);
    if (hasError) {
      ElMessage.error('部分字段排序更新失败');
    }
  });
};

const handleNew = (field) => {
  if (isAddingNewField) return; // 如果正在添加，直接返回
  isAddingNewField = true; // 设置标志位

  // const field = JSON.parse(event.dataTransfer.getData('field'));
  console.log('field--> ', field);
  // 检查目标位置是否在固定字段之前
  let targetIndex = formFields.value.length;
  const isBeforeFixedFields = targetIndex > 0 && targetIndex < fixedFields.length;
  if (isBeforeFixedFields) {
    targetIndex = -1;
    // ElMessage.warning('不能添加到固定字段之间');
    isAddingNewField = false; // 重置标志位
    return;
  }

  let newField = {}
  if (field === 'Divider') {
    newField = {
      fieldType: 'Divider',
      isRequired: 0,
      fieldName: '分组标题',
      fieldStatus: 1,
      sortOrder: targetIndex
    }
  } else {
    // 设置新添加字段的sortOrder为当前最大序号+1
    newField = {
      ...field,
      sourceId: field.id,
      sortOrder: formFields.value.length
    };
    delete newField.id;
  }
  addFormField(formCode, newField).then(res => {
    if (res.code == 0) {
      newField.id = res.data.id;  // 使用返回的id
      formFields.value.push(newField);
      for (let i = 0; i < availableFields.value.length; i++) {
        if (availableFields.value[i].id === field.id) {
          availableFields.value[i].isShow = false;
        }
      }
    } else {
      ElMessage.error(res.msg || '添加失败');
    }
    isAddingNewField = false; // 重置标志位
  }).catch(() => {
    isAddingNewField = false; // 确保在出错时也重置标志位
  })
}


const handleDrop = async (event) => {
  const type = event.dataTransfer.getData('type');
  console.log('handleDrop ', type);
  if (type === 'new') {
    const field = JSON.parse(event.dataTransfer.getData('field'));
    handleNew(field);
  } else if (type === 'reorder') {
    const sourceIndex = parseInt(event.dataTransfer.getData('index'));
    const targetIndex = formFields.value.length;
    handleReorder(sourceIndex, targetIndex);
  }
};

const handleFormDrop = (event, targetIndex) => {
  const type = event.dataTransfer.getData('type');
  console.log('handleFormDrop ', type);
  if (type === 'reorder') {
    const sourceIndex = parseInt(event.dataTransfer.getData('index'));
    handleReorder(sourceIndex, targetIndex);
  } else if (type === 'new') {
    const field = JSON.parse(event.dataTransfer.getData('field'));
    handleNew(field);
  }
};

const onChangeDividerTitle = (field) => {
  if (field.id) {
    updateFormField(field.id, field).then(res => {
      if (res.code == 0) {
        field.inedit = false;
      }
    })
  } else {
    const data = {
      fieldName: field.fieldName,
      fieldType: "Divider",
      isRequired: 0,
      sortOrder: formFields.value.length,
      fieldStatus: 1,
    }
    addFormField(formCode, data).then(res => {
      if (res.code == 0) {
        formFields.value = formFields.value.filter(x => (!!x.id && x.fieldType === 'Divider') || x.fieldType !== 'Divider')
        formFields.value.push(res.data);
      } else {
        ElMessage.error(res.msg || '添加失败');
      }
    })
  }
}

const onChangeFieldRequired = (field) => {
  console.log('onChangeFieldRequired', field);
  updateFormField(field.id, field).then(res => {
  })
}

const removeField = (index) => {
  const field = formFields.value[index];
  if (fixedFields.some(f => f.id === field.sourceId)) {
    return;
  }
  deleteFormField(field).then(res => {
    if (res.code == 0) {
      if (field.fieldType !== 'Divider') {
        // 找到对应的原始字段并添加回可用字段列表
        for (let i = 0; i < availableFields.value.length; i++) {
          if (availableFields.value[i].id === field.sourceId) {
            availableFields.value[i].isShow = true;
          }
        }
      }
      formFields.value.splice(index, 1);
    } else if (res.code == 3029) {
      refModalHint.value.show();
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  })
};

defineExpose({ title, show_edit, cbModal });
</script>

<style lang="scss" scoped>
.dialog-content {
  display: flex;
  min-height: 400px;


  .move {
    cursor: move;
  }

  .left-list {
    width: 200px;
    border-right: 1px solid #e4e7ed;
    padding: 14px 0;

    .field-group {
      .field-item {
        padding: 10px 20px;
        margin: 5px 0;
        background: #f5f7fa;
        user-select: none;
        border: none;

        &:hover {
          background: #ecf5ff;
          border-color: #409EFF;
        }
      }
    }
  }

  .right-content {
    flex: 1;
    padding: 20px;
    min-height: 400px;

    .el-alert {
      margin-bottom: 10px;
      background: #F0F6FF;
      color: #262626;
      font-size: 14px;

      :deep(.el-icon) {
        color: #436BFF;
      }
    }

    .move {
      cursor: move;
    }

    .divider-title-input {
      width: 180px;
    }

    .field-label-container {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;

      .field-label {
        font-size: 14px;
      }
    }

    .edit-icon {
      cursor: pointer;
      color: #436BFF;
      margin-left: 10px;
    }

    .form-field {
      padding: 10px;
      margin-bottom: 10px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      &.is-fixed {
        background: #f5f7fa;
        cursor: not-allowed;


      }

      &:hover {
        border-color: #436BFF;
      }

      .field-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .field-actions {
          display: flex;
          align-items: center;
          gap: 40px;

          .icon_del {
            cursor: pointer;
          }
        }
      }

      .divider-field {
        .divider-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .divider-title {
            width: 200px;
            margin-bottom: 10px;
          }

          .divider-btrn {
            display: flex;
            align-items: center;
            gap: 10px;
          }
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
  }
}
</style>
