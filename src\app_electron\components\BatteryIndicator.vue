<template>
    <div :class="`device-card-battery ts_${textSide}`" v-if="batteryPer > 0">
        <div v-if="textSide == 'top'" class="text">{{ batteryPer }}%</div>
        <div class="battery-bar">
            <div class="battery-level" :style="{ width: batteryPer * 0.94 + '%', background: batteryColor }"></div>
            <div class="battery-head"></div>
        </div>
        <div v-if="textSide == 'right'" class="text">{{ batteryPer }}%</div>
    </div>
</template>

<script setup>

const props = defineProps({
    textSide: {
        type: String,
        default: 'right'
    },
    batteryPer: {
        type: Number,
        required: true,
        default: -1
    }
});

const batteryColor = computed(() => {
    if (props.batteryPer <= 20) return '#ff4d4f';// 红色
    // if (props.batteryPer <= 60) return '#faad14';// 橙色
    return '#40D8B9';// 绿色
});
</script>

<style scoped lang="scss">
.ts_top {
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;

    .text {
        // margin-bottom: 6px;
    }
}

.ts_right {
    flex-direction: row;

    .text {
        margin-left: 6px;
    }
}

.device-card-battery {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .text {
        font-weight: 500;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        text-align: center;
        width: 27px;
    }

    .battery-bar {
        width: 24px;
        height: 11px;
        border: 1px solid rgba(0, 0, 0, 0.35);
        border-radius: 3px;
        position: relative;

        .battery-level {
            position: absolute;
            left: 1px;
            top: 1px;
            bottom: 1px;
            border-radius: 1px;
            transition: all 0.3s;
        }

        .battery-head {
            width: 2px;
            height: 6px;
            background: #666;
            position: absolute;
            right: -3px;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 0 1px 1px 0;
        }
    }
}
</style>