import { <PERSON>u, <PERSON><PERSON>erWindow, globalShortcut, shell } from 'electron'
import config from '../utils/config'
import { keyDevToolsOpen, getStore } from '../utils/store'

export function createContextMenu(win: BrowserWindow) {
    const isNeedDevTools = getStore(keyDevToolsOpen, false);
    if (isNeedDevTools) {
        const contextMenu = Menu.buildFromTemplate([
            {
                label: '开发者工具',
                click: () => {
                    win?.webContents.toggleDevTools()
                }
            },
            {
                label: '打开应用数据目录',
                click: () => {
                    shell.openPath(config.appPath)
                }
            }
        ])
        win.webContents.on('context-menu', (event) => {
            event.preventDefault()
            contextMenu.popup({ window: win! })
        })
    }

    if (config.isProd) {
        win.on('focus', () => {
            globalShortcut.register('CommandOrControl+Shift+alt+i+o+p', function () {
                if (!win.isDestroyed()) {
                    win?.webContents?.openDevTools();
                }
            })
        })
        win.on('blur', () => {
            globalShortcut.unregisterAll();
        })
    }
}
