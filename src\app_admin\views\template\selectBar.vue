<template>
    <div class="select_bar_wrap flex-row">
        <div class="hint" v-if="!hasData">请选择</div>
        <div v-else class="tags">
            <TagLabel ref="refTag" @callback="cbTag" />
        </div>
        <div class="right_dot">
            <div @click="onSelect">
                <el-icon>
                    <MoreFilled />
                </el-icon>
            </div>
        </div>
    </div>
</template>

<script setup>
import { MoreFilled } from "@element-plus/icons-vue"
import TagLabel from "./TagLabel.vue";

const emit = defineEmits(['callback'])
const hasData = ref(false)
const currData = ref({})
const refTag = ref();

const update = (data) => {
    currData.value = { ...{ docs: [], categories: [] }, ...data };
    _upHasData()
    if (hasData.value) {
        nextTick(() => {
            refTag.value.updateDoc(data['docs'])
            refTag.value.updateCate(data['categories'])
        })
    }
}

const _upHasData = () => {
    hasData.value = currData.value.docs.length > 0 || currData.value.categories.length > 0;
}

const cbTag = (action, data) => {
    if (action == "close_doc") {
        currData.value.docs = currData.value.docs.filter(x => x.id !== data.id)
        refTag.value.updateDoc(currData.value.docs);
    } else if (action == "close_cate") {
        currData.value.categories = currData.value.categories.filter(x => x.id !== data.id)
        refTag.value.updateCate(currData.value.categories);
    }
    emit('callback', 'update', currData.value)
    _upHasData()
}

const onSelect = () => {
    emit('callback', 'select', currData.value)
}

defineExpose({
    update,
    hasData,
    refTag,
    cbTag,
    TagLabel,
    MoreFilled,
    onSelect
})
</script>

<style lang="scss">
.select_bar_wrap {
    width: calc(100vw - 800px);
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #D9D9D9;
    position: relative;
    margin-top: 14px;

    .hint {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #BFBFBF;
        line-height: 22px;
        width: 200px;
        margin: 5px;
    }

    .tags {
        padding: 12px;
        max-height: calc(100vh - 415px);
        overflow: auto;

        .el-tag {
            margin: 7px;
        }
    }

    .right_dot {
        text-align: center;
        position: absolute;
        right: 0px;
        height: 100%;
        display: flex;
        align-items: center;
        color: #D9D9D9;
        cursor: pointer;
        width: 45px;
        justify-content: center;
    }
}
</style>