<template>
    <div class="login-box-h5">
        <div class="login-box-h5-bg" v-if="step == 1 && clientType != 'win'"></div>
        <div class="login-logo" v-if="step == 1"></div>
        <Login class="login" productCode="nova_guide" factorCode="nova_guide" :defaultLoginType="1" title="欢迎使用绚星销售助手"
            :deviceType="2" :defaultSSODomain="defaultSSODomain" @targetOrgInfo="getInfo" @stepChange="stepChange">
        </Login>
    </div>
</template>
<script>
import Login from "./LoginWebCore.vue"
import { getYxtProductAll } from '@/js/api_yxt'
import { getClientType } from '@/js/utils'
export default {
    name: 'LoginH5',
    components: {
        Login,

    },
    data() {
        return {
            clientType: getClientType(),
            defaultSSODomain: '',
            returnUrl: '',
            step: 1
        }
    },
    mounted() {
        const host = location.host;
        // meet?.yxt.com 是固定域名，不参与动态域名逻辑
        if ((host.indexOf('127.0.0.1') == -1 && host.indexOf('localhost') == -1) && host.indexOf('x-mate.com') == -1 && host.indexOf('yxt.com') == -1) {
            this.defaultSSODomain = location.host
        } else {
            this.defaultSSODomain = this.$route.query.origin || g.config.defaultSSODomain
        }
        this.returnUrl = this.$route.query.redirectUrl || g.config.ssoReturnUrl
    },
    methods: {
        getInfo(orgInfo) {
            console.log('getInfo', orgInfo)
            if (orgInfo.code) {
                if (this.returnUrl) {
                    this.redirect(orgInfo)
                } else {
                    getYxtProductAll().then(res => {
                        console.log('getYxtProductAll', res)
                        if (res.data.length > 0) {
                            for (let i = 0; i < res.data.length; i++) {
                                if (res.data[i].productCode === this.productCode) {
                                    this.returnUrl = res.data[i].homePath
                                }
                            }
                            this.redirect(orgInfo)
                        } else {
                            this.returnUrl = g.config.ssoReturnUrl
                            this.redirect(orgInfo)
                        }
                    }).catch(() => {
                        this.returnUrl = g.config.ssoReturnUrl
                        this.redirect(orgInfo)
                    })
                }
            } else {
                const url = `https://${orgInfo.origin}/m/#/login?productCode=nova_guide&platform=1&redirectUrl=${encodeURIComponent(this.returnUrl)}`
                // console.log('url11', url)
                window.location.href = url
            }
        },
        redirect(orgInfo) {
            let url = ''
            if (this.returnUrl.indexOf('?') > -1) {
                url = `${this.returnUrl}&code=${orgInfo.code}&e=${window.feConfig.apiEnv}&origin=${orgInfo.origin}`
            } else {
                url = `${this.returnUrl}?code=${orgInfo.code}&e=${window.feConfig.apiEnv}&origin=${orgInfo.origin}`
            }
            // console.log('url22', url)
            window.location.href = url
        },
        stepChange(step) {
            this.step = step
        }
    }
}
</script>
<style scoped lang="scss">
body {
    padding: 0;
    margin: 0;
}

.login-box-h5 {
    padding: 14px 1.15rem 30px;
    margin: 0;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    height: 100%;

    .login-box-h5-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('https://stc.yxt.com/ufd/989eac/login-h5-bk.png') no-repeat 0 0;
        background-size: 100%;
    }

    .login-logo {
        margin-top: 60px;
        margin-bottom: 24px;
        width: 48px;
        height: 40px;
        background: url('https://stc.yxt.com/assets/c626e3c1/8d6b435a/salesmatelogo.png') no-repeat 0 0;
        background-size: 100%;
    }

    .login {
        flex: 1;
    }
}

.mac-content {
    .login-box-h5 {
        height: calc(100vh - 42px);
    }
}

.win-content {
    .login-box-h5 {
        height: 100vh;
    }
}
</style>