<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="销售管理效能跃迁" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" opacity="0.800000012">
            <g id="我的沟通" transform="translate(-22, -801)">
                <g id="编组-15" transform="translate(0, 787)">
                    <g id="编组-12备份-2" transform="translate(10, 0)">
                        <g id="icon" transform="translate(12, 14)">
                            <polygon id="路径" points="0 0 20 0 20 20 0 20"></polygon>
                            <path
                                d="M10,18.3333333 C5.3975,18.3333333 1.66666667,14.6025 1.66666667,10 C1.66666667,5.3975 5.3975,1.66666667 10,1.66666667 C14.6025,1.66666667 18.3333333,5.3975 18.3333333,10 C18.3333333,14.6025 14.6025,18.3333333 10,18.3333333 Z M10,16.6666667 C13.6818983,16.6666667 16.6666667,13.6818983 16.6666667,10 C16.6666667,6.31810167 13.6818983,3.33333333 10,3.33333333 C6.31810167,3.33333333 3.33333333,6.31810167 3.33333333,10 C3.33333333,13.6818983 6.31810167,16.6666667 10,16.6666667 Z M9.16666667,12.5 L10.8333333,12.5 L10.8333333,14.1666667 L9.16666667,14.1666667 L9.16666667,12.5 Z M10.8333333,11.1291667 L10.8333333,11.6666667 L9.16666667,11.6666667 L9.16666667,10.4166667 C9.16666667,9.95642938 9.53976271,9.58333333 10,9.58333333 C10.6584595,9.583292 11.2039823,9.07245885 11.2472233,8.41542071 C11.2904643,7.75838257 10.8165945,7.1804612 10.1638197,7.09412496 C9.51104488,7.00778872 8.90322473,7.44264539 8.77416667,8.08833333 L7.13916667,7.76083333 C7.4195713,6.35937026 8.67395767,5.36737787 10.1023174,5.41751788 C11.5306771,5.46765789 12.7124174,6.54516615 12.8938612,7.96284159 C13.075305,9.38051702 12.2030247,10.7209129 10.8333333,11.1291667 L10.8333333,11.1291667 Z"
                                id="形状" fill="#FFFFFF"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'ClearIcon',
}
</script>
