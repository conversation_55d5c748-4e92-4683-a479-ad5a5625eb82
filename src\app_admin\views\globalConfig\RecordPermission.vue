<template>
  <div class="config-section">
    <div class="permission-item">
      <el-switch v-model="prohibitViewOthers" active-color="#409EFF" @change="onChange" />
      <span class="permission-label">禁止他人查看录制文件与字幕</span>
    </div>

    <div class="permission-group">
      <h4 class="sub-title">禁止范围</h4>
      <el-radio-group v-model="prohibitScope" @change="onChange" :disabled="!prohibitViewOthers">
        <el-radio :value="1">除创建人自己之外的其他人</el-radio>
        <el-radio :value="2">除创建人及其部门经理之外的其他人</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script setup>
import { getGlobalConfig, updateGlobalConfig } from "@/app_admin/tools/api";

// value:企业配置-记录查看权限 0 不限制 1 仅自己可见 2 仅自己及部门经理可见
const prohibitViewOthers = ref(false);
const prohibitScope = ref(1);

const onChange = () => {
  let value;
  if (prohibitViewOthers.value) {
    value = prohibitScope.value;
  } else {
    value = 0;
  }

  const param = {
    configCode: "xmate_record_permission",
    configValue: value,
  };
  updateGlobalConfig(param)
    .then((res) => {
      if (res.code === 0) {
        ElMessage.success("修改成功");
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((err) => {
      ElMessage.error("修改失败" + err);
    });
};

onMounted(() => {
  const param = {
    code: "xmate_record_permission",
  };
  getGlobalConfig(param).then((res) => {
    if (res.data) {
      const value = res.data.configValue;
      prohibitViewOthers.value = value != "0";
      prohibitScope.value = value === "2" ? 2 : 1;
    }
  });
});
</script>
<style lang="scss" scoped>
.config-section {
  background: #fff;
  border-radius: 4px;

  .permission-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .permission-label {
      margin-left: 12px;
    }
  }

  .permission-group {
    .sub-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 16px;
    }

    .el-radio {
      display: block;
      margin-bottom: 12px;
    }
  }
}
</style>
