<template>
  <div class="global-config">
    <el-collapse v-model="activeNames" v-if="isMainConfig">
      <el-collapse-item title="拜访记录查看权限" name="1">
        <RecordPermission />
      </el-collapse-item>
      <el-collapse-item title="参会人属性设置" name="2">
        <PersonFileds @callback="cbFunction" />
      </el-collapse-item>
      <el-collapse-item title="语音识别热词" name="3">
        <HotWords />
      </el-collapse-item>
    </el-collapse>
    <CourseField ref="refCourseField" v-else @callback="cbFunction" />
  </div>
</template>

<script setup>
import RecordPermission from "./RecordPermission.vue";
import PersonFileds from "./PersonFileds/PersonFileds.vue";
import CourseField from "./PersonFileds/CourseField.vue";
import HotWords from "./HotWords.vue";

const activeNames = ref(["1", "2", "3"]);
const isMainConfig = ref(true);
const refCourseField = ref(null);

const cbFunction = (action, data) => {
  if (action === "open_config") {
    isMainConfig.value = false;
    nextTick(() => {
      refCourseField.value.showConfig(data);
    });
  } else if (action === "back") {
    isMainConfig.value = true;
  }
};

defineExpose({
  refCourseField,
  CourseField,
  RecordPermission,
  PersonFileds,
});
</script>
<style lang="scss" scoped>
.global-config {
  height: calc(100vh - 75px);
  overflow: auto;

  .el-collapse {
    padding: 6px 24px;
  }
}
</style>
