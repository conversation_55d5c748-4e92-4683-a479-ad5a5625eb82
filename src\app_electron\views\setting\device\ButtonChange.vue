<template>
    <el-button type="primary" class="bind-button" @click="onChange">更换设备</el-button>
    <!-- 添加工牌设备弹框 -->
    <el-dialog v-model="showBindDialog" title="更换工牌设备" width="430px" :show-close="false"
        modal-class="bind-dialog-change">
        <div class="dialog-content">
            <div class="input-label">请输入工牌上的SN序列号：</div>
            <el-input v-model="snNumber" placeholder="SN-" class="sn-input" :formatter="(value) => `SN: ${value}`"
                :parser="(value) => value.replace(/\SN:\s?|(,*)/g, '')" />
            <div class="help-text">
                <div class="text">SN序列号可在工牌背面或包装盒上找到</div>

                <el-popover placement="bottom-end" :width="350" trigger="hover">
                    <template #reference>
                        <el-link type="primary" :underline="true">如何查找序列号？</el-link>
                    </template>
                    <div>
                        <img :src="getOssUrl('device_sn.jpg')" class="device-sn-img" />
                    </div>
                </el-popover>
            </div>
            <div class="unbind-tip">更换设备将解绑当前设备</div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="showBindDialog = false">取消</el-button>
                <el-button type="primary" @click="normalBind" :disabled="!isValidSN">确认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { getOssUrl } from '@/js/utils.js';
import { bindDevice } from '@/app_electron/tools/api.js';

const showBindDialog = ref(false);
const snNumber = ref('');
const emit = defineEmits(['callback']);
let forceBind = 0;
const onChange = () => {
    showBindDialog.value = true;
}

const isValidSN = computed(() => {
    return snNumber.value.length > 4;
})

const _openConfirm = () => {
    ElMessageBox.confirm(
        '该工牌已被其他用户绑定，是否要覆盖',
        '警告',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            forceBind = 1;
            confirmBind();
        })
        .catch(() => {
        })
}

const normalBind = () => {
    forceBind = 0;
    confirmBind();
}

// 确认绑定
const confirmBind = () => {
    if (snNumber.value === g.appStore.user.audioDeviceCode) {
        ElMessage.error('与当前设备相同，无需更换');
        return;
    }

    const param = {
        code: snNumber.value,
        type: "AISPEECH",
        forceBind
    }
    bindDevice(param).then(res => {
        if (res.code === 0) {
            showBindDialog.value = false;
            emit('callback', 'change', snNumber.value);
            snNumber.value = '';
        } else if (res.code == 3010) {
            _openConfirm();
        } else {
            ElMessage.error(res.message);
        }
    }).catch(() => {
        ElMessage.error('绑定失败，请稍后再试');
    })

};
defineExpose({
})

</script>

<style lang="scss" scoped>
.bind-dialog-change {
    .dialog-content {
        padding: 0 20px;

        .input-label {
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
        }

        .sn-input {
            margin-bottom: 8px;
        }

        .help-text {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;

            .text {
                color: #999;
            }
        }

        .unbind-tip {
            margin-top: 8px;
            font-size: 12px;
            color: #f56c6c;
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        gap: 12px;
        padding-top: 8px;
    }

    .el-dialog__header {
        margin-right: 0;
        text-align: center;
    }
}

.device-sn-img {
    width: 100%;
    height: 100%;
}
</style>