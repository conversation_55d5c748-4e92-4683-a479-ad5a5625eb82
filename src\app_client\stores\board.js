import { defineStore } from "pinia";
import {
  getReportSubTeam,
  getReportSubUser,
  getSalesCompare,
  getReportSubUserPager,
  getAssessment,
  getTaskCompleteRatio,
} from "@/app_client/tools/api.js";
import { getDefaultPeriodType } from "@/app_client/tools/utils.js";
import {
  round,
  getUrlParam,
  formatDate,
  getDateByTimeUnit,
} from "@/js/utils.js";
import { getReportDate } from "@/app_client/components/TeamBoardCore/misc.js";
const _avgOverviewData = (overviewData, count = 0) => {
  const columns = [
    "totalVisitPlanCount",
    "visitPlanCustomerCount",
    "completedVisitPlanCount",
    "totalVisitCount",
    "visitedCustomerCount",
    "salesAbilityPassCount",
  ];
  if (count > 0) {
    for (const column of columns) {
      overviewData[column] = round(overviewData[column] / count, 2);
    }
  }
  return overviewData;
};

export default defineStore("client_board", {
  state: () => ({
    boardPage: "VisitSituation", //VisitSituation,  SalesPerformance
    periodType: getDefaultPeriodType(), //weekly,monthly,quarterly,yearly
    category: "visitPlan",
    dimension: "",
    tableType: "",
    overviewDept: {},
    overviewData: {},
    regionType: "", //dept,user,or empty
    regionData: {},
    regionDept: {},
    topicData: [],
    reportDateTitle: "",
    perfType: "ability",
    perfTypeRanking: "ability",
    perfTypeSale: "ability",
    perfTypeAssessment: "ability",
    perfTypeTrend: "ability",
    perfTypeContrast: "ability",
    perfRankRowData: {},
    perfTeamType: "department", //user,department
    perfAllDims: {},
    perfTypeOptions: [
      {
        label: "能力评估",
        value: "ability",
      },
      {
        label: "任务达成",
        value: "taskCompletion",
      },
    ],
    standardSetting: {},
  }),
  getters: {
    getMemberOverviewData_average(state) {
      let overviewData = { ...state.overviewData };
      const count = state.overviewData?.teamMemberCount;
      overviewData = { ..._avgOverviewData(overviewData, count) };
      return overviewData;
    },
  },
  actions: {
    setPerfType(perfType) {
      this.perfType = perfType;
    },
    getPeriodType() {
      return this.periodType;
    },
    getTopDeptId() {
      return this.overviewDept?.value || "";
    },
    overviewHasSubDept() {
      return (
        this.overviewDept.children && this.overviewDept.children.length > 0
      );
    },
    regionHasSubDept() {
      return this.regionDept.children && this.regionDept.children.length > 0;
    },
    detailNeedShowTeam() {
      if (this.regionType === "dept") {
        return this.regionHasSubDept();
      } else if (this.regionType === "") {
        return this.overviewHasSubDept();
      }
      return false;
    },
    detailNeedShowMember() {
      if (this.regionType === "user") {
        return this.regionHasSubDept();
      }
      return false;
    },
    getUserTableDeptId() {
      if (this.regionType === "dept") {
        return this.regionDept.value;
      } else if (this.regionType === "user") {
        return this.regionData.deptId;
      } else {
        return this.overviewDept.value;
      }
    },
    getTeamTableDeptId() {
      if (this.regionType === "dept") {
        return this.regionDept.value;
      } else if (this.regionType === "user") {
        return this.regionDept.value;
      } else {
        return this.overviewDept.value;
      }
    },
    getTeamOverviewData(average = false) {
      let overviewData = {};
      let count = 0;
      if (this.regionType === "") {
        overviewData = { ...this.overviewData };
        count = this.overviewDept.children?.length + 1;
      } else {
        overviewData = { ...this.regionData.data };
        count = this.regionDept.children?.length + 1;
      }
      if (average) {
        overviewData = _avgOverviewData(overviewData, count);
      }
      return overviewData;
    },
    getMemberOverviewData(average = false) {
      let overviewData = { ...this.overviewData };
      const count = this.overviewData?.teamMemberCount;
      if (average) {
        overviewData = { ..._avgOverviewData(overviewData, count) };
      }
      return overviewData;
    },
    getRightChartType() {
      if (this.regionType === "user") {
        const field = this.dimension;
        if (field === "salesAbilityPassRate") {
          return "sales_ability_radar";
        } else if (field === "taskCompletePassRate") {
          return "task_completion";
        } else if (field === "visitTargetAchievementRate") {
          return "ai_evaluation";
        }
      }
      return "";
    },
    getReportSubTeamWrap(dptId, periodType, reportDate) {
      return new Promise(async (resolve, reject) => {
        const res = await getReportSubTeam(dptId, periodType, reportDate);
        if (res.code === 0 && res.data.datas?.length > 0) {
          return resolve(res.data.datas);
        }
        return resolve([]);
      });
    },
    getReportSubUserWrap(dptId, periodType, reportDate) {
      return new Promise(async (resolve, reject) => {
        const res = await getReportSubUser(dptId, periodType, reportDate);
        if (res.code === 0 && res.data.datas?.length > 0) {
          return resolve(res.data.datas);
        }
        return resolve([]);
      });
    },

    getPerfQueryTime() {
      let startTime = "";
      let endTime = "";
      if (getUrlParam("startDate") && getUrlParam("endDate")) {
        startTime = getUrlParam("startDate");
        endTime = getUrlParam("endDate");
      } else {
        const { startDate, endDate } = getDateByTimeUnit(
          g.clientBoardStore.periodType.replace("ly", "")
        );
        startTime = startDate;
        endTime = endDate;
      }
      startTime = formatDate(startTime, "yyyy-MM-dd") + " 00:00:00";
      endTime = formatDate(endTime, "yyyy-MM-dd") + " 23:59:59";
      return { startTime, endTime };
    },
    apiGetSalesCompare(ssoUserIds, perfType) {
      return new Promise(async (resolve, reject) => {
        try {
          const orgId = g.appStore.user.orgId;
          const deptId = this.overviewDept.value;
          if (!orgId || !deptId || !ssoUserIds) {
            console.log(
              "sales compare vs table init no orgId or deptId or ssoUserIds",
              orgId,
              deptId,
              ssoUserIds
            );
            return resolve({});
          }
          console.log("sales compare vs table init");
          const dataType = perfType == "ability" ? "ABILITY" : "COMPLETION";
          const { startTime, endTime } = this.getPerfQueryTime();
          const parm = {
            startTime,
            endTime,
            reportType: this.periodType,
            ssoUserIds,
          };
          const res = await getTaskCompleteRatio(orgId, deptId, dataType, parm);
          resolve(res);
        } catch (error) {
          reject(error);
        }
      });
    },
    apiGetAssessment(ssoUserId) {
      return new Promise(async (resolve, reject) => {
        try {
          const orgId = g.appStore.user.ssoOrgId;
          // const ssoUserId = analyseUser.value.ssoUserId;
          const startTime = getUrlParam("startDate");
          const endTime = getUrlParam("endDate");
          const param = {};
          if (startTime && endTime) {
            param.startTime = startTime;
            param.endTime = endTime;
          }
          const res = await getAssessment(
            this.periodType,
            orgId,
            ssoUserId,
            "ABILITY",
            param
          );
          let radarData = [];
          if (res.code === 0 && res.data?.datas && res.data.datas.length > 0) {
            radarData = res.data?.datas.map((x) => {
              return {
                label: x.assessmentName,
                value: round(x.assessmentValue, 0),
              };
            });
          } else {
            radarData = [];
          }
          resolve(radarData);
        } catch (error) {
          reject(error);
        }
      });
    },
    apiGetReportSubUserPager(ssoUserId) {
      return new Promise(async (resolve, reject) => {
        try {
          const deptId = this.getUserTableDeptId();
          const param = {
            ssoUserIds: [ssoUserId],
          };
          console.log(11, deptId);
          getReportSubUserPager(
            deptId,
            this.periodType,
            param,
            getReportDate()
          ).then((res) => {
            if (res.code === 0 && res.data.datas?.length > 0) {
              return resolve(res.data.datas[0]);
            }
            resolve({});
          });
        } catch (error) {
          reject(error);
        }
      });
    },
    getApiParam(perfType) {
      const orgId = g.appStore.user.orgId;
      const deptId = this.overviewDept.value;
      const dataType = perfType == "ability" ? "ABILITY" : "COMPLETION";
      const { startTime, endTime } = this.getPerfQueryTime();
      const periodType = this.periodType;
      return {
        orgId,
        deptId,
        dataType,
        startTime,
        endTime,
        periodType,
      };
    },
  },
});
