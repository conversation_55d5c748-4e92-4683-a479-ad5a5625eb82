<template>
    <div class="flex-row">
        <el-radio-group v-model="localPerfType" @change="handleTypeChange">
            <el-radio-button value="ability">能力评估</el-radio-button>
            <el-radio-button value="taskCompletion">任务达成</el-radio-button>
        </el-radio-group>
    </div>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: String,
        default: 'ability'
    }
});

const emit = defineEmits(['update:modelValue', 'change']);
const localPerfType = ref(props.modelValue);

watch(() => props.modelValue, (newVal) => {
    g.clientBoardStore.setPerfType(newVal)
    localPerfType.value = newVal;
});

const handleTypeChange = (value) => {
    emit('update:modelValue', value);
    emit('change', value);
};
</script>
<style lang="scss" scoped>
.radio-group {
    height: 32px;

    :deep(.el-radio-button__inner) {
        font-weight: 400;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
    }
}
</style>