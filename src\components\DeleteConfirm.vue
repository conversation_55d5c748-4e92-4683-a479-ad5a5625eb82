<template>
    <el-dialog :title="title" v-model="visible" width="400px" :show-close="true" align-center @close="handleClose"
        modal-class="delete-confirm-dialog">
        <template #header>
            <div class="header-container">
                <div class="warning-icon">
                    <el-icon color="#ff4d4f" :size="24">
                        <WarningFilled />
                    </el-icon>
                </div>
                <div class="title">{{ title }}</div>
            </div>
        </template>
        <div class="content">{{ content }}</div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">{{ cancelText }}</el-button>
                <el-button type="danger" @click="handleConfirm">{{ confirmText }}</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'

const props = defineProps({
    title: {
        type: String,
        default: '确认删除'
    },
    content: {
        type: String,
        default: '删除后将无法恢复文件'
    },
    cancelText: {
        type: String,
        default: '取消'
    },
    confirmText: {
        type: String,
        default: '删除'
    }
})

const emit = defineEmits(['confirm', 'cancel'])
const visible = ref(false)

const handleConfirm = () => {
    emit('confirm')
    visible.value = false
}

const handleCancel = () => {
    emit('cancel')
    visible.value = false
}

const handleClose = () => {
    visible.value = false
}

const show = () => {
    visible.value = true
}

defineExpose({
    show
})
</script>

<style lang="scss">
.delete-confirm-dialog {
    .el-dialog {
        .el-dialog__header {
            padding: 0;
            margin: 0;
            border-bottom: none !important;

            .header-container {
                padding: 20px 20px 0;
                display: flex;
                text-align: flex-start;

                .title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                    text-align: center;
                    margin-left: 9px;
                }

                .warning-icon {
                    margin: 0;
                }

                .close-icon {
                    cursor: pointer;
                    color: #909399;

                    &:hover {
                        color: #333;
                    }
                }
            }
        }

        .el-dialog__body {
            padding: 0;

            .content {
                padding: 30px 20px 24px;
                font-size: 14px;
                color: #333;
                line-height: 1.2;
                margin-left: 33px;
            }
        }

        .el-dialog__footer {
            padding: 0 20px;
            border-top: none !important;

            .dialog-footer {
                display: flex;
                gap: 16px;
                justify-content: flex-end;
                padding: 0 20px 24px;

                :deep(.el-button) {
                    min-width: 80px;
                    margin: 0;
                }
            }
        }
    }

    :deep(.el-dialog__headerbtn) {
        display: none;
    }
}
</style>