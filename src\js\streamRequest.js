import { getYxtOrgDomain } from "@/js/utils";

export default function fetchWithTimeout(url, options, timeout = 4 * 60 * 1000) {
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), timeout),
    ),
  ]);
}

function splitData(text) {
  const lines = text.split("\n");
  const result = [];

  let currentRecord = "";
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (line.startsWith("data:")) {
      if (currentRecord !== "") {
        result.push(currentRecord.trim());
      }
      currentRecord = line;
    } else {
      currentRecord += line;
    }
  }

  if (currentRecord !== "") {
    result.push(currentRecord.trim());
  }
  return result;
}
let recordId = "";
let currNodeInstanceId = "";

export const initConversation = () => {
  recordId = "";
  currNodeInstanceId = "";
};

function convertAnswer(resp) {
  let txt = "";
  try {
    resp = splitData(resp);
    resp = resp.filter((x) => !!x);
    for (let b of resp) {
      try {
        b = JSON.parse(b.replace("data:", ""));
        if (!!b.choices && !!b.choices[0]["delta"]["content"]) {
          txt += b.choices[0]["delta"]["content"];
        } else {
          if (b.code == "AI_532") {
            recordId = b.recordId;
            currNodeInstanceId = b.data;
          }
        }
      } catch (e1) {
        // console.log('error1', e1)
      }
    }
  } catch (e2) {
    // console.log('error2', e2)
  }

  txt = txt.replace(/\n\n/g, "\n").replace(/\n/g, "<br />");
  return txt;
}

let controller;
let signal;

export function abortRequest() {
  try {
    !!controller && controller.abort("user cancel");
  } catch (e) {
    console.log("abortRequest error", e);
  }
}

export async function askChatGpt(param) {
  let response;
  const url = `${g.config.meetApiHost}/rest/api/aichat/configs/prompt/assistant`;
  controller = new AbortController();
  signal = controller.signal;
  if (recordId) {
    param.recordId = recordId;
  }
  if (currNodeInstanceId) {
    param.currNodeInstanceId = currNodeInstanceId;
    param.processStatus = "continue";
  }
  response = await fetchWithTimeout(url, {
    method: "post",
    signal,
    headers: {
      stream: true,
      token: g.appStore.user.token,
      "yxt-orgdomain": getYxtOrgDomain(),
    },
    body: JSON.stringify(param),
  });

  const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();

  let answer = "";
  let lastIdx = 0;
  while (true) {
    const { value, done } = await reader.read();
    answer += value;
    const resp = convertAnswer(answer);
    const new_resp = resp.substring(lastIdx);
    if (new_resp) {
      // console.log('新的答案：', new_resp)
      g.emitter.emit("gpt_resp", new_resp);
    }
    lastIdx += new_resp.length;
    if (done) {
      break;
    }
  }
  return "";
}
