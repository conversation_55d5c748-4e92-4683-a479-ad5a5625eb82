<template>
    <div class="list-filter">
        <div class="title">查看范围</div>
        <div class="filter-options">
            <div class="filter-item" @click="handleSelect1()">
                <div class="circle  circle1">
                    <SuccessFilled v-if="selected1" />
                </div>
                <span>未完成拜访</span>
            </div>
            <div class="filter-item" @click="handleSelect2()">
                <div class="circle circle2">
                    <SuccessFilled v-if="selected2" />
                </div>
                <span>已完成拜访</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { SuccessFilled } from '@element-plus/icons-vue'

const selected1 = ref(true)
const selected2 = ref(true)

const emit = defineEmits(['callback'])

const handleSelect1 = () => {
    if (selected1.value && !selected2.value) {
        selected1.value = false;
        selected2.value = true;
    } else {
        selected1.value = !selected1.value;
    }
    handleSelect()
}

const handleSelect2 = () => {
    if (selected2.value && !selected1.value) {
        selected2.value = false;
        selected1.value = true;
    } else {
        selected2.value = !selected2.value;
    }
    handleSelect()
}

const handleSelect = () => {
    let value = null;
    if (selected1.value && !selected2.value) {
        value = false
    } else if (!selected1.value && selected2.value) {
        value = true
    }
    emit('callback', value)
}

defineExpose({
    selected1,
    selected2
})
</script>

<style lang="scss" scoped>
.list-filter {
    padding: 16px;

    .title {
        font-size: 14px;
        color: #333;
        margin-bottom: 12px;
    }

    .filter-options {
        .filter-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            cursor: pointer;

            .circle {
                width: 22px;
                height: 22px;
                border-radius: 50%;
                background: #fff;
                margin-right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .circle1 {
                border: 1px solid #FF6634;
                color: #FF6634;
            }

            .circle2 {
                border: 1px solid #67C23A;
                color: #67C23A;
            }
        }
    }
}
</style>
