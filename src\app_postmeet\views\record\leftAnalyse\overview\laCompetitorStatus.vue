<template>
  <div class="flex-column">
    <div class="la_com_status_wrap">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">竞争情况</div>
        </div>
        <div class="la_detail" @click="onDetail('competitor')" v-if="list.length > 0">
          详情
        </div>
      </div>
      <div class="la_tags flex-row" v-if="list.length > 0">
        <div :class="`tag ${recordViewPermission ? 'tag_enable' : ''}`" v-for="item in list"
          @click="onClickCompetitor(item)">
          {{ `${item.competitor.commonName}(${item.count})` }}
        </div>
      </div>
      <div v-else class="no_data_hint">未提及竞争对手</div>
    </div>
    <div class="la_com_status_wrap">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">风险项</div>
        </div>
        <div class="la_detail" @click="onDetail('risk')" v-if="list_risk.length > 0">
          详情
        </div>
      </div>
      <div class="la_tags flex-row" v-if="list_risk.length > 0">
        <div :class="`tag ${recordViewPermission ? 'tag_enable' : ''}`" v-for="item in list_risk"
          @click="onClickRisk(item)">
          {{ `${item.name}(${item.count})` }}
        </div>
      </div>
      <div v-else class="no_data_hint">无识别风险项</div>
    </div>

  </div>
</template>

<script>
import { getLaCompetitorStatus } from "@/app_postmeet/tools/sale_report.js";
export default {
  data() {
    return {
      list: [],
      list_risk: [],
      activeName: "概览",
      recordViewPermission: false,
      tags: ["概览", "客户态度", "风险项"],
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    init() {
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      [this.list, this.list_risk] = getLaCompetitorStatus();
    },
    onDetail(tag) {
      g.emitter.emit("update_analyse_menu", tag);
    },
    onClickCompetitor(item) {
      const ar = g.postmeetStore.data.saleAnalyseList["竞争对手"];
      const row = ar.find((x) => x.label == item.competitor.commonName);
      g.emitter.emit("setAnalyseTag", [row, 1, "竞争对手"]);
    },
    onClickRisk(item) {
      const ar = g.postmeetStore.data.saleAnalyseList["风险项"];
      const row = ar.find((x) => x.label == item.name);
      g.emitter.emit("setAnalyseTag", [row, 1, "风险项"]);
    },
  },
};
</script>

<style lang="scss">
.la_com_status_wrap {
  background: #f9fafc;
  border-radius: 8px;
  padding: 20px;
  margin: 12px 0;

  .la_header {
    justify-content: space-between;

    .la_left {
      width: 300px;

      .la_title {
        width: 96px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #262626;
        line-height: 26px;
        text-align: left;
        font-style: normal;
      }
    }

    .la_detail {
      height: 22px;
      font-size: 14px;
      color: #436bff;
      line-height: 22px;
      cursor: pointer;
    }
  }

  .la_tags {
    margin: 12px 0;

    .tag {
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      height: 24px;
      font-size: 14px;
      color: #262626;
      line-height: 24px;
      margin-right: 12px;
      padding: 5px 12px;
    }

    .tag_enable {
      cursor: pointer;
    }

    .tag:hover {
      border: 1px solid #436bff;
      color: #436bff;
    }
  }

  .no_data_hint {
    margin: 20px 0;
  }
}
</style>
