<template>
  <div class="manage-container">
    <div v-if="error != ''" class="error">
      {{ error }}
    </div>
    <iframe :src="iframeSrc" frameborder="0" class="manage-iframe" v-else></iframe>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { getYxtAuthCode } from "@/js/api_yxt.js"

const route = useRoute()
const iframeSrc = ref('')
const error = ref('')

// URL 路径映射配置
const urlPathMap = {
  employee: '/udp/#/nova_guide/orgoumgmt',
  position: '/udp/#/nova_guide/positionmgmt',
  role: '/#/nova_guide/role/mgmt',
  department: '/udp/#/nova_guide/department',
  account: '/udp/#/nova_guide/account/recovery',
  rank: '/udp/#/nova_guide/position',
  extendfield: '/udp/#/nova_guide/orgoumgmt/extendfield'
}

// 根据页面参数获取对应的 iframe src
const getIframeSrc = async (page) => {
  try {
    const baseUrl = `https://${g.appStore.user.ssoDomain}`
    const returnUrl = urlPathMap[page] ? `${baseUrl}${urlPathMap[page]}` : ''

    if (!returnUrl) {
      error.value = '无效的页面路径'
      return
    }

    const { ssoOrgId, name, ssoUserId } = g.appStore.user
    // 构建认证参数
    const authParams = {
      returnUrl: encodeURIComponent(returnUrl),
      orgId: ssoOrgId,
      username: name,
      userId: ssoUserId,
      clientType: 0,
      loginMode: 2
    }

    // 重置状态
    iframeSrc.value = ''
    error.value = ''

    // 获取认证码并构建 iframe URL
    const authResponse = await getYxtAuthCode(authParams)
    const iframeUrl = `${baseUrl}/auth/sso?c=0&redirectUrl=${authParams.returnUrl}&code=${authResponse.code}&decode=1&productCode=${g.config.productCode}`
    iframeSrc.value = iframeUrl
  } catch (err) {
    error.value = '暂无权限'
    console.error('获取认证码失败:', err)
  }
}

// 监听路由参数变化
watch(() => route.path, (newQuery) => {
  const paths = newQuery.split('/');
  // 假设路由参数中有一个名为 'page' 的参数用来指定要显示的页面
  if (paths && paths.length > 2) {
    getIframeSrc(paths[2])
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.manage-container {
  width: 100%;
  height: 100%;

  .error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: red;
}

.manage-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
}


</style>
