<template>
    <el-drawer v-model="isShow" :with-header="true" size="640" show-close @closed="onClose"
        class="drawer_requirement-insight-wrap" direction="rtl">
        <template #header>
            <div class="vd_title flex-row">
                <div class="vd_title_name">详情</div>
            </div>
        </template>
        <div class="table-container">
            <el-table :data="data" style="width: 100%">
                <el-table-column fixed prop="name" label="人员" min-width="120" />
                <el-table-column prop="deptName" label="部门" min-width="300" />
                <el-table-column prop="abilityScore" label="能力评估" min-width="120" sortable />
                <el-table-column prop="taskCompleteRatio" label="任务达成" min-width="120" sortable />
            </el-table>
        </div>


        <template #footer>
            <el-button type="primary" @click="onClose">我知道了</el-button>
        </template>
    </el-drawer>
</template>

<script setup>
const props = defineProps({
    data: {
        type: Array,
        default: () => ([])
    }
})
const emit = defineEmits(['onClose'])
const isShow = ref(true);
const tableData = ref([]);
const onClose = () => {
    isShow.value = false;
    nextTick(() => {
        emit('onClose');
    })
};
</script>

<style lang="scss">
.drawer_requirement-insight-wrap {
    .el-drawer__header {
        padding: 16px 28px 16px 4px !important;
        height: auto;

        .vd_title {
            .vd_title_name {
                color: #262626;
                font-weight: 700;
            }
        }
    }

    .el-drawer__footer {
        padding: 12px 24px;
        box-shadow: inset 0px 1px 0px 0px #e9e9e9;

        .el-button--primary {
            margin-left: 16px;
        }
    }

    .el-drawer__body {
        margin-bottom: 0 !important;
        padding: 24px !important;
    }

    .table-container {
        overflow-x: auto;
        width: 100%;

        thead {
            background-color: #E9E9E9;
            color: #262626;

            th {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }



}
</style>