import fs from 'fs'
import path from 'path'
import axios from 'axios'
import { app } from 'electron'
import extract from 'extract-zip'
import { setStore, keyWebResourcePath, keyLocalVersionInfo } from './store'
import { getUpgradeBaseUrl, getLocalResourceInfo, getStorePrefix } from './tools'

interface VersionInfo {
  version: string
  buildTime: string
}

export class WebResourceManager {
  private baseUrl = getUpgradeBaseUrl() + '/electron'
  private resourcePath: string
  private currentVersion: string

  constructor(currentVersion: string) {
    this.currentVersion = currentVersion
    this.resourcePath = path.join(app.getPath('userData'), getStorePrefix() + 'web-resources')
    if (!fs.existsSync(this.resourcePath)) {
      fs.mkdirSync(this.resourcePath, { recursive: true })
    }
  }

  async checkUpdate(): Promise<{ needUpdate: boolean; remoteInfo?: VersionInfo }> {
    try {
      const localInfo = getLocalResourceInfo()
      console.log('checkUpdate localInfo', localInfo)
      setStore(keyLocalVersionInfo, localInfo)
      const response = await axios.get<VersionInfo>(`${this.baseUrl}/${localInfo.version}/version.json`, {
        timeout: 5000,
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      const remoteInfo = response.data
      console.log('checkUpdate remoteInfo', remoteInfo)
      const needUpdate = remoteInfo.version >= localInfo.version && remoteInfo.buildTime > localInfo.buildTime;

      return {
        needUpdate,
        remoteInfo: needUpdate ? remoteInfo : undefined
      }
    } catch (error) {
      console.error('Check update failed:', error)
      return { needUpdate: false }
    }
  }

  async downloadAndExtract(version: string): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/${version}/${version}.zip`, {
        responseType: 'arraybuffer'
      })

      const zipPath = path.join(this.resourcePath, `${version}.zip`)
      fs.writeFileSync(zipPath, response.data)

      const extractPath = path.join(this.resourcePath, version)
      // 如果目标目录已存在，先删除
      if (fs.existsSync(extractPath)) {
        fs.rmSync(extractPath, { recursive: true, force: true })
      }

      await extract(zipPath, { dir: extractPath })

      // 检查解压后的目录结构是否正确
      const distPath = path.join(extractPath, 'dist')
      const indexPath = path.join(distPath, 'index.html')
      if (!fs.existsSync(distPath) || !fs.existsSync(indexPath)) {
        throw new Error('Invalid zip file structure')
      }

      // 获取远程的 version.json
      const versionResponse = await axios.get(`${this.baseUrl}/${version}/version.json`)
      const versionInfo = versionResponse.data

      // 直接写入 version.json 到本地
      const localVersionPath = path.join(this.resourcePath, 'version.json')
      fs.writeFileSync(localVersionPath, JSON.stringify(versionInfo, null, 2))

      // 清理临时文件
      if (fs.existsSync(zipPath)) {
        fs.unlinkSync(zipPath)
      }

      return true
    } catch (error) {
      console.error('Download and extract failed:', error)
      return false
    }
  }


  // 静态方法
  static getInstance(): WebResourceManager {
    return new WebResourceManager(app.getVersion())
  }

  // 更新资源
  async updateResource(): Promise<boolean> {
    return await this.downloadAndExtract(this.currentVersion)
  }

  // 检查并执行更新
  async checkAndUpdate(): Promise<boolean> {
    const { needUpdate, remoteInfo } = await this.checkUpdate()

    if (needUpdate && remoteInfo) {
      const success = await this.downloadAndExtract(remoteInfo.version)
      if (success) {
        return true
      }
    }
    return false
  }

  // 新增一个方法用于获取完整的加载路径
  getLoadPath(hash: string = ''): { filePath: string; isFile: boolean } {
    const { indexPath } = getLocalResourceInfo()
    let pathInfo = {
      filePath: '',
      isFile: false
    }

    if (app.isPackaged) {
      pathInfo = {
        filePath: indexPath,
        isFile: true
      }
    } else {
      // 确保 hash 不以 / 开头，避免双斜杠
      const cleanHash = hash.startsWith('/') ? hash.substring(1) : hash
      pathInfo = {
        filePath: cleanHash ?
          `${process.env.VITE_DEV_SERVER_URL}#/${cleanHash}` :
          process.env.VITE_DEV_SERVER_URL,
        isFile: false
      }
    }
    console.log('getLoadPath', pathInfo)
    return pathInfo
  }

  // 添加检查并执行热更新的方法
  async checkAndApplyHotUpdate(window: Electron.BrowserWindow): Promise<void> {
    if (!app.isPackaged) return

    const { needUpdate } = await this.checkUpdate()
    console.log('checkWebVersion', needUpdate)

    if (needUpdate) {
      const success = await this.updateResource()
      console.log('updateWebResource', success)

      if (success) {
        // 把新的资源路径保存到 store
        const newResourcePath = path.join(this.resourcePath, this.currentVersion, 'dist')
        setStore(keyWebResourcePath, newResourcePath)

        // 重新加载新版本资源
        const { filePath } = this.getLoadPath()
        window.loadFile(filePath)
      }
    }
  }
}
