import { app, BrowserWindow } from 'electron'
import os from 'node:os'
import config from '../utils/config'
import { macInstall, killVirtualAudio } from '../utils/macInstall'
import { startMainWindow } from './createMainWindow'
import { createSubWindows } from './listenSubWindow'
import { handleProtocolUrl, getSystemInfo } from '../utils/tools'
import { setStore, getStore, keySystemInfo } from '../utils/store'
import { errorWriteLocal } from '../utils/errorWriteLocal'
import windowManager from './windowManager'

// Disable GPU Acceleration for Windows 7
if (os.release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

app.commandLine.appendSwitch('--disable-gpu');
app.commandLine.appendSwitch('ignore-certificate-errors')
app.commandLine.appendSwitch('--force-fieldtrials', "WebRTC-SpsPpsIdrIsH264Keyframe/Enabled/");
app.commandLine.appendSwitch('disable-features', 'IOSurfaceCapturer');
app.commandLine.appendSwitch('disable-features', 'DesktopCaptureMacV2,IOSurfaceCapturer');
app.commandLine.appendSwitch('no-sandbox');

if (!app.requestSingleInstanceLock()) {
    app.quit()
    process.exit(0)
}

app.on('window-all-closed', (event) => {
    if (config.isMac) {
        // 清理虚拟音频进程
        killVirtualAudio();
    } else {
        event.preventDefault();
    }

    // 只清理窗口资源，不退出应用
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
        if (!window.isDestroyed()) {
            window.destroy();
        }
    });
});

app.on('activate', () => {
    const allWindows = BrowserWindow.getAllWindows()
    console.log('allWindows activate', allWindows.length)
    try {
        const main = windowManager.getWindow('main');
        main.window.focus()
        main.window.show()
    } catch (e) {
        startMainWindow()
    }
})

app.on('ready', function () {
    if (config.isMac) {
        macInstall()
    }
    const systemInfo = getStore(keySystemInfo)
    if (!systemInfo) {
        getSystemInfo().then((res) => {
            setStore(keySystemInfo, res)
        })
    }
})

// var url = `yxtnovaguide://action=meet&data=${Base64.encode(JSON.stringify(plan))}`
app.on('second-instance', (_event, _argv) => {
    handleProtocolUrl(_argv, createSubWindows, startMainWindow)
})

// macOS
app.on('open-url', (_event, urlStr) => {
    handleProtocolUrl([urlStr], createSubWindows, startMainWindow)
});

// 添加正常退出事件处理
app.on('before-quit', (event) => {
    // 在这里做退出前的清理工作
    if (config.isMac) {
        killVirtualAudio();
    }
});


process.on('uncaughtException', (_e) => {
    errorWriteLocal('uncaughtException', _e.message)
})

app.on('render-process-gone', (_event, _webContents, details) => {
    errorWriteLocal('render-process-gone error=>', details)
})
app.on('child-process-gone', (_event, details) => {
    errorWriteLocal('child-process-gone error=>', details)
})

const createHideWindow = () => {
    const arg = {
        urlParam: {
            id: 'hide_window',
            url: '/electron/hide_window',
        },
    }
    createSubWindows(JSON.stringify(arg))
}

app.whenReady().then(() => {
    console.log('app ready')
    createHideWindow()
})