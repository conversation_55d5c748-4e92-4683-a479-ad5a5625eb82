<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close>
    <el-form
      ref="refForm"
      :model="formData"
      label-width="auto"
      label-position="top"
      size="default"
      :rules="rules"
    >
      <el-form-item label="" prop="name">
        <el-input
          v-model="formData.name"
          maxlength="50"
          show-word-limit
          placeholder="请输入分类名称"
        />
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { reactive, ref, toRaw } from "vue";
import {
  createGoodsCategory,
  updateGoodsCategory,
} from "@/app_admin/tools/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
const refModal = ref();
const title = ref("");
const refForm = ref("");
const emit = defineEmits(["callback"]);

const defaultForm = {
  id: "",
  name: "",
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "480px",
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const show_add = () => {
  _resetForm();
  cfg["title"] = "添加分类";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (data) => {
  formData.value = { ...data };
  cfg["title"] = "编辑分类";
  refModal.value.show(cfg);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;
  refForm.value.validate((valid, fields) => {
    if (valid) {
      const data = toRaw(formData.value);
      if (!data.id) {
        apiHintWrap(createGoodsCategory(data), cfg["title"]).then(
          ({ status, resp }) => {
            if (status) {
              emit("callback", "reload");
              btnCancel();
            }
          }
        );
      } else {
        apiHintWrap(updateGoodsCategory(data.id, data), cfg["title"]).then(
          ({ status }) => {
            if (status) {
              emit("callback", "reload");
              btnCancel();
            }
          }
        );
      }
    }
  });
};

const rules = reactive({
  name: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
});

defineExpose({ title, show_add, show_edit, cbModal, formData, rules });
</script>

<style lang="scss">
.el-dialog__body {
  padding: 15px 24px 5px 24px;
}
</style>
