<template>
  <div class="audio-track-right" ref="refAudioTrack" :style="{ width: containerWidth + 'px' }"></div>
</template>

<script setup>
import WaveSurfer from "wavesurfer.js";

const props = defineProps({
  scale: {
    type: Number,
    default: 0,
  },
});

const refAudioTrack = ref();
const wavesurfer = ref(null);
const containerWidth = ref(0);
const isShowLibrary = ref(false);

const handleKeyPress = (event) => {
  if (event.code === "Space") {
    if (isShowLibrary.value) {
      return;
    }
    event.preventDefault();
    if (wavesurfer.value) {
      const willPlay = !wavesurfer.value.isPlaying();
      willPlay ? wavesurfer.value.play() : wavesurfer.value.pause();
      // 发送播放状态更新事件
      g.emitter.emit("audio_play_status_changed", willPlay);
    }
  }
};

const init = () => {
  wavesurfer.value = WaveSurfer.create({
    container: refAudioTrack.value,
    height: 32,
    waveColor: "rgb(200, 0, 200)",
    progressColor: "rgb(100, 0, 100)",
    url: g.clipperStore.audioUrl,
    backend: "MediaSource",
    barWidth: 2,
    minPxPerSec: 10,
    dragToSeek: true,
  });

  // 等待音频加载完成
  wavesurfer.value.on("ready", (duration) => {
    g.clipperStore.setPageInfo("duration", duration);
    g.emitter.emit("audio_ready", duration);
  });

  // 添加点击或拖动结束后的时间更新事件
  wavesurfer.value.on("interaction", (currentTime) => {
    g.emitter.emit("update_current_time", currentTime);
  });

  // 添加播放时的时间更新事件
  wavesurfer.value.on("timeupdate", (currentTime) => {
    g.emitter.emit("update_current_time", currentTime);
  });

  // 添加播放状态变化的监听
  wavesurfer.value.on("play", () => {
    g.emitter.emit("audio_play_status_changed", true);
  });

  wavesurfer.value.on("pause", () => {
    g.emitter.emit("audio_play_status_changed", false);
  });
};

watch(props, ({ scale }) => {
  wavesurfer.value.zoom(scale);
});

onMounted(() => {
  window.addEventListener("keydown", handleKeyPress);

  // 监听时间轴的缩放事件，确保在音频加载完成后才响应缩放
  g.emitter.on("update_audio_zoom", (pixelsPerSecond) => {
    wavesurfer.value.zoom(pixelsPerSecond);
  });

  // 修改设置音频当前时间的事件监听
  g.emitter.on("set_audio_time", (time) => {
    wavesurfer.value.setTime(time);
  });

  g.emitter.on("clipper_toggle_play", (isPlaying) => {
    if (isPlaying) {
      wavesurfer.value.play();
    } else {
      wavesurfer.value.pause();
    }
  });

  g.emitter.on("clipper_show_library", (status) => {
    isShowLibrary.value = status;
  });

  // 监听容器宽度变化
  watch(
    () => g.clipperStore.pageInfo.containerWidth,
    (newWidth) => {
      containerWidth.value = newWidth;
    }
  );
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyPress);
  g.emitter.off("update_audio_zoom");
  g.emitter.off("set_audio_time");
  g.emitter.off("clipper_toggle_play");
  g.emitter.off("clipper_show_library");
  if (wavesurfer.value) {
    wavesurfer.value.destroy();
  }
});

defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.audio-track {
  height: 80px;

  .audio-track-right {
    height: 40px;
  }
}
</style>
