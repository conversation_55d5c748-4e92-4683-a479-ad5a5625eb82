<template>
    <div class="text-ellipsis-wrapper">
        <input :id="toggleId" class="text-ellipsis-exp" type="checkbox" v-model="expanded">
        <div ref="textRef" class="text-ellipsis-text" @click="toggle"
            :style="{ '--max-lines': props.maxLines, '--line-height': lineHeight, '--max-height': maxHeight, '--shadow-color': props.shadowColor }">
            <!-- 只有在文本超出高度时才显示展开/收起按钮 -->
            <label v-if="isOverflow" class="text-ellipsis-label" :for="toggleId">
                <slot name="toggle" :expanded="expanded">
                    <button class="default-btn">
                        <van-icon :name="iconName" />

                    </button>
                </slot>
            </label>
            <slot :msg="text">
                {{ text }}
            </slot>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    maxLines: {
        type: Number,
        default: 2
    },
    shadowColor: {
        type: String,
        default: '#F9FAFC'
    },

    text: {
        type: String,
        default: ''
    },
    lineHeight: {
        type: Number,
        default: 1.5
    }
});



const expanded = ref(false)
const toggleId = ref(`exp-${Math.random().toString(36).slice(2, 11)}`)
const textRef = ref(null)
const isOverflow = ref(false)

const iconName = computed(() => expanded.value ? 'arrow-up' : 'arrow-down')
const lineHeight = props.lineHeight
const maxHeight = computed(() => `${props.maxLines * lineHeight * 14}px`)

const toggle = () => {
    expanded.value = !expanded.value
}

// 检测文本是否超出高度
const checkOverflow = () => {
    nextTick(() => {
        const el = textRef.value
        if (el) {
            const maxHeightValue = props.maxLines * lineHeight * parseFloat(getComputedStyle(el).fontSize)
            isOverflow.value = el.scrollHeight > maxHeightValue + 12
        }
    })
}

// 监听文本和最大行数变化
watch(() => [props.text, props.maxLines], () => {

    checkOverflow()
}, { immediate: true, deep: true })

defineExpose({
    toggle
})
</script>


<style scoped>
.text-ellipsis-wrapper {
    display: flex;
    overflow: hidden;
    /* border-radius: 8px; */
    /* padding: 15px; */
}

.text-ellipsis-text {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
    position: relative;
    line-height: var(--line-height);
    max-height: var(--max-height);
    transition: .3s max-height;
    padding: 4px 0;
}

.text-ellipsis-text::before {
    content: '';
    height: calc(100% - 18px);
    float: right;
}

/* .text-ellipsis-text::after {
    content: '';
    width: 999vw;
    height: 999vw;
    position: absolute;
    box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 var(--shadow-color);
    margin-left: -100px;
} */

.text-ellipsis-label {
    position: relative;
    float: right;
    clear: both;
    margin-left: 20px;
}

.default-btn {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
    color: #3f3f3f;
    width: 20px;
    height: 20px;
}

.text-ellipsis-label::before {
    content: '...';
    position: absolute;
    left: -5px;
    color: #333;
    transform: translateX(-100%);
    display: inline-block;
}

.text-ellipsis-exp {
    display: none;
}

.text-ellipsis-exp:checked+.text-ellipsis-text {
    max-height: none;
}

.text-ellipsis-exp:checked+.text-ellipsis-text::after {
    visibility: hidden;
}

.text-ellipsis-exp:checked+.text-ellipsis-text .text-ellipsis-label::before {
    visibility: hidden;
}
</style>