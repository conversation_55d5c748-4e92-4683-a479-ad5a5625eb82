<template>
  <div :class="`customer_wrap ${props.team ? 'team_customer_wrap' : 'my_customer_wrap'} `">
    <el-card class="customer_card card_no_border" shadow="never">
      <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
        <template #_header_left>
          <BtnAddCustomer @callback="onAdd" v-show="!props.team" :isAdmin="false" />
          <BtnFile ref="refFile" @reload="onSearch" :param="datas.param" :team="props.team" :isAdmin="false" />
        </template>

        <template #_header_filter>
          <SelectCustomerType v-model="datas.param.customerTypeId" @reload="onSearch" />
          <DrawerSelectMixed @callback="onSelectUser" type="user" v-if="props.team" :rootDeptId="rootDeptId" />
          <SelectLabel ref="refLabel" v-model:value="datas.param.tags" @reload="onSearch" />
          <BtnDate v-model:start="datas.param.startTime" v-model:end="datas.param.endTime" @reload="onDateChange" />
        </template>

        <template #header_VisitNews>
          <VisitNewsHeader :data="meetSummarys.colors" />
        </template>

        <template #header_meddic>
          <MeddicHeader />
        </template>

        <template #col_riskCount="{ row }">
          <ColRiskCount :row="row" @callback="onShowCustomer" />
        </template>

        <template #col_companyName="{ row }">
          <CustomerName :row="row" @callback="onShowCustomer" />
        </template>

        <template #col_VisitNews="{ row }">
          <VisitNews :row="row" :data="meetSummarys" @callback="onShowCustomer" />
        </template>

        <template #col_meetCount="{ row }">
          <div class="link" @click="onShowCustomer(row, 'visitRecord')">
            {{ formatCustNum(row.meetCount) }}
          </div>
        </template>

        <template #col_hostNames="{ row }">
          {{ row.hostNames }}
        </template>

        <template #col_hostDeptNames="{ row }">
          <ColDept :name="row.hostDeptNames" />
        </template>

        <template #col_meddic="{ row }">
          <Meddic :row="row" @callback="onShowCustomer" />
        </template>

        <template #col_tag="{ row }">
          <ColTag :id="row.id" :row="row.tag || []" :edit="true" @callback="cbTag" />
        </template>

        <template #col_todosCount="{ row }">
          <div class="link" @click="onShowCustomer(row, 'todo')">
            {{ formatCustNum(row.todosCount) }}
          </div>
        </template>

        <template #col_attendeeCount="{ row }">
          <div class="link" @click="onShowCustomer(row, 'customerAttend')">
            {{ formatCustNum(row.attendeeCount) }}
          </div>
        </template>

        <template #col_competitorNames="{ row }">
          <ColTag :id="row.id" :row="getCompetitorNames(row)" :edit="false"
            @callback="(action, data) => cbTagCompet(action, data, row)" />
        </template>
      </MyTable>
    </el-card>
    <drawerCustomer ref="refDrawer" @callback="onCustomerDrawer" :team="props.team" />
    <DiaCustomerType ref="refDiaCustomerType" :isAdmin="false" />
    <DrawerCustomerForm ref="refDiaCustomer" @reload="onSearch" :isAdmin="false" />
  </div>
</template>

<script setup>
import { getKanbanCustomers } from "@/app_client/tools/api.js";
import { getColumns, column_widths, calcSaleColors } from "./misc.js";
import { formatCustNum, getDefaultDateRange } from "@/app_client/tools/utils.js";
import MyTable from "@/components/Table.vue";
import SelectLabel from "./SelectLabel.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import ColRiskCount from "./ColRiskCount.vue";
import ColTag from "./ColTag.vue";
import VisitNews from "./VisitNews.vue";
import VisitNewsHeader from "./VisitNewsHeader.vue";
import MeddicHeader from "./MeddicHeader.vue";
import CustomerName from "@/app_client/components/CustomerName.vue";
import drawerCustomer from "@/components/drawerCustomer.vue";
import ColDept from "@/app_client/components/ColDept.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import Meddic from "./Meddic.vue";
import { removeURLParams } from "@/js/utils.js";
import BtnAddCustomer from "@/components/BtnAddCustomer/BtnAddCustomer.vue";
import DrawerCustomerForm from "@/components/BtnAddCustomer/DrawerCustomerForm.vue";
import DiaCustomerType from "@/components/BtnAddCustomer/DiaCustomerType.vue";
import { useRoute } from 'vue-router';
import BtnFile from "@/components/BtnCustomerFile/BtnFile.vue";
import SelectCustomerType from "./SelectCustomerType.vue"

const refFile = ref(null);
const pageAccess = ref({})
const route = useRoute();
const refDiaCustomer = ref()
const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});
const emit = defineEmits(["callback"]);
const refTable = ref();
const meetSummarys = ref({ colors: [] });
const refLabel = ref();
const refDrawer = ref();
const refDiaCustomerType = ref();
const tags = ref([]);

const columns = getColumns(props.team);

const _getKanbanCustomers = (p) => {
  if (typeof p.dptIds == "string") {
    p["dptIds"] = [p["dptIds"]];
  }
  return getKanbanCustomers(props.team, p);
};

const { startTime, endTime } = getDefaultDateRange()
const datas = reactive({
  tableid: props.team ? "team_customer" : "customer",
  param: {
    categoryIds: [],
    startTime, //开始时间，注间时间格式 YYYY-mm-dd HH24:mi:ss
    endTime, //结束时间
    tags: [], //标签
    customerTypeId: '',
    dptIds: [],
    userIds: [],
  },
  need_init_load: false,
  show_search: true,
  need_header: true,
  form: {},
  fixed_column: "companyName",
  sortable: "custom",
  modal_type: "link",
  search_ph: "请输入客户名称搜索",
  delete_hint_column: "companyName",
  show_link_column: false,
  columns: columns,
  always_show_columns: ["companyName"],
  sortables: ["riskCount", "meetCount", "todosCount", "attendeeCount"],
  template: columns,
  template_header: ["VisitNews", "meddic"],
  column_widths,
  urlGet: _getKanbanCustomers,
});
const rootDeptId = computed(() => datas.param.dptIds.length > 0 ? datas.param.dptIds[0] : '');
const cbTag = (action) => {
  if (action == "update") {
    onSearch();
    refLabel.value.search();
  }
};

const cbTagCompet = (action, tags, row) => {
  if (action == "view") {
    onShowCustomer(row, "competition");
  }
};

const onCustomerDrawer = (action, data) => {
  if (action == "reload") {
    refTable.value.search();
  }
}

const cbDatas = (action, data) => {
  if (action == "after_search") {
    const dataTags = [...new Set(data.flatMap((x) => x.meets.flatMap((m) => m.tag)))];
    const allTags = [...new Set([...tags.value, ...dataTags])];
    meetSummarys.value["colors"] = calcSaleColors(allTags);
    meetSummarys.value["min_dt"] = new Date(datas.param.startTime).getTime();
    meetSummarys.value["max_dt"] = new Date(datas.param.endTime).getTime();
    meetSummarys.value["middle_dt"] =
      (meetSummarys.value["min_dt"] + meetSummarys.value["max_dt"]) / 2;
  }
};

const onSelectUser = (action, data) => {
  datas.param.userIds = data.users.map((x) => x.id);
  onSearch();
};

const getCompetitorNames = (row) => {
  const item = toRaw(row);
  if (item.competitorNames) {
    return item.competitorNames.split(",").filter((x) => !!x);
  } else {
    return [];
  }
};
const onDateChange = () => {
  removeURLParams(['startDate', 'endDate'])
  onSearch();
}

const onSearch = () => {
  refTable.value.search();
};

const setDeptIds = (ids) => {
  datas.param.dptIds = ids;
  onSearch();
};

const onShowCustomer = (row, page = "") => {
  refDrawer.value.show(toRaw(row), page, toRaw(datas.param), props.team);
};

const onAdd = (item) => {
  nextTick(() => {
    refDiaCustomer.value.show_add(item);
  });
};


const handleUrlParams = () => {
  const action = route.query.action;

  if (action === 'create_customer') {
    removeURLParams(['action'])
    nextTick(() => {
      refDiaCustomerType.value.show();
    });
  }
};


// 创建客户操作	customer_add_opr
// 查看客户操作	customer_view_opr
// 导入客户操作	customer_import_opr
// 导出客户操作	customer_export_opr
// 编辑客户操作	customer_edit_opr
// 删除客户操作	customer_del_opr
const setAccessCache = () => {
  const list = [
    "customer_export_opr",
    "customer_view_opr",
    "customer_edit_opr",
    "customer_add_opr",
    "customer_del_opr",
    "customer_import_opr"
  ];
  for (let code of list) {
    const access = g.cacheStore.checkPointActionByCode("customer_list", code)
    pageAccess.value[code] = access

    if (pageAccess.value.customer_export_opr || pageAccess.value.customer_import_opr) {
      nextTick(() => {
        refFile.value.setAccess(pageAccess.value)
      })
    }
  }
  datas.show_link_delete = pageAccess.value.customer_del_opr;
  datas.show_link_edit = pageAccess.value.customer_edit_opr;
  if (!props.team) {
    refTable.value.init(toRaw(datas));
  }
};

onMounted(() => {
  // if (!props.team) {
  //   refTable.value.init();
  // }

  g.cacheStore.getSalesConfigure().then((resp) => {
    if (resp && resp.customerTopics) {
      tags.value = resp.customerTopics.map((x) => x.label);
    }
  });
  g.cacheStore.getUserMenu("client").then(() => {
    setAccessCache();
  });
  handleUrlParams();
});

defineExpose({
  MyTable,
  cbDatas,
  refTable,
  SelectLabel,
  BtnDate,
  setDeptIds,
  refLabel,
  ColTag,
  ColRiskCount,
  DrawerSelectMixed,
  VisitNews,
  VisitNewsHeader,
  getCompetitorNames,
  CustomerName,
  props,
  drawerCustomer,
  refDrawer,
  onShowCustomer,
  ColDept,
});
</script>

<style lang="scss" scoped>
.customer_wrap {
  background: #f7f9fe;

  .table_wrap {
    margin: 4px;
  }

  :deep(.search_box) {

    .mixed_input_box {
      width: 94px;
    }

    .search_input {
      width: 215px !important;
    }
  }

  :deep(.table_class) {
    height: calc(100vh - 232px);

    .el-scrollbar {
      overflow: auto;
    }

    table {
      border-collapse: collapse;

      :deep(td:first-child) {
        border-right: 2px solid black;
      }

      .link {
        cursor: pointer;
      }
    }
  }
}

.team_customer_wrap {
  :deep(table) {
    width: 2100px !important;
  }
}

.my_customer_wrap {
  padding: 24px;

  :deep(table) {
    min-width: 1700px !important;
  }
}
</style>
