.KeyPoints {
  padding-bottom: 10px;

  .key-points-list {
    height: calc(100vh - 270px);
    overflow-y: auto;

    li {
      background-color: #F7F6FE;
      display: flex;
      flex-direction: row;
      padding: 10px;
      margin: 5px 0;
      position: relative;

      .rbox {
        width: 100%;

        .rtop {
          font-weight: 500;
          color: #262626;
        }

        .rbottom {
          color: #27264DA6;
          margin-top: 10px;
        }

        .diy_box {
          margin: 10px 0;
        }
      }

      .back_btn {
        position: absolute;
        right: 11px;
        top: 3px;
        color: #7976E9;
        cursor: pointer;
        display: none;
      }
    }

    .act_qa,
    li:hover {
      background-color: #E7E7FB;

      .back_btn {
        display: block;
      }
    }
  }

  .qa-tag {
    display: inline-block;
    padding: 1px 6px;
    margin-right: 2px;
    border-radius: 2px;
    font-size: 12px;
    height: 20px;
    color: #fff;
    z-index: 2;
    position: relative;
  }

  .role-tag {
    width: 24px;
    height: 20px;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    text-align: left;
    padding: 3px 12px;
    border-radius: 4px;
    margin-left: -5px;
  }

  .rtop {
    .qa-tag {
      background: #52C41A;
    }

    .role-tag {
      background: #D9F7BE;
      color: #52C41A;
    }
  }

  .rbottom {
    .qa-tag {
      background: #E6F1FF;
      background: #436BFF;
    }

    .role-tag {
      background: #E6F1FF;
      color: #4B7FE8;
    }
  }
}