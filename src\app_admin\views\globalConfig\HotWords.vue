<template>
  <div class="hot-words-container">
    <div class="section-header">
      <div class="header-content">
        <div class="subtitle">自定义热词，有助于提升拜访过程中语音识别的准确率</div>
        <div class="add-button" @click="handleAdd">
          <el-icon class="icon-plus">
            <Plus />
          </el-icon>
          <span>添加</span>
        </div>
      </div>
    </div>

    <div class="hot-words-list">
      <div class="hot-word-item" v-for="(item, index) in hotWordsList" :key="index">
        <span class="word-text">{{ item }}</span>
        <el-icon class="delete-icon" @click="handleDelete(item, index)">
          <Close />
        </el-icon>
      </div>
    </div>

    <el-drawer v-model="dialogVisible" :title="'热词管理'" direction="rtl" size="480px" :close-on-click-modal="false"
      class="hot-words-drawer">
      <template #default>
        <div class="drawer-content">
          <div class="input-label">
            <span>热词</span>
            <span class="word-count">({{ currentWordsCount }}/300)</span>
          </div>
          <el-input v-model="formData.words" type="textarea" placeholder="请输入热词，每行一个" class="hot-words-textarea"
            :rows="30" @input="handleWordsInput" />
          <div class="rules-section">
            <div class="rules-title">填写须知</div>
            <div class="rule-item">1. 一个热词一行，最多设置300个热词</div>
            <div class="rule-item">2. 单个热词最多10个字符</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { Plus, Close } from '@element-plus/icons-vue'
import { getOrgHotWords, updateOrgHotWords } from '@/app_admin/tools/api'

const hotWordsList = ref([])
const dialogVisible = ref(false)
const formData = ref({
  words: ''
})

const currentWordsCount = computed(() => {
  if (dialogVisible.value) {
    // 当对话框打开时，计算文本框中的有效行数
    return formData.value.words
      .split('\n')
      .map(word => word.trim())
      .filter(word => word).length
  }
  // 当对话框关闭时，显示列表中的数量
  return hotWordsList.value.length
})

// 获取热词列表
const getHotWords = async () => {
  const res = await getOrgHotWords()
  hotWordsList.value = res.words ? res.words.split(',') : []
}

const handleAdd = () => {
  if (hotWordsList.value.length >= 300) {
    ElMessage.warning('最多只能设置300个热词')
    return
  }
  formData.value.words = hotWordsList.value.join('\n')
  dialogVisible.value = true
}

const handleDelete = async (word, index) => {
  hotWordsList.value.splice(index, 1)
  try {
    await updateOrgHotWords({ words: hotWordsList.value.join(',') })
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleSubmit = async () => {
  if (!formData.value.words.trim()) {
    return ElMessage.warning('请输入热词')
  }

  try {
    // 将文本框内容按行分割成数组，过滤掉空行
    const words = formData.value.words
      .split('\n')
      .map(word => word.trim())
      .filter(word => word)

    if (words.length === 0) {
      return ElMessage.warning('请输入有效的热词')
    }

    if (words.length > 300) {
      return ElMessage.warning('最多只能设置300个热词')
    }

    // 检查每个热词的长度
    const invalidWord = words.find(word => word.length > 10)
    if (invalidWord) {
      return ElMessage.warning(`热词"${invalidWord}"超过10个字符`)
    }

    await updateOrgHotWords({ words: words.join(',') })
    hotWordsList.value = [...words] // 使用展开运算符确保响应性
    ElMessage.success('保存成功')
    dialogVisible.value = false
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const handleWordsInput = (value) => {
  const words = value
    .split('\n')
    .map(word => word.trim())
    .filter(word => word)

  if (words.length > 300) {
    // 保留前300个有效词
    formData.value.words = words.slice(0, 300).join('\n')
    ElMessage.warning('最多只能设置300个热词')
  }
}

onMounted(() => {
  getHotWords()
})

defineExpose({
  hotWordsList,
  dialogVisible,
  formData,
  getHotWords,
  Plus,
  Close,
  handleAdd,
  handleDelete,
  handleSubmit,
  currentWordsCount,
  handleWordsInput
})
</script>

<style lang="scss">
.hot-words-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 14px 24px;
    border-bottom: 1px solid #f2f3f5;
    margin-top: 0;

    .el-drawer__title {
      font-size: 16px;
      font-weight: 500;
      color: #1f2329;
      margin-left: 20px;
    }
  }

  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;

    .drawer-content {
      flex: 1;
      padding: 20px 24px;

      .input-label {
        font-size: 14px;
        color: #1f2329;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .word-count {
          color: #86909c;
        }
      }

      :deep(.el-textarea) {
        display: block;

        .el-textarea__inner {
          display: block;
          font-family: monospace;
          line-height: 1.6;
          resize: none;
          box-sizing: border-box;
        }
      }

      .rules-section {
        margin-top: 12px;

        .rules-title {
          font-size: 14px;
          color: #1f2329;
          margin-bottom: 8px;
        }

        .rule-item {
          font-size: 13px;
          color: #4e5969;
          line-height: 1.6;
        }
      }
    }
  }

  .el-drawer__footer {
    padding: 0;
    border-top: 1px solid #f2f3f5;
    margin-top: 0;

    .drawer-footer {
      padding: 20px 24px 20px 84px;
      text-align: right;

      .el-button {
        min-width: 96px;
        margin-left: 12px;
      }
    }
  }

  .el-textarea {
    display: block;

    .el-textarea__inner {
      display: block;
      font-family: monospace;
      line-height: 1.6;
      resize: none;
      box-sizing: border-box;
      height: calc(100vh - 290px);
      overflow-y: auto;
    }
  }
}

.hot-words-container {
  .section-header {
    .header-content {
      display: flex;
      flex-direction: column;

      .title-group {
        .title {
          font-size: 14px;
          color: #1f2329;
          margin-bottom: 8px;
        }
      }

      .subtitle {
        font-size: 12px;
        color: #86909c;
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .add-button {
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        color: #436BFF;
        font-size: 13px;

        .icon-plus {
          margin-right: 4px;
          font-size: 14px;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .hot-words-list {
    margin-top: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .hot-word-item {
      display: inline-flex;
      align-items: center;
      background-color: #f5f6f7;
      padding: 1px 10px;
      border-radius: 4px;

      .word-text {
        color: #595959;
        font-size: 14px;
      }

      .delete-icon {
        margin-left: 8px;
        font-size: 14px;
        color: #86909c;
        cursor: pointer;

        &:hover {
          color: #f53f3f;
        }
      }
    }
  }
}
</style>
