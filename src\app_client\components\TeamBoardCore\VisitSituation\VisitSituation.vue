<template>
    <Overview ref="refOverview" :isReport="isReport" />
    <StatisticsPad ref="refStatisticsPad" />
    <DetailTable ref="refDetailTable" />
</template>

<script setup>
import StatisticsPad from '../StatisticsPad/StatisticsPad.vue'
import Overview from "../Overview/Overview.vue";
import DetailTable from "../DetailTable/DetailTable.vue";
const props = defineProps({
    isReport: {
        type: Boolean,
        default: false
    },
    data: {
        type: Object,
        default: () => ({})
    }
});
const refOverview = ref();
const refStatisticsPad = ref();
const refDetailTable = ref();

const data = computed(() => g.clientBoardStore.overviewDept || {})

const setDept = () => {
    nextTick(() => {
        refOverview.value.setDept(data.value);
        refStatisticsPad.value.setDept(data.value);
        refDetailTable.value.init();
    })
}




defineExpose({
    setDept
});
</script>