<template>
  <div class="template-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #col_templateName="{ row }">
        <div class="template-name">{{ row.templateName }}</div>
      </template>

      <template #col_dimensions="{ row }">
        <div class="dimensions">
          <div v-for="(line, index) in (row.dimensions || '').split('\n')" :key="index" class="dimension-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
      </template>
    </MyTable>

    <TemplateTableDrawer ref="templateTableDrawerRef" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getTemplateListData, deleteTemplate } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import TemplateTableDrawer from './TemplateTableDrawer.vue';

const refTable = ref();
const templateTableDrawerRef = ref();
const tableConfig = reactive({
  tableid: 'template_list',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模板名称",
  delete_hint_column: 'templateName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["templateName", "dimensions", "creator", "createTime"],
  template: ["templateName", "dimensions"],
  urlGet: getTemplateListData,
  urlDelete: deleteTemplate
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleEdit = (row) => {
  templateTableDrawerRef.value.openDrawer(row);
};

const handleDelete = (row) => {
  confirmDelete(row.templateName, (status) => {
    if (status) {
      deleteTemplate(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.template-table {
  padding: 24px 0;

  .template-name {
    font-weight: 500;
    color: #333;
  }

  .dimensions {
    max-width: 400px;

    .dimension-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>