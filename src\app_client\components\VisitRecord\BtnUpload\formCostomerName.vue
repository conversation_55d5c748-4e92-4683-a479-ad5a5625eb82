<template>
    <div class="vbp_box">
        <div class="vbp_title">
            拜访关联客户<span>*</span>
        </div>
        <div class="vbp_value">
            <CustomerNamePicker v-model="localParam.salesMateCustomerName" v-model:customerId="localParam.customerId" />
        </div>
    </div>
</template>

<script setup>
import CustomerNamePicker from '@/components/CustomerNamePicker'

const props = defineProps({
    param: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['update:param'])
const localParam = ref(props.param)

watch(localParam, (val) => {
    emit('update:param', val)
}, { deep: true })

watch(() => props.param, (val) => {
    localParam.value = val
}, { deep: true })

defineExpose({
    CustomerNamePicker,
    localParam
})
</script>
