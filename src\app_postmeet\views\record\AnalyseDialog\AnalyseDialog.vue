<template>
  <div class="right_analyse" :class="{ folded: isFolded }" :style="[analyseDialogStyle, foldedStyle]"
    ref="analyseDialog">
    <div class="drag-header" @mousedown="startDrag" @mousemove="onDrag" @mouseup="stopDrag">
      <span>对话分析</span>
      <div class="header-actions">
        <div class="fold-btn" @click="toggleFold">
          <ArrowUp v-if="isFolded" />
          <ArrowDown v-else />
        </div>
        <div class="adf_close" @click="onClose">
          <CloseIcon />
        </div>
      </div>
    </div>
    <div class="analyse-dialog-content">
      <adHeader @callback="cbHeader" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import adHeader from "./adHeader.vue";
import CloseIcon from "@/app_postmeet/icons/close.vue";
import ArrowUp from "@/icons/arrow_up.vue";
import ArrowDown from "@/icons/arrow_down.vue";
import { getAnalyseTime, adjustTime } from "@/app_postmeet/tools/sale_report.js";

const isFolded = ref(false);
const isDragging = ref(false);
const dialogX = ref(document.body.offsetWidth - 400);
const dialogY = ref(130);
const dragStartX = ref(0);
const dragStartY = ref(0);

const analyseDialogStyle = computed(() => ({
  position: "fixed",
  top: dialogY.value + "px",
  left: dialogX.value + "px",
  zIndex: 1000,
}));

const foldedStyle = computed(() => {
  if (!isFolded.value) return {};

  const analyseDialog = document.querySelector(".right_analyse");
  const hasWordNav = analyseDialog?.querySelector(".word_nav")?.style.display !== "none";

  return {
    height: hasWordNav ? "96px" : "40px",
  };
});

const startDrag = (e) => {
  isDragging.value = true;
  dragStartX.value = e.clientX - dialogX.value;
  dragStartY.value = e.clientY - dialogY.value;

  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup", stopDrag);
};

const onDrag = (e) => {
  if (!isDragging.value) return;

  // 获取窗口尺寸
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  // 获取对话框尺寸
  const dialogWidth = 363; // 对话框固定宽度
  const dialogHeight = isFolded.value ? 96 : 376; // 根据折叠状态确定高度

  // 计算新位置
  let newX = e.clientX - dragStartX.value;
  let newY = e.clientY - dragStartY.value;

  // 限制左边界
  newX = Math.max(0, newX);
  // 限制右边界
  newX = Math.min(windowWidth - dialogWidth, newX);

  // 限制顶部边界 (考虑 RecordHeader 高度)
  const headerHeight = 60; // RecordHeader 的高度
  newY = Math.max(headerHeight, newY);
  // 限制底部边界
  newY = Math.min(windowHeight - dialogHeight, newY);

  dialogX.value = newX;
  dialogY.value = newY;
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
};

const onClose = () => {
  g.emitter.emit("update_show_analyse", false);
};

const toggleFold = () => {
  isFolded.value = !isFolded.value;
  g.emitter.emit("toggle_analyse_fold", isFolded.value);
};

const cbHeader = (index, _item, key) => {
  // 发送事件通知 subtitlesSummary 组件过滤数据
  if (_item && _item.type) {
    g.emitter.emit("filterAnalyseType", _item);
  } else {
    g.emitter.emit("filterAnalyseType", { type: "" }); // 空字符串表示显示所有类型
  }

  if (_item && _item.value) {
    _toTime(_item.type, _item.value[index]);
  }
};

const _toTime = (type, row) => {
  const vtime = getAnalyseTime(type, row);
  const markTime = adjustTime(vtime);
  console.log('toTime', type, row, vtime, markTime)
  g.emitter.emit("setVideoTime", { markTime });
};

onMounted(() => {
  g.emitter.on("toggle_analyse_fold", (value) => {
    isFolded.value = value;
  });
});
</script>

<style lang="scss">
.right_analyse {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  width: 363px;
  height: 376px;
  transition: height 0.3s ease;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 160px); // 防止超出视窗
  position: fixed; // 确保使用固定定位
  z-index: 999; // 确保在其他元素之上，但在 RecordHeader 之下

  &.folded {
    .analyse-dialog-content {
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    :deep(.word_nav) {
      margin: 0;
      padding: 8px 16px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .nav-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .drag-header {
    height: 40px;
    min-height: 40px; // 确保header不会被压缩
    padding: 0 16px;
    background: #f5f7fa;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: move;
    user-select: none;
    border-bottom: 1px solid #e4e7ed;

    span {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .fold-btn,
    .adf_close {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #e6e8eb;
      }

      svg {
        width: 16px;
        height: 16px;
        color: #909399;
      }
    }
  }

  .analyse-dialog-content {
    flex: 1;
    overflow: auto;
    padding: 10px;
    height: 0; // 配合flex:1使用
    min-height: 0; // 确保内容可以滚动

    // 美化滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
    }
  }
}
</style>
