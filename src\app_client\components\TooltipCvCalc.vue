<template>
    <el-tooltip class="et_cv_calc_wrap" placement="top" effect="light">
        <template #content>
            <div class="radar_tooltip">
                <p>
                    <span>声量含义：</span><span style="color: #595959;">产品在市场中引发的关注、讨论及品牌影响的的综合体现</span>
                </p>
                <p>
                    <span>声量公式：</span><span style="color: #595959;">声量 = Σ(积极次数 × <i
                            style="color:#04CCA4;font-weight: 900;">1</i> + 中立次数
                        ×
                        <i style="color: #595959;font-weight: 900;">0.5</i> + 消极次数 × ( <i
                            style="color: #FF6B3B;font-weight: 900;">-1</i>
                        ))</span>
                </p>
            </div>
        </template>

        <el-icon class="radar_icon">
            <QuestionFilled color="#fff" />
        </el-icon>
    </el-tooltip>
</template>
<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'

</script>

<style lang="scss" scoped>
.et_cv_calc_wrap {
    color: #666666;

    p {
        span:nth-child(1) {
            color: #262626;
            font-weight: 700;
        }
    }

    i {
        font-style: normal;
        margin-left: 4px;
    }
}
</style>