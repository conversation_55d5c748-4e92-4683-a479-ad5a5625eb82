<template>
    <totalCharts :data="chartData" :metrics="totalChartsMetrics" :setting="totalChartsSetting" :loading="loading"
        class="feedback-chart" />
</template>

<script setup>
import totalCharts from '@/app_client/views/BusinessInsights/components/totalChart.vue'
const chartData = computed(() => g.clientBiStore.cvCustomerAttiDistList);

const props = defineProps(['colorObject'])
const loading = ref(true)

// 图表配置
const totalChartsMetrics = [{
    key: 'positive',
    name: '积极',
    color: props.colorObject['positive']
}, {
    key: 'negative',
    name: '消极',
    color: props.colorObject['negative']
}]

const totalChartsSetting = {
    title: '客户态度分布图',
    width: 82
}
const init = async () => {
    loading.value = true;
    await g.clientBiStore.getCvCustomerAttiDistList()
    loading.value = false;
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

// // 初始化数据
onMounted(() => {
    init()
})
</script>

<style lang="scss" scoped>
.feedback-chart {
    width: 70%;
    height: 460px;
    box-shadow: none
}
</style>