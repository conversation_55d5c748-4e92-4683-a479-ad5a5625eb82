<template>
    <div class="mi_box" :style="leftStyle">
        <div class="flex-row">
            <btnRadio ref="refMenu" :active-id="rdViewType" @callback="cbMenu" :analysis-type="rdSaleMethodType"
                @change-analysis-type="onChangeAnalysisType" />
        </div>
        <div class="mi_right" @click="onCopy" v-if="rdViewType == 'sale_meet' && hasSaleReport && saleTags.length > 0">
            <div class="icon">
                <CopyIcon />
            </div>
            <div class="txt"> 复制 </div>
        </div>
    </div>
</template>

<script>
import CopyIcon from "@/icons/copy.vue"
import btnRadio from '@/app_postmeet/components/btnRadio.vue';
import { ArrowDown } from '@element-plus/icons-vue'
import { getUrlParam, removeURLParams } from '@/js/utils'

export default {
    components: { CopyIcon, btnRadio, ArrowDown },
    data() {
        return {
            activeId: '',
            tags: {},
            analysisType: {},
            meetingType: 0,
            leftStyle: {},
            rdSaleMethodType: 'BANT',
            isShowSetup: false,
            isInterview: false,
            rdViewType: '',
            isShowSaleReportHeader: false,
            hasSaleReport: false,
            saleReport: {},
            hasMind: false,
            analysisTypes: [],
            isShowAnalysisType: false,
            saleTags: [],
        }
    },
    methods: {
        init(that) {
            const { meetingType, leftStyle, isShowSetup, isShowSaleReportHeader, saleReport, saleTags, isInterview } = that;
            this.meetingType = meetingType;
            this.leftStyle = leftStyle;
            this.isShowSetup = isShowSetup;
            this.isShowSaleReportHeader = isShowSaleReportHeader;
            this.saleReport = saleReport;
            this.saleTags = saleTags || [];
            this.isInterview = isInterview;
            this.hasMind = g.postmeetStore.data.MindMapMd.length > 0;
            this.hasSaleReport = JSON.stringify(g.postmeetStore.data.saleReport) != "{}";
            this.analysisTypes = g.cacheStore.salesMethodology;
            this._initTags()
        },
        _initTags() {
            const ar = this.saleReport.analysisReports;
            if (ar) {
                const summary = ar
                if (summary.length > 0) {
                    this.isShowAnalysisType = true;
                    this.rdSaleMethodType = g.postmeetStore.data.defaultSalesMethodology;
                }
            }
            this.tags = {}
            if (this.saleTags.length > 0) {
                for (let i = 0; i < this.saleTags.length; i++) {
                    this.tags['saletag' + i] = {
                        name: this.saleTags[i],
                        show: true, disabled: true
                    };
                }
            } else if (this.isShowSaleReportHeader && ar && ar.length > 0) {
                for (let i = 0; i < ar.length; i++) {
                    const { label, name } = ar[i];
                    this.tags[label] = { name, show: true, disabled: this.isShowSetup };
                }
            } else if (this.meetingType != 7) {
                this.tags = {
                    meet_minutes: { name: '会议纪要', show: true, disabled: this.isShowSetup },
                    segmented_summary: { name: '分段摘要', show: true, disabled: this.isShowSetup },
                    sale_meet: { name: '对客纪要', show: true, disabled: this.isShowSetup },
                    interview_meet: { name: '面试评价', show: this.isInterview, disabled: this.isShowSetup },
                    speecher_summary: { name: '发言总结', show: true, disabled: this.isShowSetup },
                    key_points: { name: '问答回顾', show: true, disabled: this.isShowSetup },
                    chart_mind: { name: '思维导图', show: this.hasMind, disabled: this.isShowSetup },
                }
            }
            this.$refs.refMenu.setTags(this.tags);
            setTimeout(() => {
                this.autoSelectTag()
            }, 200)
        },
        autoSelectTag() {
            // 检查URL中是否有menu1参数，如果有则自动切换到对应tab
            const menuParam = getUrlParam('menu2')
            const tagsName = Object.keys(this.tags);
            const that = this;
            if (menuParam && tagsName.includes(menuParam)) {
                setTimeout(() => {
                    that.cbMenu(menuParam)
                }, 200)
                removeURLParams('menu2')
            }
        },
        onChangeAnalysisType(selectType) {
            this.rdViewType = 'normal_meet'
            this.analysisType = selectType;
            this.$emit("callback", 'onChangeViewType', { rdViewType: 'normal_meet', from: 'normal' })
            this.$nextTick(() => {
                g.emitter.emit('onChangeSaleAnalyseType', this.analysisType.value);
            })
        },
        cbMenu(tag) {
            this.update(tag);
            const from = this.meetingType == 7 ? 'sale' : 'normal';
            this.$emit("callback", 'onChangeViewType', { rdViewType: this.rdViewType, from })
        },
        update(type) {
            this.rdViewType = type;
            this.$nextTick(() => {
                if (this.$refs.refAnalysisType) {
                    this.$refs.refAnalysisType.setRdType(type);
                }
            })
        },
        onCopy() {
            const { challenge, plan, suggestion } = g.postmeetStore.data.salesMateSummary
            let txt = ''
            txt += array2ListText(challenge, '培训诉求')
            txt += array2ListText(suggestion, '建议和方案')
            txt += array2ListText(plan, '待办')
            g.appStore.doCopy(txt, '已复制')
        },
    }
}

</script>

<style lang="scss" scoped>
.select-button {
    width: 100%;
    text-align: left;
    justify-content: space-between;
}

:deep(.speaker-type-popover) {
    padding: 0;
}

.menu-container {
    .menu-item {
        padding: 12px 16px;
        cursor: pointer;

        &:hover {
            background-color: #f5f7fa;
        }

        .item-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .item-desc {
            font-size: 12px;
            color: #909399;
        }
    }
}
</style>