<template>
    <el-form :model="formData" ref="formRef" label-width="120px" :rules="rules" :validate-on-rule-change="false"
        label-position="top">
        <el-form-item label="姓名" prop="username" :required="true">
            <el-input v-model="formData.username" placeholder="请输入客户姓名" @blur="validateField('username')" />
        </el-form-item>
        <el-form-item v-for="field in formFields" :key="field.id" :label="field.fieldName"
            :prop="'fieldValues.' + field.id" :required="field.isRequired == 1">

            <el-input v-if="field.fieldType == 'String'" v-model="formData.fieldValues[field.id]"
                :placeholder="field.placeholder" :maxlength="field.maxLength" :minlength="field.minLength"
                @blur="validateField('fieldValues.' + field.id)" />

            <el-select v-if="field.fieldType == 'Select'" v-model="formData.fieldValues[field.id]"
                :placeholder="field.placeholder" @blur="validateField('fieldValues.' + field.id)">
                <el-option v-for="item in field.fieldOptionsArray" :key="item" :label="item" :value="item">
                </el-option>
            </el-select>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { get_rules } from "@/js/utils";
const { proxy } = getCurrentInstance();
const formRef = ref(null);
const rules = ref({})
const formFields = ref([]);
const formData = ref({
    username: '',
    fieldValues: {}
});

// Add ref to store original field values
const originalFieldValues = ref({});

const get_data = () => {
    return new Promise((resolve, reject) => {
        if (formRef.value) {
            formRef.value.validate((valid) => {
                if (valid) {
                    let data = {
                        "name": formData.value.username,
                        "fieldValues": []
                    }
                    formFields.value.forEach(item => {
                        // 获取原始字段值
                        const existingFieldValue = originalFieldValues.value[item.id];

                        data.fieldValues.push({
                            // 使用原始 id，如果存在的话
                            id: existingFieldValue?.id || '',
                            fieldId: item.id,
                            fieldName: item.fieldName,
                            fieldValue: formData.value.fieldValues[item.id]
                        })
                    });
                    resolve(data);
                } else {
                    reject(new Error('表单验证失败'));
                }
            });
        } else {
            reject(new Error('表单未初始化'));
        }
    });
}

const validateField = (prop) => {
    if (formRef.value) {
        formRef.value.validateField(prop);
    }
}

const setFormData = (data, fields) => {
    fields.forEach(item => {
        item.fieldId = item.id;
        item[item.id] = '';
        if (item.fieldType == 'Select') {
            item.fieldOptionsArray = JSON.parse(item.fieldOptions);
        }
    });
    formFields.value = fields;
    rules.value = get_rules(fields);
    formData.value.username = data.username;

    formData.value.fieldValues = {};
    originalFieldValues.value = {};

    // 处理原始的 fieldValues 数组
    if (Array.isArray(data.fieldValues)) {
        data.fieldValues.forEach(fieldValue => {
            // 设置表单显示值
            formData.value.fieldValues[fieldValue.fieldId] = fieldValue.fieldValue;
            // 保存原始字段值，包含原始 id
            originalFieldValues.value[fieldValue.fieldId] = {
                id: fieldValue.id,  // 保存原始 id
                fieldId: fieldValue.fieldId,
                fieldValue: fieldValue.fieldValue
            };
        });
    }

    proxy.$forceUpdate();
}

const init = (fields) => {
    fields.forEach(item => {
        item.fieldId = item.id;
        item[item.id] = '';
        if (item.fieldType == 'Select') {
            item.fieldOptionsArray = JSON.parse(item.fieldOptions);
        }
    });
    formFields.value = fields;
    formData.value.username = '';
    formData.value.fieldValues = {};
    if (formRef.value) {
        formRef.value.clearValidate();
        formRef.value.resetFields();
    }
    rules.value = get_rules(fields);
}

defineExpose({
    init,
    get_data,
    setFormData
})
</script>
