<template>
    <div class="warning_tag_wrap">
        <el-icon size="16">
            <Warning />
        </el-icon>
        {{ hint }}
    </div>
</template>

<script setup>
import Warning from '@/app_electron/icons/warning.vue'

const props = defineProps({
    hint: { type: String, default: '' },
})

</script>


<style lang="scss">
.warning_tag_wrap {
    display: flex;
    justify-content: center;
    padding: 12px;
    background: #FFF7E8;
    border-radius: 8px;
    font-size: 14px;

    .el-icon {
        width: 16px;
        height: 16px;
        margin: 4px 8px 0 0;
        color: #FF5219;
    }
}
</style>