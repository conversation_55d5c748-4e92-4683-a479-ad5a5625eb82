<template>
  <div class="et_bottom flex-row">
    <div class="et_left">
      <div class="title flex-row">
        <div class="title_left">
          PPT大纲
        </div>
        <div class="title_right" @click="onEditOutline">
          编辑
        </div>
      </div>
      <div class="tree">
        <PptTree ref="refTree" @callback="cbTree" />
      </div>
    </div>

    <div class="et_right">
      <div class="title">
        设置 - {{ currNode.label }}
      </div>
      <div class="title1">组装方式</div>
      <el-radio-group v-model="currNode.assemblyMethod" @change="onChangeMode" class="rg_group">
        <el-radio value="FIX_MODE" size="large">固定模板</el-radio>
        <el-radio value="AI_MODE" size="large">
          <div class="rg_txt">
            AI单页抽取
          </div>
          <el-tooltip content="AI将智能地从一系列相关的PPT文档中，根据您的指定，抽取出确切数量的单独页面" placement="top">
            <div class="q_icon">
              <QuestionIcon />
            </div>
          </el-tooltip>
        </el-radio>
        <el-radio value="FILE_MODE" size="large">
          <div class="rg_txt">
            AI精选PPT
          </div>
          <el-tooltip content="AI将在一系列已关联的PPT文档中进行筛选，根据您的需求选择出指定数量的完整PPT" placement="top">
            <div class="q_icon">
              <QuestionIcon />
            </div>
          </el-tooltip>
        </el-radio>
      </el-radio-group>
      <div class="title1">关联内容</div>
      <selectBar ref="refSelectBar" @callback="cbSelectBar" />

      <div v-show="currNode.assemblyMethod == 'AI_MODE'">
        <div class="title1">匹配数量</div>
        <el-input-number v-model="selectTopN[currNode.assemblyMethod]" :min="1" :max="99" @change="onSelectChange"
          controls-position="right" class="sel_topn" />
      </div>

      <div v-show="currNode.assemblyMethod == 'FILE_MODE'">
        <div class="title1">匹配数量</div>
        <div class="rf_box flex-row">
          <el-radio v-model="rdTopType" value="1">限制</el-radio>
          <el-input-number v-model="selectTopN[currNode.assemblyMethod]" :min="1" controls-position="right"
            @change="onSelectChange" class="sel_topn" :disabled="rdTopType == 0" />
          <div class="td_txt">
            份 （需要小于关联的PPT份数）
          </div>
        </div>
        <div>
          <el-radio v-model="rdTopType" value="0">不限</el-radio>
        </div>
      </div>

      <div v-show="currNode.assemblyMethod !== 'FIX_MODE'">
        <div class="title1">
          AI检索关联字段
        </div>
        <AiRetFields ref="refAiField" @callback="cbAiField" />
      </div>

      <div class="title tppt">
        PPT背景
      </div>
      <TemplatePics />
    </div>
  </div>


  <div class="bottom_btn flex-row">
    <el-button @click="onCancel">取消</el-button>
    <el-button type="primary" @click="onConfirm">
      确认
    </el-button>
  </div>
  <selectFile ref="refSelectFile" @callback="cbSelectFile"></selectFile>
</template>

<script setup>
import { updatePptTemplateSetting } from "@/app_admin/tools/api.js"
import { transTemplateSaveObject, apiHintWrap } from "@/app_admin/tools/utils.js"
import selectFile from "./selectFile.vue";
import selectBar from "./selectBar.vue";
import EditOutline from "./EditOutline.vue";
import PptTree from "./PptTree.vue";
import { useRoute } from 'vue-router'
import AiRetFields from "./AiRetFields.vue";
import TemplatePics from "./TemplatePics.vue";
import QuestionIcon from "@/app_admin/icons/question.vue"

const selectTopN = ref({
  FIX_MODE: 1,
  AI_MODE: 1,
  FILE_MODE: 1
});
const refSelectBar = ref();
const refSelectFile = ref();
const refTree = ref();
const refOutLine = ref();
const route = useRoute();
const tempInfo = ref({});
const currNode = ref({ docs: [] })
const checkList = ref([])
const refAiField = ref()
const rdTopType = ref('1')

const emit = defineEmits(['callback']);
// "id":<string>, //模板的设置项的id
// "assemblyMethod": <string>, //组合方式 FIX_MODE(固定模式), AI_MODE(AI检索)
// "docs": [<string>] //关联的文档id
// "categories": [<string>] //关联的类别id


// 保存需要提交的数据
const templateId = route.params.id;
const store_key = `template_edit_${templateId}`
let saveForm = {}


const init = (data) => {
  tempInfo.value = data;
  refTree.value.init(data.child)
}


const cbTree = (action, data) => {
  if (action == 'click') {
    const ids = Object.keys(saveForm);
    if (!ids.includes(data.id)) {
      saveForm[data.id] = {
        id: data.id,
        label: data.label,
        assemblyMethod: data.assemblyMethod,
        docs: data?.docs || [],
        categories: data?.categories || [],
        topK: data?.topK || 5,
        selectedFields: data.selectedFields || []
      }
      currNode.value = saveForm[data.id];
    } else {
      currNode.value = { ...data, ...saveForm[data.id] }
    }
    refSelectBar.value.update(saveForm[data.id])
    selectTopN.value[currNode.value.assemblyMethod] = currNode.value.topK;
    refAiField.value.setValue(templateId, saveForm[data.id])
  }
}

const cbSelectFile = (action, data) => {
  if (action == 'confirm') {
    refSelectBar.value.update(data)
    _setIdFrom('docs', data['docs'])
    _setIdFrom('categories', data['categories'])
  }
}

const onChangeMode = (e) => {
  saveForm[currNode.value.id]['assemblyMethod'] = e;
  onSelectChange()
}

const cbSelectBar = (action, data) => {
  if (action == 'select') {
    refSelectFile.value.show(data)
  } else if (action == 'update') {
    _setIdFrom('docs', data['docs'])
    _setIdFrom('categories', data['categories'])
  }
}

const onSelectChange = () => {
  const topK = selectTopN.value[currNode.value.assemblyMethod];
  _setIdFrom('topK', topK)
}

const onEditOutline = () => {
  emit('callback', 'open_edit')
}

const onCancel = () => {
  g.router.push({
    path: `/admin/template`
  })
}

const _setIdFrom = (key, value) => {
  const { id } = currNode.value;
  saveForm[id][key] = value;
  currNode.value = saveForm[id]
}

const cbAiField = (action, data) => {
  if (action == "update") {
    _setIdFrom('selectedFields', data)
  }
}

const onConfirm = () => {
  const configures = transTemplateSaveObject(saveForm)
  let issueItem = {}
  let isAllSelectedFields = true;
  for (let i = 0; i < configures.length; i++) {
    const { assemblyMethod, selectedFields } = configures[i];
    if (assemblyMethod != 'FIX_MODE' && selectedFields.length == 0) {
      isAllSelectedFields = false;
      issueItem = configures[i];
    }
  }
  if (isAllSelectedFields) {
    apiHintWrap(updatePptTemplateSetting(templateId, { configures }), '更新').then(({ status }) => {
      if (status) {
        saveForm = {}
        onCancel()
      }
    })
  } else {
    ElMessage.error(`${issueItem.label}有未选择的AI检索关联字段`);
  }
}

defineExpose({
  selectTopN, refTree, refSelectBar, refSelectFile, rdTopType,
  refOutLine, tempInfo, checkList, currNode, refAiField,
  selectBar, PptTree, EditOutline, selectFile, QuestionIcon,
  AiRetFields, TemplatePics, init, cbSelectFile, onSelectChange, cbSelectBar,
  onChangeMode, onCancel, onConfirm, onEditOutline, cbAiField
})
</script>

<style lang='scss'>
.et_bottom {
  .et_left {
    width: 480px;
    padding: 20px;

    .title {
      justify-content: space-between;
      margin-bottom: 18px;

      .title_left {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        padding-left: 9px;
        line-height: 24px;
        border-left: 3px solid var(--el-color-primary);
      }

      .title_right {
        font-size: 14px;
        color: var(--el-color-primary);
        cursor: pointer;
      }

    }

    .tree {
      width: 480px;
      height: 612px;
      background: #FAFAFA;
      border-radius: 4px;
    }
  }

  .et_right {
    padding: 20px;
    width: 100%;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      line-height: 24px;
      padding-left: 9px;
      border-left: 3px solid var(--el-color-primary);
    }

    .title1 {
      margin-top: 13px;
    }

    .rg_group {
      .el-radio__label {
        display: flex;
        flex-direction: row;

        .q_icon {
          margin: 0px 5px;
          padding-top: 1px;
        }
      }
    }

    .eb_fs {
      .ch_box {
        display: flex;
        flex-direction: row;
      }
    }

    .sel_topn {
      margin-top: 13px;
    }

    .r_input {
      width: 100%;
      margin-top: 14px;

      .el-input-group__append {
        cursor: pointer;
        padding: 0;
        text-align: center;

        .rmore {
          width: 70px;
        }
      }
    }

    .tppt {
      margin-top: 54px;
    }

    .rf_box {
      align-items: center;

      .el-radio {
        margin-bottom: -9px;
      }

      .td_txt {
        margin: 11px 0 0 12px;
      }
    }
  }
}

.bottom_btn {
  position: fixed;
  bottom: 0;
  height: 57px;
  width: calc(100vw - 213px);
  justify-content: center;
  background: #fff;
  box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.04);

  .el-button {
    margin-top: 13px;
  }
}
</style>