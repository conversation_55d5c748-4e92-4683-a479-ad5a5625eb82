<template>
    <div class="la_customer_wrap flex-column">
        <div class="la_header flex-row">
            <div class="la_left flex-row">
                <div class="la_title">
                    客户整体态度
                </div>
                <faceIcon :status="item['整体情绪']" :showface="true" />
            </div>

            <div class="la_detail" @click="onDetail">
                详情
            </div>
        </div>
        <div class="la_sub">
            态度分析
        </div>
        <div class="la_md">
            {{ item['理由'] }}
        </div>
    </div>
</template>

<script>
import faceIcon from "@/app_postmeet/components/faceIcon.vue"
export default {
    components: { faceIcon },
    data() {
        return {
            item: {
                '整体态度': '',
                '理由': '',
            },
            activeName: '概览',
            tags: ['概览', '客户态度', '风险项']
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })

    },
    methods: {
        init() {
            const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
            if (ar) {
                const t1 = ar.find(x => x.systemId == 101);
                if (t1.report) {
                    this.item = t1.report['客户侧整体态度'] || {
                        '整体态度': '',
                        '理由': '',
                    };
                }
            }
        },
        onDetail() {
            g.emitter.emit('update_analyse_menu', 'attitude')
        }
    }
}

</script>

<style lang="scss">
.la_customer_wrap {
    position: relative;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 20px;
    margin: 12px 0;

    .la_header {
        justify-content: space-between;

        .la_left {
            width: 300px;

            .la_title {
                width: 96px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #262626;
                line-height: 26px;
                text-align: left;
                font-style: normal;
            }
        }

        .icon {
            margin: 4px 0 0 12px;
        }

        .la_detail {
            height: 22px;
            font-size: 14px;
            color: #436BFF;
            line-height: 22px;
            cursor: pointer;
        }
    }

    .la_sub {
        font-weight: 500;
        font-size: 12px;
        color: #436BFF;
        line-height: 18px;
        width: 56px;
        height: 20px;
        background: #E6EBFD;
        border-radius: 2px;
        text-align: center;
        margin: 12px 0;
    }

    .la_md {
        font-size: 14px;
    }
}
</style>