<template>
  <div class="ser_task_completion" v-ai-tip="'bottom-right'">
    <div class="box-shadow">
      <div class="task-header">
        <span class="title">任务达成情况</span>
        <div class="target-score">任务评估达成率
          <el-tooltip class="box-item" effect="dark" content="各任务评估维度达成率之和 / 任务评估维度数" placement="top">
            <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon_min" />
          </el-tooltip>
          <span :style="{ color: pieData.averageTaskValue >= taskStandardScore ? '#52C41A' : '#F5222D' }">{{
            pieData.averageTaskValue }}%</span>
          <span class="target-score-1">达标值: {{ taskStandardScore }}%</span>
        </div>
      </div>
      <AvgBarChart ref="refAvgBarChart" :isShowAverage="false" class="avg_bar_chart_2" :innerWidth="innerWidth"
        :standardScore="taskStandardScore" mark="%" />
      <PieChartWithTitle title="任务达成详情" sub-title="点击饼图图例可跳转查看拜访详情" :chart-data="chartData" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Question from "@/icons/question.vue";
import AvgBarChart from '@/app_client/components/AvgBarChart.vue'
import PieChartWithTitle from '@/app_client/components/TeamBoardCore/components/PieChartWithTitle.vue'
import { round } from '@/js/utils.js'
import { getOssUrl } from "@/js/utils.js";
const props = defineProps({
  pieData: {
    type: Object,
    default: () => ({})
  },
  barData: {
    type: Array,
    default: () => []
  }
})
const avgRate = ref(0)
const userData = ref({})
const refAvgBarChart = ref(null)

const innerWidth = ref(0)

const chartData = computed(() => {
  return [
    {
      value: props.pieData.taskCompletePassCount,
      name: '达成',
      type: 'achievement',
      color: '#67C23A'
    },
    {
      value: props.pieData.taskCompleteNotPassCount,
      name: '未达成',
      type: 'unAchievement',
      color: '#F56C6C'
    },
    {
      value: unDetermineNum.value,
      name: '结果未评估',
      type: 'unDetermine',
      color: '#909399'
    }
  ]
})
const taskStandardScore = computed(() => {
  return g.clientBoardStore.standardSetting.taskRate4Pass;
})

const averageValue = ref(0)

const unDetermineNum = computed(() => {
  return props.pieData.totalVisitCount - props.pieData.taskCompletePassCount - props.pieData.taskCompleteNotPassCount
})

watch(() => props.barData, () => {
  nextTick(() => {
    innerWidth.value = document.querySelector(".ser_task_completion").offsetWidth

    const sum = props.barData.reduce((total, item) => {
      return total + (+item.value || 0)
    }, 0)
    console.log('sum', sum, props.barData)
    averageValue.value = (sum / props.barData.length).toFixed(2)
    refAvgBarChart.value.init(props.barData)
  })
})

</script>

<style scoped lang="scss">
.ser_task_completion {
  background: #fff;
  border-radius: 8px;
  padding: 1px;
  border: none;
  border-radius: 6px;
  background-image: linear-gradient(#fff, #fff), // 跟背景色保持一致，根据实际情况修改
    linear-gradient(135deg, rgba(194, 54, 253, 1), rgba(97, 70, 255, 1), rgba(67, 169, 241, 1)); // 取border-image的渐变色，按实际来修改
  background-origin: border-box;
  background-clip: content-box, border-box;
  overflow: hidden;

  .box-shadow {
    padding: 24px;
    box-sizing: border-box;
    border-radius: 6px;
    background: linear-gradient(135deg, rgba(199, 120, 255, 0.03) 0%, rgba(118, 161, 255, 0.03) 50%, rgba(67, 221, 255, 0.03) 100%), #FFFFFF;
  }

  .task-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-weight: 500;
      margin-right: 12px;
      font-size: 16px;
      color: #262626;
    }

    .target-score,
    .target-score-1 {
      font-size: 12px;
      color: #8C8C8C;
      margin-left: 8px;
      display: flex;
      align-items: center;
      line-height: 22px;
      font-weight: 400;

      img {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }

    .target-score-1 {
      margin-left: 0;
      position: relative;
      margin-left: 16px;

      &::before {
        content: '';
        display: block;
        width: 1px;
        height: 12px;
        background-color: #86909C;
        position: absolute;
        left: -8px;
        top: 4px;
      }
    }

    .cgreen {
      color: #52C41A;
    }

    .cred {
      color: #F5222D;
    }

    .target-value {
      color: #999;
      font-size: 12px;
      margin-right: auto;
      margin-left: 12px;
    }
  }

  .vertical-bars {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 20px 0;
    padding: 0 20px;

    .bar-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;

      .bar-label {
        width: 120px;
        margin-right: 12px;
        text-align: right;

        .text {
          font-size: 12px;
          color: #999;
          white-space: nowrap;
          margin-bottom: 4px;
        }

        .percentage {
          font-size: 14px;
          color: #666;
        }
      }

      .bar-container {
        flex: 1;
        height: 24px;
        background: #f5f5f5;
        border-radius: 0 12px 12px 0;
        position: relative;
        overflow: hidden;

        .vertical-bar {
          position: absolute;
          left: 0;
          height: 100%;
          border-radius: 2px;
          transition: width 0.3s ease;

          &.success {
            background-color: #52C41A;
          }

          &.danger {
            background-color: #F5222D;
          }
        }
      }
    }
  }
}

.avg_bar_chart_2 {
  width: 100%;
  background: transparent;
  box-shadow: none;
}

.task-completion-dropdown {
  height: 200px;
  overflow-y: auto;
}
</style>