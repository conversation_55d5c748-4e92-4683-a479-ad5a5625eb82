<template>
  <div class="task-model-page">
    <el-tabs v-model="activeTab" class="model-tabs">
      <el-tab-pane label="任务项" name="task">
        <TaskListTable ref="taskListRef" />
      </el-tab-pane>
      <el-tab-pane label="任务模型" name="model">
        <TaskModelTable ref="taskModelTableRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import TaskListTable from './components/TaskListTable.vue';
import TaskModelTable from './components/TaskModelTable.vue';

const activeTab = ref('task');
const taskListRef = ref();
const taskModelTableRef = ref();

</script>

<style lang="scss" scoped>
.task-model-page {
  padding: 20px;
}
</style>