<template>
  <MyTable ref="refTable" :cfg="datas" class="board_team_table" @callback="cbTable">
    <template #_header_left>
      <div>
        <el-radio-group v-model="viewType" @change="onTypeChange" v-show="needShowTeam">
          <el-radio-button value="team">团队</el-radio-button>
          <el-radio-button value="member">成员</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <template #_header_right_pre>
      <div class="filter-group">
        <el-button type="default" @click="exportData" :disabled="exportLoading">
          <el-icon style="vertical-align: middle" v-if="exportLoading">
            <Loading />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </template>
    <template #header_deptName>
      <LowHintHeader title="部门" />
    </template>
    <template #col_deptName="scope">
      <div v-if="scope.row._index == 0" class="dept_header">
        <DeptIcon />
        <span>{{ scope.row.deptName }}</span>
        <span>({{ totalSubDeptCount }})</span>
      </div>
      <div v-else>
        <span>{{ scope.row.deptName }}</span>
      </div>
    </template>
    <template #header_visitPlanCompletionRate>
      <HintHeader column="visitPlanCompletionRate" />
    </template>
    <template #header_totalVisitCount>
      <HintHeader column="totalVisitCount" />
    </template>
    <template #header_visitTargetAchievementRate>
      <HintHeader column="visitTargetAchievementRate" />
    </template>
    <template #header_salesAbilityPassRate>
      <HintHeader column="salesAbilityPassRate" />
    </template>
    <template #header_performanceExpectationsRate>
      <HintHeader column="performanceExpectationsRate" />
    </template>
    <template #col_totalVisitPlanCount="scope">
      <TableCell :scope="scope" column="totalVisitPlanCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_averageVisitDurationPerPerson="scope">
      <TableCell :scope="scope" column="averageVisitDurationPerPerson" :overviewData="overviewDataAvg"
        valueType="average" />
    </template>
    <template #col_totalVisitDuration="scope">
      <TableCell :scope="scope" column="totalVisitDuration" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitPlanCompletionRate="scope">
      <TableCell :scope="scope" column="visitPlanCompletionRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
    <template #col_visitTargetAchievementRate="scope">
      <TableCell :scope="scope" column="visitTargetAchievementRate" :overviewData="overviewDataAvg"
        valueType="percent" />
    </template>
    <template #col_performanceExpectationsRate="scope">
      <TableCell :scope="scope" column="performanceExpectationsRate" :overviewData="overviewDataAvg"
        valueType="percent" />
    </template>
    <template #col_totalVisitCount="scope">
      <TableCell :scope="scope" column="totalVisitCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_averageVisitCountPerPerson="scope">
      <TableCell :scope="scope" column="averageVisitCountPerPerson" :overviewData="overviewDataAvg"
        valueType="average" />
    </template>
    <template #col_averageVisitPlanCountPerPerson="scope">
      <TableCell :scope="scope" column="averageVisitPlanCountPerPerson" :overviewData="overviewDataAvg"
        valueType="average" />
    </template>
    <template #col_averageVisitDurationPerVisit="scope">
      <TableCell :scope="scope" column="averageVisitDurationPerVisit" :overviewData="overviewDataAvg"
        valueType="average" />
    </template>
    <template #col_visitPlanCount="scope">
      <TableCell :scope="scope" column="visitPlanCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitCompletedCount="scope">
      <TableCell :scope="scope" column="visitCompletedCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitTargetCount="scope">
      <TableCell :scope="scope" column="visitTargetCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitTargetCompletedCount="scope">
      <TableCell :scope="scope" column="visitTargetCompletedCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_completedVisitPlanCount="scope">
      <TableCell :scope="scope" column="completedVisitPlanCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitPlanCustomerCount="scope">
      <TableCell :scope="scope" column="visitPlanCustomerCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitedCustomerCount="scope">
      <TableCell :scope="scope" column="visitedCustomerCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitTargetAchievedCount="scope">
      <TableCell :scope="scope" column="visitTargetAchievedCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_salesAbilityPassCount="scope">
      <TableCell :scope="scope" column="salesAbilityPassCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_salesAbilityPassRate="scope">
      <TableCell :scope="scope" column="salesAbilityPassRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
    <template #col_averageVisitPlanPerPerson="scope">
      <TableCell :scope="scope" column="averageVisitPlanPerPerson" :overviewData="overviewDataAvg"
        valueType="average" />
    </template>
    <template #col_averageVisitsPerPerson="scope">
      <TableCell :scope="scope" column="averageVisitsPerPerson" :overviewData="overviewDataAvg" valueType="average" />
    </template>
    <template #col_taskCompletePassRate="scope">
      <TableCell :scope="scope" column="taskCompletePassRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
  </MyTable>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import { Loading } from "@element-plus/icons-vue";
import LowHintHeader from "./LowHintHeader.vue";
import HintHeader from "./HintHeader.vue";
import TableCell from "./TableCell.vue";
import DeptIcon from "@/app_client/icons/dept.vue";
import { now } from "@/js/utils.js";
import { column_widths, getColumns, numal_team_column, getReportDate, type_column_hints, default_column_width } from "../misc.js";
import { getReportSubTeamsPager, getTeamVisitReportExport } from "@/app_client/tools/api.js";

const totalSubDeptCount = ref(0);
const emit = defineEmits(["typeChange"]);
const viewType = ref("team");
const refTable = ref();
const deptId = ref('');
const needShowTeam = ref(false);
let hasInit = false;
const exportLoading = ref(false);
const columns = getColumns(true);
const periodType = computed(() => g.clientBoardStore.periodType);
const reportDate = getReportDate();
const overviewData = computed(() => g.clientBoardStore.getTeamOverviewData(false));
const overviewDataAvg = computed(() => g.clientBoardStore.getTeamOverviewData(true));

const tableWidth = ref(columns.length * default_column_width + 'px');
const cbTable = (type, columns) => {
  if (type == "update_columns") {
    tableWidth.value = columns.length * default_column_width + 'px';
  }
};

const datas = reactive({
  tableid: "team_situation_team",
  param: {},
  need_init_load: false,
  need_header: true,
  show_pager: true,
  show_search: false,
  fixed_column: "deptName",
  sortable: "custom",
  columns,
  always_show_columns: ["deptName"],
  template_header: ["deptName", ...Object.keys(type_column_hints)],
  default_select_columns: columns,
  template: ['deptName', ...numal_team_column],
  sortables: [...numal_team_column],
  column_widths,
  urlGet: (p) => {
    p.dptIds = toRaw(p.dptIds);
    return new Promise((resolve) => {
      getReportSubTeamsPager(deptId.value, periodType.value, p, reportDate).then((resp) => {
        if (resp.data.totalNum > 0 && overviewData.value && Object.keys(overviewData.value).length > 0) {
          resp.data.datas.unshift(overviewData.value);
        }
        totalSubDeptCount.value = resp.data.totalNum;
        resolve(resp);
      });

    });
  },
});

const onTypeChange = (value) => {
  emit("typeChange", value);
};

const onTimeChange = (value) => {
  if (hasInit) {
    datas.param.startTime = value.startTime;
    datas.param.endTime = value.endTime;
    refTable.value.search();
  }
};

const init = () => {
  viewType.value = 'team';
  needShowTeam.value = g.clientBoardStore.detailNeedShowTeam();
  deptId.value = g.clientBoardStore.getTeamTableDeptId();

  // 确保在设置数据后立即刷新表格
  nextTick(() => {
    if (!hasInit) {
      hasInit = true;
    }
    refTable.value.search();
  });
};

const exportData = () => {
  const data = toRaw(datas.param);
  data.filename = "团队情况导出_" + now("yyyyMMddhhmmss");
  exportLoading.value = true;
  getTeamVisitReportExport(deptId.value, periodType.value, data, reportDate).then((resp) => {
    exportLoading.value = false;
  });
};


defineExpose({
  init,
  refTable,
  BtnDate,
  onTimeChange,
  exportData,
});
</script>

<style lang="scss" scoped>
.board_team_table {
  .filter-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .dept-select {
      width: 160px;
      margin-left: 12px;
    }
  }

  :deep(.el-table) {
    .dept_header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      color: #262626;
      font-size: 14px;
      font-weight: 500;
    }

    th {
      .cell {
        width: 100%;
        justify-content: center;
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        h3 {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
          margin: 0;
        }

        .el-icon {
          color: #8C8C8C;
          cursor: pointer;
        }
      }
    }

    td {
      padding: 0 !important;
      height: 40px;
      text-align: center;
      line-height: 40px;
    }

    .cell {
      padding: 0 !important;
    }
  }

  :deep(table) {
    width: v-bind(tableWidth) !important;
  }
}
</style>
