<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="添加到沟通纪要" transform="translate(-1030.000000, -366.000000)">
                <g id="编组-7" transform="translate(1016.000000, 354.000000)">
                    <g id="icon/上传" transform="translate(14.000000, 12.000000)">
                        <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="16" height="16"></rect>
                        <path
                            d="M13.3333333,14.6666667 L2.66666667,14.6666667 C2.29848,14.6666667 2,14.3682 2,14 L2,2 C2,1.63181333 2.29848,1.33333333 2.66666667,1.33333333 L13.3333333,1.33333333 C13.7015333,1.33333333 14,1.63181333 14,2 L14,14 C14,14.3682 13.7015333,14.6666667 13.3333333,14.6666667 Z M12.6666667,13.3333333 L12.6666667,2.66666667 L3.33333333,2.66666667 L3.33333333,13.3333333 L12.6666667,13.3333333 Z M5.33333333,6 L10.6666667,6 L10.6666667,7.33333333 L5.33333333,7.33333333 L5.33333333,6 Z M5.33333333,8.66666667 L10.6666667,8.66666667 L10.6666667,10 L5.33333333,10 L5.33333333,8.66666667 Z"
                            id="形状" fill="#595959"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
