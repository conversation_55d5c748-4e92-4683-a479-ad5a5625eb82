<template>
  <div class="la_sale_perform_wrap flex-column">
    <AbilityAssessment :status="status" :score="avgScore" @on-upgrade="onUpgrade">
      <template #detail>
        <div @click="onExpand" class="la_detail">
          {{ isExpand ? "收起" : "展开详情" }}
        </div>
      </template>
    </AbilityAssessment>
    <div v-if="isExpand">
      <lcTaskPerfHeader ref="refTaskPerfHeader" />
    </div>
    <RadarChart ref="refRadar" />
    <!-- new code here -->
    <div class="dimension-list">
      <div v-for="(item, index) in dimensionList" :key="index" class="dimension-item">
        <div class="dimension-header">
          <span class="dimension-title">{{ item.content.name }}</span>
          <span :class="`dimension-score ${getStatusClass(item.content.score2)}`">{{
            item.content.score2
          }}</span>
        </div>
        <div class="dimension-content">
          <MdViewer :md="item.content.reasonAndEvidence.replace(/\n/g, ' \n\n').replace(/'''/g, '')" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MdViewer from "@/components/Markdown/MdViewer.vue"
import AbilityAssessment from "../lcOverview/components/AbilityAssessment.vue";
import lcTaskPerfHeader from "./lcTaskPerfHeader.vue";
import RadarChart from "@/components/RadarChart.vue";

export default {
  components: { MdViewer, AbilityAssessment, RadarChart, lcTaskPerfHeader },
  data() {
    return {
      isExpand: false,
      avgScore: 0,
      status: "",
      evaluation: "",
      list: [],
      dimensionList: [],
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    getStatusClass(score) {
      return score > 80 ? "s2" : score > 60 ? "s1" : "s0";
    },
    init() {
      this.status = "优秀";
      let ar =
        g.postmeetStore.data.saleReport.salesCounsellingReports.find(
          (x) => x.systemId == 205
        )?.report?.tasks || [];
      ar = ar.filter(x => x?.content?.score);
      ar.forEach((item) => {
        item.content["score2"] = parseInt(item.content.score.replace("/100", ""));
      });
      if (ar.length === 0) {
        this.avgScore = 0;
      } else {
        this.avgScore = Math.round(
          ar.reduce((sum, item) => sum + item.content.score2, 0) / ar.length
        );
      }
      this.dimensionList = ar;
      this.showChart();
    },
    showChart() {
      const data = this.dimensionList.map((x) => {
        return {
          label: x.content.name,
          value: x.content.score2,
        };
      });
      this.$refs.refRadar.init(data, 100);
    },
    onExpand() {
      this.isExpand = !this.isExpand;
    },
    onUpgrade() {
      this.$emit("on-upgrade");
    },
  },
};
</script>

<style lang="scss">
@use "../coach.scss";

.la_sale_perform_wrap {
  position: relative;

  .la_box {
    margin: 24px 0;
    background: #f9fafc;
    padding: 20px;
    margin: 12px 0;
    border-radius: 8px;

    .la_left {
      width: 300px;

      .la_title {
        font-weight: 500;
        font-size: 16px;
        color: #262626;
        line-height: 26px;
      }
    }

    .icon {
      margin: 4px 0 0 12px;
    }

    .la_detail {
      height: 22px;
      font-size: 14px;
      color: #436bff;
      line-height: 22px;
      cursor: pointer;
    }
  }
}

.dimension-list {
  margin-top: 20px;

  .dimension-item {
    background: #f9fafc;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .dimension-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .dimension-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        border-left: 4px solid #436bff;
        padding-left: 8px;
      }

      .dimension-score {
        font-size: 14px;
        font-weight: bold;
        margin-left: 12px;
      }
    }

    .dimension-content {
      .detail-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .time-range {
          margin-bottom: 8px;
          color: #8c8c8c;
          font-size: 14px;

          .time {
            color: #436bff;
          }
        }

        .detail-content {
          font-size: 14px;
          color: #595959;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
