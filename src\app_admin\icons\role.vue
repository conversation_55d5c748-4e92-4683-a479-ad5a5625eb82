<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="登录" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="管理后台-我的企业导航" transform="translate(-20.000000, -216.000000)" fill="#000000">
                <g id="一级菜单/未选中备份-4" transform="translate(0.000000, 204.000000)">
                    <g id="员工名册---云学堂111-1" transform="translate(20.000000, 12.000000)">
                        <rect id="矩形" opacity="0" x="0" y="0" width="16" height="16"></rect>
                        <g id="编组-4" transform="translate(0.816296, 0.666667)">
                            <g id="icon_讲师库备份" fill-rule="nonzero">
                                <g id="编组-5">
                                    <path
                                        d="M6.98277653,8 C7.16687144,8 7.31610986,8.14923842 7.31610986,8.33333333 L7.31610986,9 C7.31610986,9.18409492 7.16687144,9.33333333 6.98277653,9.33333333 L4.66666667,9.33333333 C2.87416353,9.33333333 1.41212126,10.7482045 1.33641301,12.5220736 L1.33333333,12.6666667 L1.33333333,13.6666667 C1.33333333,13.8507616 1.18409492,14 1,14 L0.333333333,14 C0.149238417,14 0,13.8507616 0,13.6666667 L0,12.6666667 C0,10.1430322 2.00318892,8.08726754 4.5062335,8.00270601 L4.66666667,8 L6.98277653,8 Z M10.8229087,8.0778784 C11.8942842,7.45931948 13.2642467,7.82639982 13.8828056,8.8977753 C14.5013645,9.96915078 14.1342842,11.3391133 13.0629087,11.9576722 C11.9915332,12.5762311 10.6215707,12.2091508 10.0030118,11.1377753 C9.38445286,10.0663998 9.7515332,8.69643732 10.8229087,8.0778784 Z M12.7742931,9.5377753 C12.5091964,9.07861438 11.9220696,8.92129423 11.4629087,9.18639091 C11.0037478,9.45148759 10.8464276,10.0386144 11.1115243,10.4977753 C11.376621,10.9569362 11.9637478,11.1142564 12.4229087,10.8491597 C12.8820696,10.584063 13.0393897,9.99693622 12.7742931,9.5377753 Z M9.3837037,0 C10.0922646,0 10.6666667,0.574402084 10.6666667,1.28296296 L10.6666667,1.28296296 L10.6666667,3.48148148 C10.6666667,5.40425061 9.10795431,6.96296296 7.18518519,6.96296296 C5.26241606,6.96296296 3.7037037,5.40425061 3.7037037,3.48148148 C3.7037037,1.55871235 5.26241606,0 7.18518519,0 L7.18518519,0 L9.3837037,0 Z M9.33303704,1.33266667 L7.18518519,1.33333333 C5.99879572,1.33333333 5.03703704,2.29509202 5.03703704,3.48148148 C5.03703704,4.66787094 5.99879572,5.62962963 7.18518519,5.62962963 C8.37157465,5.62962963 9.33333333,4.66787094 9.33333333,3.48148148 L9.33333333,3.48148148 L9.33303704,1.33266667 Z"
                                        id="形状结合"></path>
                                </g>
                            </g>
                            <g id="编组"
                                transform="translate(8.638807, 12.294903) rotate(-30.000000) translate(-8.638807, -12.294903) translate(6.238807, 11.174903)">
                                <path
                                    d="M0.4,0 L4.8,0 L4.8,0 L4.8,1.28 L0.4,1.28 C0.1790861,1.28 0,1.1009139 0,0.88 L0,0.4 C0,0.1790861 0.1790861,0 0.4,0 Z"
                                    id="矩形"></path>
                                <path
                                    d="M1.6,0.8 L2.8,0.8 C3.0209139,0.8 3.2,0.9790861 3.2,1.2 L3.2,1.68 C3.2,1.9009139 3.0209139,2.08 2.8,2.08 L1.6,2.08 L1.6,2.08 L1.6,0.8 Z"
                                    id="矩形备份"
                                    transform="translate(2.400000, 1.440000) rotate(90.000000) translate(-2.400000, -1.440000) ">
                                </path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'DocIcon',
}
</script>
