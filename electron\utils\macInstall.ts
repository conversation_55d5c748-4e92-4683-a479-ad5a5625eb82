import { join } from 'path';
import { app, crashReporter } from 'electron';
import extract from 'extract-zip';
import { spawn } from 'child_process';
import fs from 'fs';
import sudo from 'sudo-prompt';
import { errorWriteLocal } from './errorWriteLocal';
import config from './config';

const APP_PATH = 'AudioUnitMac.app/Contents/MacOS/AudioUnitMac';
const DRIVER_NAME = 'yxtaudioproxy.driver';
const TARGET_PATH = '/Library/Audio/Plug-Ins/HAL';

// 获取资源路径
const getResourcePath = (): string => {
    return join(
        config.isPackaged ? process.resourcesPath : process.cwd(),
        'build/extraResources'
    );
};

/**
 * 解压 Mac 相关资源文件
 * 包含 ffmpeg 和音频驱动等
 */
export async function macInstall(): Promise<void> {
    // 开发环境系统不执行
    if (process.env.NODE_ENV === 'development') {
        console.log('macInstall development')
        return;
    }

    const resourcePath = getResourcePath();
    const source = join(resourcePath, 'mac.zip');
    const targetDir = resourcePath;

    errorWriteLocal('Mac resources extraction - Source:', source);
    errorWriteLocal('Mac resources extraction - Target:', targetDir);

    try {
        await extract(source, { dir: targetDir });
        errorWriteLocal('Mac resources extraction completed');
    } catch (err: any) {
        errorWriteLocal('Mac resources extraction failed:', err?.message);
        throw new Error(`Failed to extract mac resources: ${err?.message}`);
    }
}

interface DriverPaths {
    driverPath: string;
    appPath: string;
}

// 获取驱动路径
const getDriverPath = (): DriverPaths => {
    const resourcePath = join(
        process.env.NODE_ENV === 'development' ? process.cwd() : process.resourcesPath,
        'build/extraResources/mac'
    );
    return {
        driverPath: join(resourcePath, DRIVER_NAME),
        appPath: join(resourcePath, APP_PATH)
    };
};

// 获取驱动版本
function getDriverVersion(plistData: Buffer): string | undefined {
    const info = plistData.toString();
    const versionMatch = info.match(/<key>CFBundleShortVersionString<\/key>\n\s+<string>(\d+\.\d+\.\d+)<\/string>/);
    const version = versionMatch?.[1];
    errorWriteLocal('getDriverVersion', version);
    return version;
}

// 启动音频应用
async function startAudioApp(): Promise<boolean> {
    errorWriteLocal('startAudioApp');
    if (global.virtualAudioProcess) {
        errorWriteLocal('startAudioApp result ready has');
        return true;
    }

    try {
        const { appPath } = getDriverPath();
        global.virtualAudioProcess = spawn(appPath, []);
        errorWriteLocal('startAudioApp result success');
        return true;
    } catch (err) {
        errorWriteLocal('Start audio app failed:', err);
        return false;
    }
}

// 安装驱动
function installDriver(): Promise<boolean> {
    return new Promise((resolve) => {
        errorWriteLocal('installDriver');
        const { driverPath } = getDriverPath();
        // @ts-ignore
        const options: sudo.Options = { name: 'NovaGuide' };

        let command = `rm -rf "${TARGET_PATH}/${DRIVER_NAME}" && cp -r "${driverPath}" "${TARGET_PATH}" && launchctl kickstart -kp system/com.apple.audio.coreaudiod`;

        if (!fs.existsSync(TARGET_PATH)) {
            command = `mkdir -p "${TARGET_PATH}" && ${command}`;
        }

        errorWriteLocal(`Installing audio driver: ${driverPath} to ${TARGET_PATH}`);

        sudo.exec(command, options, async (error: Error | null) => {
            if (error) {
                errorWriteLocal('Driver installation error:', error?.message);
                resolve(false);
                return;
            }
            resolve(true);
        });
    });
}

// 检查是否有新版本驱动
async function hasNewDriverVersion(): Promise<boolean> {
    try {
        errorWriteLocal('hasNewDriverVersion');
        const { driverPath } = getDriverPath();
        const installedPlistPath = join(TARGET_PATH, DRIVER_NAME, 'Contents/Info.plist');
        const newPlistPath = join(driverPath, 'Contents/Info.plist');
        const newVersion = getDriverVersion(fs.readFileSync(newPlistPath));
        const installedVersion = getDriverVersion(fs.readFileSync(installedPlistPath));

        errorWriteLocal(`Audio driver versions - New: ${newVersion}, Installed: ${installedVersion}`);

        return newVersion > installedVersion;
    } catch (err) {
        errorWriteLocal('Version check error:', err);
        return false;
    }
}

// 安装并启动虚拟声卡
export async function installMacVirtualAudio(): Promise<boolean> {
    errorWriteLocal('installMacVirtualAudio')
    if (!config.isMac) {
        return true
    }
    if (!await checkMacVirtualAudio() || await hasNewDriverVersion()) {
        await installDriver();
    }
    return await startAudioApp();
}

// 只检查驱动是否安装
export async function checkMacVirtualAudio(): Promise<boolean> {
    errorWriteLocal('checkMacVirtualAudio')
    const installedPlistPath = join(TARGET_PATH, DRIVER_NAME, 'Contents/Info.plist');
    const hasDriver = fs.existsSync(installedPlistPath);
    errorWriteLocal('checkMacVirtualAudio result', hasDriver)
    return hasDriver;
}

export function killVirtualAudio() {
    if (config.isMac && global.virtualAudioProcess) {
        errorWriteLocal('killVirtualAudio', global.virtualAudioProcess)
        global.virtualAudioProcess.kill(2);
        global.virtualAudioProcess = null
    }
}

export function initCrashReporter() {
    crashReporter.start({
        productName: 'NovaGuide',
        companyName: '江苏云学堂网络科技有限公司',
        submitURL: '', // 本地存储不需要提交URL
        uploadToServer: false
    })
}