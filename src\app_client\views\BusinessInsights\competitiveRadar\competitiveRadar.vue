<template>
    <div class="competitiveRadar_wrap">
        <h1 class="cr_title">总览</h1>
        <div class="competitiveRadar_wrap_content">
            <div class="p15">
                <aiAnalysis :data="aiAnalysisAllData" :loading="loadingAiAnalyse" style="margin-bottom: 24px;" />
                <overallCompetitors :totalChartsMetrics="totalChartsMetrics" />
            </div>
            <div style="page-break-after: always;"></div>
            <p class="cr_title">竞品分析</p>
            <middleDetails class="p15" :totalChartsMetrics="totalChartsMetrics" @wordClickToTable="wordClickToTable" />
            <div style="page-break-after: always;"></div>
            <p class="cr_title" ref="evaluationRef">竞品评价</p>
            <checkSource ref="checkSourceRef" />
        </div>
    </div>
</template>

<script setup>
import aiAnalysis from '../components/aiAnalysis.vue'
import overallCompetitors from './infoComponents/overallCompetitors.vue'
import middleDetails from './infoComponents/middleDetails.vue'
import checkSource from './infoComponents/checkSource/checkSource.vue'

const evaluationRef = ref(null)
const checkSourceRef = ref(null)

const totalChartsMetrics = computed(() => [{
    key: 'positiveCount',
    name: '积极',
    color: colorObject.value['positive']
}, {
    key: 'negativeCount',
    name: '消极',
    color: colorObject.value['negative']
}, {
    key: 'neutralCount',
    name: '中性',
    color: colorObject.value['neutral']
}])

const colorObject = computed(() => {
    return g.clientBiStore.colorObject || {}
})

const loadingAiAnalyse = ref(true);
// 构建总览页面的AI分析数据对象
const aiAnalysisAllData = computed(() => g.clientBiStore.selectAIAnalysisAll || '');

const wordClickToTable = (obj, value) => {
    // if (evaluationRef.value) {
    //     evaluationRef.value.scrollIntoView({
    //         behavior: 'smooth',
    //         block: 'start'
    //     })
    // }
}

const init = async () => {
    await g.clientBiStore.getEvaluationDistribution()
    loadingAiAnalyse.value = true;
    await g.clientBiStore.getCompetitorAIAnalysisAll()
    loadingAiAnalyse.value = false;
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

// 初始化获取总览数据
onMounted(() => {
    init()
})


</script>

<style lang="scss" scoped>
.competitiveRadar_wrap {
    padding: 24px 0;
    background-color: #fff;
    min-height: 100vh;

    .competitiveRadar_wrap_content {
        display: flex;
        flex-direction: column;
        // gap: 16px;
    }

    .p15 {
        padding: 24px 15px;
    }

    .cr_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }
}
</style>