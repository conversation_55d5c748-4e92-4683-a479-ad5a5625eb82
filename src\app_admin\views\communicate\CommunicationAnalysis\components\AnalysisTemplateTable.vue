<template>
  <div class="analysis-template-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>

      <template #col_templateName="{ row }">
        <div class="template-name">{{ row.templateName }}</div>
      </template>

      <template #col_dimensions="{ row }">
        <div class="dimensions">
          <div v-for="(line, index) in (row.dimensions || '').split('\n')" :key="index" class="dimension-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>
    <AnalysisTemplateDrawer ref="analysisTemplateDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getAnalysisTemplateListData, deleteAnalysisTemplate } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import AnalysisTemplateDrawer from './AnalysisTemplateDrawer.vue';

const refTable = ref();
const analysisTemplateDrawerRef = ref();
const tableConfig = reactive({
  tableid: 'analysis_template_list',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模板名称",
  delete_hint_column: 'templateName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["templateName", "dimensions", "creator", "createTime"],
  template: ["templateName", "dimensions"],
  urlGet: getAnalysisTemplateListData,
  urlDelete: deleteAnalysisTemplate
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  analysisTemplateDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  analysisTemplateDrawerRef.value.openDrawer(row);
};

const handleDelete = (row) => {
  confirmDelete(row.templateName, (status) => {
    if (status) {
      deleteAnalysisTemplate(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

const handleFormSuccess = () => {
  refTable.value.search();
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.analysis-template-table {
  padding: 24px 0;

  .template-name {
    font-weight: 500;
    color: #333;
  }

  .dimensions {
    max-width: 400px;

    .dimension-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>