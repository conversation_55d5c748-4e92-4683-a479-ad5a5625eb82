<template>
  <div class="col_detail_wrap">
    <div class="detail-item">
      <span class="label">客户参与人：</span>
      <span>{{ row.attendees }}</span>
    </div>
    <div class="detail-item">
      <span class="label">目标：</span>
      <div class="target-wrap">
        <span>{{ row.salesGoal }}</span>
        <el-tag size="small" :type="getTagType(row.salesAchievementStatus)" v-if="row.salesAchievementStatus">{{
          row.salesAchievementStatus }}</el-tag>
        <el-tag size="small" type="warning" v-else-if="!row.reportGenerated">未生成报告</el-tag>
      </div>
    </div>
    <div class="detail-item" v-if="row.salesAchievementAnalysis">
      <span class="label">达成分析：</span>
      <span>{{ row.salesAchievementAnalysis }}</span>
    </div>
    <div class="detail-item" v-if="row.achieveResult">
      <span class="label">成果：</span>
      <span>{{ row.achieveResult }}</span>
    </div>
    <div class="detail-item" v-if="row.nextPlan">
      <span class="label">下一步：</span>
      <span>{{ row.nextPlan }}</span>
    </div>
    <div class="detail-item" v-if="row.existRisk">
      <span class="label">风险：</span>
      <span>{{ row.existRisk }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  row: {
    type: Object,
    required: true,
  },
});

const getTagType = (status) => {
  if (status == '达成') {
    return 'success'
  } else if (status == '未达成') {
    return 'danger'
  }
  return 'warning'
}

// const record = ref({
//   participants: '董航晨、发言人1、任铭',
//   target: '推动合同签署',
//   result: '确认报价接受度，但需补充技术文档',
//   keyIssues: '客户担忧交付周期，我方承诺提供过往案例数据，周三提交技术文档，周五预约高层会议',
//   nextStep: '周三提交技术文档，周五预约高层会议',
//   risk: '竞品B公司近期持续跟客户'
// })

</script>

<style lang="scss" scoped>
.col_detail_wrap {
  padding: 16px;

  .detail-item {
    margin-bottom: 12px;
    line-height: 20px;
    display: flex;
    .label {
      color: #909399;
      flex-shrink: 0;
    }

    .target-wrap {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>