<template>
  <div class="meddic_head flex-row">
    <div>MEDDIC</div>
    <el-popover :width="500" trigger="click" popper-class="meddic_popper">
      <template #reference>
        <div class="vh_refer">
          <img :src="getOssUrl('meddic_header.png')" />
        </div>
      </template>
      <div class="vnh_pop_main">
        <ol>
          <li v-for="item in metrics" :key="item.name">
            <div class="vp2">
              <div class="vp2_title">
                <span class="vp2_name">{{ item.name }}</span>
                <span class="vp2_cn_name">({{ item.cn_name }}):</span>
                <span class="vp2_desc">{{ item.desc }}</span>
              </div>
            </div>
          </li>
        </ol>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils";
import { meddic_metrics } from "./misc";

const props = defineProps(['data']);

const metrics = ref(meddic_metrics)

defineExpose({
});
</script>

<style lang="scss">
.meddic_head {
  justify-content: space-between;

  .vh_refer {
    margin-top: 2px;
    margin-left: 59px;
    cursor: pointer;
  }
}

.meddic_popper {
  .vnh_pop_main {
    ol {
      list-style: decimal !important;
      padding-left: 20px;
      margin: 0;

      li {
        list-style: decimal !important;
        padding: 8px 0;

        .vp2 {
          .vp2_title {
            display: contents;
            align-items: baseline;
            gap: 4px;
            flex-wrap: wrap;
          }

          .vp2_name {
            font-family: "PingFang SC";
            font-weight: 600;
            font-size: 14px;
            color: #333;
          }

          .vp2_cn_name {
            font-family: "PingFang SC";
            font-weight: 600;
            font-size: 14px;
            color: #333;
          }

          .vp2_desc {
            font-size: 14px;
            color: #666;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
