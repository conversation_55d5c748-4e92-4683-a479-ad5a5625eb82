<template>
    <div class="btn_radio_wrap flex-row" v-if="tags_">
        <div v-for="tag in Object.keys(tags_)" :key="tag" @click="onClick(tag, tags_[tag])" v-show="tags_[tag].show">
            <BtnSelectAnalyseType ref="refAnalysisType" @change="onChangeAnalysisType"
                v-if="tag == 'normal_meet' && isShowAnalysisType" />
            <div v-else :class="`lam ${activeId == tag ? 'active' : 'normal'}`">
                {{ tags_[tag].name }}
            </div>
        </div>
    </div>
</template>

<script>
import BtnSelectAnalyseType from '@/components/BtnSelectAnalyseType.vue';
export default {
    components: { BtnSelectAnalyseType },
    props: {
        activeId: {
            type: String,
            default: ''
        },
        tags: {
            type: Object,
            default: () => ({})
        },
        analysisType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShowAnalysisType: false,
            tags_: {},
            rdViewType: ''
        }
    },
    watch: {
        activeId(val) {
            this.$nextTick(() => {
                this.$refs.refAnalysisType?.[0]?.setRdType(val);
            });
        },
        tags() {
            this.tags_ = this.tags;
        },
        analysisType: {
            immediate: true,
            handler(val) {
                if (val) {
                    this._updateRdType();
                }
            }
        }
    },
    mounted() {
        this.tags_ = this.tags;
    },
    methods: {
        setTags(tags) {
            this.tags_ = tags;
            this.isShowAnalysisType = true;
            this.$forceUpdate();
        },
        onChangeAnalysisType(selectType) {
            this.rdViewType = 'normal_meet';
            this.$emit('change-analysis-type', selectType);
        },
        onClick(tag, item) {
            if (!item.disabled) {
                this.setRdType(tag);
                this.$emit('update:activeId', item);
                this.$emit('callback', tag)
            }
        },
        _updateRdType() {
            this.$nextTick(() => {
                const refAnalysisType = this.$refs.refAnalysisType?.[0];
                if (refAnalysisType) {
                    refAnalysisType.setAnalysisType(this.analysisType);
                } else {
                    // console.log("refAnalysisType no dom")
                }
            });
        },
        setRdType(type) {
            this.rdViewType = type;
            this.$refs.refAnalysisType?.[0]?.setRdType(type);
        }
    }
}

</script>

<style lang="scss">
.btn_radio_wrap {
    flex-wrap: wrap;
    margin-bottom: 12px;

    .lam {
        padding: 8px 12px;
        height: 20px;
        line-height: 20px;
        border-radius: 4px;
        font-size: 14px;
        margin-right: 12px;
        cursor: pointer;
        user-select: none;
        margin-top: 12px;
    }

    .active {
        background: #436BFF;
        color: #FFFFFF;
    }

    .normal {
        background: #F0F3FF;
        color: #436BFF;
    }
}
</style>