<template>
    <AddContactOuterForm ref="refAddContactOuterForm" @submit="onSubmit" @cancel="onCancel" />
</template>

<script setup>
const refAddContactOuterForm = ref()
const emit = defineEmits(['callback'])

const onSubmit = (data) => {
    emit('callback', 'add_person', data)
    g.electronStore.sendMessage('arrange_visit', 'add-contact-outer', data)
    g.electronStore.closeWin('add_outer_contact')
}

const onCancel = () => {
    g.electronStore.closeWin('add_outer_contact')
}

onMounted(() => {
    g.electronStore.waitPageReady().then(data => {
        refAddContactOuterForm.value.show(data.id)
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

</script>