<template>
    <el-dialog v-model="dialogVisible" title="" width="316px" :close-on-click-modal="false" :show-close="false"
        class="dia_hint_confirm">
        <div>{{ hint }}</div>
        <template #footer>
            <div class="dialog-footer">
                <div @click="onConfirm('left')" class="dbtn btn_left">
                    {{ leftText }}
                </div>
                <div class="spline"></div>
                <div @click="onConfirm('right')" class="dbtn btn_right">
                    {{ rightText }}
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>

const dialogVisible = ref(false)
const props = defineProps({
    leftText: {
        type: String,
        default: "取消"
    },
    rightText: {
        type: String,
        default: "确定"
    },
    hint: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(['callback'])

const onConfirm = (action) => {
    emit('callback', action)
    dialogVisible.value = false
}

const show_dialog = () => {
    dialogVisible.value = true
}

defineExpose({
    show_dialog
})

</script>

<style lang="scss">
.dia_hint_confirm {
    padding: 0;
    --el-dialog-margin-top: 38vh;

    .el-dialog__body {
        padding: 12px;
    }

    .dialog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #e9e9e9;
        padding: 0 12px;
        height: 50px;

        .spline {
            width: 1px;
            height: 100%;
            background: #e9e9e9;
        }

        .dbtn {
            font-size: 16px;
            cursor: pointer;
            text-align: center;
            width: 50%;
            padding: 0;
        }

        .btn_left {
            color: #262626;
        }

        .btn_right {
            color: #436BFF;
        }
    }

}
</style>
