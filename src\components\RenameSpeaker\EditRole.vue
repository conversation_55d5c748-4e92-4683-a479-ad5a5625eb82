<template>
  <div class="diaedit_role_wrap">
    <div class="h_main">
      <div class="dia_header">编辑客户角色-已有发言人</div>
      <div class="dia_main">
        <FieldAdd ref="refCustomerAdd" />
      </div>
    </div>
    <div class="footer">
      <div class="blank"></div>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { getFormFields } from '@/js/api';
import { saveCustomRole, getAsrStoreUpdated, updateParName } from "@/app_postmeet/tools/api";
import FieldAdd from "./FieldAdd.vue";

const emit = defineEmits(["callback"]);
const refCustomerAdd = ref(null);
const customerFields = ref([]);
const confId = ref('');
const formData = reactive({
  userType: 'customer',
  editMode: 'select'
});

const show = async (id, data) => {
  confId.value = id;
  formData.selectPerson = data;
  const respCustomer = await getFormFields('OUTER_PARTNER');
  if (respCustomer.code == 0) {
    customerFields.value = respCustomer.data.filter(item => item.fieldStatus == 1);
    const formDataToSet = {
      username: data.name,
      fieldValues: data?.formData?.fieldValues || []
    };
    refCustomerAdd.value.setFormData(formDataToSet, customerFields.value);
  }
};

const _get_data = async () => {
  let param = {};
  try {
    param = await refCustomerAdd.value.get_data();
    param['ui'] = formData.selectPerson.ui;
    param['id'] = formData.selectPerson.id;
    return param;
  } catch (error) {
    throw error; // Propagate validation errors up
  }
}

const _send_emit = (param) => {
  const data = { id: param.ui, value: param.name };
  updateParName(confId.value, data).then((resp) => {
    if (resp.code == 0) {
      getAsrStoreUpdated().then((resp2) => {
        if (resp2.code == 0) {
          if (resp2.data.hasUpdatedContent) {
            g.postmeetStore.setUpdatedContent(resp2.data.asrUpdatedContent);
            g.postmeetStore.setValue("allowSaleReGen", true);
            g.emitter.emit("updatedAsrContent", "");
            ElMessage.success("更新成功");
            emit("callback", "close");
          }
        }
      });
    }
  });
}

const confirm = async () => {
  try {
    const param = await _get_data();
    console.log("param", param);
    const resp = await saveCustomRole(confId.value, param);
    if (resp.code == 0) {
      _send_emit(param);
    } else {
      ElMessage.error(resp.message);
    }
  } catch (e) {
    console.log("has error", e);
    ElMessage.error(e.message);
  }
};

const cancel = () => {
  emit("callback", "cancel");
}

defineExpose({
  show
});

</script>

<style lang="scss">
.diaedit_role_wrap {
  .h_main {
    padding: 16px 0;
    cursor: pointer;
    display: flex;
    flex-direction: column;

    .dia_header {
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      padding: 0 24px 10px 24px;
      font-size: 16px;
      color: #262626;
      line-height: 24px;
      border-bottom: 1px solid #e9e9e9;
    }

    .dia_main {
      margin: 12px 24px;

      .rd_box_inner {
        position: relative;
      }

      .rd_box {
        .rd_title {
          display: flex;
          align-items: center;
          margin: 0;

          p {
            width: 4px;
            height: 4px;
            background-color: red;
            border-radius: 50%;
            margin-left: 6px;
          }
        }

        .rd_input {
          margin: 10px 0;
        }

        .el-select {
          width: 100%;
          margin-top: 12px;
        }

        .rd_tip {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 20px;
          margin-top: 10px;
        }
      }

      .rd_name {
        margin-top: 12px;

        .rdn_txt {
          span {
            margin-right: 10px;
          }
        }

        .rnd_icon {
          margin-top: 2px;
          cursor: pointer;
          color: #436bff;
        }
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    padding: 19px;
    border-top: 1px solid #e9e9e9;

    .el-checkbox {
      margin: 5px 0;
    }

    .blank {
      flex-grow: 1;
    }
  }
}
</style>
