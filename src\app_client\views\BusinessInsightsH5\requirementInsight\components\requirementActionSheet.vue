<template>
    <van-popup v-model:show="isShow" round position="bottom" @closed="closed"
        :style="{ height: 'calc(100% - 44px)', display: 'flex', flexDirection: 'column' }" closeable teleport="body"
        class="requirement-action-sheet">
        <div class="title">
            {{ title }}
        </div>
        <div class="feedback-list-wrapper-wrap">
            <div class="feedback-list-wrapper" @scroll="handleScroll">
                <feedback-list :list="fbList" :is-finished="finished" v-loading="loading" :is-loading="loading"
                    @load-more="() => handleLoadMore(currentDemandId)" />
            </div>
            <div class="popup-content-bottom" v-if="total > 0">
                共{{ total }}条
            </div>
        </div>

    </van-popup>
</template>
<script setup>
import feedbackList from "./feedbackList.vue";
import {
    getCVDemandInsightDetailApi,
} from "@/app_client/tools/api.js";

const emit = defineEmits(['closed']);
const fbList = ref([])
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)
const isShow = ref(false)
const title = ref('')
const currentDemandId = ref(null)
const total = ref(0)
const closed = () => {
    emit('closed');
};

const showSheet = async (data) => {
    title.value = data.demand
    isShow.value = true;
    page.value = 1
    finished.value = false
    currentDemandId.value = data.id
    queryData(1, data.id)
};
const queryData = async (pageNum, demandId) => {
    try {
        loading.value = true
        const param = {
            pageNumber: pageNum || page.value,
            pageSize: pageSize.value,
        }
        const response = await getCVDemandInsightDetailApi(demandId, param) || {}
        const list = response.data.datas || []
        total.value = response.data.totalNum || 0
        if (pageNum === 1) {
            fbList.value = list
        } else {
            fbList.value.push(...list)
        }
        // 判断是否还有更多数据
        if (list.length < pageSize.value) {
            finished.value = true
        }
        page.value = pageNum || page.value

    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false

    }
}
const handleLoadMore = (demandId) => {
    if (!loading.value && !finished.value) {
        queryData(page.value + 1, demandId)
    }
}

defineExpose({
    showSheet
})

</script>
<style lang="scss">
.requirement-action-sheet {
    .title {
        font-size: 16px;
        color: #262626;
        line-height: 24px;
        font-weight: 500;
        padding: 12px 36px;
        max-width: 90%;
        text-align: center;
        min-height: 44px;
    }


    .feedback-list-wrapper-wrap {
        display: flex;
        flex-direction: column;
        flex: 1;
        // height: calc(100% - 144px);
        overflow: scroll;
    }

    .popup-content-bottom {
        width: 100%;
        box-sizing: border-box;
        font-size: 12px;
        color: #8C8C8C;
        line-height: 18px;
        height: 44px;
        background: #FFFFFF;
        box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.06);
        position: fixed;
        bottom: 0;
        padding: 7px 20px;
        z-index: 9;

    }

    .feedback-list-wrapper {
        display: flex;
        flex-direction: column;
        // flex: 1;
        // height: calc(100% - 144px);
        overflow: scroll;
        padding-bottom: 44px;
    }
}
</style>
