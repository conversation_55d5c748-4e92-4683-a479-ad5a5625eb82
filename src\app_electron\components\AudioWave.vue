<template>
    <div class="audio-wave">
        <Vue3Lottie ref="refLottie" :animationData="animationData" :height="42" :width="42" v-if="isPlaying" />
        <img :src="getAssetUrl('inmeet_play.png')" class="pause_btn" v-else>
    </div>
</template>

<script setup>
import { Vue3Lottie } from 'vue3-lottie'
import animationDataJson from './audio.json';
import { getAssetUrl } from "@/js/utils";

const emit = defineEmits(['callback']);
const refLottie = ref(null);
const animationData = ref(animationDataJson);
const isPlaying = ref(false);

const startAnimation = () => {
    isPlaying.value = true;
    nextTick(() => {
        refLottie.value.play();
    })
}

const stopAnimation = () => {
    refLottie.value.stop();
    isPlaying.value = false;
}

onBeforeUnmount(() => {
    stopAnimation();
})

defineExpose({
    startAnimation,
    stopAnimation,
    Vue3Lottie,
    animationData
})
</script>

<style scoped lang="scss">
.audio-wave {
    width: 42px;
    height: 42px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .audio-ball {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FF7676, #F54EA2);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.15s ease;
        animation: rotate 2s linear infinite;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 118, 118, 0.5), rgba(245, 78, 162, 0.5));
            border-radius: 50%;
            filter: blur(10px);
            z-index: -1;
        }
    }


    .pause_btn {
        width: 42px;
        height: 42px;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>