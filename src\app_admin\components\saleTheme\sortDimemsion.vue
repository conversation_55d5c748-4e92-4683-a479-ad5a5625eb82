<template>
    <el-drawer v-model="is_show" direction="rtl" class="sort_dim_drawer" @close="onClose">
        <template #header>
            <div class="vd_title">
                维度排序
            </div>
        </template>
        <template #default>
            <div class="draw_main flex-row">
                <table>
                    <thead>
                        <tr>
                            <td class="order_col"> 序号 </td>
                            <td class="name_col"> 维度名称 </td>
                            <td class="op_col"> 排序 </td>
                        </tr>
                    </thead>
                    <tbody v-sortable @end="onOrderChange">
                        <tr v-for="row in data" :key="row.order + row.name">
                            <td class="order_col">
                                {{ row.order }}
                            </td>
                            <td class="name_col">
                                {{ row.name }}
                            </td>
                            <td class="op_col">
                                <SortIcon />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="cancelClick">取消</el-button>
                <el-button type="primary" @click="confirmClick">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import SortIcon from "@/app_admin/icons/sort.vue"
import { updateXmTopicDimensionOrder } from "@/app_admin/tools/api"

const is_show = ref(false);
const emit = defineEmits(['callback'])
const data = ref([])
const sortData = ref([])
const topicId = ref('')
let dimensionType = ''

const show = (id, _dimensionType, _initData) => {
    dimensionType = _dimensionType;
    topicId.value = id;
    data.value = [..._initData];
    sortData.value = [..._initData];
    is_show.value = true;
}

const onClose = () => {
    data.value = []
    sortData.value = []
    topicId.value = ''
}

const cancelClick = () => {
    is_show.value = false;
}

const onOrderChange = (event) => {
    let item = sortData.value.splice(event.oldIndex, 1)[0];
    sortData.value.splice(event.newIndex, 0, item);
}

const confirmClick = () => {
    const ids = sortData.value.map(x => x.id)
    updateXmTopicDimensionOrder(topicId.value, dimensionType, { ids }).then(resp => {
        if (resp.code == 0) {
            emit('callback', 'reload')
            is_show.value = false;
        } else {
            ElMessage.error(`更新失败`);
        }
    }).catch(() => {
        ElMessage.error(`更新失败`);
    })
}


defineExpose({
    is_show, data, SortIcon, onOrderChange,
    show, cancelClick, confirmClick, onClose
})

</script>

<style lang="scss">
.sort_dim_drawer {
    width: 480px !important;

    .draw_main {
        table {
            width: 100%;

            tr {
                font-size: 14px;
                color: #595959;
                text-align: center;

                .order_col {
                    width: 36px;
                }

                .op_col {
                    width: 36px;
                }

                .name_col {
                    text-align: left;
                    font-size: 14px;
                    color: #595959;
                }
            }

            thead {
                width: 100%;
                height: 50px;
                background: #F9FAFB;
                box-shadow: inset 0px -1px 0px 0px #E9E9E9;
                border-radius: 4px 4px 0px 0px;
            }

            tbody {
                tr {
                    height: 36px;
                    box-shadow: inset 0px -1px 0px 0px #E9E9E9;
                    cursor: pointer;
                }
            }
        }

        ul {
            width: 100%;

            li {
                width: 100%;
                height: 50px;
                line-height: 50px;
                box-shadow: inset 0px -1px 0px 0px #E9E9E9;
            }
        }
    }

    .el-drawer__footer {
        width: 92%;
        height: 24px;
        background: #FFFFFF;
        box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.08);
    }
}
</style>