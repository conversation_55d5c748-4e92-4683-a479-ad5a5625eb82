import os from 'os'
import path from 'node:path'
import { app } from "electron";
import { fileURLToPath } from 'node:url'
import packageJson from '../../package.json'
import { getStorePrefix } from './tools'


const platform = os.platform() === 'win32' ? 'windows' : 'mac';
const isMac = platform === 'mac';


const DIR_NAME = path.dirname(fileURLToPath(import.meta.url))
process.env.APP_ROOT = path.join(DIR_NAME, '../..')

const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist');

const joinPath = path.join;

const meetApiHost = import.meta.env.VITE_LX_API_HOST;

const appPath = path.join(app.getPath("appData"), packageJson.name);

const iconPath = joinPath(RENDERER_DIST, 'favicon.ico')

const preload = joinPath(DIR_NAME, '../preload/index.js')

const apiEnv = import.meta.env.VITE_APP_APIENV;
let storePrefix = getStorePrefix()

const config = {
  storePrefix,
  hideWindowId: 'hide_window',//隐藏窗口id：hide_window,改个别的名字，可以把隐藏窗口的显示出来，查看ws链接消息
  apiEnv,
  platform,
  isMac,
  isPackaged: app.isPackaged,
  isProd: apiEnv.indexOf('prod') !== -1,
  version: packageJson.version,
  name: packageJson.name,
  appPath,
  preload,
  meetApiHost,
  joinPath,
  iconPath,
  downloadPath: import.meta.env.VITE_DOWNLOAD_PATH,
}

export default config
