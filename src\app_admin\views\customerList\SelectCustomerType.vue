<template>
    <div class="visit_filter_item">
        <el-select v-model="localValue" collapse-tags placeholder="客户类型" popper-class="vf-header" style="width: 150px"
            :empty-values="[NaN]" clearable :value-on-clear="NaN">
            <el-option v-for="item in options" :key="item.type" :label="item.description" :value="item.type" />
        </el-select>
    </div>
</template>

<script setup>
import { getCustomerTypeSearch } from "@/js/api.js";

const props = defineProps({
    value: {
        required: true,
    },
});

const emit = defineEmits(["update:value", "reload"]);
const localValue = ref(props.value);
const options = ref([]);

watch(localValue, (newValue) => {
    emit("update:value", toRaw(newValue));
    emit("reload", "");
});

const updateValue = (v) => {
    localValue.value = v;
};

const query = () => {
    getCustomerTypeSearch().then(res => {
        options.value = [
            ...res.data.datas.map(x => {
                return {
                    type: x.id,
                    description: x.name
                }
            })
        ];
    });
}

onMounted(() => {
    query();
})

defineExpose({
    options,
    updateValue,
})
</script>