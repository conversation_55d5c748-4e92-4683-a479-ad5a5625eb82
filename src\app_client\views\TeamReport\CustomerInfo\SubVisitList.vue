<template>
  <div class="sub_visit_history_wrap">
    <el-card class="customer_card card_no_border" shadow="never">
      <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
        <template #col_name="{ row }">
          <CustomerName :row="row" />
        </template>

        <template #col_date="{ row }">
          <div class="link">
            <div class="visit-time">{{ formatTime(row.createdTime) }}</div>
            <div class="interest-label">{{ row.salesMateTags }}</div>
          </div>
        </template>

        <template #col_visitDetail="{ row }">
          <ColDetail :row="row" />
        </template>

      </MyTable>
    </el-card>
  </div>
</template>

<script setup>
import { getCustomerMeetings } from "@/app_client/tools/api.js";
import { getUrlParam } from "@/js/utils.js";
import MyTable from "@/components/Table.vue";
import CustomerName from "@/app_client/components/CustomerName.vue";
import ColDept from "@/app_client/components/ColDept.vue";
import ColDetail from "./ColDetail.vue";

const props = defineProps({
  row: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(["callback"]);
const refTable = ref();

const columns = ["date", "visitDetail"]

const _getKanbanCustomers = (p) => {
  p["dptIds"] = [g.clientBoardStore.getTopDeptId()];
  const user = g.appStore.user;
  return getCustomerMeetings(user.orgId, user.id, props.row.customerId, p);
};

const startTime = getUrlParam('startDate') + ' 00:00:00';
const endTime = getUrlParam('endDate') + ' 23:59:59';
const datas = reactive({
  tableid: "sub_visit_history",
  param: {
    categoryIds: [],
    startTime, //开始时间，注间时间格式 YYYY-mm-dd HH24:mi:ss
    endTime, //结束时间
    tags: [], //标签
    dptIds: [],
    userIds: [],
    orderBy: "meetCount",
    asc: false,
  },
  need_init_load: false,
  show_search: false,
  need_header: false,
  show_pager: false,
  form: {},
  sortable: "custom",
  modal_type: "link",
  show_link_column: false,
  columns: columns,
  template: columns,
  urlGet: _getKanbanCustomers,
});


const cbDatas = (action, data) => {
  if (action == "after_search") {
  }
};

const onSearch = () => {
  refTable.value.search();
};

const setDeptIds = (ids) => {
  datas.param.dptIds = ids;
  onSearch();
};

watch(() => props.row, (newVal) => {
  if (newVal) {
    if (props.row.customerId) {
      nextTick(() => {
        refTable.value?.init();
      });
    }
  }
}, { immediate: true });

// 添加时间格式化函数
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  // 移除秒数部分
  return timeStr.replace(/:\d{2}$/, '');
};

defineExpose({
  MyTable,
  cbDatas,
  refTable,
  setDeptIds,
  CustomerName,
  props,
  ColDept,
});
</script>

<style lang="scss" scoped>
.sub_visit_history_wrap {
  background: #f7f9fe;

  .table_wrap {
    margin: 4px;
  }

  .link {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .visit-time {
      color: #666;
    }

    .interest-label {
      background-color: #e6f4ff;
      color: #1677ff;
      width: fit-content;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
}
</style>
