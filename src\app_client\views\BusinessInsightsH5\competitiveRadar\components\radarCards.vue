<template>
    <div class="cards hide-scrollbar">
        <li v-for="item in data" :key="item.name" class="cards__item"
            :class="[(competitorId === item.competitorId) && 'active']" @click="handleClick(item)">
            <div class="cards__info">
                <div class="cards__dot" :style="{ background: item.color }"></div>
                <div class="cards__name">{{ item.name }}</div>
            </div>
            <div class="cards__score" :style="{ color: item.cardColor }">{{ item.totalVolume }}</div>
        </li>
    </div>
</template>

<script setup lang="js">
const emit = defineEmits(['callback']);
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    colorObject: {
        type: Object,
        default: () => { }
    }
})
const competitorId = ref('')
const handleClick = item => {
    competitorId.value = item.competitorId
    emit('callback', item)
}
const reset = () => {
    competitorId.value = ''
}

defineExpose({
    reset
})

</script>

<style lang="scss">
.cards {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    margin-bottom: 24px;
    width: 100%;
    overflow-x: scroll;


    &__item {
        display: flex;
        flex-direction: column;
        flex: 1;
        padding: 8px 12px;
        background: #F9FAFC;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        border: 1px solid #F9FAFC;
        min-width: 80px;

        // box-sizing: border-box;
        &.active {
            background: rgba(67, 107, 255, 0.04);
            border-radius: 8px;
            border: 1px solid #436BFF;
        }
    }

    &__dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    &__info {
        display: flex;
        align-items: center;
        margin-bottom: 2px;
        // justify-content: center;
    }

    &__name {
        font-size: 12px;
        color: #8C8C8C;
        margin-right: 12px;
        word-break: keep-all;
    }

    &__score {
        font-weight: 700;
        font-size: 16px;
        margin-left: 18px;
    }
}
</style>