<template>
    <ul v-if="isOpen" class="dropdown-menu" @mouseleave="startTimer" @mouseenter="clearTimer" :style="dl_style">
        <li v-for="option in options" :key="option.id" @click="selectOption(option)">{{ option.label }}
        </li>
    </ul>
</template>

<script setup>
const options = ref([])
const emit = defineEmits(['callback'])

const refToggle = ref();
const isOpen = ref(false);
const dl_style = ref({})
let timer = null;
let hideDelay = 500;

const selectOption = (option) => {
    isOpen.value = false;
    g.emitter.emit('ddl_select', option)
}

function startTimer() {
    clearTimer()
    timer = setTimeout(() => {
        isOpen.value = false;
    }, hideDelay);
}

function clearTimer() {
    clearTimeout(timer);
}

onMounted(() => {
    g.emitter.on('ddl_status', (e) => {
        const { status, data, style } = e;
        isOpen.value = status;
        options.value = data
        dl_style.value = style;
    })
})

onUnmounted(() => {
    g.emitter.off('ddl_status');
});

defineExpose({
    isOpen,
    refToggle,
    dl_style,
    startTimer,
    clearTimer,
    selectOption
});
</script>

<style lang="scss">
.dropdown_list_wrap {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: fixed;
    left: 200px;
    top: 200px;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    z-index: 99;

    li {
        padding: 5px;
        width: 200px;
        cursor: pointer;
    }

    li:hover {
        background-color: #f2f2f2;
    }

    li:active {
        background-color: #e6e6e6;
    }

    li:first-child {
        border-top: none;
    }

    li:last-child {
        border-bottom: none;
    }
}
</style>