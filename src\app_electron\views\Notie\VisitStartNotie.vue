<template>
    <div class="visit-notification">
        <div class="notification-card">
            <div class="header">
                <img :src="getAssetUrl('logo_mini.png')" alt="icon" class="icon">
                <span>你预约的拜访即将开始</span>
                <div class="close-btn" @click="handleClose">
                    <img :src="getAssetUrl('close.svg')" />
                </div>
            </div>

            <div class="content">
                <div class="title">{{ plan.subject }}</div>
                <div class="time">拜访时间：{{ visitTime }}</div>
                <div class="customer">客户名称：{{ plan.salesMateCustomerName }}</div>

                <div class="action-buttons">
                    <button class="cancel-btn" @click="handleCancel">暂不开始</button>
                    <button class="start-btn" @click="handleStart">立即开始</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getAssetUrl, formatDate } from '@/js/utils'

const plan = ref({})
const visitTime = ref('')

const handleClose = () => {
    g.electronStore.closeWin('visit_notification')
}

const handleCancel = () => {
    g.electronStore.closeWin('visit_notification')
}

const handleStart = () => {
    g.electronStore.openWin('meet', plan.value)
    g.electronStore.closeWin('visit_notification')
}

onMounted(() => {
    g.electronStore.waitPageReady().then(data => {
        plan.value = data;
        visitTime.value = `${formatDate(plan.value.scheduleStartTime, 'hh:mm')} - ${formatDate(plan.value.scheduleEndTime, 'hh:mm')}`
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

defineExpose({
    handleClose,
    handleCancel,
    handleStart
})

</script>

<style lang="scss">
body {
    background: #ffffff00;
    font-size: 16px;
}

.electron-layout {
    .mac-titlebar {
        display: none !important;
    }

    .mac-content {
        height: 100vh !important;
        margin-top: 0 !important;
    }
}

.visit-notification {
    display: flex;
    justify-content: center;
    align-items: center;

    .notification-card {
        width: 396px;
        margin: 2px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);

        .header {
            padding: 12px;
            display: flex;
            align-items: center;
            background: #fff;
            -webkit-app-region: drag;

            .icon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                -webkit-app-region: no-drag;
            }

            .close-btn {
                margin-left: auto;
                cursor: pointer;
                -webkit-app-region: no-drag;
            }
        }

        .content {
            padding: 0 20px 12px 20px;

            .title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 12px;
            }

            .time,
            .customer {
                margin-bottom: 12px;
                color: #666;
                font-size: 14px;
            }

            .action-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                margin-top: 0;
            }

            .cancel-btn {
                padding: 8px 16px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
            }

            .start-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                background: #436BFF;
                color: white;
                cursor: pointer;
            }
        }
    }
}
</style>