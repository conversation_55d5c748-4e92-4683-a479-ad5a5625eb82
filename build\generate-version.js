const fs = require('fs')
const path = require('path')
const packageJson = require('../package.json')

// 生成 version.json
const generateVersionJson = (env, buildTime) => {
  const rootDir = path.join(__dirname, '..')
  const subFolder = env === 'prod' ? 'prod' : 'test';
  const outputDir = path.join(rootDir, 'release', subFolder, packageJson.version, 'web-resources', buildTime)

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const versionJson = {
    version: packageJson.version,
    buildTime: new Date().toISOString()
  }

  fs.writeFileSync(
    path.join(outputDir, 'version.json'),
    JSON.stringify(versionJson, null, 2)
  )

  fs.writeFileSync(
    path.join(rootDir, 'dist', 'version.json'),
    JSON.stringify(versionJson, null, 2)
  )

  console.log('version.json generated successfully!')
}

module.exports = generateVersionJson 