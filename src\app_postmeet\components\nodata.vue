<template>
    <div class="nodata_wrap2" v-show="!loading">
        <div class="setup_nodata_wrap" v-if="lessData">
            <div class="nodata_left">
                <InterviewNodata />
            </div>
            <div class="nodata_right">
                <div class="header">
                    本次沟通的字幕文本量不足以生成详细内容。
                </div>
                <div class="note">
                    <span>录制时长过短</span> 或 <span>字幕文本量不足</span>(字数＜1500字) 无法生成详细内容。为确保录制质量，建议：
                    <br />
                    1. 录音设备靠近声源，确保收音清晰<br />
                    2. 录制时避免误触界面，防止意外结束<br />
                </div>
            </div>
        </div>
        <div v-if="currMenu == 'coach'">
            <div v-if="isHost" class="coche_nodata">
                <el-empty description=" ">
                    <div class="nodata_main">当前仅对“沟通创建人”生成辅导内容，请检查已标注的发言人是否包含您本人
                    </div>
                    <div class="nodata_sub">
                        若发言人角色标注有误，请点击发言人右侧的【编辑】按钮修改，然后点击【重新生成】按钮
                    </div>
                </el-empty>
            </div>
            <div v-else class="coche_nodata">
                <el-empty description="本次沟通暂无待辅导角色">
                </el-empty>
            </div>
        </div>
        <div v-else>
            <el-empty description="暂无数据">
            </el-empty>
        </div>
    </div>
</template>

<script>
import InterviewNodata from "@/app_postmeet/icons/interview_nodata.vue";

export default {
    components: {
        InterviewNodata
    },
    data() {
        return {
            loading: false,
            min_words_count: g.postmeetStore.data.min_words_count,
            currMenu: g.postmeetStore.data.curr_menu,
            isHost: !g.postmeetStore.isReadonly(),
            lessData: g.postmeetStore.data.words_count < g.postmeetStore.data.min_words_count,
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
        g.emitter.on('postmeet_click_left_menu', (menu) => {
            this.init()
        })
        g.emitter.on('monitor_sale_report', () => {
            this.loading = true
        })
    },
    methods: {
        init() {
            this.isHost = !g.postmeetStore.isReadonly()
            this.currMenu = g.postmeetStore.data.curr_menu
            this.lessData = g.postmeetStore.data.words_count < g.postmeetStore.data.min_words_count
        }
    }

}
</script>

<style lang='scss'>
.nodata_wrap2 {
    .setup_nodata_wrap {
        display: flex;
        flex-direction: row;
        border-radius: 4px;
        margin: 5px 0;
        padding: 0;

        .nodata_right {
            padding: 37px 20px;
            flex-direction: column;

            .header {
                height: 22px;
                font-weight: 500;
                font-size: 14px;
                color: #262626;
                line-height: 22px;
                margin-bottom: 12px;
            }

            .note {
                height: 66px;
                font-size: 14px;
                color: #595959;
                line-height: 22px;

                span {
                    font-weight: 600;
                }
            }
        }

        .nodata_left {
            display: flex;
            align-items: center;
        }
    }

    .coche_nodata {
        .nodata_main {
            font-size: 16px;
            color: #262626;
            line-height: 22px;
        }

        .nodata_sub {
            font-size: 14px;
            color: #909399;
            line-height: 22px;
            margin-top: 10px;
        }
    }
}
</style>