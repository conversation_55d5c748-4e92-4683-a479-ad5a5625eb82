{"v": "5.6.10", "fr": 25, "ip": 0, "op": 45, "w": 200, "h": 200, "nm": "合成 1", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "路径 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [23.999, 24, 0], "ix": 2}, "a": {"a": 0, "k": [-0.921, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -13.25], [13.25, 0], [3.71, 8.47], [0, 0], [0, 0]], "o": [[13.25, 0], [0, 13.25], [-9.84, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.919, -24], [23.081, 0], [-0.919, 24], [-22.919, 9.61], [-23.079, 9.24], [-0.919, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 128, "st": 3, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 6", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [24, 24, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [48, 48], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 128, "st": 3, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "大背景", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [95, 76, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-63, -76], [24.39, -76], [63, -38], [63, 76], [-63, 76]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "圆角 1", "r": {"a": 0, "k": 24, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.502, 0.698, 0.992, 0.305, 0.355, 0.594, 0.996, 0.611, 0.208, 0.49, 1, 0.805, 0.447, 0.506, 1, 1, 0.686, 0.522, 1], "ix": 9}}, "s": {"a": 0, "k": [-69.026, 68.73], "ix": 5}, "e": {"a": 0, "k": [52.097, -62.847], "ix": 6}, "t": 1, "nm": "abc", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "大背景", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 125, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "背景", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [40, 106, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [80, 92], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 16, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 5, "k": {"a": 0, "k": [0, 0.502, 0.698, 0.992, 0.305, 0.355, 0.594, 0.996, 0.611, 0.208, 0.49, 1, 0.805, 0.447, 0.506, 1, 1, 0.686, 0.522, 1], "ix": 9}}, "s": {"a": 0, "k": [29.251, 46], "ix": 5}, "e": {"a": 0, "k": [-40, -42.439], "ix": 6}, "t": 1, "nm": "abc", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "背景", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 125, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [98.999, 63, 0], "ix": 2}, "a": {"a": 0, "k": [24, 24, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 19, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 22, "s": [80, 80, 100]}, {"t": 25, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.3, -3.901], [24.004, -2.948], [24.004, 24.005], [23.994, 23.997], [23.999, -1.14], [49.675, -1.688], [49.425, 48.697], [24.39, 52.089], [52.8, 50.849]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[95.491, -31.463], [23.975, 0.075], [23.975, 23.95], [52.431, -8.658], [58.719, 47.741], [52.475, 52.538], [42.6, 51.674], [24.495, 50.521], [63.725, 62.45]], "c": true}]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.63, "y": 0}, "t": 3, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[84.491, -16.213], [23.975, 0.075], [23.975, 23.95], [55.181, -3.158], [58.719, 47.741], [52.475, 52.538], [42.6, 51.674], [24.495, 50.521], [63.725, 62.45]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.167, "y": 0.19}, "t": 4.75, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[69.865, -2.808], [24.053, -4.87], [24.053, 24.005], [59.735, 10.787], [59.451, 58.679], [52.303, 56.343], [42.613, 53.63], [24.885, 52.042], [61.928, 63.755]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 6.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[58.269, -0.844], [23.983, -13.06], [23.983, 24.065], [50.355, 24.004], [48.035, 47.622], [47.983, 48.153], [33.378, 50.484], [24.762, 51.858], [47.983, 48.065]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 8.25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[56.5, -1.846], [23.941, -12.651], [23.941, 23.974], [47.435, 47.285], [47.93, 48.045], [47.941, 48.062], [32.816, 51.572], [23.941, 51.849], [47.941, 47.974]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.6, 0.554], [23.972, -13.417], [23.972, 23.958], [23.972, 43.983], [23.972, 46.245], [23.972, 46.296], [23.972, 46.682], [22.842, 55.991], [47.972, 47.958]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 11.75, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.613, -0.274], [23.957, -9.636], [23.957, 23.989], [2.719, 45.191], [0.32, 47.586], [0.266, 47.64], [-0.143, 48.049], [26.192, 58.629], [47.957, 47.989]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 13.5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[58.857, -11.83], [23.941, -10.151], [23.941, 23.974], [-2.511, 24.002], [-6.322, 24.005], [-6.407, 24.005], [-0.684, 47.849], [26.852, 61.793], [61.691, 55.474]], "c": true}]}, {"i": {"x": 0.37, "y": 1}, "o": {"x": 0.63, "y": 0}, "t": 15.25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66.622, 7.827], [24.092, -12.116], [24.092, 24.009], [-4.06, -4.286], [-4.171, 15.593], [-4.173, 16.04], [-0.533, 47.884], [25.755, 55.959], [48.092, 48.009]], "c": true}]}, {"t": 17, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[73.276, -1.002], [24.01, -7.115], [23.981, 23.993], [24.024, -9.9], [-6.839, -0.038], [-11.618, 3.774], [-0.644, 47.868], [26.952, 65.544], [47.981, 47.993]], "c": true}]}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 48, "h": 48, "ip": 0, "op": 45, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [116, 105, 0], "ix": 2}, "a": {"a": 0, "k": [15, 8, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.625, 7.75], [51, 7.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.56], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 19, "s": [0]}, {"i": {"x": [0.56], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 24, "s": [17]}, {"i": {"x": [0.56], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 27, "s": [45]}, {"t": 29, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.56], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 19, "s": [28]}, {"i": {"x": [0.56], "y": [1]}, "o": {"x": [1], "y": [0]}, "t": 23, "s": [49]}, {"t": 28, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 13, "op": 53, "st": 13, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "形状图层 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [116, 125, 0], "ix": 2}, "a": {"a": 0, "k": [15, 8, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.625, 7.75], [51, 7.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.644], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.755], "y": [0]}, "t": 30, "s": [17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.776], "y": [0]}, "t": 33, "s": [45]}, {"t": 35, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.722], "y": [0]}, "t": 25, "s": [28]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.586], "y": [0]}, "t": 29, "s": [49]}, {"t": 34, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 19, "op": 53, "st": 19, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [116, 145, 0], "ix": 2}, "a": {"a": 0, "k": [15, 8, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.625, 7.75], [51, 7.75]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "形状 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.644], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.755], "y": [0]}, "t": 36, "s": [17]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.776], "y": [0]}, "t": 39, "s": [45]}, {"t": 41, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.722], "y": [0]}, "t": 31, "s": [28]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.586], "y": [0]}, "t": 35, "s": [49]}, {"t": 40, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 25, "op": 53, "st": 25, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 0, "nm": "背景", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 99, 0], "ix": 2}, "a": {"a": 0, "k": [79, 76, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [100, 106, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20, "s": [100, 100, 100]}, {"t": 25, "s": [100, 106, 100]}], "ix": 6, "x": "var $bm_rt;\nvar enable, elastic, gravity, jumpMax, on_off, n, n, t, v, vl, vu, vu, tCur, segDur, tNext, numberOfBounces, delta, inOutExp;\nenable = effect('Jump - \\u7f29\\u653e')('Pseudo/aHCxb79bbc95d-0001');\nelastic = $bm_div(effect('Jump - \\u7f29\\u653e')('Pseudo/aHCxb79bbc95d-0004'), 100);\ngravity = $bm_mul(effect('Jump - \\u7f29\\u653e')('Pseudo/aHCxb79bbc95d-0005'), 100);\njumpMax = effect('Jump - \\u7f29\\u653e')('Pseudo/aHCxb79bbc95d-0006');\n$bm_rt = on_off = effect('Jump - \\u7f29\\u653e')('Pseudo/aHCxb79bbc95d-0002');\nif (enable == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n > 0) {\n        t = $bm_sub(time, key(n).time);\n        v = $bm_mul($bm_neg(velocityAtTime($bm_sub(key(n).time, 0.001))), elastic);\n        vl = length(v);\n        if ($bm_isInstanceOfArray(value)) {\n            vu = vl > 0 ? normalize(v) : [\n                0,\n                0,\n                0\n            ];\n        } else {\n            vu = v < 0 ? -1 : 1;\n        }\n        tCur = 0;\n        segDur = $bm_div($bm_mul(2, vl), gravity);\n        tNext = segDur;\n        numberOfBounces = 1;\n        while (tNext < t && numberOfBounces <= jumpMax) {\n            vl *= elastic;\n            segDur *= elastic;\n            tCur = tNext;\n            tNext = $bm_sum(tNext, segDur);\n            numberOfBounces++;\n        }\n        if (numberOfBounces <= jumpMax) {\n            delta = $bm_sub(t, tCur);\n            $bm_rt = inOutExp = $bm_mul($bm_mul(vu, delta), $bm_sub(vl, $bm_div($bm_mul(gravity, delta), 2)));\n            if (on_off == 1) {\n                $bm_rt = value = $bm_sub(value, inOutExp);\n            } else {\n                $bm_rt = value = $bm_sum(value, inOutExp);\n            }\n        } else {\n            $bm_rt = value = value;\n        }\n    } else {\n        $bm_rt = value = value;\n    }\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Jump - 缩放", "np": 9, "mn": "Pseudo/aHCxb79bbc95d", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "启用", "mn": "Pseudo/aHCxb79bbc95d-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 7, "nm": "弹跳", "mn": "Pseudo/aHCxb79bbc95d-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 6, "nm": "属性", "mn": "Pseudo/aHCxb79bbc95d-0003", "ix": 3, "v": 0}, {"ty": 0, "nm": "展开", "mn": "Pseudo/aHCxb79bbc95d-0004", "ix": 4, "v": {"a": 0, "k": 60, "ix": 4}}, {"ty": 0, "nm": "重力", "mn": "Pseudo/aHCxb79bbc95d-0005", "ix": 5, "v": {"a": 0, "k": 8, "ix": 5}}, {"ty": 0, "nm": "最大弹跳", "mn": "Pseudo/aHCxb79bbc95d-0006", "ix": 6, "v": {"a": 0, "k": 8, "ix": 6}}, {"ty": 6, "nm": "", "mn": "Pseudo/aHCxb79bbc95d-0007", "ix": 7, "v": 0}]}], "w": 158, "h": 152, "ip": 0, "op": 46, "st": 0, "bm": 0}], "markers": []}