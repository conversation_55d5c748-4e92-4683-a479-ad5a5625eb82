<template>
    <el-form :model="formData" ref="formRef" label-position="top">
        <el-form-item label="客户" prop="selectPersonId">
            <div class="custom-select" ref="selectRef">
                <div class="select-input" @click="toggleDropdown">
                    <template v-if="selectedOption">
                        <div class="selected-content">
                            <div class="main-text">{{ selectedOption.name }}</div>
                        </div>
                    </template>
                    <span v-else class="placeholder">请选择</span>
                    <span class="arrow" :class="{ 'is-reverse': isOpen }">▼</span>
                </div>
                <div class="select-dropdown" v-show="isOpen">
                    <div v-if="optPersons.length === 0">
                        <div class="no-data">
                            <span>暂无数据</span>
                        </div>
                    </div>
                    <div v-else v-for="item in optPersons" :key="item.id" class="select-option"
                        :class="{ 'is-selected': item.id === formData.selectPersonId }" @click="selectOption(item)">
                        <div class="option-main">{{ item.name }}</div>
                        <el-tooltip :content="getFullFieldContent(item)" placement="top" :show-after="100">
                            <div class="option-sub">
                                <template v-for="(field, index) in item?.formData?.fieldValues" :key="field.fieldId">
                                    <span class="field-value text-normal">
                                        {{ field.fieldName }} : {{ field.fieldValue }}
                                    </span>
                                    <span v-if="index !== item?.formData?.fieldValues.length - 1"
                                        class="separator">|</span>
                                </template>
                            </div>
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </el-form-item>
        <div class="rd_name flex-row" v-show="selectPerson.name && formData.selectPersonId">
            <div class="rdn_txt">
                <template v-for="(field, index) in fields" :key="field.id">
                    <span
                        :class="`${(getFieldValue(field.id) === null || getFieldValue(field.id) === undefined || getFieldValue(field.id) === '') && field.isRequired === 1 ? 'text-red' : 'text-normal'}`">
                        {{ field.fieldName }} : {{ getFieldValue(field.id) || '-' }}
                    </span>
                    <span v-if="index !== fields.length - 1" class="separator">|</span>
                </template>
            </div>
            <div class="rnd_icon" @click="onRdNameEdit">
                <EditIcon />
            </div>
        </div>
    </el-form>
</template>

<script setup>
import EditIcon from "@/icons/edit.vue"
import { getCustomerContact } from "@/js/api";

const formRef = ref(null);
const selectRef = ref(null);
const optPersons = ref([]);
const isOpen = ref(false);
const _defaultFieldValues = { name: '', ui: '', formData: { fieldValues: [] } }
const selectPerson = ref({ ..._defaultFieldValues });
const emit = defineEmits(['callback']);
const confId = ref('');
const fields = ref([]);

let sbUser = {}
const formData = reactive({
    selectPersonId: '',
    fieldValues: []
});

const selectedOption = computed(() => {
    return optPersons.value.find(item => item.id === formData.selectPersonId) || null;
});

const toggleDropdown = () => {
    isOpen.value = !isOpen.value;
};

const selectOption = (item) => {
    formData.selectPersonId = item.id;
    isOpen.value = false;
    onPersonChange();
};

const query = () => {
    onPersonChange()
    getCustomerContact(confId.value).then((resp) => {
        if (resp.code == 0) {
            optPersons.value = resp.data;
            const sbUser2 = resp.data.find(item => item.name == sbUser.name);
            console.log('sbUser2', sbUser2)
            if (sbUser2) {
                selectPerson.value = sbUser2;
                formData.name = sbUser2.name;
                formData.selectPersonId = sbUser2.id;
                formData.fieldValues = sbUser2?.formData?.fieldValues || [];
            }
        }
    });
}

const onPersonChange = () => {
    selectPerson.value = { ..._defaultFieldValues, ...optPersons.value.find(item => item.id == formData.selectPersonId) };
}

const onRdNameEdit = () => {
    emit("callback", "show_edit_role", selectPerson.value);
}

const get_data = () => {
    const param = {
        "name": selectPerson.value.name,
        "ui": selectPerson.value.ui,
        "id": selectPerson.value.id,
        "fieldValues": []
    }

    if (selectPerson.value.formData?.fieldValues) {
        param.fieldValues = toRaw(selectPerson.value.formData.fieldValues);
    }
    else if (selectPerson.value.fieldValues) {
        param.fieldValues = toRaw(selectPerson.value.fieldValues);
    }
    return param;
}

const init = (id, data, fieldData) => {
    console.log("rename init", id, data, fieldData)
    sbUser = data;
    confId.value = id;
    selectPerson.value = { ..._defaultFieldValues, name: data.name, ui: data.ui };
    fields.value = fieldData || [];
    query();
}

// 处理点击外部关闭下拉框
const handleClickOutside = (event) => {
    const select = selectRef.value;
    if (select && !select.contains(event.target)) {
        isOpen.value = false;
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

const getFieldValue = (fieldId) => {
    if (!selectPerson.value?.formData?.fieldValues) return null;
    const fieldValue = selectPerson.value.formData.fieldValues.find(item => item.fieldId === fieldId);
    return fieldValue ? fieldValue.fieldValue : null;
}

const getFullFieldContent = (item) => {
    return fields.value.map(field => {
        const fieldValue = item?.formData?.fieldValues?.find(f => f.fieldId === field.id)?.fieldValue || '-';
        return `${field.fieldName}: ${fieldValue}`;
    }).join(' | ');
}

defineExpose({
    init,
    EditIcon,
    get_data
})
</script>

<style lang="scss" scoped>
.custom-select {
    position: relative;
    width: 90%;
}

.select-input {
    position: relative;
    width: 100%;
    min-height: 32px;
    padding: 0 30px 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.select-input:hover {
    border-color: #c0c4cc;
}

.arrow {
    position: absolute;
    right: 8px;
    font-size: 12px;
    color: #c0c4cc;
    transition: transform 0.3s;
}

.arrow.is-reverse {
    transform: rotate(180deg);
}

.placeholder {
    color: #c0c4cc;
}

.select-dropdown {
    position: absolute;
    top: calc(100% + 5px);
    left: 0;
    width: 100%;
    max-height: 274px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    z-index: 1000;

    .no-data {
        padding: 12px 16px;
        text-align: center;
        color: #909399;
    }
}

.select-option {
    padding: 12px 16px;
    cursor: pointer;
}

.select-option:hover {
    background-color: #f5f7fa;
}

.select-option.is-selected {
    color: #409eff;
    font-weight: bold;
}

.option-main {
    font-size: 13px;
    color: #606266;
    line-height: 1.2;
}

.option-sub {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.field-value {
    margin-right: 4px;
    display: inline-block;
}

.selected-content {
    width: 100%;
    padding: 5px 0;
}

.main-text {
    font-size: 13px;
    color: #606266;
    line-height: 1.2;
}

.sub-text {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
    line-height: 1;
}

.rd_name {
    margin-top: 8px;
    display: flex;
    align-items: center;
}

.rdn_txt {
    font-size: 14px;
    line-height: 1.5;
}

.text-normal {
    color: #606266;
}

.text-red {
    color: #f56c6c;
}

.separator {
    margin: 0 8px;
    color: #909399;
}
</style>
