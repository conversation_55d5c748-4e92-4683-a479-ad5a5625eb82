<template>
    <div class="comm_right_list_wrap">
        <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
            <template #_header_left>
                <el-button type="default" @click="onBatchDelete" :disabled="checkIds.length == 0">批量删除</el-button>
            </template>
            <template #_header_filter>
                <div class="input_title">内容丰富度</div>
                <el-select v-model="datas.param.richness" class="sel_richness" placeholder="内容丰富度"
                    @change="onChangeRichness">
                    <el-option label="查看所有" value="" />
                    <el-option v-for="item in optionsRichness" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <div class="input_title">上架状态</div>
                <el-select v-model="datas.param.onShelf" class="sel_shelf" placeholder="上架状态"
                    @change="onChangeRichness">
                    <el-option label="查看所有" value="" />
                    <el-option v-for="item in optionsShelf" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </template>
            <template #_header_bottom>
                <div class="right_hint_line">
                    <div class="hint"> 同一分类下商品清单格式需保持一致；商品清单建议包含：商品名称、描述、价格、目标客户群、适用场景等信息。</div>
                    <div class="download" @click="downloadTemplate">下载商品清单模板</div>
                </div>
            </template>
            <template #col_onShelf="{ row }">
                <div @click="viewDoc(row)" class="shelf_box">
                    <div :class="`${row.onShelf ? 'green' : 'gray'} point`">
                    </div>
                    <div>
                        {{ row.onShelf ? '已上架' : '未上架' }}
                    </div>
                </div>
            </template>
            <template #col_supplements="{ row }">
                <el-input v-model="row.supplements" autosize readonly type="textarea" />
            </template>
            <template #col_richness="{ row }">
                <div @click="viewDoc(row)" class="richness">
                    <div>{{ labMap[row.richness] }}</div>
                </div>
            </template>
            <template #_link_pre="{ row }">
                <div class="op_column flex-row">
                    <div class="obtn shelf" @click="onDdlClick('shelf', row)" v-show="row.richness == 'OK'">{{
                        row.onShelf
                            ? '下架' : '上架' }}</div>
                    <el-dropdown @command="(cmd) => onDdlClick(cmd, row)">
                        <span class="obtn ddl_more">
                            更多
                            <el-icon class="el-icon--right">
                                <ArrowDown />
                            </el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                                <el-dropdown-item command="download">下载</el-dropdown-item>
                                <el-dropdown-item command="delete">删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </template>


        </MyTable>
    </div>
    <dialogEditName ref="refDialog" @callback="cbModal" />
    <UploadModal ref="refUpload" />
</template>

<script setup>
import dialogEditName from "./dialogEditName.vue";
import { getGoodsList, deleteGoods, updateGoodsShelf } from "@/app_admin/tools/api.js"
import { apiHintWrap, downloadFile, a2o } from "@/app_admin/tools/utils.js"
import MyTable from "@/components/Table.vue";
import UploadModal from "@/app_admin/components/UploadModal.vue";
import { ArrowDown } from '@element-plus/icons-vue'
import { richnessTypes, shelfTypes } from "./const_value"
import { getOssUrl } from "@/js/utils.js"
const emit = defineEmits(['callback']);
const refDialog = ref(null);
const refUpload = ref();
const currItem = ref({})
const evaluations = ref([])
const refTable = ref(null);
const checkIds = ref([])
const optionsRichness = ref(richnessTypes)
const optionsShelf = ref(shelfTypes)

const labMap = ref(a2o(richnessTypes))


const datas = reactive({
    tableid: 'commodity_list',
    param: {
        cid: '',
        richness: '',
        searchKey: "",
        onShelf: ''
    },
    template: ["onShelf", "supplements", "richness"],
    need_init_load: false,
    show_search: false,
    need_header: true,
    show_btn_add: true,
    form: {},
    modal_type: "link",
    add_txt: "上传商品清单",
    delete_hint_column: 'listName',
    show_link_column: true,
    show_link_view: false,
    enable_checkbox: true,
    columns: ["listName", "richness", "supplements", "onShelf"],
    columns_span: [],
    urlGet: getGoodsList,
    urlDelete: deleteGoods
});


const onChangeRichness = () => {
    query()
}

const cbDatas = (action, data) => {
    if (action === "init_add") {
        g.adminFileStore.get_config('goods').then(config => {
            refUpload.value.show(config, currItem.value.id);
        })
    } else if (action == 'check_row') {
        const { checked } = data;
        checkIds.value = checked.map(x => x.id)
    } else if (action == 'after_search') {
        for (let row of refTable.value.cfg.data) {
            if (row['supplements']) {
                row['supplements'] = row['supplements'].replace(/\\n/g, "\n");
            } else {
                row['supplements'] = ''
            }

        }
    }
}

const cbModal = (action) => {
    if (action == "reload") {
        query()
    }
}

const onBatchDelete = () => {
    if (checkIds.value.length == 0) {
        return
    }
    ElMessageBox.confirm(
        `您确定要删除所选的${checkIds.value.length}个商品清单吗？删除后不可恢复`,
        '删除提示',
        {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        apiHintWrap(deleteGoods(currItem.value.id, { ids: checkIds.value }), '删除商品清单').then(({ status, resp }) => {
            if (status) {
                query()
            }
        })
    }).catch(() => { })
}

const query = () => {
    datas.param.cid = currItem.value.id;
    refTable.value.search();
}


const _deleteOne = (row) => {
    ElMessageBox.confirm(
        `您确定要删除商品清单${row.listName}吗？删除后不可恢复`,
        '删除提示',
        {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        apiHintWrap(deleteGoods(currItem.value.id, { ids: [row.id] }), '删除商品清单').then(({ status, resp }) => {
            if (status) {
                query()
            }
        })
    }).catch(() => { })
}


const onDdlClick = (action, row) => {
    switch (action) {
        case 'edit':
            refDialog.value.show_edit(currItem.value.id, toRaw(row))
            break;
        case 'download':
            const url = row.sharePath;
            downloadFile(url, url.listName + '.xlsx')
            break;
        case 'shelf':
            const newStatus = row.onShelf ? 'off' : 'on'
            const hint = row.onShelf ? '下架' : '上架'
            apiHintWrap(updateGoodsShelf(currItem.value.id, row.id, newStatus), hint).then(({ status, resp }) => {
                if (status) {
                    query()
                }
            })
            break;
        case 'delete':
            _deleteOne(row)
            break;
        default:
            break;
    }
}

const downloadTemplate = () => {
    const filename = '商品清单模板.xlsx';
    downloadFile(getOssUrl(filename), filename)
}

const init = (item) => {
    currItem.value = item;
    query()
}

onMounted(() => {
    g.emitter.on('file_uploaded', () => {
        setTimeout(() => {
            query()
        }, 500)
    })
})

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    init,
    cbModal,
    onBatchDelete,
    refTable,
    evaluations,
    refDialog,
    MyTable,
    UploadModal,
    optionsRichness,
    optionsShelf,
    ArrowDown,
    refUpload,
    dialogEditName
})

</script>

<style lang="scss">
.comm_right_list_wrap {
    .sel_richness {
        width: 200px;
    }

    .sel_shelf {
        width: 200px;
    }

    .right_header {
        padding-bottom: 10px;
    }

    .input_title {
        min-width: 50px;
        margin: 0 12px;
    }

    .right_hint_line {
        display: flex;
        flex-direction: row;
        font-size: 14px;
        margin: 16px 0;

        .hint {
            height: 20px;
            color: #8C8C8C;
            line-height: 20px;
        }

        .download {
            height: 20px;
            color: #436BFF;
            line-height: 20px;
            cursor: pointer;
        }
    }

    .shelf_box {
        display: flex;
        flex-direction: row;

        .point {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-top: 9px;
            margin-right: 5px;
        }

        .gray {
            background-color: #d9d9d9;
        }

        .green {
            background-color: #52C41A;
        }
    }

    .richness {
        .bad {
            color: red;
        }
    }

    .col_operation_ {
        width: 120px !important;
    }

    .op_column {
        .obtn {
            height: 40px;
            font-size: 14px;
            color: #436BFF;
            line-height: 40px;
            cursor: pointer;
            outline: none;
        }

        .shelf {
            margin-right: 10px;
        }

        .ddl_more {
            .el-icon--right {
                margin-left: -3px;
            }
        }
    }

    .col_supplements {
        textarea {
            width: 100%;
            height: 300px;
            border: none;
            resize: none;
            outline: none;
            font-size: 16px;
            color: #595959;
            box-shadow: none;
            font-family: PingFangSC, PingFang SC;
        }
    }
}
</style>