<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="ability-form-drawer">
    <template #header>
      <div class="drawer-header">
        <el-icon class="close-icon" @click="closeDrawer">
          <Close />
        </el-icon>
        <span class="drawer-title">{{ drawerTitle }}</span>
      </div>
    </template>

    <div class="drawer-content">
      <el-form ref="formRef" :model="abilityForm" :rules="rules" label-width="88px">
        <div>
          <el-form-item label="模版名称" prop="name">
            <el-input v-model="abilityForm.name" type="textarea" show-word-limit maxlength="50" :rows="1" />
          </el-form-item>
        </div>

        <div class="form-section">

          <el-form-item label="关联维度" :rules="behaviorRules">
          </el-form-item>
          <div v-sortable @end.prevent="handleDragEnd">
            <div v-for="(behaviorText, index) in abilityForm.behaviors" :key="index" class="behavior-item">
              <div class="drag-handle">
                <el-icon>
                  <Menu />
                </el-icon>
                <p>{{ behaviorText }}</p>
                <el-icon>
                  <Close @click="removeBehavior(index)" />
                </el-icon>
              </div>
            </div>
          </div>
          <!-- <div class="behavior-item" v-for="(behaviorText, index) in abilityForm.behaviors" :key="index">
            <div class="drag-handle">
              <el-icon>
                <Menu />
              </el-icon>
            </div>
            <el-input v-model="abilityForm.behaviors[index]" type="textarea" :placeholder="`请输入第${index + 1}条关联维度`"
              maxlength="200" :rows="1" class="behavior-textarea" show-word-limit />

            <el-button v-if="abilityForm.behaviors.length > 1" class="delete-behavior" @click="removeBehavior(index)"
              circle type="danger" size="small">
              <el-icon>
                <Close />
              </el-icon>
            </el-button>
          </div> -->

        </div>

        <!-- <div class="add-behavior-section">
          <el-button class="add-behavior-btn" @click="addBehavior" text>
            <el-icon>
              <Plus />
            </el-icon>
            <span>添加模版名称</span>
          </el-button>
        </div> -->
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="closeDrawer">取 消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close, Menu } from '@element-plus/icons-vue'

const emit = defineEmits(['success'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

const drawerTitle = computed(() => {
  return isEditMode.value ? '编辑模板' : '添加模板'
})

const abilityForm = reactive({
  name: '',
  behaviors: ['1212', '3131', '3131']
})

const rules = {
  name: [
    { required: true, message: '请输入能力名称', trigger: 'blur' },
    { max: 50, message: '最多输入50个字符', trigger: 'blur' }
  ]
}

const behaviorRules = [
  { required: true, message: '', trigger: 'blur' },
  // { max: 200, message: '最多输入200个字符', trigger: 'blur' }
]

const openDrawer = (data = null) => {
  drawerVisible.value = true

  if (data) {
    // 编辑模式
    isEditMode.value = true
    currentId.value = data.id
    abilityForm.name = data.templateName
    abilityForm.behaviors = data.behaviors && data.behaviors.length > 0
      ? [...data.behaviors]
      : abilityForm.behaviors
  }
}

const closeDrawer = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const addBehavior = () => {
  abilityForm.behaviors.push('')
}

const removeBehavior = (index) => {
  if (abilityForm.behaviors.length > 1) {
    abilityForm.behaviors.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个关联维度')
  }
}

const handleDragEnd = (evt) => {
  // 交换数组元素
  const movedItem = abilityForm.behaviors.splice(evt.oldIndex, 1)[0]
  abilityForm.behaviors.splice(evt.newIndex, 0, movedItem)
}

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 处理表单提交
      const formData = {
        id: currentId.value,
        name: abilityForm.name,
        behaviors: abilityForm.behaviors
      }

      emit('success', {
        mode: isEditMode.value ? 'edit' : 'add',
        data: formData
      })
      closeDrawer()
    } else {
      ElMessage.error('请检查输入内容')
      return false
    }
  })
}

const handleClose = (done) => {
  closeDrawer()
  done()
}

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer
})
</script>

<style scoped lang="scss">
.ability-form-drawer {
  :deep(.el-drawer__header) {
    padding: 0;
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
  }

  p {
    margin: 0;
    padding: 0;
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  .close-icon {
    font-size: 16px;
    color: #999;
    cursor: pointer;
    margin-right: 16px;

    &:hover {
      color: #666;
    }
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.drawer-content {
  // padding: 24px;
  // height: calc(100% - 120px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;

  .form-label {
    // display: flex;
    // align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: #333;
    font-weight: 500;

    .required-mark {
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;

    .el-form-item__error {
      position: static;
      margin-top: 4px;
    }
  }
}

.input-wrapper {
  position: relative;

  .name-input {
    :deep(.el-input__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &::placeholder {
        color: #bfbfbf;
      }
    }
  }

  .char-count {
    position: absolute;
    right: 12px;
    bottom: 12px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }
}

.behavior-item {
  position: relative;
  // margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  width: 100%;

  .drag-handle {
    cursor: move;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    color: #909399;
    background: #d9e9f6;
    width: 100%;
    gap: 12px;
    margin-bottom: 4px;

    >p {
      display: flex;
      flex: 1;
    }

    // &:hover {
    //   color: #409EFF;
    // }
  }

  // .behavior-textarea {
  //   flex: 1;

  //   :deep(.el-textarea__inner) {
  //     border-radius: 6px;
  //     border: 1px solid #d9d9d9;
  //     padding: 12px 16px;
  //     font-size: 14px;
  //     resize: none;

  //     &:focus {
  //       border-color: #1890ff;
  //       box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  //     }

  //     &::placeholder {
  //       color: #bfbfbf;
  //     }
  //   }
  // }

  .char-count {
    position: absolute;
    right: 12px;
    bottom: 12px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }

  .delete-behavior {
    margin-top: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: #ff4d4f;

    &:hover {
      background: #ff7875;
    }

    .el-icon {
      font-size: 12px;
    }
  }
}

.add-behavior-section {
  margin-top: 24px;
  padding: 24px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  text-align: center;

  .add-behavior-btn {
    color: #666;
    font-size: 14px;

    &:hover {
      color: #1890ff;
    }

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    padding: 8px 24px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    color: #666;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .confirm-btn {
    padding: 8px 24px;
    border-radius: 6px;
    background: #1890ff;
    border-color: #1890ff;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
}
</style>