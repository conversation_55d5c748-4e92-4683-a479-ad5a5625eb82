
function splitData(text) {
    const lines = text.split('\n');
    const result = [];

    let currentRecord = '';
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.startsWith('data:')) {
            if (currentRecord !== '') {
                result.push(currentRecord.trim());
            }
            currentRecord = line;
        } else {
            currentRecord += line;
        }
    }

    if (currentRecord !== '') {
        result.push(currentRecord.trim());
    }
    return result;
}


export function convertAnswer(resp) {
    let txts = [];
    const allowTypes = ['answer', 'verbose', 'follow_up']
    try {
        resp = splitData(resp);
        resp = resp.filter(x => !!x)
        for (let b of resp) {
            try {
                b = JSON.parse(b.replace('data:', ''));
                if (!!b.message && !!b.message['content']) {
                    if (allowTypes.includes(b.message.type)) {
                        txts.push(b.message['content'])
                    }
                }
            } catch (e1) {
                // console.log('error1', e1)
            }
        }
    } catch (e2) {
        console.log('error2', e2)
    }
    return txts;
}


export const mergeHistory = (raw) => {
    let chat_history = []
    if (raw.length > 0) {
        for (let i = 0; i < raw.length; i++) {
            const item = raw[i];
            const isHello = (item.type || item.type == "hello");
            const isLastQuery = i == raw.length - 1 && item.my;
            if (!isHello && !isLastQuery && item.message) {
                const chat = {
                    "role": item.my ? "user" : "assistant",
                    "content_type": "text",
                    "content": item.message
                }
                chat_history.push(chat);
            }
        }
        // 传入【3轮】聊天记录
        if (chat_history.length > 6) {
            chat_history = chat_history.slice(-6);
        }
    }
    return chat_history;
}

export const splitMessage = (messages) => {
    const data = {
        message: [],
        questions: []
    }
    let msg_type = ''
    for (let i = 0; i < messages.length; i++) {
        const line = messages[i];
        if (line.indexOf("{") > -1) {
            try {
                const lienObj = JSON.parse(line)
                if (lienObj.msg_type) {
                    msg_type = lienObj.msg_type
                }
                continue
            } catch (e) {
            }
        }
        if (msg_type == "") {
            data.message += line;
        } else if (msg_type == "generate_answer_finish") {
            data.questions.push(line);
        }
    }
    return data;
}


export const splitRawMessage = (messages) => {
    const data = {
        message: "",
        questions: []
    }
    let msg_type = ''
    function fn(line) {
        if (!line) {
            return
        }
        if (line.indexOf("{") > -1) {
            try {
                const lienObj = JSON.parse(line)
                if (lienObj.msg_type) {
                    msg_type = lienObj.msg_type
                }
            } catch (e) {
            }
        }
        if (msg_type == "") {
            data.message += line;
        } else if (msg_type == "generate_answer_finish" && line.indexOf("generate_answer_finish") == -1) {
            data.questions.push(line);
        }
    }
    for (let i = 0; i < messages.length; i++) {
        const lines = messages[i];
        if (Array.isArray(lines)) {
            for (let j = 0; j < lines.length; j++) {
                fn(lines[j])
            }
        } else {
            fn(lines)
        }
    }
    return data;
}


export const cozeApi2history = (raw) => {
    let history = []
    for (let i = 0; i < raw.length; i++) {
        const { ai, messageType, message, id } = raw[i];
        let row = {}
        if (messageType === "COZE_USER") {
            row = {
                id, message,
                my: true
            }
            history.push(row)
        } else if (messageType === "COZE_ASSISTANT") {
            let item = {};
            if (message.indexOf("data:{") == 0) {
                const messages = convertAnswer(message)
                item = splitMessage(messages)
            } else {
                item = JSON.parse(message)
            }
            row = {
                id, message: item.message, my: false,
                questions: i == raw.length - 1 ? item.questions : []
            }
            history.push(row)
        }
    }
    return history;
}