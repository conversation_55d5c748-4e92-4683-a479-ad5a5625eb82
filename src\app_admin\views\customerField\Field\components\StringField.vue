<template>
    <el-form-item label="最小字符数">
        <el-input-number v-model="localMinLength" @change="validateCharLength"></el-input-number>
    </el-form-item>
    <el-form-item label="最大字符数">
        <el-input-number v-model="localMaxLength" @change="validateCharLength"></el-input-number>
    </el-form-item>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const localMinLength = ref(props.modelValue.minLength)
const localMaxLength = ref(props.modelValue.maxLength)

watch(() => props.modelValue, (newVal) => {
    localMinLength.value = newVal.minLength
    localMaxLength.value = newVal.maxLength
}, { deep: true })

const validateCharLength = () => {
    if (localMinLength.value > localMaxLength.value) {
        ElMessage.warning('最小字符数不能大于最大字符数')
        localMinLength.value = localMaxLength.value
    }

    emit('update:modelValue', {
        ...props.modelValue,
        minLength: localMinLength.value,
        maxLength: localMaxLength.value,
    })
}
</script>

<style lang="scss" scoped>
.char-limit {
    display: flex;
    gap: 20px;

    .limit-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
}
</style>