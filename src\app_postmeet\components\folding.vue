<template>
  <div>
    <div v-if="show_all" class="fold_icon" @click="onClick(true)">
      <ArrowUp />
    </div>
    <div v-else class="fold_icon" @click="onClick(false)">
      <ArrowDown />
    </div>
  </div>
</template>
<script>
import ArrowUp from "@/icons/arrow_up.vue"
import ArrowDown from "@/icons/arrow_down.vue"
export default {
  name: 'folling',
  components: { ArrowUp, ArrowDown },
  props: ['sname'],
  data() {
    return {
      show_all: true
    }
  },
  methods: {
    onClick(status) {
      this.show_all = !status
      this.$emit('callback', this.sname, status)
    }
  }
}
</script>

<style lang='scss' scoped>
// .fold_icon {
//   cursor: pointer;
//   width: 30px;
//   height: 30px;
//   border-radius: 4px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// .fold_icon:hover {
//   color: #436BFF;
// }
</style>
