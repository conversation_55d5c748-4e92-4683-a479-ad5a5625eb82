<template>
  <div class="customer_btn_column" v-click-outside="onBlur">
    <el-popover placement="bottom-end" :width="200" trigger="click" popper-class="ccolumn_pop" :visible="visible"
      ref="refPopover">
      <template #reference>
        <div class="cbtn flex-center" @click.stop="onShow">
          <el-icon size="18">
            <Setting />
          </el-icon>
        </div>
      </template>
      <div class="cbd_main flex-colomn">
        <div class="cbs_title">请选择需要展示的列</div>
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          全选
        </el-checkbox>
        <ul v-sortable @end="onOrderChange" class="custom-scrollbar">
          <li v-for="column in columns" :key="column" class="flex-row">
            <div class="flex-row">
              <input type="checkbox" v-model="checked[column]" @change="onChange" :disabled="checkedCount==1 && checked[column]"/>
              <div class="txt" @click="onClick(column)">
                {{ trans(column) }}
              </div>
            </div>
            <el-icon size="18">
              <DragIcon />
            </el-icon>
          </li>
        </ul>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { Setting } from "@element-plus/icons-vue";
import trans from "@/js/lang.js";
import { getStore, setStore } from "@/js/utils.js";
import DragIcon from "@/icons/drag.vue";
import { ClickOutside as vClickOutside } from "element-plus";

const visible = ref(false);
const refPopover = ref();
const columns = ref([]);
const checked = ref({});
const checkAll = ref(false);
const isIndeterminate = ref(true);
const emit = defineEmits(["callback"]);
let columns_full = [];
let tableid = "";
const timer = ref(null);
const checkedCount= computed(() => {
  return Object.values(checked.value).filter((x) => x).length;
})

const onBlur = () => {
  timer.value = setTimeout(() => {
    visible.value = false;
  }, 500);
};

const onShow = () => {
  visible.value = !visible.value;
};

const handleCheckAllChange = (val) => {
  const checkedList = val ? columns_full : [columns_full[0]];
  const temp = {};
  for (let column of checkedList) {
    temp[column] = true;
  }
  checked.value = temp;
  onChange();
};

const onClick = (column) => {
  timer.value && clearTimeout(timer.value);
  checked.value[column] = !checked.value[column];
  onChange();
};

const onChange = () => {
  timer.value && clearTimeout(timer.value);
  const result = toRaw(columns.value).filter((x) => checked.value[x]);
  setStore(tableid, result);
  emit("callback", result);
};

// 更新 columns 排序
const onOrderChange = (event) => {
  let item = columns.value.splice(event.oldIndex, 1)[0];
  columns.value.splice(event.newIndex, 0, item);
  setStore(tableid + "_orders2", toRaw(columns.value));
  onChange();
};

const _setSelect = (status, _checked = []) => {
  const temp = {};
  for (let column of columns.value) {
    temp[column] = _checked.includes(column) ? status : false;
  }
  checked.value = temp;
};

const init = (_tableid, data, checked = []) => {
  tableid = _tableid;
  const _checked = getStore(tableid, checked);
  columns_full = getStore(tableid + "_orders2", data);

  // Compare sorted arrays to check if they contain the same elements
  const sortedData = [...data].sort();
  const sortedColumnsFull = [...columns_full].sort();
  const areEqual = sortedData.length === sortedColumnsFull.length &&
    sortedData.every((value, index) => value === sortedColumnsFull[index]);

  // If arrays are different, update local storage with new data
  if (!areEqual) {
    columns_full = data;
    setStore(tableid + "_orders2", data);
  }

  columns.value = columns_full;
  _setSelect(true, _checked);
};


onMounted(() => {
  g.emitter.on("onLayoutClick", () => {
    visible.value = false;
  });
});

onUnmounted(() => {
  g.emitter.off("onLayoutClick");
});

defineExpose({
  refPopover,
  columns,
  checked,
  visible,
  onShow,
  init,
  onChange,
  onBlur,
});
</script>

<style lang="scss">
.customer_btn_column {
  cursor: pointer;

  .cbtn {
    width: 30px;
    height: 30px;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    user-select: none;

    .el-icon {
      color: #595959;
    }
  }
}

.ccolumn_pop {
  padding: 12px 4px 12px 12px !important;

  .cbs_title {
    color: #BCBCBC;
    margin-bottom: 12px;
  }

  ul {
    height: 200px;
    overflow-y: auto;
    list-style-type: none;

    li {
      height: 36px;
      align-items: center;
      justify-content: space-between;

      input[type="checkbox"] {
        width: 14px;
        position: relative;
        margin-right: 6px;
        cursor: pointer;
      }

      input[type="checkbox"]::after {
        position: absolute;
        top: 0;
        color: #000;
        width: 14px;
        height: 14px;
        display: inline-block;
        visibility: visible;
        padding-left: 0px;
        text-align: center;
        content: " ";
        border-radius: 2px;
      }

      input[type="checkbox"]:checked::after {
        content: "✓";
        color: #fff;
        font-size: 12px;
        line-height: 15px;
        background-color: #436bff;
        margin-top: 2px;
      }

      .txt {
        cursor: pointer;
      }

      .el-icon {
        cursor: move;
      }
    }
  }
}
</style>
