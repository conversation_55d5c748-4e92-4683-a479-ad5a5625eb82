<template>
    <div class="meeting_summary_notification">
        <div class="notification-card">
            <div class="header">
                <img :src="getAssetUrl('logo_mini.png')" alt="icon" class="icon">
                <span>{{ notice.title || '您有一条新的录制总结' }}</span>
                <div class="close-btn" @click="handleClose">
                    <img :src="getAssetUrl('close.svg')" />
                </div>
            </div>

            <div class="content">
                <div class="title">{{ notice.content }}</div>
                <div class="action-buttons">
                    <button class="cancel-btn" @click="handleClose">关闭</button>
                    <button class="start-btn" @click="handleStart">立即查看</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getAssetUrl } from '@/js/utils'

const notice = ref({})
const handleClose = () => {
    g.electronStore.closeWin('meeting_summary_notification')
}

const handleStart = () => {
    const url = `${g.config.postmeet_h5_customer}/index.html#/msgCenter/msgDetail?id=${notice.value.bizId}`;
    const user = localStorage.getItem('yxtlm_userInfo');
    const openParam = {
        urlParam: {
            id: 'dummy_window',
            url
        },
        newPageData: {
            "yxt-userInfo": user
        }
    }
    g.electronStore.createWindows(openParam);
    handleClose()
}

const title_max = 17;
const content_max = 47;

const handleMsg = (dataStr) => {
    try {
        const data = JSON.parse(dataStr);
        const { hostUserId, content, bizId } = data;
        if (!hostUserId || !content || !bizId) {
            console.error('get  data error', data)
            handleClose()
            return;
        }
        if (data.title && data.title.length > title_max) {
            data.title = data.title.slice(0, title_max) + '...';
        }
        if (data.content.length > content_max) {
            data.content = data.content.slice(0, content_max) + '...';
        }
        notice.value = data;
    } catch (err) {
        console.error('meet waitPageReady error', err)
    }
}

const addListener = () => {
    g.ipcRenderer.on('page-opened', (_, { id, data }) => {
        if (id === 'meeting_summary_notification') {
            handleMsg(data);
        }
    });
}

onMounted(() => {
    g.electronStore.waitPageReady().then(data => {
        handleMsg(data);
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
    addListener()
})

defineExpose({
    handleClose,
    handleStart
})

</script>

<style lang="scss">
body {
    background: #ffffff00;
    font-size: 16px;
}

.electron-layout {
    .mac-titlebar {
        display: none !important;
    }

    .mac-content {
        height: 100vh !important;
        margin-top: 0 !important;
    }
}

.meeting_summary_notification {
    display: flex;
    justify-content: center;
    align-items: center;

    .notification-card {
        width: 396px;
        margin: 2px;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);

        .header {
            padding: 12px;
            display: flex;
            align-items: center;
            background: #fff;
            -webkit-app-region: drag;

            .icon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                -webkit-app-region: no-drag;
            }

            .close-btn {
                margin-left: auto;
                cursor: pointer;
                -webkit-app-region: no-drag;
            }
        }

        .content {
            padding: 0 20px 12px 20px;

            .title {
                height: 39px;
                overflow-y: auto;
                margin-bottom: 12px;
                color: #666;
                font-size: 14px;
            }

            .action-buttons {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                margin-top: 0;
            }

            .cancel-btn {
                padding: 8px 16px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
            }

            .start-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                background: #436BFF;
                color: white;
                cursor: pointer;
            }
        }
    }
}
</style>