.record_header_wrap {
  height: 49px;
  width: 100%;
  font-size: 24px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  z-index: 3;
  position: relative;
  background-color: #fff;

  .rh_left {
    display: flex;

    .rh_logo {
      width: 40px;
      height: 40px;
      padding: 12px 10px 12px 32px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .rh_meet_info {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .diy_box {
        input:focus {
          width: 400px;
        }
      }

      .rh_title {
        height: 24px;
        font-weight: 600;
        font-size: 16px;
        color: #262626;
        line-height: 24px;
      }

      .rh_sub_title {
        font-size: 14px;
        color: #595959;
      }
    }
  }

  .header_tabs_box {
    padding-left: 30px;
    margin-top: 10px;
  }


  ._blank {
    flex-grow: 1;
  }

  .rh_right {
    align-items: center;
    display: flex;

    .btn {
      height: 32px;
      padding-top: 3px;
      margin-right: 10px;
      margin-left: 0;

      .icon {
        position: relative;
        right: 6px;
      }

      .icon:hover {
        color: red;
      }
    }

    .ebtn {
      cursor: pointer;
      width: 32px;
      height: 32px;
      background: #F5F5F5;
      border-radius: 16px;
      margin: 0px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dl_btn {
      width: 34px;
      padding: 0;
      margin: 0;
    }


    .rh_name {
      display: inline-flex;
      margin: 0 9px;

      .rh_abridge {
        flex: 0 0 auto;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}