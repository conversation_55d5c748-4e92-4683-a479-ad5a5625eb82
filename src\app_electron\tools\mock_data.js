export const mockDataReceived = [
    {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说，",
            "seq": 0,
            "time": 2200,
            "begin_time": 0,
            "message_id": "c30dc44a4f1042269528ecbf77cc8d71",
            "speaker": "",
            "words": [{
                "text": "来说",
                "start_time": "1220",
                "end_time": "2200"
            }]
        }
    }, {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话",
            "seq": 1,
            "time": 0,
            "begin_time": 0,
            "message_id": "c19581d5a3104b2ca7be4822218a7f5a",
            "speaker": "",
            "words": [{
                "text": "来说",
                "start_time": "1610",
                "end_time": "1890"
            }, {
                "text": "话",
                "start_time": "1890",
                "end_time": "2170"
            }]
        }
    },
    {
        "MsgType": "TwSummary",
        "Data": {
            "summary_content": "根据提供的内容，这段对话似乎并不是销售与客户之间的沟通记录，而是关于国际政治局势特别是乌克兰问题以及俄罗斯与北约之间紧张关系的描述。因此，基于给定的任务要求，我将尝试从这段文本中提取关键信息并形成一个摘要，尽管它并不完全符合销售场景根据提供的内容，这段对话似乎并不是销售与客户之间的沟通记录，而是关于国际政治局势特别是乌克兰问题以及俄罗斯与北约之间紧张关系的描述。因此，基于给定的任务要求，我将尝试从这段文本中提取关键信息并形成一个摘要，尽管它并不完全符合销售场景根据提供的内容，这段对话似乎并不是销售与客户之间的沟通记录。",
        }
    },
    {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话哦，小",
            "seq": 2,
            "time": 3680,
            "begin_time": 0,
            "message_id": "f1ef343c957840b997056e47d4306e02",
            "speaker": "1",
        }
    }, {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话，小声音了",
            "seq": 3,
            "time": 4040,
            "begin_time": 0,
            "message_id": "bfcebe7e7bf442c8976116633468590d",
            "speaker": "2",
        }
    }, {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话，小声音了。好",
            "seq": 4,
            "time": 4760,
            "begin_time": 0,
            "message_id": "a03d95bbee524608b4cbda41235c1364",
            "speaker": "3",
        }
    }, {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话，小声音了。好的",
            "seq": 5,
            "time": 5400,
            "begin_time": 0,
            "message_id": "335f734ac24546d5b96f415ea77b2ff0",
            "speaker": "3",
        }
    }, {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "来说话，小声音了。好的，再见",
            "seq": 5,
            "time": 5840,
            "begin_time": 0,
            "message_id": "a898b80ed1f14596814333e7d9ee9b9f",
            "speaker": "4",
        }
    },
    {
        "MsgType": "TwMsg",
        "Data": {
            "asr_result_changed": "拜访结束吧",
            "seq": 6,
            "time": 5870,
            "begin_time": 5840,
            "message_id": "a898b80ed1f14596814333e7d9ee9b9f",
            "speaker": "4",
        }
    }
]
