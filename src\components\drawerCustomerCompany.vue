<template>
    <el-drawer v-model="isShow" :title="company.name" :with-header="true" size="640" show-close @close="onClose"
        class="drawer_customer_company">
        <template #header>
            <div class="vd_title flex-row">
                <div class="vd_title_name">
                    {{ company.name }}
                </div>
            </div>
        </template>
        <iframe ref="refIframe" :src="url" width="620px" height="100%" frameborder="no" border="0" marginwidth="0"
            marginheight="0" @load="onIframeLoad"></iframe>
    </el-drawer>
</template>

<script setup>
import { generateUrl } from "@/js/utils.js"
import RightArrowIcon from "@/icons/right_arrow.vue"
const url = ref('');
const isShow = ref(false)
const refIframe = ref()
const company = ref({})

const show = (data) => {
    isShow.value = true;
    company.value = data;
    const { companyId, name } = company.value;
    const iframeurl = generateUrl(`${g.config.postmeet_h5_record}/#/xmateCustomerDetail`, {
        companyName: name,
        companyId,
        token: g.appStore.user.token
    })
    url.value = '';
    setTimeout(() => {
        url.value = iframeurl
    }, 1);
}

const onClose = () => {
    if (refIframe.value) {
        refIframe.value.src = 'about:blank';
    }
    url.value = '';
    company.value = {};
}

const onIframeLoad = () => {
}

defineExpose({ onClose, show, url, isShow, refIframe, RightArrowIcon })

</script>

<style lang="scss">
.drawer_customer_company {
    .el-drawer__header {
        margin-bottom: 6px;
        padding: 0 12px;

        .vd_title {
            .vd_title_name {
                color: #262626;
                font-weight: 500;
            }
        }

        .el-drawer__title {
            color: #262626;
            font-weight: 500;
        }
    }

    .el-drawer__body {
        margin-bottom: 0 !important;
        padding: 0 !important;
    }
}
</style>