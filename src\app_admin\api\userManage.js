import { getHttp } from "@/js/request.js";

// 获取字段设置列表
export const getFieldSettingsListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            const mockData = [
                {
                    id: 1,
                    fieldName: "姓名",
                    fieldType: "必填",
                    creator: "-",
                    createTime: "-",
                    status: "required"
                },
                {
                    id: 2,
                    fieldName: "负责人",
                    fieldType: "必填",
                    creator: "-",
                    createTime: "-",
                    status: "required"
                },
                {
                    id: 3,
                    fieldName: "地址",
                    fieldType: "有内容",
                    creator: "王美丽",
                    createTime: "2025-07-21 14:30",
                    status: "filled"
                }
            ];

            // 简单的搜索过滤
            let filteredData = mockData;
            if (params.searchKey) {
                filteredData = mockData.filter(item =>
                    item.fieldName.includes(params.searchKey) ||
                    item.fieldType.includes(params.searchKey) ||
                    (item.creator && item.creator.includes(params.searchKey))
                );
            }

            resolve({
                code: 0,
                data: {
                    datas: filteredData,
                    totalNum: filteredData.length,
                }
            });
        }, 500);
    });
};

// 删除字段设置
export const deleteFieldSetting = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};

// 添加字段设置
export const addFieldSetting = (params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "添加成功",
                data: {
                    id: Date.now(),
                    ...params
                }
            });
        }, 300);
    });
};

// 更新字段设置
export const updateFieldSetting = (id, params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "更新成功",
                data: {
                    id,
                    ...params
                }
            });
        }, 300);
    });
};

export const getPageSettingsListData = (params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            const mockData = [
                {
                    id: 1,
                    fieldName: "地址",
                    fieldType: "单行文本",
                    create: true,
                    required: false,
                    table: true,
                    filter: false,
                    search: true,
                    detail: true
                },
                {
                    id: 2,
                    fieldName: "等级",
                    fieldType: "单选列表",
                    create: true,
                    required: false,
                    table: true,
                    filter: false,
                    search: false,
                    detail: true
                }
            ];
            resolve({
                code: 0,
                data: {
                    datas: mockData,
                    totalNum: mockData.length
                }
            });
        }, 500);
    });
};