<template>
  <div class="recording-settings">
    <div class="setting-item">
      <el-checkbox v-model="settings.cloudRecording" label="云录制已默认开启" :disabled="true" />
    </div>
    <div class="setting-item">
      <el-checkbox v-model="settings.localRecording" label="本地录制" />
    </div>
    <div v-if="isMac && !hasVirtualAudio" class="setting-item audio-warning">
      需要管理员权限安装驱动才能录制系统音频
    </div>
    <div class="setting-item record-path">
      <div class="record-path-setting">
        <span>设置录制文件保存地址</span>
        <div>
          <el-button type="primary" @click="chooseLocalRecordingPath">选择</el-button>
          <el-button type="default" v-if="settings.localRecordingPath" @click="openLocalRecordingPath">打开</el-button>
        </div>
      </div>
      <div class="record-path-current">{{ settings.localRecordingPath }}</div>
    </div>
  </div>
</template>

<script setup>
const settings = ref(g.electronStore.settings.recording);
const isMac = ref(process.platform === 'darwin');
const hasVirtualAudio = ref(false);

const checkVirtualAudio = async () => {
  if (isMac.value) {
    hasVirtualAudio.value = await g.ipcRenderer.invoke('check-mac-virtual-audio');
    if (!hasVirtualAudio.value) {
      settings.value.recordSystemAudio = false;
      g.electronStore.updateSetting('recording', toRaw(settings.value))
    }
  }
};

const chooseLocalRecordingPath = () => {
  g.ipcRenderer.invoke("choose-local-recording-path", settings.value.localRecordingPath).then((path) => {
    if (path) {
      settings.value.localRecordingPath = path
    }
  })
};

const openLocalRecordingPath = () => {
  g.ipcRenderer.send("open-local-recording-path", settings.value.localRecordingPath)
};

watch(() => settings.value, (newSettings) => {
  g.electronStore.updateSetting('recording', newSettings)
}, { deep: true });

const checkRecordingPath = () => {
  g.electronStore.checkRecordingPath().then((res) => {
    if (!res) {
      ElMessage.error('录制路径不存在，请检查设置')
    }
  })
}

onMounted(async () => {
  settings.value = g.electronStore.settings.recording;
  checkRecordingPath()
  await checkVirtualAudio();
});

</script>

<style lang="scss" scoped>
.recording-settings {
  .setting-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }

  .audio-warning {
    color: #e6a23c;
    font-size: 12px;
    padding-left: 20px;
    border-bottom: none;
  }

  .record-path {
    display: flex;
    flex-direction: column;

    .record-path-setting {
      display: flex;
      justify-content: space-between;
    }

    .record-path-current {
      margin-top: 10px;
    }
  }
}
</style>