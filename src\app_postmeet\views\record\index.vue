<template>
  <div class="index_wrap">
    <RecordHeader ref="refHeader" class="record-header" />
    <div v-if="loading" v-loading="loading" class="loading"></div>
    <div v-show="hasMeet && !loading" class="index_main">
      <div v-if="hasAsr === 'no'" class="only_video">
        <VideoPlayer ref="refVideo"></VideoPlayer>
      </div>
      <div v-if="hasAsr === 'yes'" class="column_box">
        <div :style="index_style">
          <div class="column-left">
            <div class="resize-bar" :style="bar_style"></div>
            <div class="resize-line" v-show="recordViewPermission"></div>
            <div class="resize-save">
              <div v-show="salesMateType != 1">
                <menuMain ref="refMenuMain" @callback="cbMenuMain" />
              </div>
              <div v-show="isGenReporting" class="ai_reload">
                <loadingStep ref="refLoading" />
              </div>
              <div v-show="!isGenReporting && left_menu == 'summary'">
                <LeftSummary ref="refLeftSummary" />
              </div>
              <div v-show="!isGenReporting && left_menu == 'analyse'">
                <LeftAnalyse ref="refLeftAnalyse" />
              </div>
              <div v-show="!isGenReporting && left_menu == 'coach'">
                <LeftCoach ref="refLeftCoach" />
              </div>
            </div>
          </div>
          <div class="column-right" id="sharerecord_right" v-show="recordViewPermission">
            <RightHeader ref="refRightHeader" @callback="cbRightHeader" />
            <!-- <AiInput /> -->
            <div v-show="isShowVideo">
              <video-player ref="refVideo"></video-player>
            </div>
            <SubtitlesSummary ref="refSubtitles"></SubtitlesSummary>
            <AudioPlayer ref="refAudio" v-show="!isShowVideo" />
          </div>
        </div>
        <div v-show="isShowAnalyseDia">
          <AnalyseDialog />
        </div>
      </div>
      <div v-if="hasAsr == 'no'">
        <el-empty description="暂无转写内容" />
      </div>
    </div>
    <div v-show="hasError">
      <div class="error_wrap">
        <HasError ref="refHasError" />
      </div>
    </div>
    <div v-show="!hasMeet && !loading && !hasError">
      <NoAccess ref="refNoAccess" />
    </div>
    <HintDialog ref="refHintDialog" @callback="cbHintDia" />
    <SelectPop />
    <addTodoPop />
    <DiaEditRecord ref="refDiaEdit" @callback="handleCommand" />
    <MdEditorDialog ref="refMdEditorDialog" @callback="cbBaseMdEditorDialog" />
  </div>
</template>

<script>
import MdEditorDialog from "@/components/Markdown/MdEditor.vue";
import RecordHeader from "./header";
import SubtitlesSummary from "./subtitlesSummary/subtitlesSummary.vue";
import VideoPlayer from "./video_player/video_player.vue";
import {
  getAsrStore,
  getTranslateStatus,
  getSaleReports,
  putTypeUpdate,
  updateSummary
} from "@/app_postmeet/tools/api";
import { parseAsrRaw, saveUrlAndLogin } from "@/app_postmeet/tools/tools";
import { throttle } from "@/js/utils";
import CopyIcon from "@/icons/copy.vue";
import SelectPop from "@/app_postmeet/components/selectPop.vue";
import addTodoPop from "@/app_postmeet/components/addTodoPop.vue";
import NoAccess from "@/app_postmeet/views/share_pwd/no_access.vue";
import HasError from "@/app_postmeet/views/share_pwd/has_error.vue";
import FoldHideIcon from "@/icons/fold_hide.vue";
import DiaEditRecord from "@/components/DiaEditRecord.vue";
import menuMain from "./menu_main/menuMain.vue";
import HintDialog from "@/app_postmeet/components/HintDialog.vue";
import LeftSummary from "./leftSummary/leftSummary.vue";
import LeftAnalyse from "./leftAnalyse/leftAnalyse.vue";
import LeftCoach from "./leftCoach/leftCoach.vue";
import RightHeader from "@/app_postmeet/components/rightHeader/rightHeader.vue";
import AnalyseDialog from "./AnalyseDialog/AnalyseDialog.vue";
import AudioPlayer from "@/app_postmeet/components/audio_player/AudioPlayer.vue";
import AiInput from "@/app_postmeet/components//aiInput.vue";
import { isPC } from "@/js/utils.js";
import loadingStep from "@/app_postmeet/components/loadingStep.vue";

export default {
  components: {
    NoAccess,
    RecordHeader,
    DiaEditRecord,
    CopyIcon,
    SelectPop,
    AiInput,
    loadingStep,
    FoldHideIcon,
    LeftAnalyse,
    LeftCoach,
    HintDialog,
    menuMain,
    LeftSummary,
    RightHeader,
    AnalyseDialog,
    AudioPlayer,
    VideoPlayer,
    SubtitlesSummary,
    addTodoPop,
    HasError,
    MdEditorDialog,
  },
  data() {
    return {
      confId: "",
      loading: true,
      hasMeet: false,
      hasError: false,
      hasAsr: "",
      hint: "",
      isHost: false,
      timer: null,
      meetingType: 0,
      isGenReporting: false,
      asrContent: {},
      asrSummary: {},
      recordInfo: {},
      leftStyle: {},
      isShowSetup: false,
      needAsrMail: false,
      isInterview: false,
      isShowMd: false,
      rdViewType: "",
      isShowSaleReportHeader: false,
      recordViewPermission: true,
      allowSaleReGen: false,
      saleReport: {},
      isSetupLoading: false,
      currLabel: { systemPreset: true },
      saleTags: [],
      error: "没有找到相关的沟通",
      bar_style: {
        width: parseInt(0.5 * document.body.offsetWidth) + "px",
      },
      left_menu: "summary",
      isShowAnalyseDia: false,
      isShowVideo: true,
      salesMateType: 2,
      isShowSetRoleCompleteDig: true,
      index_style: { width: "100vw" },
      saleAnalyseType: ""
    };
  },
  mounted() {
    if (isPC()) {
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      this.init();
      g.emitter.on("show_edit_customer_name", () => {
        this._showEditCustomerName();
      });
      g.emitter.on("update_show_analyse", (data) => {
        this.isShowAnalyseDia = data;
      });
      g.emitter.on("is_re_gen_sale_report", (status) => {
        this.isGenReporting = status;
      });

      g.appStore.createWsAgent();
      this.watchEmitter()
    } else {
      this.goMobileRecord();
    }

  },
  methods: {
    handleCommand(cmd) {
      if (cmd == "reload") {
        this.init();
      }
    },
    goMobileRecord() {
      const url = `${g.config.postmeet_h5_record}/#/record/${this.$route.params.confId}`;
      console.log("goMobileRecord url", url);
      location.href = url;
    },
    cbHintDia(action, data) {
      if (action == "setup_role_complete") {
        if (data == "confirm") {
          g.emitter.emit(g.postmeetStore.get_curr_on_report(), "");
        }
        this.isShowSetRoleCompleteDig = false;
      } else if (action == "regen_complete") {
        if (data == "confirm") {
          location.reload();
        }
      } else if (action == "regen_fail") {
        if (data == "confirm") {
        }
      }
    },
    cbRightHeader(action, data) {
      if (action == "analyse") {
        this.isShowAnalyseDia = data;
        this.$refs.refSubtitles.setIsShowAnalyse(data);
        this.$refs.refVideo.setIsShowAnalyse(data);
        const width = document.body.offsetWidth;
        this.bar_style.width = parseInt((data ? 0.33 : 0.5) * width) + "px";
      } else if (action == "playing_video") {
        this.isShowVideo = data;
        this.$refs.refSubtitles.setIsShowVideo(data);
        if (!this.isShowVideo) {
          this.$nextTick(() => {
            this.$refs.refAudio.init(this.recordInfo.duration);
          });
        }
      }
    },
    cbMenuMain(data) {
      this.left_menu = data;
    },
    init() {
      this.confId = this.$route.params.confId
      g.postmeetStore.setValue("confId", this.confId)
      this.getAsr()
      document.title = "绚星销售助手录制纪要";
      this.ListenerStats();
      this.ListenerLeftWidth();
      this.ListenerContext();
      localStorage.removeItem("clickTag");
      g.emitter.on("show_index_hint", ([type, hint]) => {
        if (this.type == "setup_role_complete") {
          if (!this.isShowSetRoleCompleteDig) {
            this.isShowSetRoleCompleteDig = true;
            this.$refs.refHintDialog.show(type, hint);
          }
        } else {
          this.$refs.refHintDialog.show(type, hint);
        }
      });
    },
    addRightSizeMonitor() {
      const that = this;
      const func = () => {
        g.emitter.emit("sharerecord_right_resize", "");
      };

      const sendWsThrottleFunc = throttle(() => {
        func();
      }, 800);

      const myObserver = new ResizeObserver((entries) => {
        sendWsThrottleFunc();
      });
      const someEl = document.querySelector("#sharerecord_right");
      someEl && myObserver.observe(someEl);
    },
    ListenerContext() {
      const that = this;
      g.emitter.on("updatedAsrContent", () => {
        that.allowSaleReGen = g.postmeetStore.data.allowSaleReGen;
      });
    },
    ListenerLeftWidth() {
      const that = this;
      g.emitter.on("leftWidth", (width) => {
        that.leftStyle = { width: width };
      });
    },
    ListenerStats() {
      const that = this;
      g.emitter.on("startConvertStatusTimer", () => {
        that.timer && clearInterval(that.timer);
        that.timer = setInterval(() => {
          getTranslateStatus(g.postmeetStore.data.confId).then((resp) => {
            if (resp.code == 0) {
              // recognitionStatus: number, //转写 状态， 0，没有转写，1：正在转写， 2：转写成功， 3：转写失败
              const status = resp.data.recognitionStatus;
              if (status == 2) {
                this.$refs.refHintDialog.show(
                  "regen_complete",
                  "转录已完成，是否现在刷新页面重新加载数据？"
                );
                that.timer && clearInterval(that.timer);
              } else if (status == 3) {
                that.timer && clearInterval(that.timer);
                this.$refs.refHintDialog.show("regen_fail", "转录失败！");
              }
            }
          });
        }, 10000);
      });
    },
    getRecord() {
      const user = g.appStore.user;
      if ((user && user.orgId)) {
        this.getAsr();
      } else {
        this.$refs.refNoAccess.show(`无权限访问`);
        this.loading = false;
        if (!this.hasMeet) {
          saveUrlAndLogin(this.$router);
        }
      }
    },
    onChangeViewType(from = "") {
      this.$refs.refLeftSummary.onChangeViewType(from);
    },
    _setCurrLabel(_currLabelItem) {
      this.currLabel = _currLabelItem;
      this.$refs.refLeftSummary.btnRegen_setCurrItem(_currLabelItem);
    },
    setAsrContent() {
      this._setDefaultType();
      this.$refs.refLeftSummary.init(this);
      this.$refs.refSubtitles.init();
      this.$refs.refHeader.init();
      const width = document.body.offsetWidth;
      this.bar_style.width =
        parseInt((this.isShowAnalyseDia ? 0.33 : 0.5) * width) + "px";
      this.bar_style.minWidth = parseInt(0.2 * width) + "px";
      this.bar_style.maxWidth = parseInt(0.7 * width) + "px";
      this.isHost = !g.postmeetStore.isReadonly();
      this.needAsrMail = g.postmeetStore.data.needAsrMail;
      this.$refs.refVideo.setHasAsr(this.hasAsr);
      this.loading = false;
      this.$forceUpdate();
    },
    _updateViewtype(type) {
      this.rdViewType = type;
      this.$refs.refLeftSummary.update_menu_type(type);
    },
    _setDefaultType() {
      let defaultSaleLabe = "sale_meet";
      if (this.saleReport.analysisReports && this.saleReport.analysisReports.length > 0) {
        const firstItem = this.saleReport.analysisReports[0];
        defaultSaleLabe = firstItem.label;
      }
      this._updateViewtype(defaultSaleLabe);
      this.onChangeViewType();
    },
    _setSaleReport(resp) {
      g.postmeetStore.setSaleReport(resp.data);
      const { status, complete } = resp.data;
      g.postmeetStore.setValue("saleReportStatus", status);
      if (this.recordViewPermission) {
        this.$nextTick(() => {
          this.$refs.refRightHeader.init();
        });
      }
      if (status == "SUCCESS") {
        g.postmeetStore.data.defaultSalesMethodology = resp.data.defaultSalesMethodology;
        console.log("saleReport", g.postmeetStore.data.saleReport);
        this.saleReport = g.postmeetStore.data.saleReport;
        g.postmeetStore.setValue("conferenceId", this.saleReport.conferenceId);
        this.isShowSaleReportHeader = true;
        const sa = this.saleReport.analysisReports;
        if (sa && sa.length > 0) {
          this.saleTags = [];
          this._updateViewtype(sa[0].label);
          this._setCurrLabel(sa[0]);
          this.onChangeViewType();
        } else {
          this._setDefaultType();
        }
      } else {
        this.isShowSetup = this.isHost;
        this.saleTags = resp.data.tabs || [];
        this._setDefaultType();
        if (status == "IN_PROCESS") {
          g.emitter.emit("update_report_complete_process", complete);
          this.$nextTick(() => {
            g.emitter.emit("monitor_sale_report");
          });
        }
      }
      this.isHost = !g.postmeetStore.isReadonly();
      this.isShowSetup = g.postmeetStore.getIsShowSetup();
      if (this.isHost) {
        this.$nextTick(() => {
          if (!this.isShowSetup) {
            this.$refs.refBtnRegen && this.$refs.refBtnRegen.init();
          }
          this.$refs.refLeftSummary.setupRoleInit();
        });
      }

      this.setAsrContent();
      g.emitter.emit("after_update_sale", "");
      this.addRightSizeMonitor();
    },
    getSaleReport() {
      getSaleReports(g.postmeetStore.data.confId || this.confId).then((resp) => {
        if (resp.code == 0) {
          this._setSaleReport(resp);
        }
      });
    },
    _showEditCustomerName() {
      if (!this.isHost) {
        console.log("not host showEditCustomerName");
        return;
      }
      const param = {
        conferenceIds: [this.confId],
        salesMateCustomerName: "",
        salesRelatedType: 1,
        salesMateTags: "",
        salesGoodsCategories: "",
        salesMateType: 2,
        salesGoal: "",
        subject: g.postmeetStore.data.recordInfo.subject,
      };
      this.$refs.refDiaEdit.show(param);
    },
    getAsr() {
      const handleNoAsr = () => {
        this.hasAsr = "no";
        this.$nextTick(() => {
          this.$refs.refHeader.init();
          this.$refs.refVideo.setHasAsr(this.hasAsr);
          this.loading = false;
        });
      };
      console.log('getAsr confId', this.confId)
      getAsrStore(this.confId)
        .then((response) => {
          // console.log("getAsrStore response", response);
          if (response.code == -1) {
            this.$refs.refNoAccess.show(
              `您暂时无权限查看此沟通纪要`, '请检查您的登录信息'
            );
            this.loading = false;
            return;
          }
          else if (response.code == 1315) {
            const shareId = response.data;
            this.$router.push({
              path: `/postmeet/share/${shareId}`,
              query: {
                confId: this.confId
              }
            });
            return;
          }
          const data = response.data;
          this.loading = false;
          const { recordInfo, customerName, needCalRealTime } = data;
          if (!!recordInfo) {
            recordInfo['needCalRealTime'] = needCalRealTime
          }

          if (!customerName) {
            this._showEditCustomerName();
          }
          if (
            recordInfo &&
            recordInfo.recordList
          ) {
            recordInfo["confId"] = this.confId;
            this.hasMeet = true;
            this.recordInfo = recordInfo;

            g.postmeetStore.setValue("recordInfo", recordInfo);
            this.$refs.refHeader.setRoomInfo(recordInfo);
            g.postmeetStore.setValue("asrRaw", parseAsrRaw(data));
            g.postmeetStore.setValue("interview", data.interview);
            g.postmeetStore.updateValue();
            this.salesMateType = g.postmeetStore.data.asrRaw.salesMateType;
            this.isInterview = g.postmeetStore.data.isInterview;
            this.hasMind = !!g.postmeetStore.data.MindMapMd;
            this.meetingType = g.postmeetStore.data.asrRaw.meetingType;
            this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
            if (this.recordViewPermission) {
              this.index_style = { width: "100vw" };
              if (this.recordViewPermission && this.hasAsr == "yes") {
                this.$nextTick(() => {
                  console.log("init right header", this.loading, this.hasAsr);
                  this.$refs.refRightHeader.init();
                });
              }
            } else {
              this.index_style = {
                width: "100vw",
                display: "flex",
                "justify-content": "center",
              };
            }
            this.hasAsr = g.postmeetStore.getHasAsr();
            this.$nextTick(() => {
              if (this.hasAsr == "yes") {
                this.asrSummary = data.asrSummary;
                this.asrContent = data.asrContent;
                this.getSaleReport();
              } else {
                handleNoAsr();
              }
            });
            g.emitter.emit("after_update_store", "");
          } else {
            this.hasError = true;
            console.log("hasError", response);
            if (response.message) {
              this.$nextTick(() => {
                let message = response.message;
                if (message == 'success') {
                  message = '抱歉，由于系统异常，沟通记录生成失败';
                }
                this.$refs.refHasError.setErrorMsg(message);
              });
            }
            console.log("openpostmeetfail", recordInfo);
          }
        })
        .catch((e) => {
          console.log("getAsrStore error", e);
          if (e.code == 1315) {
            const shareId = e.data;
            this.$router.push({
              path: `/share/${shareId}`,
            });
          } else if (e.code == 1319) {
            this.$refs.refNoAccess.show(
              `您暂时无权限查看沟通纪要，请联系沟通主持人${e.data.hostName}`
            );
          } else {
            this.$refs.refNoAccess.show(`${e.message}，请使用正确的分享地址打开！`);
          }

          this.loading = false;
        });
    },

    watchEmitter() {

      // 监测markdown的变更记录
      g.emitter.on('setMdTypeEditorValue', (value) => {
        this.cbMdEditorDialog(value)
      })
      g.emitter.on('onChangeSaleAnalyseType', (type) => {
        this.saleAnalyseType = type || ''
      })
    },
    // 基本类型的编辑markdown
    cbBaseMdEditorDialog(ob) {
      this.saleAnalyseType = this.saleAnalyseType ? this.saleAnalyseType : this.currLabel.defaultSalesMethodology
      const data = {
        content: ob
      }
      if (g.postmeetStore.data.summaryMdId) {
        putTypeUpdate(g.postmeetStore.data.confId, g.postmeetStore.data.summaryMdId, data).then(resp => {
          if (resp.code == 0) {
            ElMessage.success("编辑保存成功!");
            this.$nextTick(() => {
              this.cbMdEditorDialogMain(ob)
            })
          }
          this.$refs.refMdEditorDialog?.onCancel?.()
        })
      } else {
        updateSummary(g.postmeetStore.data.confId, this.saleAnalyseType, data).then(resp => {
          if (resp.code == 0) {
            ElMessage.success("编辑保存成功!");
            g.emitter.emit('updateSummary', this.saleAnalyseType);
            this.$refs.refMdEditorDialog?.onCancel?.()
          }
        })
      }
    },
    // 设置会议纪要的md
    cbMdEditorDialogMain(ob) {
      this.$refs.refLeftSummary.changeMd(ob)
      this.saleReport.analysisReports.forEach(item => {
        if (item.label == 'meet_minutes') {
          item.report = ob
        }
      })
    },
    // 编辑markdown信息保存
    cbMdEditorDialog(ob) {
      const data = {
        content: ob.md
      }
      putTypeUpdate(g.postmeetStore.data.confId, g.postmeetStore.data.summaryMdId, data).then(resp => {
        if (resp.code == 0) {
          ElMessage.success("编辑保存成功!");
        }
      })
    },
    unmounted() {
      g.emitter.off('setMdTypeEditorValue');
      g.emitter.off('onChangeSaleAnalyseType');
    },
  },
};
</script>

<style lang="scss">
@use './index.scss';

.record-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  height: 72px;
}

.index_wrap {
  padding-top: 72px;
}
</style>
