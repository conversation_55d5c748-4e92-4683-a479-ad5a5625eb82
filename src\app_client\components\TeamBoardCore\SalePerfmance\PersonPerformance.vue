<template>
    <div class="personperfm_wrap flex-col">
        <div class="overview-header flex-row">
            <div class="flex-row title-line-wrap">
                <div class="title-line"></div>
                <div class="title">
                    个人表现趋势
                </div>
            </div>
        </div>
        <div class="flex-row-personperfm-wrap">
            <DrawerSelectMixed @callback="onSelectUser" ref="refUser" type="user" :rootDeptId="rootDeptId"
                :enableClose="true" :maxTagCount="1" :maxSelect="1" />
            <RadioGroup v-model="perfType" class="fch_radio" :options="options" />
        </div>
        <template v-if="selectUserObj.ssoUserId">
            <aiAnalysis :data="aiAnalysisArr" class="ai_analysis" :loading="loading" />
            <perfTrendLineChart ref="refTrendLineChart" v-loading="chartLoading" element-loading-text="数据加载中... 请稍等" />
        </template>
        <template v-else>
            <div class="no_select_col">
                <el-empty description="请选择销售人员" :image="getOssUrl('no-data.png', 3)" />
            </div>
        </template>

    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import perfTrendLineChart from "@/app_client/components/TeamBoardCore/components/perfTrendLineChart.vue"
import { getUserTrends, getUserPerformanceTrendAnalysis } from "@/app_client/tools/api.js"
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import RadioGroup from '@/app_client/components/RadioGroup.vue'
import aiAnalysis from '@/app_client/components/TeamBoardCore/components/aiAnalysis.vue'
const props = defineProps({
    isReport: {
        type: Boolean,
        default: false
    }
});
const options = g.clientBoardStore.perfTypeOptions;
const aiAnalysisArr = ref([])
const result1 = ref('加载中...')
const result2 = ref('加载中...')
const refTrendLineChart = ref();
const loading = ref(false);
const chartLoading = ref(false);
const selectSsoUserIds = ref()
const selectUserObj = ref({})
const perfType = ref('');
const refUser = ref()

// 添加请求控制变量
const currentRequest = ref(null);
const currentAnalysisRequest = ref(null);
const requestTimeout = ref(null);
const currentRequestId = ref(0);
const currentAnalysisRequestId = ref(0);

const overviewDept = computed(() => {
    return g.clientBoardStore.overviewDept;
});
const periodType = computed(() => {
    return g.clientBoardStore.periodType;
})

const rootDeptId = computed(() => {
    return g.clientBoardStore.overviewDept.value;
});

const loadingResult = ref(false)
// 默认选中前两项用户
const setDefaultUsers = async () => {
    // 清除之前的定时器
    if (requestTimeout.value) {
        clearTimeout(requestTimeout.value);
    }

    // 设置防抖延迟
    requestTimeout.value = setTimeout(async () => {
        try {
            // 使用API获取用户列表
            const { getUserListPager } = await import('@/js/api');
            const response = await getUserListPager({
                pageNum: 1,
                pageSize: 10,
                deptIds: [rootDeptId.value]
            });

            if (response.code === 0 && response.data.datas) {
                const defaultUsers = response.data.datas.slice(0, 1);
                selectUserObj.value = defaultUsers[0] || {}
                // 设置默认选中的用户
                refUser.value?.init({ users: defaultUsers });
                selectSsoUserIds.value = defaultUsers.map(item => item.ssoUserId);
                init()
            }
        } catch (error) {
            console.error('获取用户列表失败:', error);
        }
    }, 300); // 300ms防抖延迟
};

const init = async () => {
    aiAnalysisArr.value = []
    const { orgId, deptId, dataType, startTime, endTime, periodType } = g.clientBoardStore.getApiParam(perfType.value)
    if (!orgId || !deptId) return;

    // 生成新的请求ID
    const requestId = ++currentRequestId.value;
    const analysisRequestId = ++currentAnalysisRequestId.value;

    chartLoading.value = true;
    loading.value = true;
    const param = {
        "pageSize": 10,
        "pageNumber": 1,
        startTime, endTime,
        "reportType": periodType
    }
    const ssoUserId = selectUserObj.value.ssoUserId;

    try {
        const res = await getUserTrends(orgId, deptId, ssoUserId, dataType, param);

        // 检查请求是否仍然有效
        if (requestId !== currentRequestId.value) {
            console.log('趋势请求已过期，忽略结果');
            return;
        }

        if (res.code == 0) {
            nextTick(() => {
                refTrendLineChart.value.init(dataType, res.data);
                chartLoading.value = false;
            })

            const obj = {
                "userName": selectUserObj.value.username,
                "dimension": options.find(item => item.value === perfType.value)?.label,
                "trendData": {
                    "reportTimes": res.data.reportTimes || [],
                    "datas": res.data.datas || []
                }
            }

            getUserPerformanceTrendAnalysis(obj).then(res => {
                // 检查分析请求是否仍然有效
                if (analysisRequestId !== currentAnalysisRequestId.value) {
                    console.log('分析请求已过期，忽略结果');
                    return;
                }

                const data = res.data || []
                const arr = [
                    {
                        "title": "洞察结果",
                        "content": data.analysis || '',
                        "img": 'star4.png'
                    },
                    {
                        "title": "辅导建议",
                        "content": data.advice || '',
                        "img": 'star3.png'
                    }
                ]
                aiAnalysisArr.value = arr;
            }).catch(error => {
                // 检查分析请求是否仍然有效
                if (analysisRequestId !== currentAnalysisRequestId.value) {
                    console.log('分析请求已过期，忽略错误');
                    return;
                }
                console.error('获取分析数据失败:', error);
            }).finally(() => {
                // 只有当前分析请求才更新loading状态
                if (analysisRequestId === currentAnalysisRequestId.value) {
                    loading.value = false;
                }
            })
        }
    } catch (error) {
        // 检查请求是否仍然有效
        if (requestId !== currentRequestId.value) {
            console.log('趋势请求已过期，忽略错误');
            return;
        }
        console.error('获取趋势数据失败:', error);
        chartLoading.value = false;
        loading.value = false;
    } finally {
        // 只有当前请求才更新loading状态
        if (requestId === currentRequestId.value) {
            chartLoading.value = false;
        }
    }
}

const onSelectUser = (action, data) => {
    if (action == "confirm") {
        console.log('onSelectUser', data);
        selectUserObj.value = data.users[0] || {}
        init()
    }
};

watch(() => g.clientBoardStore.perfType, () => {
    perfType.value = g.clientBoardStore.perfType;
}, { immediate: true })

// watch(() => [periodType.value, overviewDept.value], () => {
//     aiAnalysisArr.value = []
//     selectUserObj.value = {}
//     refUser.value?.reset(true, true)

// }, { immediate: true })

watch(() => [perfType.value, periodType.value,], () => {
    if (selectUserObj.value.ssoUserId) {
        // 清除之前的定时器
        if (requestTimeout.value) {
            clearTimeout(requestTimeout.value);
        }

        // 设置防抖延迟
        requestTimeout.value = setTimeout(() => {
            init();
        }, 500); // 500ms防抖延迟
    }
}, { immediate: true });

watch(() => [overviewDept.value], () => {
    selectUserObj.value = {}
    refUser.value?.reset(true, true)
    // 当部门变化时，重新设置默认用户
    nextTick(() => {
        setDefaultUsers();
    });
}, { immediate: true });

// 组件挂载后设置默认用户
onMounted(() => {
    nextTick(() => {
        setDefaultUsers();
    });
});

defineExpose({
    perfType,
    init
});
</script>

<style lang="scss" scoped>
.personperfm_wrap {
    .flex-row-personperfm-wrap {
        display: flex;
        justify-content: space-between;
    }

    .ai_analysis {
        margin-top: 20px;
    }

    .no_select_col {
        width: 100%;
        margin: 20px 0;
        font-size: 16px;
        color: #262626;
        // height: 250px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .overview-header {
        justify-content: space-between;
        margin: 40px 0 20px 0;

    }

}
</style>