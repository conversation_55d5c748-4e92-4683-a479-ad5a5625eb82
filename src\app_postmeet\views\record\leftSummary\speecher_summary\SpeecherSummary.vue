<template>
  <div class="SpeecherSummary">
    <div class="title ">
      <div class="title_left">共{{ ConversationalSummary.length }}个发言人</div>
      <div class="title_right">
        <el-icon @click="onCopy">
          <CopyIcon />
        </el-icon>
        <el-icon @click="onEdit" class="edit-icon" v-if="isHost && !isEdit && enable_edit">
          <EditIcon />
        </el-icon>
        <div class="edit-actions" v-if="isEdit">
          <span class="action-btn" @click="onCancelEdit">取消</span>
          <span class="action-btn confirm" @click="onConfirmEdit">完成编辑</span>
        </div>
      </div>
    </div>
    <ul v-show="ConversationalSummary.length > 0">
      <li v-for="(item, index) in ConversationalSummary" :class="`${currName == item.name ? 'act_qa' : ''}`"
        :key="index">
        <div class="lbox">
          <div class="name"> {{ item.name }}
          </div>
          <div class="back_btn" v-if="recordViewPermission && isShowBtn(item)" @click="onClick(item.name)">
            回顾
          </div>
        </div>
        <div class="rbox">
          <div-edit v-model="item.Summary" @callback="(e) => onSummaryEdit(item, e)" :edit="isEdit"
            :auto-focus="isEdit && index === 0">
          </div-edit>
        </div>
      </li>
    </ul>
    <el-empty v-show="ConversationalSummary.length == 0">
      <template #text class="description">暂无数据</template>
    </el-empty>
  </div>
</template>
<script>
import { md2html } from "@/js/md.js"
import CopyIcon from "@/icons/copy.vue"
import EditIcon from "@/icons/edit.vue"
import DivEdit from "@/app_postmeet/components/div_edit.vue"
export default {
  name: 'SpeecherSummary',
  components: {
    CopyIcon,
    EditIcon,
    DivEdit
  },
  data() {
    return {
      ConversationalSummary: [],
      currName: '',
      recordViewPermission: true,
      isEdit: false,
      enable_edit: false,
      isHost: false,
    }
  },
  mounted() {
    this.ListenerClicktag()
    this.isHost = !g.postmeetStore.isReadonly()
  },
  methods: {
    init() {
      const temp = JSON.parse(localStorage.getItem("clickTag") || "{}");
      if (JSON.stringify(temp) != "{}") {
        const { tag, tagType } = temp
        if (tagType === 'username') {
          this.currName = tag;
        }
      }
      this.ConversationalSummary = g.postmeetStore.data.ConversationalSummary || []
      this.enable_edit = g.postmeetStore.data.enable_edit
    },
    onCopy() {
      const content = this.ConversationalSummary.map(item => {
        return `${item.name}:\n${this._filterLongIds(item.Summary)}\n`;
      }).join('\n');

      g.appStore.doCopy(content, '已复制');
    },
    _filterLongIds(text) {
      const pattern = /[,，]?\b[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}\b[,，]?/g;
      return text.replace(pattern, '');
    },
    tomd(raw) {
      return md2html(this._filterLongIds(raw))
    },
    onClick(name) {
      this.currName = name;
      g.emitter.emit('clickTag', [name, 'username'])
    },
    isShowBtn(item) {
      return this.recordViewPermission && item.Summary.length > 0
    },
    ListenerClicktag() {
      g.emitter.on('clickTag', ([tag, type]) => {
        if (type === 'username') {
          this.currName = tag;
        }
      })
      g.emitter.on('clearTag', () => {
        this.currName = '';
      })
    },
    onEdit() {
      this.isEdit = true
    },
    onCancelEdit() {
      this.isEdit = false
      this.init()
    },
    onConfirmEdit() {
      this.isEdit = false
      g.postmeetStore.data.ConversationalSummary = this.ConversationalSummary
      const data = this.ConversationalSummary.map(item => {
        return {
          ui: item.person.ui,
          Summary: item.Summary
        }
      })
      g.emitter.emit('setMdTypeEditorValue', { type: 'SPEAKER_SUMMARY', md: data });
    },
    onSummaryEdit(item, newValue) {
      item.Summary = newValue
    }
  }
}
</script>

<style lang='scss'>
.SpeecherSummary {
  padding: 0 0 10px 0;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title_left {
      font-size: 14px;
      color: #262626;
    }

    .title_right {
      cursor: pointer;
      font-size: 14px;
      color: #595959;
      display: flex;
      align-items: center;
      gap: 12px;

      .el-icon {
        cursor: pointer;
        font-size: 14px;
        color: #595959;
      }

      .edit-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          cursor: pointer;
          font-size: 14px;
          color: #436BFF;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }

  ul {

    li {
      display: flex;
      flex-direction: row;
      padding: 10px;
      margin: 5px 0;
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #D9D9D9;

      .lbox {
        width: 12%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .name {
          font-size: 14px;
          color: #262626;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding: 0 4px;
        }

        .back_btn {
          font-size: 14px;
          color: #436BFF;
          cursor: pointer;
          visibility: hidden;
          height: 20px;
        }
      }

      .rbox {
        width: 88%;
        color: #27264DD9;
      }
    }

    .act_qa,
    li:hover {
      background: #F1F3FB;

      .lbox {
        .back_btn {
          visibility: visible;
        }
      }
    }
  }
}
</style>