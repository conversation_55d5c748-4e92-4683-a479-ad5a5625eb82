<template>
    <svg width="58px" height="20px" viewBox="0 0 58 20" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <linearGradient x1="50%" y1="-13.4063045%" x2="50%" y2="86.5936955%" id="lg_middle_1">
                <stop stop-color="#FECA52" offset="0%"></stop>
                <stop stop-color="#FCAD56" offset="100%"></stop>
            </linearGradient>
            <radialGradient cx="50%" cy="36.5936955%" fx="50%" fy="36.5936955%" r="50%" id="lg_middle-2">
                <stop stop-color="#FECA52" offset="0%"></stop>
                <stop stop-color="#FCAD56" offset="100%"></stop>
            </radialGradient>
            <radialGradient cx="50%" cy="22.7387209%" fx="50%" fy="22.7387209%" r="50%" id="lg_middle-3">
                <stop stop-color="#FFD151" offset="0%"></stop>
                <stop stop-color="#FCAD56" stop-opacity="0" offset="100%"></stop>
            </radialGradient>
        </defs>
        <g id="沟通过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="分析-概览" transform="translate(-108.000000, -1025.000000)">
                <g id="中性" transform="translate(108.000000, 1025.000000)">
                    <g id="编组-12" transform="translate(15.918968, 0.000000)">
                        <path
                            d="M32.0817967,0.0013181781 C37.5106216,0.0013181781 41.9289756,4.32765826 42.0771908,9.72067038 L42.0810323,10.0006633 C42.0810323,15.5230887 37.604222,19.999899 32.0817967,19.999899 L0.00219802834,19.9998855 C3.37087746,17.9585323 5.62388945,14.2441734 5.62388945,10.0006086 C5.62388945,5.75619662 3.36997778,2.04120182 0.000180318931,0.000109217274 L32.0817967,0.0013181781 Z"
                            id="形状结合" fill="url(#lg_middle_1)"></path>
                        <text id="中性" font-family="PingFangSC-Medium, PingFang SC" font-size="12" font-weight="400"
                            line-spacing="12" fill="#2A2429">
                            <tspan x="9.19607806" y="15.1665764">中性</tspan>
                        </text>
                    </g>
                    <g id="编组-26">
                        <g id="编组-23">
                            <circle id="椭圆形" fill="url(#lg_middle-2)" cx="10" cy="10" r="10"></circle>
                            <circle id="椭圆形" fill="url(#lg_middle-3)" cx="10" cy="10" r="10"></circle>
                        </g>
                        <path
                            d="M6.7768595,9.09090909 C7.41586665,9.09090909 7.9338843,8.57289145 7.9338843,7.9338843 C7.9338843,7.29487715 7.41586665,6.7768595 6.7768595,6.7768595 C6.13785236,6.7768595 5.61983471,7.29487715 5.61983471,7.9338843 C5.61983471,8.57289145 6.13785236,9.09090909 6.7768595,9.09090909 Z M13.2231405,9.09090909 C13.8621476,9.09090909 14.3801653,8.57289145 14.3801653,7.9338843 C14.3801653,7.29487715 13.8621476,6.7768595 13.2231405,6.7768595 C12.5841333,6.7768595 12.0661157,7.29487715 12.0661157,7.9338843 C12.0661157,8.57289145 12.5841333,9.09090909 13.2231405,9.09090909 Z M7.27272727,12.0661157 L12.892562,12.0661157 C13.1664222,12.0661157 13.3884298,12.2881233 13.3884298,12.5619835 C13.3884298,12.8358437 13.1664222,13.0578512 12.892562,13.0578512 L7.27272727,13.0578512 C6.99886707,13.0578512 6.7768595,12.8358437 6.7768595,12.5619835 C6.7768595,12.2881233 6.99886707,12.0661157 7.27272727,12.0661157 Z"
                            id="形状结合" fill="#2A2429"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>
