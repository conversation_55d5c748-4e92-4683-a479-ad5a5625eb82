<template>
  <div class="audio-settings">
    <div class="setting-item">
      <el-checkbox v-model="settings.recordSystemAudio" label="默认录制电脑音频" />
    </div>
    <div class="setting-item">
      <AudioInputSelector v-model="settings.audioInputDevice" />
      <el-button type="primary" @click="toggleChecking" class="btn-check">{{ isChecking ? '停止检测' : '检测麦克风'
        }}</el-button>
    </div>
    <div class="setting-item">
      <div class="audio-levels">
        <div v-for="index in 10" :key="index" :class="{ 'level-active': index <= Math.ceil(currentLevel * 10) }"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import AudioInputSelector from '@/app_electron/components/AudioInputSelector.vue';
import { ElMessage } from 'element-plus';
import SoundMeter from '@/app_electron/tools/SoundMeter';

const settings = ref(g.electronStore.settings.audio);
const isChecking = ref(false);
const currentLevel = ref(0);
let audioContext = null;
let audioStream = null;
let checkTimer = null;
let audio = null;
let soundMeter = null;
let maxLevel = 0;

const stopChecking = async () => {
  if (checkTimer) {
    clearInterval(checkTimer);
    checkTimer = null;
  }

  if (audioStream) {
    audioStream.getTracks().forEach(track => track.stop());
    audioStream = null;
  }

  if (soundMeter) {
    soundMeter.stop();
    soundMeter = null;
  }

  if (audioContext) {
    await audioContext.close();
    audioContext = null;
  }

  if (audio) {
    audio = null;
  }

  isChecking.value = false;
  currentLevel.value = 0;
  maxLevel = 0;
}

const startChecking = async () => {
  try {
    audioStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        deviceId: settings.value.audioInputDevice ? { exact: settings.value.audioInputDevice } : undefined,
      },
      video: false
    });

    audio = document.createElement('audio');
    audio.srcObject = audioStream;
    audio.autoplay = true;

    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    soundMeter = new SoundMeter(audioContext);

    soundMeter.connectToSource(audioStream, (error) => {
      if (error) {
        console.error('Error connecting to audio source:', error);
        ElMessage.error(error.message || '麦克风连接失败');
        stopChecking();
        return;
      }

      checkTimer = setInterval(() => {
        const level = parseInt((soundMeter.instant * 100).toFixed(0));
        maxLevel = Math.max(maxLevel, level);
        currentLevel.value = level / 100;
      }, 100);
    });

    isChecking.value = true;
  } catch (error) {
    console.error('Failed to start audio checking:', error);
    ElMessage.error(error.message || '无法访问麦克风');
    currentLevel.value = 0;
    if (checkTimer) {
      clearInterval(checkTimer);
    }
    isChecking.value = false;
  }
}

const toggleChecking = async () => {
  if (isChecking.value) {
    await stopChecking();
  } else {
    await startChecking();
  }
}

watch(() => settings.value, (newSettings) => {
  g.electronStore.updateSetting('audio', newSettings)
}, { deep: true, immediate: true });

watch(() => settings.value.audioInputDevice, (newVal) => {
  if (isChecking.value) {
    stopChecking();
  }
})

onMounted(async () => {
  const hasPermission = await g.ipcRenderer.invoke('ensurePermissions', 'microphone')
  if (!hasPermission) {
    ElMessage.error('请授予麦克风权限');
  }
})

onUnmounted(() => {
  stopChecking();
});

defineExpose({
  settings,
  isChecking,
  stopChecking,
  toggleChecking,
});
</script>

<style lang="scss" scoped>
.audio-settings {
  .audio-input-device {
    width: 350px;
  }

  .setting-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }

  .btn-check {
    margin-left: 10px;
  }

  .audio-levels {
    display: flex;
    width: 100%;

    div {
      flex: 1;
      height: 10px;
      background-color: #ccc;
      margin: 0 2px;

      &.level-active {
        background-color: #4CAF50;
      }
    }
  }
}
</style>
