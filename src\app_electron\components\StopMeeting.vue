<template>
    <el-button class="ebtn end-btn" @click="stopMeeting" :loading="isEnding">结束</el-button>
</template>

<script setup>
import { MeetStatus } from '@/app_electron/tools/utils';
const emit = defineEmits(['callback'])
const isEnding = ref(false)

const stopMeeting = () => {
    if (!g.meetStore.isNetworkConnected) {
        emit('callback', MeetStatus.no_network);
        return;
    }
    if (isEnding.value) {
        return
    }
    isEnding.value = true
    ElMessageBox.confirm(
        '确定要结束录制吗？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            emit('callback', MeetStatus.stop)
        })
        .catch(() => {
            isEnding.value = false
        })
};

const setIsEnding = () => {
    isEnding.value = false
}

defineExpose({
    stopMeeting,
    setIsEnding
})
</script>

<style scoped lang="scss">
.stop-meeting {
    width: 20px;
    height: 20px;
    cursor: pointer;
}
</style>
