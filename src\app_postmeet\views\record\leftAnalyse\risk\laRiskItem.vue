<template>
  <div class="la_risk_item_wrap flex-column">
    <div v-if="tag">
      <div v-for="item in list" class="item_box">
        <div class="la_header flex-row">
          <div class="la_left flex-row">
            <div :class="`atti_tag tag_${getTagClass(item)}`">
              {{ item["风险等级"] }}
            </div>
            <div class="la_title">
              {{ item["风险描述"] }}
            </div>
          </div>
          <div :class="`la_detail ${recordViewPermission ? 'la_detail_enable' : ''}`" @click="onReview(item)"
            v-if="recordViewPermission">
            回顾
          </div>
        </div>
        <div class="la_md">
          {{ item["风险识别原因"] }}
        </div>
        <div class="la_line"></div>
        <div class="la_md">
          <div>建议措施：</div>
          <ul>
            <li class="tag" v-for="item in item['应对措施']">
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div v-else>
      <el-empty description="无识别风险项"> </el-empty>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      tag: { id: "all", name: "全部", count: 1 },
      list_all: [],
      list: [],
      recordViewPermission: false,
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    init() {
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
      if (ar) {
        const t1 = ar.find((x) => x.systemId == 104);
        this.list_all = t1.report["风险项"] || [];
        this._update_list();
      }
    },
    setTag(tag) {
      this.tag = tag;
      this._update_list();
    },
    _update_list() {
      if (this.tag.id == "all") {
        this.list = this.list_all;
      } else {
        this.list = this.list_all.filter(
          (x) => x["风险等级"] == this.tag.name.replace("风险", "")
        );
      }
    },
    getTagClass: function (item) {
      const status = item["风险等级"];
      const map = {
        高: "high",
        中: "middle",
        低: "low",
      };
      return map[status];
    },
    onReview(item) {
      const label = item["风险等级"] + "风险";
      const row = g.postmeetStore.data.saleAnalyseList["风险项"].find(
        (x) => x.label == label
      );
      const index =
        row.value.map((x) => x["风险描述"]).indexOf(item["风险描述"]) + 1;
      g.emitter.emit("setAnalyseTag", [row, index, "风险项"]);
    },
  },
};
</script>

<style lang="scss">
.la_risk_item_wrap {
  position: relative;

  .item_box {
    background: #f9fafc;
    border-radius: 8px;
    padding: 20px;
    margin: 12px 0;

    .la_header {
      justify-content: space-between;

      .la_left {
        .la_title {
          font-weight: 500;
          font-size: 16px;
          color: #262626;
          line-height: 30px;
        }
      }

      .atti_tag {
        margin: 4px 12px 0 0;
        padding: 2px 8px;
        border-radius: 4px;
        height: 20px;
        font-size: 12px;
        color: #ffffff;
        line-height: 20px;
        text-align: center;
      }

      .tag_high {
        background: #ff111d;
      }

      .tag_middle {
        background: #ff761d;
      }

      .tag_low {
        background: #f6bd16;
      }

      .la_detail {
        display: none;
      }
    }

    .la_line {
      width: 100%;
      height: 1px;
      background: #e9e9e9;
    }

    .la_md {
      margin: 12px 0;
    }

    .la_tags {
      .tag {
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        height: 24px;
        font-size: 14px;
        color: #262626;
        line-height: 24px;
        margin-right: 12px;
        padding: 5px 12px;
        cursor: pointer;
      }

      .tag:hover {
        border: 1px solid #436bff;
        color: #436bff;
      }
    }
  }

  .item_box:hover {
    background: #f1f3fb;

    .la_header {
      .la_detail {
        display: block;
        height: 22px;
        font-size: 14px;
        color: #436bff;
        line-height: 22px;
      }

      .la_detail_enable {
        cursor: pointer;
      }
    }
  }
}
</style>
