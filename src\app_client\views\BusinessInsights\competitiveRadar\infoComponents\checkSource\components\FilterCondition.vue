<template>
    <div class="filter_condition_wrap flex-col">
        <div class="fch_filter flex-row">
            <div class="filter_item">
                <el-select v-model="attitudeType" placeholder="全部态度" class="filter_select" clearable>
                    <el-option v-for="item in emotionList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </div>
            <div class="filter_item">
                <el-select v-model="customerIds" placeholder="全部客户" class="filter_customer" collapse-tags multiple
                    clearable filterable remote :remote-method="remoteMethod" :reserve-keyword="false">
                    <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName"
                        :value="item.customerId" />
                </el-select>
            </div>
            <div class="filter_item">
                <el-button type @click="handleSearch" class="filter_btn">确定</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
const emotionList = [
    {
        value: '',
        label: '全部态度'
    }, {
        value: '0',
        label: '积极态度'
    }, {
        value: '2',
        label: '消极态度'
    }, {
        value: '1',
        label: '中性态度'
    }
]

// 0:积极 1:中性 2:消极
const dimensionId = ref('');
const attitudeType = ref('');
const customerIds = ref('');

const customerList = computed(() => g.clientBiStore.crCustomerList || [])

const remoteMethod = async (query) => {
    await g.clientBiStore.getCrCustomerList(query)
}

const handleSearch = () => {
    g.clientBiStore.setCrFilterCondition({
        attitudeType: attitudeType.value,
        customerIds: customerIds.value,
    })
    g.clientBiStore.updateCrFbVersion()
}
</script>
