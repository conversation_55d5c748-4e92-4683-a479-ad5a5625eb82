<template>
  <div class="communication-analysis-page">
    <el-tabs v-model="activeTab" class="analysis-tabs">
      <el-tab-pane label="维度" name="dimension">
        <AnalysisDimensionList ref="dimensionRef" />
      </el-tab-pane>
      <el-tab-pane label="模板" name="template">
        <AnalysisTemplateTable ref="templateRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import AnalysisDimensionList from './components/AnalysisDimensionList.vue';
import AnalysisTemplateTable from './components/AnalysisTemplateTable.vue';

const activeTab = ref('dimension');
const dimensionRef = ref();
const templateRef = ref();

</script>

<style lang="scss" scoped>
.communication-analysis-page {
  padding: 20px;
}
</style>