<template>
  <div v-show="hasHoldingPlan">
    <planInfoTitle title="进行中" img='e_plan_doing.png' />
    <div class="holding-plan-container custom-scrollbar flex-row">
      <div class="hp_left flex-col">
        <div class="plan-header">
          <div class="plan-title-wrapper">
            <span class="plan-title" :title="item.subject">{{ item.subject }}</span>
          </div>
        </div>
        <div class="plan-info">
          <div class="time-wrapper">
            <span v-if="!item.scheduleId">开始时间：</span>
            <span>{{ item.startDt }}</span>
          </div>
          <div class="line" v-if="!isRecording"></div>
          <span class="customer-name" v-if="!isRecording">{{
            item.durationShow
          }}</span>
          <div class="line" v-if="item.customerName"></div>
          <span class="customer-name" :title="item.customerName" v-if="item.customerName">{{
            item.customerName
          }}</span>
        </div>
        <div class="plan-status">
          <span :class="`status ${item.status}`">{{ getLang(item.status) }}</span>
        </div>
      </div>
      <div class="plan-actions flex-row" v-if="item.recordType == 'DEVICE'">
        <el-button type="primary" class="start_button" @click="startCardPlan" size="small">
          {{ isRecording ? '进入' : '继续' }}
        </el-button>
      </div>
      <div class="plan-actions flex-row" v-if="item.recordType == 'APP'">
        <el-button :type="localConferenceId ? 'primary' : 'info'" @click="tryNormalMeeting" size="small">
          {{ isRecording ? '进入' : '继续' }}
        </el-button>
      </div>
    </div>
  </div>
  <DiaConfirm1 v-model="isShowDia1" title="录制进行中" hint="此录制正在另一设备上进行，请在原设备完成录制后再进行其他操作。" />
</template>

<script setup>
import getLang from "@/js/lang.js";
import { getSecondsShowTime } from "@/js/utils.js";
import { getOngoingConference } from "@/js/api.js";
import planInfoTitle from "./planInfoTitle.vue"
import DiaConfirm1 from '@/app_electron/components/DiaConfirm1.vue';

let autoRefreshTimer = null;
const loading = ref(false);
const isRecording = ref(false);
const emit = defineEmits(['callback']);
const localConferenceId = ref(false);
const isShowDia1 = ref(false)
// recordType 记录类型,可用值:APP,DEVICE	string	
// recordStatus	录制状态,可用值:RECORDING,PAUSED
const statusLangMap = {
  RECORDING: 'ongoing',
  PAUSED: 'hasPaused',
}

const item = ref({
  // "conferenceId": "1925145287114989568",
  // "duration": 145861,
  // "recordStatus": "RECORDING",
  // "recordType": "DEVICE",
  // "startTime": "2025-05-21 19:02:59",
  // "subject": "wumx_xmate01的即时录制"
})
const hasHoldingPlan = ref(false);


const getLocalMeeting = () => {
  const meetingInfo = g.appStore.getStore(g.cv.keyMeetingInfo, {})
  localConferenceId.value = meetingInfo && item.value.conferenceId == meetingInfo.conferenceId;
  g.meetStore.isLocalMeeting = localConferenceId.value;
}


const tryNormalMeeting = () => {
  if (localConferenceId.value) {
    g.appStore.setStore(g.cv.keyMeetTimer, item.value.duration || 0)
    g.electronStore.openWin("meet", { fromStore: true });
    g.electronStore.sendMessage('meet', 'resume_recording', {})
  } else {
    isShowDia1.value = true;
  }
}

const startCardPlan = () => {
  g.meetStore.openCardMeetWindow(item.value.scheduleId, 'true');
}

const reload = () => {
  if (loading.value) return;
  loading.value = true;
  hasHoldingPlan.value = false;
  g.meetStore.setOngoingConference({});
  getOngoingConference().then((res) => {
    console.log('getOngoingConference res', res);
    g.electronStore.setIsNetworkConnected(true);
    g.meetStore.isLocalMeeting = false;
    if (res.code == 0 && res.data?.startTime) {
      const info = res.data;
      info["startDt"] = info.startTime.substring(11, 16);
      info["durationShow"] = getSecondsShowTime(info.duration);
      info['isJishi'] = info?.subject.indexOf('即时') > -1 || true;
      info['status'] = statusLangMap[info.recordStatus];
      isRecording.value = info.recordStatus == "RECORDING";
      g.electronStore.sendMessage("meet", "update_record_duration", info.duration);
      item.value = info;
      getLocalMeeting()
      hasHoldingPlan.value = true;
      g.meetStore.setOngoingConference(info);
      emit('callback', info);
    } else {
      isRecording.value = false;
      hasHoldingPlan.value = false;
      g.meetStore.setOngoingConference({});
      g.appStore.removeStore(g.cv.keyMeetingInfo)
      emit('callback', { duration: -1 });
    }
  }).catch((err) => {
    if (err.code == 'ERR_NETWORK') {
      g.electronStore.setIsNetworkConnected(false);
    }
    console.error('getOngoingConference error', err);
  }).finally(() => {
    loading.value = false;
    if (autoRefreshTimer) clearInterval(autoRefreshTimer);
    autoRefreshTimer = setTimeout(() => {
      reload();
    }, 40 * 1000);
  });
}

defineExpose({
  reload
});
</script>

<style lang="scss" scoped>
.holding-plan-container {
  padding: 0 10px 12px 10px;
  background: #ffffff;
  overflow-x: hidden;

  .hp_left {
    .plan-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .plan-title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 280px;
        overflow: hidden;
      }

      .plan-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        max-width: 240px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
      }

      .visit-tag {
        padding: 0 4px;
        height: 18px;
        line-height: 18px;
        background: #FFF7E6;
        border-radius: 2px;
        font-size: 12px;
        color: #FF7300;
        font-weight: normal;
        flex-shrink: 0;
      }
    }

    .plan-info {
      display: flex;
      font-size: 12px;
      color: #8c8c8c;
      padding: 8px 0;

      .time-wrapper {
        position: relative;
        display: inline-flex;
        align-items: flex-start;

        .plus-one {
          position: absolute;
          top: -3px;
          right: -16px;
          font-size: 10px;
          color: #ff5219;
          font-weight: 500;
        }
      }

      .have_plus {
        margin-right: 12px;
      }

      .line {
        width: 1px;
        height: 12px;
        background-color: #bfbfbf;
        margin: 3px 8px;
      }

      .customer-name {
        font-size: 12px;
        color: #8c8c8c;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 108px;
      }
    }

    .plan-status {
      .status {
        padding: 2px 0;
        font-size: 12px;
        border-radius: 4px;
      }

      .ongoing {
        color: #26d08e;
      }

      .hasPaused {
        color: #ff5219;
      }
    }

  }

  .plan-actions {
    margin-left: auto;
    display: flex;

    .el-button {
      width: 58px;
      height: 30px;
      border-radius: 5px;
      font-size: 14px;
      font-weight: 500;
    }

    .el-button--primary {
      color: #ffffff;
      background: #436BFF;

      &:hover {
        background-color: #6B90FF;
      }

      &:active {
        background-color: #2E4DD9;
      }
    }
  }


}
</style>
