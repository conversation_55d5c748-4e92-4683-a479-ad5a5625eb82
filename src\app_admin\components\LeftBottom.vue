<template>
    <div class="left_bottom_wrap" @click="onHelp">
        <HelpIcon />
        <div class="help_txt">
            帮助中心
        </div>
    </div>
</template>

<script setup>
import HelpIcon from "@/app_admin/icons/help.vue"

const onHelp = () => {
    // const url = `${g.config.premeet}/xmate-doc/index.html`;
}

defineExpose({ HelpIcon })

</script>

<style lang="scss">
.left_bottom_wrap {
    position: absolute;
    bottom: 0;
    height: 54px;
    border-top: 1px solid #e9e9e9;
    padding-left: 16px;
    width: calc(100% - 16px);
    display: flex;
    align-items: center;
    font-size: 16px;
    background-color: #fff;

    .help_txt {
        width: 56px;
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 22px;
        margin-left: 5px;
        cursor: pointer;
    }
}

.left_bottom_wrap:hover {

    svg,
    .help_txt {
        color: #436BFF;
    }
}
</style>