<template>
  <div class="risk_wrap">
    <div class="risk_header flex-row">
      <div class="rh_title">风险识别配置</div>

      <div class="rh_right">
        <template v-if="isEdit">
          <el-button type="default" @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onComfirm">完成</el-button>
        </template>
        <el-button type="primary" @click="onEdit" v-else>编辑</el-button>
      </div>
    </div>
    <table class="risk_table">
      <thead>
        <tr>
          <td>风险名称</td>
          <td>风险识别条件</td>
          <td>操作</td>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>客户参会人不足</td>
          <td>
            客户参会人小于
            <el-input-number v-model="setting.companyContactCount" :min="1" :max="99" controls-position="right"
              :disabled="!isEdit" />
            个
          </td>
          <td>
            <btnStatus ik="enableCompanyContactCount" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
        <tr>
          <td>客户沉默风险</td>
          <td>
            距离上一次拜访的时间间隔大于
            <el-input-number v-model="setting.companyVisitInterval" :min="1" :max="99" controls-position="right"
              :disabled="!isEdit" />
            天
          </td>
          <td>
            <btnStatus ik="enableCompanyVisitInterval" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
        <tr>
          <td>销售能力评估持续不达标</td>
          <td>
            销售能力评估得分连续
            <el-input-number v-model="setting.abilityCheckThreshold" :min="1" :max="9" controls-position="right"
              :disabled="!isEdit" />
            次低于 {{ setting.taskRate4ass }} 分
          </td>
          <td>
            <btnStatus ik="enableAbilityCheck" :data="setting" @callback="cbStatus" />
          </td>
        </tr>

        <tr>
          <td>销售任务达成持续不达标</td>
          <td>
            销售任务达成率连续
            <el-input-number v-model="setting.taskCheckThreshold" :min="1" :max="9" controls-position="right"
              :disabled="!isEdit" />
            次低于 {{ setting.taskRate4ass }} 分
          </td>
          <td>
            <btnStatus ik="enableTaskCheck" :data="setting" @callback="cbStatus" />
          </td>
        </tr>

        <tr>
          <td>客户推进受阻</td>
          <td>
            拜访目标连续
            <el-input-number v-model="setting.customerSuccessCheckThreshold" :min="1" :max="9" controls-position="right"
              :disabled="!isEdit" />
            次未达成
          </td>
          <td>
            <btnStatus ik="enableCustomerSuccessCheck" :data="setting" @callback="cbStatus" />
          </td>
        </tr>

        <tr>
          <td>客户态度持续消极</td>
          <td>
            客户态度连续
            <el-input-number v-model="setting.negativeFeedbackThreshold" :min="1" :max="9" controls-position="right"
              :disabled="!isEdit" />
            次评分消极
          </td>
          <td>
            <btnStatus ik="enableNegativeFeedbackThreshold" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
        <tr>
          <td>竞争对手提及次数过高</td>
          <td>
            客户在上一次拜访中提及竞争对手次数大于
            <el-input-number v-model="setting.competitorMentionsThreshold" :min="1" :max="99" controls-position="right"
              :disabled="!isEdit" />
            次
          </td>
          <td>
            <btnStatus ik="enableCompetitorMentionsThreshold" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
        <tr>
          <td>频繁提及同一竞争对手</td>
          <td>
            在最近
            <el-input-number v-model="setting.sameCompetitorMeetingsThreshold" :min="1" :max="9"
              controls-position="right" :disabled="!isEdit" />
            次拜访中，提及同一竞争对手的拜访次数超过
            <el-input-number v-model="setting.sameCompetitorMentionsThreshold" :min="1" :max="99"
              controls-position="right" :disabled="!isEdit" />
            次
          </td>
          <td>
            <btnStatus ik="enableSameComptMentionsThreshold" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
        <tr>
          <td>拜访过程高危风险较多</td>
          <td>
            上一次拜访中高危风险项大于
            <el-input-number v-model="setting.highRiskThreshold" :min="1" :max="99" controls-position="right"
              :disabled="!isEdit" />
            项
          </td>
          <td>
            <btnStatus ik="enableHighRiskThreshold" :data="setting" @callback="cbStatus" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { getRiskSetting, updateRiskSetting } from "@/app_admin/tools/api.js";
import { onMounted } from "vue";
import btnStatus from "./btnStatus.vue";

// https://alidocs.dingtalk.com/i/nodes/vy20BglGWOeO7PEyc5aREBXrJA7depqY
const setting = ref({
  companyContactCount: 3, //联系人数量小于?个

  companyVisitInterval: 14, //距离上一次拜访的时间间隔
  enableCompanyVisitInterval: true, //是否启用  距离上一次拜访的时间间隔

  competitorMentionsThreshold: 3, //客户在上一次拜访中提及竞争对手次数 大于?次
  consecutiveThreshold: 2, //销售表现得分连续?次
  highRiskThreshold: 4, //上一次拜访中高危风险项大于?项
  negativeBusinessUpdatesCount: 1, //企业经营动态新增?条消极动态

  negativeFeedbackThreshold: 2, //客户态度连续？次评分消极
  enableNegativeFeedbackThreshold: true, //是否启用 客户态度连续？次评分消极'

  sameCompetitorMentionsThreshold: 2, //客户连续?次拜访中提及同一竞争对手
  enableCompetitorMentionsThreshold: true, //是否启用 客户在上一次拜访中提及竞争对手次数 大于?次

  scoreLimit: 60, //销售表现得分连续多次小于？次
  decisionContactThreshold: 1, //决策人（包含最终决策与建议决策）数量小于 x 个
  enableDecisionContactThreshold: true, // 是否启用 决策人（包含最终决策与建议决策）数量小于 x 个
  enableCompanyContactCount: true, //是否启用  联系人数量小于?个

  enableConsecutiveThreshold: true, //是否启用  销售表现得分连续?次
  enableSameComptMentionsThreshold: true, //是否启用
  enableHighRiskThreshold: true, //是否启用  上一次拜访中高危风险项大于?项

  salesCapabilityThreshold: 2,
  enableSalesCapabilityThreshold: true,

  salesTaskThreshold: 2,
  enableSalesTaskThreshold: true,

  visitGoalThreshold: 2,
  enableVisitGoalThreshold: true,

  taskCheckThreshold: 2,
  enableTaskCheck: true,

  customerSuccessCheckThreshold: 2,
  enableCustomerSuccessCheck: true,

  abilityCheckThreshold: 2,
  enableAbilityCheck: true,
});
const isEdit = ref(false);

const onEdit = () => {
  isEdit.value = true;
};
const onCancel = () => {
  isEdit.value = false;
};
const onComfirm = () => {
  const param = toRaw(setting.value);
  updateRiskSetting(param).then((resp) => {
    if (resp.code == 0) {
      ElMessage({
        type: "success",
        message: "已更新",
      });
      isEdit.value = false;
    } else {
      ElMessage({
        type: "error",
        message: "操作失败",
      });
    }
  });
};

const cbStatus = (ik, target) => {
  setting.value[ik] = target;
};

onMounted(() => {
  getRiskSetting().then((resp) => {
    if (resp.code == 0) {
      setting.value = resp.data;
    }
  });
});

defineExpose({
  setting,
  isEdit,
  onComfirm,
  onCancel,
  onEdit,
  btnStatus,
});
</script>

<style lang="scss">
.risk_wrap {
  height: calc(100vh - 70px);
  overflow-y: auto;
  padding: 0 24px;

  .risk_header {
    padding: 24px;
    justify-content: space-between;
    height: 24px;
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 24px;
  }

  .risk_table {
    width: 100%;

    tr {
      height: 50px;
      box-shadow: inset 0px -1px 0px 0px #e9e9e9;
      border-radius: 4px 4px 0px 0px;

      td {
        padding: 0 24px;
        font-size: 14px;
        color: #262626;

        .el-input-number {
          width: 100px;
        }
      }
    }

    thead tr {
      background: #f9fafc;
    }

    tbody tr {
      background: #fff;
    }
  }
}
</style>
