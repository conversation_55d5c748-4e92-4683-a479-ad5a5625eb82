import { addSeqTxt, formateTime } from "@/app_postmeet/tools/tools";

export const get1Fdzy = () => {
  const { topicLabResult } = g.postmeetStore.data;
  let temp = "";
  if (topicLabResult && topicLabResult.length > 0) {
    function _func(x) {
      if (x.summary) {
        const summary = x.summary.replace(/\n\n/g, " ");
        return x.value + "\n    " + summary + "\n";
      } else {
        return x.value;
      }
    }
    temp = "【分段摘要】\n";
    temp += addSeqTxt(topicLabResult.map((x) => _func(x)));
    temp += "\n";
  }
  return temp;
};

export const get2Wdhg = () => {
  const list = g.postmeetStore.data.QuestionsAnsweringSummary || [];
  let temp = "";
  if (list && list.length > 0) {
    const _func_qa = (x) => {
      let a = "";
      a += "问:" + x.Question + "\n";
      a += "答:" + x.Answer + "\n";
      return a;
    };

    temp = "【问答回顾】\n";
    temp += addSeqTxt(list.map((x) => _func_qa(x)));
    temp += "\n";
  }
  return temp;
};

export const get3Fyrzj = () => {
  const list = g.postmeetStore.data.ConversationalSummary || [];
  let temp = "";
  if (list && list.length > 0) {
    const _func_sp = (x) => {
      return x.name + ": " + x.Summary + "\n";
    };
    temp = "【发言人总结】\n";
    temp += addSeqTxt(list.map((x) => _func_sp(x)));
    temp += "\n";
  }
  return temp;
};

export const get4Hyjy = () => {
  const { recordInfo, keySentLResult, actionLabResult, ParagraphSummary } =
    g.postmeetStore.data;
  let txt = ["【沟通记要】"];
  if (ParagraphSummary.length > 0) {
    let temp = "【全文概要】\n";
    temp += ParagraphSummary;
    temp += "\n";
    txt.push(temp);
  }
  if (recordInfo.agendaRich) {
    let temp = "【议程】\n";
    const noHtmlTag = recordInfo.agendaRich.replace(/(<([^>]+)>)/gi, "");
    temp += noHtmlTag;
    temp += "\n";
    txt.push(temp);
  }
  if (keySentLResult && keySentLResult.length > 0) {
    let temp = "【关键句】\n";
    temp += addSeqTxt(keySentLResult.map((x) => x.value));
    temp += "\n";
    txt.push(temp);
  }
  let todos = actionLabResult;
  if (todos && todos.length > 0) {
    todos = todos.map((x) => x.value);
    let temp = "【待办事项】\n";
    temp += addSeqTxt(todos);
    temp += "\n";
    txt.push(temp);
  }
  return txt;
};

export const get4HySummary = () => {
  const data = g.postmeetStore.data.saleReport.analysisReports.find(
    (x) => x.systemId == 8
  );
  let txt = [];
  let text = "";
  let name = "";
  const { methodology, content } = g.postmeetStore.data.SalesMethodology;
  if (methodology && content) {
    text = content;
    name = methodology;
  } else {
    text = data.report;
    name = data.name;
  }
  if (text && name) {
    let temp = [`【${name}】\n`];
    temp += text;
    temp += "\n";
    txt.push(temp);
  }
  return txt;
};

export const get5Dkjy = () => {
  const { challenge, plan, suggestion } = g.postmeetStore.data.salesMateSummary;
  let txt = ["【对客纪要】"];
  if (challenge.length > 0) {
    let temp = "【培训诉求】\n";
    temp += addSeqTxt(challenge);
    temp += "\n";
    txt.push(temp);
  }
  if (suggestion) {
    let temp = "【建议和方案】\n";
    temp += addSeqTxt(suggestion);
    temp += "\n";
    txt.push(temp);
  }
  if (plan && plan.length > 0) {
    let temp = "【待办】\n";
    temp += addSeqTxt(plan);
    temp += "\n";
    txt.push(temp);
  }
  return txt;
};

export const sale_export = (that) => {
  let txt = [];
  for (let i = 0; i < that.saleDiyReports.length; i++) {
    const item = that.saleDiyReports[i];
    if (that.cks[item.id]) {
      let temp = "";
      if (item.systemId == 8) {
        // 商机识别
        let txt_ = get4HySummary();
        txt = [...txt, ...txt_];
      } else if (item.systemId == 1) {
        // 分段摘要
        txt.push(get1Fdzy());
      } else if (item.systemId == 2) {
        // 问答回顾
        txt.push(get2Wdhg());
      } else if (item.systemId == 3) {
        // 发言总结
        txt.push(get3Fyrzj());
      } else if (item.systemId == 5) {
        // 对客纪要
        let txt_ = get5Dkjy();
        txt = [...txt, ...txt_];
      } else {
        temp = `【${item.name}】\n`;
        temp += item.report;
        txt.push(temp);
      }
    }
  }
  return txt;
};

export const normal_export = (that) => {
  const { recordInfo, keywords, names } = g.postmeetStore.data;

  const playItem = g.postmeetStore.getCurrRecord();
  let txt = [];
  if (recordInfo) {
    txt.push(`${recordInfo.subject}\n`);
    txt.push(
      `${recordInfo.startTime}  |  ${formateTime(
        playItem.duration / 1000
      )}  |  ${playItem.hostName}\n`
    );
  }
  if (keywords) {
    let temp = "【关键字】\n";
    temp += keywords.join(" ");
    temp += "\n";
    txt.push(temp);
  }

  if (names) {
    let temp = "【发言人】\n";
    temp += names.join(" ");
    temp += "\n";
    txt.push(temp);
  }
  if (that.ck_normal_meet) {
    let txt_ = get4Hyjy();
    txt = [...txt, ...txt_];
  }

  if (that.ck_segmented_summary) {
    txt.push(get1Fdzy());
  }

  if (that.ck_speecher_summary) {
    txt.push(get3Fyrzj());
  }

  if (that.ck_key_points) {
    txt.push(get2Wdhg());
  }

  return txt;
};
