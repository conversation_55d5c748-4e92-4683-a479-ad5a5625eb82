import os from 'os'
import { ipcMain } from "electron"
import { checkUpdateApi } from "../utils/api"
import { errorWriteLocal } from "../utils/errorWriteLocal"
import { autoUpdater } from "electron-updater";
import windowManager from "./windowManager";
import { createSubWindows } from './listenSubWindow'
import config from '../utils/config'
import { getUpgradeBaseUrl } from '../utils/tools'
let _updateInfo = null;

export const listenUpdate = () => {
    ipcMain.handle("update-check", async () => {
        console.log('update-check')

        if (!config.isPackaged) {
            return {
                "code": 0,
                "data": {
                    "comment": "",
                    "forceUpdate": false,
                    "needUpdate": false,
                    "newVersion": "0"
                },
                "message": "开发环境下不需要更新"
            }
        }
        let res = await checkUpdateApi('gray')
        errorWriteLocal("check-update gray res", config.version, res)

        if (res.code == 0 && res.data.needUpdate) {
            _updateInfo = res.data
            _updateInfo['isGray'] = true;
            return res;
        } else {
            res = await checkUpdateApi('formal')
            errorWriteLocal("check-update formal res", config.version, res)
            if (res.code == 0 && res.data.needUpdate) {
                _updateInfo = res.data
                _updateInfo['isGray'] = false;
            }
            return res;
        }
    })

    ipcMain.on("update-download", () => {
        errorWriteLocal("update-download")
        beginDownloadFile()
    })

    ipcMain.on("update-install", () => {
        errorWriteLocal("update-install")
        autoUpdater.quitAndInstall()
    })
}

export function beginDownloadFile() {
    errorWriteLocal("beginDownloadFile", getUpgradeUrl())

    try {
        // 配置更新源
        autoUpdater.setFeedURL({
            provider: 'generic',
            url: getUpgradeUrl()
        })

        // 检查并下载更新
        autoUpdater.checkForUpdates().then(({ updateInfo, cancellationToken, versionInfo }) => {
            errorWriteLocal("Update check started", updateInfo)
        }).catch(err => {
            errorWriteLocal("Update check failed", err)
            sendUpgradeStatus('error', 0)
        });
    } catch (err) {
        errorWriteLocal("beginDownloadFile error", err)
        sendUpgradeStatus('error', 0)
    }
}

export const sendUpgradeStatus = (status, percent) => {
    const version = _updateInfo?.newVersion || '0';
    const data = { status, percent, version }
    errorWriteLocal("sendUpgradeStatus", data)
    windowManager.sendMessage({ to: 'download', message: { action: 'update_status', data } });
}

const bindUpdaterListener = () => {
    autoUpdater.on('checking-for-update', () => {
        errorWriteLocal('checking for update')
    })
    autoUpdater.on('update-available', () => {
        errorWriteLocal('update available')
    })
    autoUpdater.on('update-not-available', () => {
        errorWriteLocal('update not available');
    })
    autoUpdater.on('error', (err) => {
        errorWriteLocal('update error4', err)
        sendUpgradeStatus('error', 0)
    })
    autoUpdater.on('download-progress', (data) => {
        sendUpgradeStatus('downloading', data.percent)
    })

    autoUpdater.on('update-downloaded', () => {
        errorWriteLocal('update-downloaded')
        sendUpgradeStatus('downloaded', 100)
        const arg = {
            urlParam: {
                id: 'download',
                url: '/electron/download',
            },
            newPageData: _updateInfo
        }
        createSubWindows(JSON.stringify(arg))
    })
}

// 获取升级包地址
const getUpgradeUrl = function () {
    const platform = os.platform() === 'win32' ? 'windows' : 'mac';
    const gray = _updateInfo.isGray ? 'gray/' : '';
    const url = `${getUpgradeBaseUrl()}/${gray}${platform}`;
    errorWriteLocal("getUpgradeUrl", url)
    return url;
}
bindUpdaterListener()