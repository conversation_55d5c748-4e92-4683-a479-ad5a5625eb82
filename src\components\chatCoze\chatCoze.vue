<template>
    <div class="recommend_wrap">
        <LeftMenu ref="refMenu" @callback="cbMenu" />
        <LeftMin ref="refMin" @callback="cbMin" />
        <RightChat ref="refChat" @callback="cbRight">
            <template #input_top>
                <slot name="input_top" />
            </template>
            <template #input_inside_top>
                <slot name="input_inside_top" />
            </template>
        </RightChat>
    </div>
</template>

<script setup>
import LeftMenu from "./LeftMenu.vue";
import RightChat from "./rightChat.vue";
import LeftMin from "./leftMin.vue"
const emit = defineEmits(['callback'])

const refMenu = ref()
const refMin = ref()
const refChat = ref()


const cbMenu = (action, data) => {
    switch (action) {
        case 'packup':
            refMin.value.show()
            refChat.value.setFull(true)
            break;
        case 'new':
        case 'click':
            refChat.value.setConversation(data)
            break;
        default:
            break
    }

}

const cbMin = (action) => {
    refChat.value.setFull(false)
    refMenu.value.show()
}

const cbRight = (action, data) => {
    if (action == 'update_conv') {
        refMenu.value.updateConv(data)
    } else {
        emit('callback', action, data)
    }
}

const addMsg = (msg) => {
    refChat.value.addMsg(msg)
}

const getMsgs = () => {
    refChat.value.getMsgs()
}

const setMsgs = (msgs) => {
    refChat.value.setMsgs(msgs)
}

const init = (config) => {
    if (config.title) {
        window.document.title = config.title;
        refMenu.value.init(config)
        refChat.value.init(config)
    } else {
        g.emitter.emit('show_msg_box', { message: '无权限访问此应用', time: 3, need_icon: true })
    }
}

const updateConfig = (key, value) => {
    refMenu.value.updateConfig(key, value)
    refChat.value.updateConfig(key, value)
}

defineExpose({
    init, LeftMenu, LeftMin, addMsg, updateConfig,
    RightChat, cbMin, cbMenu, getMsgs, setMsgs,
    refMenu, refMin, refChat
})
</script>

<style lang="scss">
.recommend_wrap {
    display: flex;
    flex-direction: row;

    .bg_img {
        width: 361px;
        height: 129px;
        position: fixed;
        left: 0;
        top: 0;
    }

    .msg_a {
        .mbody {
            padding-top: 12px;
        }
    }
}
</style>
