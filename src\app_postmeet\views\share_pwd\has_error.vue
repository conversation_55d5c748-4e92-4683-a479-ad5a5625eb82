<template>
    <div class="has_error_wrap">
        <div class="pic">
            <Pic401 />
        </div>
        <span class="txt">{{ errorMsg }}</span>
    </div>
</template>
<script setup>
import Pic401 from "@/app_postmeet/icons/401.vue"
const errorMsg = ref('录制文件正在生成中...')

const setErrorMsg = (msg) => {
    errorMsg.value = msg
}

defineExpose({
    Pic401,
    errorMsg,
    setErrorMsg
})
</script>


<style lang="scss" scoped>
.has_error_wrap {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .txt {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 28px;
    }
}
</style>