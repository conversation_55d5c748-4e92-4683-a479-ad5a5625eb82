import { getHttp } from "@/js/request.js";
import uploadFile from "@/js/upload_file";

const _http = getHttp();

// xmate 埋点
export const saveXmatePoint = (data) =>
  _http.post(`api/user/landing/history`, data);

// 取得商品分类：
export const getGoodsCategory = () => _http.get(`api/goods/category`);

// 取得有上架商品的分类信息
export const getGoodsCategoryOnshelf = () =>
  _http.get(`api/goods/category/onshelf`);

// 获取商品分类下的引导语和问题列表
export const getCateoryPromot = (categoryId) =>
  _http.get(`api/goods/category/${categoryId}`);

// 获取商品分类下已上架的清单数
export const getCateoryCount = (categoryId) =>
  _http.get(`api/goods/category/${categoryId}/count`);

// 获得根据用户输入得到的探寻需求的问题
export const getQuestionSecond = (categoryId) =>
  _http.get(`api/goods/category/${categoryId}/question/second`);

// 引导用户继续输入：基于用户的输入内容,获得让用户继续输入的提示语
export const chatPrompt = (chatId, data) =>
  _http.post(`api/goods/chat/${chatId}/prompt`, data);

// 商品推荐
export const chatRecommend = (chatId, data) =>
  _http.post(`api/goods/chat/${chatId}/recommend`, data);

// 生成需求探寻的问题：基于用户的输入内容,推荐商品
export const chatSeek = (chatId, data) =>
  _http.post(`api/goods/chat/${chatId}/seek`, data);

// 普通对话，生成对应的回复
export const chatAnswer = (chatId, data) =>
  _http.post(`api/goods/chat/${chatId}/answer`, data);

// 生成报价单
export const chatQuote = (chatId, data) =>
  _http.post(`api/goods/chat/${chatId}/quote`, data);

// 查找我的对客拜访记要
export const getMeetingHistory = (data) =>
  _http.post(`api/conference/salesmate/records`, data);

// 提取拜访的客户需求
export const getCustomerRequirement = (conferenceId) =>
  _http.get(`api/goods/chat/report/${conferenceId}/customer/requirement`);

// 保存客户标签
export const saveCustomerTags = (customerId, data) =>
  _http.put(`api/customer/${customerId}/tags`, data);

// 获取我的客户的标签
export const getCustomerTags = (data) =>
  _http.post(`api/customer/mycustomer/tags`, data);

// 列出拜访的风险项数据
export const getConfAnalyseRisks = (confId) =>
  _http.get(`api/conference/${confId}/analysis/risks`);

// 上传Video
export const uploadVideo = (formData, onProgress, onSuccess, onFail) => {
  const url = `${g.config.meetApiHost}/rest/api/conference/upload/record`;
  return uploadFile(url, formData, onProgress, onSuccess, onFail);
};

// 列出所有ppt类别
export const getPptCategorys = (data) =>
  _http.post(`api/ppt/category/list`, data);

// 删除拜访
export const deleteRecordByConfId = (conferenceId) =>
  _http.delete(`api/customer/meetings/${conferenceId} `);

// 获得拜访纪要的分享设置
export const getShareConfig = (confId) =>
  _http.get(`api/conference/${confId}/share`);

// 列出CRM同步状态
// export const getCrmStatus = () => _http.get(`api/crm/sync/status`)

export const exportCustomerMeets = (isteam, data) => {
  return isteam
    ? _exportMyTeamCustomerMeets(data)
    : _exportMyCustomerMeets(data);
};

// 导出我的团队拜访记录
export const exportMyTeamPlans = (data) =>
  _http.download(`api/xmate/team/plans/export`, data);

// 列出客户看板数据
export const getKanbanCustomers = (isteam, data) => {
  const url = isteam
    ? `api/customer/myteam/customer/list`
    : `api/customer/mycustomer/list`;
  return _http.post(url, data);
};

// 列出我的团队拜访记录
export const getCustomerMeets = (isteam, data) => {
  const url = isteam ? `api/customer/myteam/meetings` : `api/customer/meetings`;
  return _http.post(url, data);
};

// 导出我的拜访记录
export const _exportMyCustomerMeets = (data) =>
  _http.download(`api/customer/meetings/export`, data);

// 导出我的团队拜访记录
export const _exportMyTeamCustomerMeets = (data) =>
  _http.download(`api/customer/myteam/meetings/export`, data);

export const exportCustomerList = (isteam, data) => {
  return isteam ? _exportMyTeamCustomerList(data) : _exportMyCustomerList(data);
};

// 导出我的客户看板数据：
export const _exportMyCustomerList = (data) =>
  _http.download(`api/customer/mycustomer/list/export`, data);

// 导出我的团队客户看板数据：
export const _exportMyTeamCustomerList = (data) =>
  _http.download(`api/customer/myteam/customer/list/export`, data);

// 获取道口金融authkey
export const getDaokouAuthKey = () => _http.get(`api/daokou/jinke/auth/key`);

// ---------------------------剪辑片段 -----------------------------

// 取得剪辑片段详情
export const getClipLibDetail = (id) => _http.get(`api/clip/lib/${id}`);

// 修改剪辑片段分类
export const updateClipLibCategory = (id, data) =>
  _http.put(`api/clip/lib/${id}`, data);

// 删除剪辑片段分类
export const deleteClipLibCategory = (id) => _http.delete(`api/clip/lib/${id}`);

// 新增剪辑片段分类
export const createClipLibCategory = (data) => _http.post(`api/clip/lib`, data);

// 获取分类下子分类
export const getClipLibChildrenInfo = (id, isadmin) =>
  _http.get(`api/clip/lib/${id}/children/info/${isadmin ? 1 : 0}`);

// 创建剪辑片段复制接口
export const createClipLibCopy = (data) => _http.post(`api/v1/clip`, data);

// 更新剪辑片段
export const updateClipLib = (id, data) => _http.put(`api/v1/clip/${id}`, data);

// 删除剪辑片段
export const deleteClipLib = (id) => _http.delete(`api/v1/clip/${id}`);

// 创建剪辑片段
export const createClipLib = (data) => _http.post(`api/v1/clip`, data);

// 增加播放次数
export const increaseClipLibPlayCount = (id) =>
  _http.post(`api/v1/clip/${id}/play`);

// 搜索剪辑片段
export const searchClipLib = (isadmin, data) =>
  _http.post(`api/v1/clip/search${isadmin ? "/manage" : ""}`, data);

// 获取文件播放信息
export const getPlayInfo = (fileId) =>
  _http.get(`api/xmatefile/getPlayInfo?clientType=1&fileId=${fileId}`);

// 获取销售能力提升
export const getConferenceSalesImprovement = (conferenceId) =>
  _http.get(`api/aichat/conference/${conferenceId}/sales/improvement`);

// 更新课件学习状态
export const updateCourseStudyStatus = (data) =>
  _http.post(`api/userkng/study`, data);

// 移动分类
export const moveCourseStudyStatus = (id, data) =>
  _http.put(`api/clip/lib/${id}/move`, data);

const _getReportDateStr = (reportDate) => {
  return reportDate ? `/${reportDate}` : "";
};

// 团队拜访情况统计报告 用在 teamboard.overview
export const getTeamVisitReport = (teamId, periodType, reportDate = "") => {
  let reportDateStr = _getReportDateStr(reportDate);
  if (reportDateStr) {
    reportDateStr = `${reportDateStr}/latest2`;
  }
  return _http.get(
    `api/xmate/team/${teamId}/report/${periodType}${reportDateStr}`
  );
};

// 获取团队拜访统计数据，periodType: weekly, monthly, quarterly, yearly
export const getReportSubTeam = (teamId, periodType, reportDate = "") => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.get(
    `api/xmate/team/${teamId}/report/${periodType}/sub_teams${reportDateStr}`
  );
};

// 获取团队拜访统计数据 带分页，包括所有子部门，periodType: weekly, monthly, quarterly, yearly
export const getReportSubTeamsPager = (
  teamId,
  periodType,
  param,
  reportDate = ""
) => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.post(
    `api/xmate/team/${teamId}/report/${periodType}/sub_teams/pager${reportDateStr}`,
    param
  );
};

// 获取团队拜访统计数据 只包括当前部门的用户，periodType: weekly, monthly, quarterly, yearly
export const getReportSubUser = (teamId, periodType, reportDate = "") => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.get(
    `api/xmate/team/${teamId}/report/${periodType}/users${reportDateStr}`
  );
};

// 获取团队拜访统计数据 带分页，包括所有子部门的用户，periodType: weekly, monthly, quarterly, yearly
export const getReportSubUserPager = (
  teamId,
  periodType,
  param,
  reportDate = ""
) => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.post(
    `api/xmate/team/${teamId}/report/${periodType}/users/pager${reportDateStr}`,
    param
  );
};

// 取得成员评测维度数据, dataType查找报表的类型，ABILITY 能力， COMPLETION  完成度
export const getAssessment = (
  periodType,
  orgId,
  userId,
  dataType,
  parm,
  reportDate = ""
) => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.post(
    `api/org/${orgId}/user/${userId}/assessment/${dataType}/${periodType}${reportDateStr}`,
    parm
  );
};

// 成员拜访情况统计导出
export const getUserVisitReportExport = (
  teamId,
  periodType,
  data,
  reportDate = ""
) => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.download(
    `api/xmate/team/${teamId}/report/${periodType}/users/export${reportDateStr}`,
    data
  );
};

// 我的团队拜访情况统计导出
export const getTeamVisitReportExport = (
  teamId,
  periodType,
  data,
  reportDate = ""
) => {
  const reportDateStr = _getReportDateStr(reportDate);
  return _http.download(
    `api/xmate/team/${teamId}/report/${periodType}/sub_teams/export${reportDateStr}`,
    data
  );
};

// 取得团队报告
export const getTeamReport = (orgId, userId, data) =>
  _http.post(`api/org/${orgId}/user/${userId}/team/report`, data);

// 团队报告详情：取得客户会议情况
export const getCustomerMeetings = (orgId, userId, customerId, data) =>
  _http.post(
    `api/org/${orgId}/user/${userId}/customer/${customerId}/meetings`,
    data
  );

// 团队报告详情：取得客户列表
export const getCustomerList = (orgId, userId, data) =>
  _http.post(`api/org/${orgId}/user/${userId}/customer`, data);

// 客户之声
export const getDimensionListApi = (params) =>
  _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/customers/top/feedbacks/dimension/days/${params.days}`,
    params.data
  );

// 竞品详情列表
export const getCompetitorRadarDataApi = (data) =>
  _http.post(
    `api/competitor/radar/${data.competitorId}/${data.periodType}`,
    data
  );
// 获取不同维度的声量分布
export const getVolumedistributionApi = (params) =>
  _http.get(
    `api/competitor/radar/volumedistribution/${params.periodType}`,
    params
  );
// 获取提及竞品原文在不同维度的数量&情感分布
export const getSentimentDistributionApi = (params) =>
  _http.get(
    `api/competitor/radar/sentimentdistribution/${params.competitorId}/${params.periodType}`
  );
// 获取提及竞品词云图
export const getKeywordSentimentApi = (params) =>
  _http.get(
    `api/competitor/radar/keywordsentiment/${params.competitorId}/${params.periodType}`
  );
// 获取当前竞品AI分析
export const getCompetitorAIAnalysisApi = (params) =>
  _http.get(`api/competitor/radar/aianalysis/${params.periodType}`, params);
// 获取涉及竞品评价总数及分布
export const getEvaluationDistributionApi = (params) =>
  _http.get(`api/competitor/radar/evaluationdistribution/${params.periodType}`);

// 获取全部客户列表
export const getCrCustomerList = (data) =>
  _http.get(
    `api/competitor/radar/customerlist/${data.competitorId}/${data.periodType}?customerName=${data.customerName}`
  );

// 客户反馈内容查询
export const getCustomerFeedbackApi = (days, data) => {
  return _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/feedbacks/before/days/${days}`,
    data
  );
};

//客户常见问题数量
export const getFaqQuestionsCountApi = (days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/faqs/before/days/${days}`
  );

//客户常见问题内容查询
export const getCustomerFaqApi = (days, data) =>
  _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/faqs/before/days/${days}`,
    data
  );

//客户常见问题分布
export const getCustomerFaqDistApi = (days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/faq/distribution/before/days/${days}`
  );

//客户态度分布
export const getCustomerAttiDistApi = (days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/attitude/distrubution/before/days/${days}`
  );

// 客户之声 客户名称列表
export const getCVCustomerNameListApi = (days, search = "", size = 10) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/customers/days/${days}?search=${search}&size=${size}`
  );

//客户之声 需求洞察列表
export const getCVDemandInsightListApi = (days, data) =>
  _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/demand/insight/days/${days}`,
    data
  );

//客户之声 需求洞察详情
export const getCVDemandInsightDetailApi = (insightId, data) =>
  _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/demand/insight/${insightId}/details`,
    data
  );
//Top 6 积极反馈&消极反馈
export const getTop6FeedbacksApi = (days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/customers/top/feedbacks/days/${days}`
  );

//客户之声Ai分析
export const getCustomerFeedbacksAnalysisApi = (days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/customers/top/feedbacks/analysis/days/${days}`
  );

//客户态度各维度问答总数
export const getCustomerAttiAllDistApi = (days, data) =>
  _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/attitude/all/distrubution/before/days/${days}`,
    data
  );

// 导出用户问题
export const exportUserFaq = (data) =>
  _http.download(
    `api/org/${g.appStore.user.orgId}/customer/voice/faqs/before/days/${data.days}/export`,
    data
  );

//获取维度列表
export const getCrDimensionList = (competitorId, periodType, data) =>
  _http.post(
    `api/competitor/radar/dimensionlist/${competitorId}/${periodType}`,
    data
  );

// FAQ推荐答案
export const getFaqRecomendAnswerApi = (faqId, days) =>
  _http.get(
    `api/org/${g.appStore.user.orgId}/customer/voice/faqs/${faqId}/recomend/answer/before/days/${days}`
  );
//取得部门-成员的评估排名
export const getDimensionScores = (
  orgId,
  deptId,
  userOrDeparment,
  dataType,
  dimensionId,
  param
) =>
  _http.post(
    `api/org/${orgId}/dept/${deptId}/${userOrDeparment}/${dataType}/dimension/${dimensionId}/scores`,
    param
  );

//取得销售表现对比
export const getSalesCompare = (orgId, deptId, dataType, param) =>
  _http.post(
    `api/org/${orgId}/dept/${deptId}/users/${dataType}/compare`,
    param
  );

// 个人表现趋势数据
export const getUserTrends = (orgId, deptId, userId, dataType, param) =>
  _http.post(
    `api/org/${orgId}/dept/${deptId}/users/${userId}/${dataType}/trends`,
    param
  );

export const getUserPerformanceTrendAnalysis = (params) =>
  _http.post(`api/customer/user/performance/trend/analysis`, params);

//取得团队成员销售表现任务达成度
export const getTaskCompleteRatio = (orgId, deptId, param) =>
  _http.post(`api/org/${orgId}/dept/${deptId}/users/taskCompleteRatio`, param);

//取得销售表现均值
export const getDimensionScore = (orgId, deptId, param) =>
  _http.post(`api/org/${orgId}/dept/${deptId}/depts/dimension/score`, param);
export const getDeptTaskCompleteRatios = (orgId, deptId, param) =>
  _http.post(`api/org/${orgId}/dept/${deptId}/depts/taskCompleteRatio`, param);

// 个人表现趋势数据分析复制接口复制文档复制地址
export const getUserTrendsAnalysis = (orgId, deptId, userId, dataType, param) =>
  _http.post(
    `api/org/${orgId}/dept/${deptId}/users/${userId}/${dataType}/trends/analysis`,
    param
  );

export const getUserInventoryCapability = (orgId, deptId, param) =>
  _http.post(
    `api/org/${orgId}/dept/${deptId}/users/inventory/capability`,
    param
  );

export const getListUserReportInThisTeam = (teamId, periodType) =>
  _http.get(`api/xmate/team/${teamId}/report/${periodType}/users`);

export function getUsersAssessCompareData(orgId, teamId, dataType, params) {
  return _http.post(
    `api/org/${orgId}/dept/${teamId}/users/${dataType}/compare`,
    params
  );
}

export function listSubTeamsReportInThisTeam(teamId, periodType) {
  return _http.get(`api/xmate/team/${teamId}/report/${periodType}/sub_teams`);
}

export function getDeptSalesDimensionsScore(orgId, teamId, dataType, params) {
  return _http.post(
    `api/org/${orgId}/dept/${teamId}/depts/${dataType}/dimension/score`,
    params
  );
}

export function getSalesAssessmentConfig(params) {
  return _http.post(
    `api/xmate/configs/topic/dimension/sales_tasks/assessment/settings`,
    params
  );
}

export function getUserDimensionData(
  orgId,
  ssoUserId,
  periodType,
  dataType,
  params
) {
  return _http.post(
    `api/org/${orgId}/user/${ssoUserId}/assessment/${periodType}/${dataType}`,
    params
  );
}

export function getStandardSetting(orgId) {
  return _http.get(`api/org/${orgId}/standard/setting`);
}
