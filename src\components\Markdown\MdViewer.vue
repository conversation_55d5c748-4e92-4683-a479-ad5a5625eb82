<template>
  <div class="mv_viewer_wrap">
    <div class="mv_viewer_content" v-html="getHtml"></div>
    <div class="mv_viewer_icons">
      <div v-if="copy && getHtml" class="copy_icon" @click="onCopy">
        <CopyIcon />
      </div>
      <div v-if="edit && isHost" class="edit_icon" @click="onEdit">
        <EditIcon />
      </div>
    </div>
  </div>
</template>
<script setup>
import { md2html } from "@/js/md.js";
import CopyIcon from "@/icons/copy.vue"
import EditIcon from "@/icons/edit.vue"
import { mdToText } from '@/js/md.js'

const isHost = ref(false);

const props = defineProps({
  md: {
    type: String,
    required: true,
  },
  copy: {
    type: Boolean,
    default: true,
  },
  edit: {
    type: Boolean,
    default: false,
  },
});

const getHtml = computed(() => {
  return md2html(props.md);
});

const onCopy = () => {
  const txt = mdToText(props.md);
  g.appStore.doCopy(txt, '已复制');
}

const onEdit = () => {
  g.emitter.emit('set_md_editor_value', props.md);
}

onMounted(() => {
  isHost.value = !g.postmeetStore.isReadonly();
})



</script>

<style lang="scss">
.mv_viewer_wrap {
  position: relative;

  .mv_viewer_icons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
    position: absolute;
    right: 12px;
    top: 0;

    .copy_icon {
      cursor: pointer;
    }

    .edit_icon {
      cursor: pointer;
    }
  }

  .mv_viewer_content {
    background: #f9fafc;
    border-radius: 8px;
    padding: 20px 20px 10px 20px;

    ul,
    ol {
      padding-left: 24px;
      margin: 12px 0;
    }

    ul {
      list-style: disc;

      ul {
        list-style: circle;

        ul {
          list-style: square;
        }
      }
    }

    ol {
      list-style: decimal;

      ol {
        list-style: lower-alpha;

        ol {
          list-style: lower-roman;
        }
      }
    }

    li {
      list-style: inherit;
      color: #595959;
      line-height: 24px;
      margin: 8px 0;
      font-size: 14px;

      &::marker {
        color: #8c8c8c;
      }

      p {
        margin: 0;
        display: inline;
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    ul,
    ol,
    li,
    div,
    p {
      color: #333;
    }

    h2 {
      font-size: 16px;
      margin: 16px 0;
    }

    h3 {
      font-size: 14px;
    }

    code {
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
      word-break: break-all;
      white-space: pre-wrap;
    }
  }
}
</style>
