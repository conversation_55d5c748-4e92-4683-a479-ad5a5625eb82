<template>
    <div class="meet_setting" v-loading="loading" element-loading-text="正在更新设置...">
        <el-alert v-if="errorMsg" :title="errorMsg" type="error" />
        <div class="meet_setting_options flex-col">
            <div class="meet_setting_options_item flex-row">
                <VolumeIndicator ref="volumeIndicator" :device-id="options.audio.audioInputDevice" />
                <AudioInputSelector ref="refAudioInputSelector" v-model="options.audio.audioInputDevice" />
            </div>
            <div class="meet_setting_options_item">
                <el-checkbox v-model="options.audio.recordSystemAudio" label="录制电脑音频"
                    @change="onRecordSystemAudioChange" />
            </div>
        </div>
        <div class="meet_setting_btn flex-center">
            <div class="meet_setting_btn_start" @click="onConfirm">确定</div>
        </div>
    </div>
</template>

<script setup>
import VolumeIndicator from '@/app_electron/components/VolumeIndicator.vue';
import AudioInputSelector from '@/app_electron/components/AudioInputSelector.vue';
import { getStore } from '@/js/utils'


const options = ref({ ...getStore(g.cv.keyMeetOptions, {}) })
const refAudioInputSelector = ref(null)
const volumeIndicator = ref(null)
const emit = defineEmits(['callback'])
const errorMsg = ref('');
const loading = ref(false)
let hasDeviceChange = false

const onConfirm = () => {
    console.log('onConfirm', JSON.stringify(options.value.audio), JSON.stringify(g.meetStore.meetingOptions.audio))
    const isChanged = JSON.stringify(options.value.audio) != JSON.stringify(g.meetStore.meetingOptions.audio);
    if (isChanged || hasDeviceChange) {
        g.electronStore.sendMessage('meet', 'updateMeetingOptions', toRaw(options.value))
    } else {
        console.log('no changed')
    }
    g.electronStore.closeWin('meet_setting')
}

const onRecordSystemAudioChange = () => {
    if (!g.config.isMac) return
    if (options.value.audio.recordSystemAudio) {
        g.ipcRenderer.invoke('install-mac-virtual-audio').then((res) => {
            if (!res) {
                options.value.audio.recordSystemAudio = false;
                ElMessage.error('安装虚拟音频失败，无法录制电脑音频')
            }
        })
    } else {
        g.ipcRenderer.send('kill-virtual-audio')
    }
}


const setError = (msg) => {
    errorMsg.value = msg
}

const setLoading = (status) => {
    loading.value = status
}

const handleDeviceChange = () => {
    hasDeviceChange = true
}

onMounted(() => {
    navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    onRecordSystemAudioChange()
})


onBeforeUnmount(() => {
    navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
})

defineExpose({
    options, onConfirm, refAudioInputSelector, AudioInputSelector,
    setError, setLoading
})
</script>

<style lang="scss" scoped>
.meet_setting {
    height: 250px;
    overflow: hidden;

    .el-alert {
        margin-bottom: 20px;
    }

    .meet_setting_audio {
        width: 100%;

        img {
            width: 100%;
        }
    }

    .meet_setting_options {
        margin: 20px;

        .meet_setting_options_item {
            margin: 4px 0;

            :deep(.el-select) {
                .el-select__wrapper {
                    height: 40px;
                    line-height: 40px;
                    border-radius: 8px;
                }
            }


        }
    }

    .meet_setting_btn {
        .meet_setting_btn_start {
            margin-top: 37px;
            width: 90%;
            height: 40px;
            background: linear-gradient(45deg, #691FFE 0%, #00B9E9 100%);
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
        }
    }

}
</style>