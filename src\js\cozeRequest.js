import { convertAnswer } from "@/js/coze_tools.js"
import { getUser } from "@/js/utils.js"

export default function fetchWithTimeout(url, options, timeout = 4 * 60 * 1000) {
    return Promise.race([
        fetch(url, options),
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), timeout),
        ),
    ]);
}

let controllerCoze;
let signalCoze;

export function abortCozeRequest() {
    try {
        !!controllerCoze && controllerCoze.abort("user cancel");
    } catch (e) {
        console.log('abortRequest error', e)
    }
}


export function askCozeApi(data) {
    return new Promise(async (resolve) => {
        if (!data.conversation_id) {
            console.error("no conversation_id")
            resolve(['', ''])
        }

        if (!data.bot_id) {
            console.error("no bot_id")
            resolve(['', ''])
        }

        let response;
        let token = getUser().token || '';

        const url = `${g.config.meetApiHost}/rest/api/coze/chat`;
        controllerCoze = new AbortController();
        signalCoze = controllerCoze.signalCoze;
        response = await fetchWithTimeout(url, {
            method: 'post',
            signalCoze,
            headers: {
                'Content-type': 'application/json',
                "Accept": "text/event-stream",
                token
            },
            body: JSON.stringify(data)
        });
        const chatId = response.headers.get('Chat-id');
        const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();

        let answer = ''
        let lastIdx = 0;
        let date1 = null;
        let resp = {}
        while (true) {
            const { value, done } = await reader.read();
            if (!date1) {
                date1 = new Date()
            }
            if (done) {
                const date2 = new Date()
                const timess = (date2 - date1) / 1000;
                reader.releaseLock()
                g.emitter.emit("coze_resp_done", '');
                break
            };
            answer += value;
            resp = convertAnswer(answer);
            const new_resp = resp.slice(lastIdx);
            if (new_resp) {
                // console.log('新的答案：', new_resp)
                g.emitter.emit("coze_resp", new_resp);
            }
            lastIdx += new_resp.length;
        }
        resolve([chatId, resp]);
    })
}


export function askCozeApi2(data) {
    const resps = [
        ['【', '商品', '信息', '查询', '】', '烧', '番茄', '蛋', '汤', '，', '先', '准备', '好', '番茄', '、', '鸡蛋', '、'],
        ['葱', '、', '姜', '、', '盐', '、', '糖', '、', '食用油', '等', '食材', '。', '将', '番茄', '洗净', '切块', '，', '鸡蛋', '打散', '备用'],
        ['。', '锅中', '倒油', '烧热', '，', '放入', '葱姜', '爆', '香', '，', '加入', '番茄', '块', '翻炒', '出', '汁', '，', '再', '加入', '适量'],
        ['清水', '煮开', '。', '接着', '慢慢', '倒入', '鸡蛋', '液', '，', '边', '倒', '边', '搅拌', '，', '形成', '蛋', '花', '。', '最后', '加入'],
        ['盐', '、', '糖', '等', '调味', '，', '撒', '上', '葱花', '就', '可以', '啦', '。', '', '{"msg_type":"generate_answer_finish","data":"","from_module":null,"from_unit":null}', '烧番茄蛋汤还有其他做法吗？', '做番茄蛋汤可以用其他食材替代吗？', '除了番茄蛋汤，还有什么简单易做的汤？']
    ]
    return new Promise((resolve) => {
        if (!data.conversation_id) {
            console.error("no conversation_id")
            resolve([''])
        }

        if (!data.bot_id) {
            console.error("no bot_id")
            resolve([''])
        }

        let lastIdx = 0;
        const timer = setInterval(() => {
            if (lastIdx == resps.length) {
                timer && clearInterval(timer)
                return resolve(['chatid', resps])
            }
            const new_resp = resps[lastIdx];
            // console.log('新的答案：', resps[lastIdx])
            g.emitter.emit("coze_resp", new_resp);
            lastIdx += 1;
        }, 1 * 100);
    })
}