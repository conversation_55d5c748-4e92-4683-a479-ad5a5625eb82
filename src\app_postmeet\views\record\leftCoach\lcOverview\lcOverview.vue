<template>
  <div class="la_overview_wrap flex-column">
    <div class="la_box la_main flex-column" v-if="hasPerformance">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">销售表现</div>
          <div class="icon">
            <el-tooltip class="item" effect="dark" content="根据沟通过程中销售表现智能评估，分为需改进 / 达到预期 / 优秀三个等级" placement="top">
              <questionIcon />
            </el-tooltip>
          </div>
        </div>
        <div class="la_detail" @click="onDetail('performance')">详情</div>
      </div>
      <div :class="`la_label ${getStatusClass(status)}`">
        {{ status }}
      </div>
    </div>
    <div class="la_box la_main flex-column" v-if="hasCapabilityAssessment">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">能力评估</div>
          <div class="la_desc">满分100分，{{ targetSettings }}分达标</div>
        </div>
        <div class="la_detail" @click="onDetail('capabilityAssessment')">详情</div>
      </div>
      <div class="la_label1 flex-row">
        <div :class="`la_score flex-row ${avg_style}`">
          <div class="la_score_num">{{ avgScore }}</div>
          <div class="la_score_unit" v-if="avgScore != '--'">分</div>
        </div>
        <el-button type="primary" size="small" class="la_btn" @click="onUpgrade"
          v-if="isShowGoUpgrade && avgScore < targetSettings">
          去提升
        </el-button>
        <div class="la_tip" v-show="isShowGoUpgrade">
          <div v-if="process == 0"></div>
          <div v-else-if="process == 100">恭喜，已完成提升计划</div>
          <div v-else>提升进度 {{ process }}%，继续加油</div>
        </div>
      </div>
    </div>

    <div class="la_box la_main flex-column" v-if="hasTaskAchievement">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">任务达成</div>
        </div>
        <div class="la_detail" @click="onDetail('taskAchievement')">详情</div>
      </div>
      <div :class="`la_score flex-row ${score > 80 ? 's2' : score > 60 ? 's1' : 's0'}`">
        <div class="la_score_num">{{ score }}</div>
        <div class="la_score_unit">%</div>
      </div>
    </div>

    <div class="la_box la_list flex-column" v-for="item in list">
      <div class="la_header flex-row">
        <div class="la_left flex-row">
          <div class="la_title">
            {{ item.name }}
          </div>
          <div class="icon">
            <el-tooltip class="item" effect="dark" :content="s_hints[item.name]" placement="top">
              <questionIcon />
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="la_status">
        {{ getvalue(item) }}
      </div>
      <div class="la_footer flex-row">
        <div class="la_icon">
          <component :is="getIcon(item)" />
        </div>
        <div class="la_note">
          {{ getNote(item) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import questionIcon from "./icons/question.vue";
import BadIcon from "./icons/bad.vue";
import NormalIcon from "./icons/normal.vue";
import { s_units, s_hints, s_notes, s_levels } from "./const_value.js";
import { getConferenceKnowledgePackage, getCheckFactor } from "@/js/api";
import { calculateTaskScore } from "@/app_postmeet/tools/tools.js";

export default {
  components: {
    questionIcon,
  },
  data() {
    return {
      score: 0,
      s_hints,
      s_notes,
      s_levels,
      avg_style: "",
      avgScore: '--',
      hasCapabilityAssessment: false,
      hasPerformance: false,
      hasTaskAchievement: false,
      saleSpeakPercent: 0,
      isShowGoUpgrade: false,
      list: [],
      process: 0,
      isHost: false,
      isComplete: true,
      targetSettings: 0,
      status: "",
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
    g.emitter.on("update_saleSpeakPercent", () => {
      this.saleSpeakPercent = g.postmeetStore.data.saleSpeakPercent;
    });
  },
  methods: {
    getStatusClass(score) {
      return score > 80 ? "s2" : score > 60 ? "s1" : "s0";
    },
    onUpgrade() {
      console.log("onUpgrade");
      const url = `${g.config.publicPath}/#/player/${g.postmeetStore.data.confId}`;
      window.open(url, "_blank");
    },
    init() {
      const ar = g.postmeetStore.data.saleReport.salesCounsellingReports;

      this.hasCapabilityAssessment = ar.find((x) => x.systemId == 205);
      this.hasPerformance = ar.find((x) => x.systemId == 200);
      this.hasTaskAchievement = ar.find((x) => x.systemId == 206);
      this.targetSettings = this.hasTaskAchievement.targetSettings || 80;
      g.postmeetStore.setValue("targetSettings", this.targetSettings);

      this.saleSpeakPercent = g.postmeetStore.data.saleSpeakPercent;
      if (ar) {
        const item = ar.find((x) => x.systemId == 200)?.report || undefined;
        if (item) {
          this.status = item.find((x) => x.name == "整体评估")["evaluation"]["整体等级"];
        }
        const removeIds = [200, 0, 205, 206];
        this.list = ar.filter((x) => !removeIds.includes(x.systemId));

        const report = ar.find((x) => x.systemId == 206)?.report;
        if (report && report.tasks) {
          const tasks = report.tasks.filter((x) => x.content?.evaluationResults?.subTasks?.length > 0);;
          const totalCount = tasks.reduce(
            (sum, item) => sum + item.content?.evaluationResults.subTasks?.length || 0,
            0
          );
          const completedCount = tasks.reduce(
            (sum, item) =>
              sum +
              item.content?.evaluationResults?.subTasks.filter(
                (x) => x.status === "complete"
              ).length || 0,
            0
          );
          this.score = calculateTaskScore(completedCount, totalCount);
        }
      }
      this.init2();
    },
    init2() {
      this.isHost = !g.postmeetStore.isReadonly();
      let ar =
        g.postmeetStore.data.saleReport.salesCounsellingReports.find(
          (x) => x.systemId == 205
        )?.report?.tasks || [];
      ar = ar.filter(x => x?.content?.score);
      ar.forEach((item) => {
        item.content["score2"] = parseInt(item.content.score.replace("/100", ""));
      });
      if (ar.length === 0) {
        this.avgScore = '--';
      } else {
        this.avgScore = Math.round(
          ar.reduce((sum, item) => sum + item.content.score2, 0) / ar.length
        );
        this.avg_style = this.getStatusClass(this.avgScore);
      }
      if (this.isHost) {
        this.getCheckFactor();
      }
    },
    getCheckFactor() {
      const salesPromoteCourseId = g.postmeetStore.data.asrRaw.salesPromoteCourseId || "";
      if (salesPromoteCourseId) {
        this.isShowGoUpgrade = true;
        this.getKnowledgePackage();
      } else {
        getCheckFactor(g.postmeetStore.data.confId).then((res) => {
          this.isShowGoUpgrade = res.data;
        });
      }
    },
    getKnowledgePackage() {
      this.process = g.postmeetStore.data.kngStudyProcess;
      if (this.process != 0) {
        return;
      }
      getConferenceKnowledgePackage(g.postmeetStore.data.confId).then((res) => {
        if (res.code == 0) {
          const { completedCount, totalCount } = res.data;
          this.process = Math.round((100 * completedCount) / totalCount);
          g.postmeetStore.setValue("kngStudyProcess", this.process);
        }
      });
    },
    onDetailAbility() {
      this.onDetail("capabilityAssessment");
    },
    _getLevel(item) {
      const value = item.report || 0;
      const [min, max] = s_levels[item.name];
      if (value < min) {
        return "low";
      } else if (value > max) {
        return "high";
      } else {
        return "normal";
      }
    },
    getIcon(item) {
      const map = {
        low: BadIcon,
        high: BadIcon,
        normal: NormalIcon,
      };
      const level = this._getLevel(item);
      return map[level];
    },
    getNote(item) {
      const level = this._getLevel(item);
      return s_notes[item.name][level];
    },
    getvalue(item) {
      let value = item.report || 0;
      if (item.name == "销售说话时长占比") {
        // 省得出现两边不一致的情况
        // value *= 100;
        // value = Math.round(value, 0)
        value = this.saleSpeakPercent;
      }
      return value + s_units[item.name];
    },
    onDetail(tag) {
      g.emitter.emit("update_coach_menu", tag);
    },
  },
};
</script>

<style lang="scss">
@use "../coach.scss";

.la_overview_wrap {
  position: relative;

  .la_header {
    justify-content: space-between;
  }

  .la_score {
    margin-right: 16px;

    .la_score_num {
      font-weight: 500;
      font-size: 20px;
      line-height: 30px;
    }

    .la_score_unit {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      margin-top: 6px;
    }
  }

  .la_label {
    height: 30px;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
  }

  .la_list {
    .la_status {
      height: 30px;
      font-weight: 500;
      font-size: 20px;
      color: #262626;
      line-height: 30px;
      margin: 12px 0;
    }

    .la_footer {
      .la_icon {
        margin: 3px 4px;
      }
    }
  }

  .la_label1 {
    margin-top: 12px;
    display: flex;
    align-items: center;

    .la_score {
      margin-right: 16px;

      .la_score_num {
        font-weight: 500;
        font-size: 20px;
        line-height: 30px;
      }

      .la_score_unit {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        margin-top: 6px;
      }
    }

    .la_btn {
      width: 62px;
      height: 28px;
      background: linear-gradient(270deg, #fe823f 0%, #fcbb22 100%);
      border-radius: 4px;
    }

    .la_tip {
      margin-left: 12px;
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #fa541c;
      line-height: 22px;
      text-align: justify;
      font-style: normal;
    }
  }

  .la_left {
    .la_desc {
      margin-left: 8px;
      font-size: 14px;
      color: #8C8C8C;
      line-height: 22px;
    }
  }
}
</style>
