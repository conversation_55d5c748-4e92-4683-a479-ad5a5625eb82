<template>
    <el-form-item label="时间精度">
        <el-radio-group v-model="localValue" @change="onChange">
            <el-radio value="hour">时</el-radio>
            <el-radio value="minute">分</el-radio>
        </el-radio-group>
    </el-form-item>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const localValue = ref(props.modelValue.datetimePrecision)

watch(() => props.modelValue, (newVal) => {
    localValue.value = newVal.datetimePrecision
}, { deep: true })

const onChange = () => {
    emit('update:modelValue', {
        ...props.modelValue,
        datetimePrecision: localValue.value,
    })
}
</script>

<style lang="scss" scoped>
.char-limit {
    display: flex;
    gap: 20px;

    .limit-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
}
</style>