<template>
    <div class="business-card" v-loading="loading">
        <listMenu></listMenu>
        <div class="feedback-list-wrapper">
            <feedback-list :list="cvCustomerFeedbackList" :is-finished="finished" :is-loading="loading"
                @load-more="handleLoadMore" />
        </div>
    </div>
</template>

<script setup>
import listMenu from "./listMenu.vue";
import feedbackList from "./feedbackList.vue";

const totalNum = ref(0)
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)
const cvCustomerFeedbackList = ref([])

const filterCondition = computed(() => g.clientBiStore.filterConditionObj || {})
const checkAndLoadMore = () => {
    if (document.querySelector('.feedback-list-wrapper').scrollHeight <= document.querySelector('.feedback-list-wrapper').clientHeight && !finished.value && !loading.value) {
        queryData(page.value + 1)
    }
}
const queryData = async (pageNum) => {
    try {
        loading.value = true
        const param = {
            pageNumber: pageNum || page.value,
            pageSize: pageSize.value,
        }
        const response = await g.clientBiStore.getCvCustomerFeedbackList(param) || {}
        const list = response.datas || []
        if (pageNum === 1) {
            cvCustomerFeedbackList.value = list
        } else {
            cvCustomerFeedbackList.value.push(...list)
        }
        // 判断是否还有更多数据
        if (list.length < pageSize.value) {
            finished.value = true
        }
        page.value = pageNum || page.value

    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false
        // checkAndLoadMore()

    }
}

// 处理加载更多
const handleLoadMore = () => {
    if (!loading.value && !finished.value) {
        queryData(page.value + 1)
    }
}

watch(() => [filterCondition.value, g.clientBiStore.periodType], () => {
    // 重置状态
    page.value = 1
    finished.value = false
    queryData(1)
}, { immediate: true })
</script>

<style lang="scss" scoped>
.business-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;

    .feedback-list-wrapper {
        padding: 0 16px 16px 16px;
        height: 300px;
        // height: 100px;
        overflow-y: auto;
    }
}
</style>