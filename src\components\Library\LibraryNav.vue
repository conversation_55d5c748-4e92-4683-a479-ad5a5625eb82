<template>
  <div :class="['library-nav', hasAdminPermission ? 'is-lib-admin' : 'is-lib-user']">
    <!-- 新建文件夹按钮 -->
    <el-button type="primary" class="new-folder-btn" @click="handleNewFolder"
      v-if="hasAdminPermission && getAccess('folder_manage_opr')">
      新建文件夹
    </el-button>

    <div class="tree-container">
      <!-- 树状菜单 -->
      <el-tree ref="refTree" :data="treeData" :props="defaultProps" node-key="id" highlight-current
        :expand-on-click-node="true" @node-click="handleNodeClick" :draggable="isadmin" @node-drag-end="handleDragEnd"
        :current-node-key="currNodeId" :default-expanded-keys="expandedKeys">
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <span class="tree-node-label">
              <el-tooltip class="box-item" effect="dark" :content="node.label" :show-after="1000">
                {{ node.label }}
              </el-tooltip>
            </span>
            <!-- 右侧更多操作按钮 -->
            <el-dropdown trigger="hover" @command="(command) => handleCommand(command, data)"
              v-if="hasAdminPermission && (getOpAccess(node, data, 'new') || getOpAccess(node, data, 'edit') || getOpAccess(node, data, 'delete'))">
              <span class="more-actions">
                <el-icon>
                  <MoreFilled />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="new" v-if="getOpAccess(node, data, 'new')">新建</el-dropdown-item>
                  <el-dropdown-item command="edit" v-if="getOpAccess(node, data, 'edit')">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" v-if="getOpAccess(node, data, 'delete')">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-tree>
    </div>

    <el-button type="default" class="my-clipping-btn" @click="handleMyClipping">
      我的剪辑
    </el-button>

    <NewFolderModal ref="refNewFolderModal" @callback="cbModal" />
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { MoreFilled } from "@element-plus/icons-vue";
import {
  deleteClipLibCategory,
  searchClipLib,
  moveCourseStudyStatus,
} from "@/app_client/tools/api";
import { getClipLibTree } from "@/js/api";
import NewFolderModal from "./components/NewFolderModal.vue";

const emit = defineEmits(["callback"]);
const refNewFolderModal = ref(null);
const hasAdminPermission = ref(false);
const refTree = ref(null);
const currNodeId = ref("0");
const expandedKeys = ref(["0"]);

const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

const getOpAccess = (node, data, type) => {
  if (data.id == '0') {
    return false;
  }
  const isLevel1 = node.level <= 1;
  const isLevel_sub = !isLevel1;
  const hasLevel1 = getAccess('folder_manage_opr');
  const hasLevel_sub = getAccess('sub_folder_manage_opr');
  return isLevel1 && hasLevel1 || isLevel_sub && hasLevel_sub;
}

const getAccess = (code) => {
  return g.cacheStore.checkPointActionByCode("case_library", code)
}

const handleMyClipping = () => {
  emit("callback", "my-clipping");
};

// 树状数据
const treeData = ref([]);

const defaultProps = {
  children: "children",
  label: "name",
};

// 处理新建文件夹
const handleNewFolder = () => {
  refNewFolderModal.value.show_add_top();
};

// 在 handleCommand 函数前添加这个方法
const getParentName = (id) => {
  if (!id) return '';

  const findNodeName = (nodes) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.name;
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeName(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findNodeName(treeData.value) || '';
};

// 处理下拉菜单命令
const handleCommand = async (command, data) => {

  data['parentName'] = getParentName(data.parentId);
  switch (command) {
    case "new":
      refNewFolderModal.value.show_add_sub(data);
      break;
    case "edit":
      refNewFolderModal.value.show_edit(data);
      break;
    case "move":
      ElMessage.success(`移动: ${data.name}`);
      break;
    case "delete":
      handleDelete(data);
      break;
  }
};

const checkHasFiles = (data) => {
  return new Promise((resolve) => {
    if (data.children && data.children.length > 0) {
      resolve(true);
    } else {
      const searchParams = {
        libId: data.id,
        keyword: "",
        hostUserId: "",
        pageSize: 1,
        pageNum: 0,
        sortField: "createdTime",
        sortDirection: "DESC",
      };
      searchClipLib(props.isadmin, searchParams)
        .then((res) => {
          resolve(res.content.length > 0);
        })
        .catch(() => {
          resolve(false);
        });
    }
  });
};

const handleDelete = async (data) => {
  try {
    const hasFile = await checkHasFiles(data);
    if (hasFile) {
      ElMessageBox.alert("该文件夹下有内容，无法删除。", "提示", {
        confirmButtonText: "OK",
        callback: (action) => { },
      });
    } else {
      ElMessageBox.confirm(`确定要删除文件夹 【${data.name}】 吗？`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteClipLibCategory(data.id)
            .then((resp) => {
              ElMessage.success("删除成功");
              getClipLibTreeData();
            })
            .catch((error) => {
              console.error(error.response.data);
              ElMessage.error(error?.response?.data?.message || "删除成功");
            });
        })
        .catch(() => { });
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

const handleNodeClick = async (data) => {
  emit("callback", "node-click", data);
};

const getClipLibTreeData = async () => {
  const res = await getClipLibTree(props.isadmin);
  treeData.value = [res];
  handleNodeClick(res);
};

const cbModal = async (action, data) => {
  if (action == "refresh") {
    await getClipLibTreeData();
  } else if (action == "add_ok") {
    currNodeId.value = data.id;
    await getClipLibTreeData();
  }
};

// 添加处理拖拽结束的方法
const handleDragEnd = async (draggingNode, dropNode, dropType) => {
  // 如果是放在根节点或没有目标节点，直接返回
  if (!dropNode) return;

  try {
    const dragData = draggingNode.data;
    const newParentId = dropType == "inner" ? dropNode.data.id : dropNode.data.parentId;

    // 权限检查
    const isDraggingLevel1 = draggingNode.level === 1;
    const isDropToLevel1 = dropType === "inner" ? dropNode.level === 0 : refTree.value.getNode(newParentId).level === 0;
    const hasLevel1Permission = getAccess('folder_manage_opr');
    const hasSubPermission = getAccess('sub_folder_manage_opr');

    // 检查移动权限
    if (isDraggingLevel1 && !hasLevel1Permission) {
      ElMessage.error('无权限移动一级文件夹');
      getClipLibTreeData();
      return;
    }

    // 子文件夹移动到一级的情况
    if (!isDraggingLevel1 && isDropToLevel1 && !hasLevel1Permission) {
      ElMessage.error('移动失败，无一级文件夹新建权限');
      getClipLibTreeData();
      return;
    }

    // 子文件夹的移动权限
    if (!isDraggingLevel1 && !hasSubPermission) {
      ElMessage.error('无权限移动子文件夹');
      getClipLibTreeData();
      return;
    }

    const parentNode1 = refTree.value.getNode(draggingNode.data.parentId);
    let targetIndex = -1;

    //找下是当前节点的第几个
    const index1 = parentNode1.childNodes.findIndex(
      (item) => item.data.id === draggingNode.data.id
    );

    if (index1 == -1) {
      //移动到了另一个父节点
      const parentNode2 = refTree.value.getNode(dropNode.data.parentId);
      const index2 = parentNode2.childNodes.findIndex(
        (item) => item.data.id === draggingNode.data.id
      );
      targetIndex = index2 + 1;
    } else {
      targetIndex = index1 + 1;
    }

    // 调用API更新节点
    await moveCourseStudyStatus(dragData.id, {
      targetSortNum: targetIndex,
      parentLibId: newParentId,
    });

    currNodeId.value = draggingNode.data.id;
    ElMessage.success("移动成功");
    getClipLibTreeData();

  } catch (error) {
    console.error("移动失败:", error);
    ElMessage.error("移动失败");
    // 刷新树以恢复原始状态
    getClipLibTreeData();
  }
};

const updateTreeNode = (data) => {
  currNodeId.value = data.id;
  expandedKeys.value = [[...expandedKeys.value], data.parentId];
};

const init = (value) => {
  hasAdminPermission.value = value;
  getClipLibTreeData();
};

defineExpose({
  init,
  treeData,
  getClipLibTreeData,
  updateTreeNode,
  refTree,
  currNodeId,
  defaultProps,
  handleNodeClick,
  handleNewFolder,
  refNewFolderModal,
  handleCommand,
});
</script>

<style lang="scss" scoped>
.library-nav {
  padding: 16px;
  position: relative;
  background: #fff;
  border-radius: 8px;

  .new-folder-btn {
    width: 100%;
    margin-bottom: 16px;
  }

  .tree-container {
    overflow: overlay;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;

    .tree-node-label {
      max-width: 170px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .more-actions {
    opacity: 0;
    cursor: pointer;
    color: #909399;
  }

  .custom-tree-node:hover .more-actions {
    opacity: 1;
  }

  .my-clipping-btn {
    width: 80%;
    position: absolute;
    bottom: 16px;
  }
}

.is-lib-admin {
  height: calc(100vh - 117px);

  .tree-container {
    height: calc(100vh - 208px);
    overflow: overlay;
  }
}

.is-lib-user {
  height: calc(100vh - 57px);

  .tree-container {
    height: calc(100vh - 97px);
    overflow: overlay;
  }
}
</style>