<template>
    <div class="risk_wrap">
        <laRiskMenu @callback="cbMenu" />
        <laRiskItem ref="refItem" />
    </div>
</template>
<script>
import laRiskItem from './laRiskItem.vue';
import laRiskMenu from "./laRiskMenu.vue"


export default {
    components: {
        laRiskMenu, laRiskItem
    },
    data() {
        return {
            activeId: 'overview',
        }
    },
    methods: {
        handleClick() {
            this.$emit('callback', this.activeName)
        },
        cbMenu(tag) {
            this.$refs.refItem.setTag(tag);
        }
    }
}

</script>

<style lang="scss">
</style>