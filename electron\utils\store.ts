import Store from 'electron-store'
import config from '../utils/config'

export const keyUserInfo = 'userInfo';
export const keyDevToolsOpen = 'devToolsOpen';
export const keySystemInfo = 'systemInfo';
export const keyWebResourcePath = 'webResourcePath';
export const keyWindowSizes = 'windowSizes';
export const keyLocalVersionInfo = 'localVersionInfo';
export const keyClientId = 'clientId';
export const keyPreventSaveBlockerId = 'preventSaveBlockerId'

export const store = new Store()

export function setStore(key, value) {
    store.set(config.storePrefix + key, value)
}

export function getStore(key, defaultValue: any = '') {
    let res = store.get(config.storePrefix + key, defaultValue)
    if (typeof res === 'string') {
        try {
            res = JSON.parse(res)
        } catch (error) {
        }
    }
    return res
}

export function removeStore(key) {
    store.delete(config.storePrefix + key)
}

export function getUserInfo(key = '') {
    const userInfo = getStore(keyUserInfo, { token: '' })
    if (key) {
        return userInfo[key]
    }
    return userInfo
}