<template>
    <div class="topic_edit_switch">
        <div class="topic_edit_txt">编辑模式</div>
        <el-switch v-model="editmode" @change="onChangeReadonly"></el-switch>
    </div>
</template>


<script>

export default {
    name: 'SwitchEditable',
    data() {
        return {
            editmode: false,
        }
    },
    mounted() {
        this.ListenerVideoStatus()
    },
    methods: {
        onChangeReadonly() {
            g.postmeetStore.setValue('editmode', this.editmode)
            g.emitter.emit('update_editmode', this.editmode)
        },
        ListenerVideoStatus() {
            g.emitter.on('editmode_false', () => {
                this.editmode = false;
                this.onChangeReadonly()
            })
        },
    }
}
</script>

<style lang='scss'>
.topic_edit_switch {
    padding-right: 12px;
    display: flex;
    align-items: center;

    .topic_edit_txt {
        margin-right: 3px;
        line-height: 12px;
        font-size: 14px;
    }
}
</style>
