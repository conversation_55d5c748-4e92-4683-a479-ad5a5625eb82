<template>
    <div class="ai-input flex-row" @click="onClick">
        <el-icon>
            <AIBtnIcon />
        </el-icon>
        <div class="ai-input__content">
            <div>嗨，有关于本次会议的问题可以咨询我哦～</div>
        </div>
    </div>
</template>

<script setup>
import AIBtnIcon from "@/app_postmeet/icons/ai_btn2.vue";



const onClick = () => {
    g.emitter.emit('postmeet_show_ai_chat', true)
}

</script>

<style lang="scss" scoped>
.ai-input {
    width: calc(100% - 100px);
    margin: 0 12px;
    cursor: pointer;
    padding: 8px 16px;
    color: #595959;
    background: #F9FAFC;
    border-radius: 8px;
    border: 1px solid;
    background-image: linear-gradient(135deg, #fff 0, #fff 50%, #fff 100%), linear-gradient(135deg, #c778ff, #76a2ff, #43dbff);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    border: 1px solid transparent;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.02);
    background-origin: padding-box, border-box;
    border-radius: 12px;
    background-clip: padding-box, border-box;

    .el-icon {
        font-size: 16px;
        margin: 4px 10px 0 2px;
    }
}
</style>