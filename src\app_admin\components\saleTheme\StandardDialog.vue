<template>
    <el-drawer v-model="is_show" direction="rtl" class="standard_drawer" @close="onClose">
        <template #header>
            <div class="vd_title">
                达标设置
            </div>
        </template>
        <template #default>
            <div class="draw_main">
                <div class="standard-content">
                    <div class="warning-tip">
                        <el-icon>
                            <Warning />
                        </el-icon>
                        不同主题标签相互关联，修改任一会话主题标签的达标设置，其他主题标签也会同步更新
                    </div>

                    <div class="score-item">
                        <div class="label">{{ dimData.name }}满分</div>
                        <div class="value">
                            <el-input v-model="maxScore" class="score-input" disabled />
                            <span class="unit">分</span>
                        </div>
                    </div>

                    <div class="score-item">
                        <div class="label">
                            {{ dimData.name }}达标标准
                            <p></p>
                            <el-tooltip content="在单次拜访中销售能力评估得分达到多少分即认为达到标准" placement="top">
                                <el-icon class="info-icon">
                                    <InfoFilled />
                                </el-icon>
                            </el-tooltip>
                        </div>
                        <div class="value">
                            <el-input-number v-model="standardScore" :min="0" :max="100" controls-position="right"
                                class="score-input" />
                            <span class="unit">分</span>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="cancelClick">取消</el-button>
                <el-button type="primary" @click="confirmClick">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref } from 'vue'
import { InfoFilled, Warning } from '@element-plus/icons-vue'
import { updateTopicDimension } from '@/app_admin/tools/api.js'

const is_show = ref(false)
const standardScore = ref(0)
const emit = defineEmits(['callback'])
const dimData = ref([])
const maxScore = ref(100)

const show = (data) => {
    dimData.value = data
    standardScore.value = data.targetSettings
    is_show.value = true
}

const onClose = () => {
}

const cancelClick = () => {
    is_show.value = false
}

const confirmClick = () => {
    is_show.value = false
    if (standardScore.value < 0 || standardScore.value > maxScore.value) {
        ElMessage.error('请输入0-100之间的数字')
        return
    }
    updateTopicDimension(dimData.value.topicId, dimData.value.id, { targetSettings: standardScore.value }).then(res => {
        if (res.code == 0) {
            ElMessage.success('修改成功')
            emit('callback', 'reload')
            is_show.value = false
        } else {
            ElMessage.error(res.message)
        }
    })
}

defineExpose({
    is_show, dimData, show, cancelClick, confirmClick, onClose
})
</script>

<style lang="scss">
.standard_drawer {
    width: 480px !important;

    .draw_main {
        width: 100%;
        height: 100%;
        padding: 6px;
    }

    .el-drawer__header {
        height: 56px;
        padding: 0 10px;
        border: 1px solid #E9E9E9;
        font-size: 16px;
        color: #262626;
        margin-bottom: 0;

        .vd_title {
            margin-left: 20px;
        }
    }

    .el-drawer__body {
        padding: 0;
    }

    .standard-content {
        .warning-tip {
            display: flex;
            align-items: flex-start;
            padding: 12px 16px;
            background: #FFFBE6;
            border-radius: 4px;
            color: #666666;
            font-size: 14px;
            line-height: 22px;

            .el-icon {
                color: #FAAD14;
                margin-right: 8px;
                margin-top: 3px;
            }
        }

        .score-item {
            display: flex;
            flex-direction: column;
            padding: 24px;

            .label {
                font-size: 14px;
                color: #262626;
                display: flex;
                align-items: center;

                p {
                    width: 4px;
                    height: 4px;
                    background: red;
                    border-radius: 50%;
                    margin: 0 4px;
                }

                .info-icon {
                    margin-left: 4px;
                    font-size: 16px;
                    color: #BFBFBF;
                    cursor: pointer;
                }
            }

            .value {
                margin-top: 10px;
                flex: 1;
                display: flex;
                align-items: center;

                .score {
                    font-size: 14px;
                    color: #262626;
                }

                .unit {
                    margin-left: 8px;
                    font-size: 14px;
                    color: #262626;
                }

                .score-input {
                    width: 100px;

                    :deep(.el-input-number__decrease),
                    :deep(.el-input-number__increase) {
                        border: none;
                        background: none;
                    }

                    :deep(.el-input__wrapper) {
                        padding-right: 30px;
                    }
                }
            }
        }
    }

    .el-drawer__footer {
        height: 24px;
        background: #FFFFFF;
        box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.08);
    }
}
</style>