import { getYxtOrgDomain } from "@/js/utils";

// 上传文档接口
const uploadFile = (url, formData, onProgress, onSuccess, onFail) => {
  const xhr = new XMLHttpRequest();

  const options = {
    caches: "no-cache",
    method: "POST",
    mode: "cors",
    redirect: "follow",
    referrer: "no-referrer",
    body: formData,
    headers: {
      token: g.appStore?.user?.token || "",
    },
  };
  options.headers["yxt-orgdomain"] = getYxtOrgDomain();

  // 监听上传进度
  xhr.upload.addEventListener("progress", (event) => {
    if (event.lengthComputable) {
      const percent = (event.loaded / event.total) * 100;
      onProgress && onProgress(xhr, percent);
    }
  });

  xhr.onload = function () {
    if (xhr.status >= 200 && xhr.status < 300) {
      let contentType = xhr.getResponseHeader("content-type");
      if (contentType.includes("application/json")) {
        onSuccess(JSON.parse(xhr.responseText));
      } else if (contentType.includes("text/html")) {
        onSuccess(xhr.responseText);
      } else {
        onFail(new Error("Invalid content type"));
      }
    } else if (xhr.status === 401) {
      localStorage.clear();
      sessionStorage.clear();
      g.router.push("login");
    } else {
      onFail(new Error(`HTTP status: ${xhr.status}`));
    }
  };

  xhr.onerror = function (error) {
    console.log("onload onerror", error);
    onFail(new Error("Request failed"));
  };
  xhr.open(options.method, url);
  for (const header in options.headers) {
    xhr.setRequestHeader(header, options.headers[header]);
  }
  xhr.send(options.body);
};
export default uploadFile;
