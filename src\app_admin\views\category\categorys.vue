<template>
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
        <template #col_categoryName="scope">
            <div class="column_link" @click="cbDatas('view_category', scope.row)">
                {{ scope.row.categoryName }}
            </div>
        </template>
        <template #_link_pre="scope">
            <el-button type="primary" text @click="cbDatas('link_upload', scope.row)">
                上传
            </el-button>
        </template>
    </MyTable>
    <dialogCategory ref="refCreate" @callback="cbCreate" />
    <UploadModal ref="refUpload" />
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import dialogCategory from "./dialogCategory.vue"
import UploadModal from "@/app_admin/components/UploadModal.vue";
import { getPptCategorys, deletePptCategory } from "@/app_admin/tools/api.js"

const refUpload = ref();
const refCreate = ref();
const refTable = ref();
const datas = reactive({
    tableid: 'ppt_category',
    param: {},
    need_header: true,
    show_search: false,
    need_init_load: true,
    form: {},
    add_txt: "创建分类",
    delete_hint_column: "categoryName",
    urlGet: getPptCategorys,
    urlDelete: deletePptCategory,
    delete_title: '删除分类',
    delete_hint: '删除分类将同时删除分类下所有文件，请确认是否删除',
    modal_type: "link",
    show_btn_add: true,
    show_link_column: true,
    show_link_edit: true,
    show_link_delete: true,
    columns: ['categoryName', 'fileCount', 'createdUserName', 'createdTime'],
    template: ['categoryName']
});

const cbDatas = (action, data) => {
    if (action == "init_add") {
        refCreate.value.show()
    } else if (action === "init_edit") {
        refCreate.value.show_edit(data)
    } else if (action == "view_category") {
        g.router.push({
            path: `/admin/category/list/${data.id}`
        })
    } else if (action === "link_upload") {
        g.adminFileStore.get_config('ppt').then(config => {
            refUpload.value.show(config, data.id);
        })
    }
}

const cbCreate = (action) => {
    if (action == "reload") {
        !!refTable.value && refTable.value.search()
    }
}

onMounted(() => {
    g.emitter.on('file_uploaded', () => {
        !!refTable.value && refTable.value.search()
    })
})

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    MyTable,
    refCreate,
    refTable,
    refUpload,
    cbDatas,
    UploadModal,
    dialogCategory
})
</script>
