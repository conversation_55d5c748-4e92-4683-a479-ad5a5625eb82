<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <rect id="aibtn_path-1" x="0" y="0" width="16" height="16"></rect>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-3">
                <stop stop-color="#FFBE07" offset="0%"></stop>
                <stop stop-color="#FF6B00" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-4">
                <stop stop-color="#E128FF" offset="0%"></stop>
                <stop stop-color="#FC5850" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="75.2500162%" y1="50%" x2="32.4838846%" y2="36.6540886%" id="linearGradient-5">
                <stop stop-color="#36A4FE" offset="0%"></stop>
                <stop stop-color="#A344FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-6">
                <stop stop-color="#4CD2F4" offset="0%"></stop>
                <stop stop-color="#8532FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-7">
                <stop stop-color="#4CD2F4" offset="0%"></stop>
                <stop stop-color="#8532FF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="移动端发言人标注流程优化" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="时间轴可点击+可滑动-定位当前时间对话" transform="translate(-692.000000, -140.000000)">
                <g id="会议内容" transform="translate(640.000000, 72.000000)">
                    <g id="编组-10" transform="translate(32.000000, 56.000000)">
                        <g id="ai备份-3" transform="translate(20.000000, 12.000000)">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#aibtn_path-1"></use>
                            </mask>
                            <g id="path-2"></g>
                            <g id="编组" mask="url(#mask-2)" fill-rule="nonzero">
                                <g
                                    transform="translate(8.208131, 8.212145) rotate(69.000000) translate(-8.208131, -8.212145) translate(-0.151499, -1.655572)">
                                    <path
                                        d="M0.937184341,11.79825 L11.4371843,11.79825 C12.4036826,11.79825 13.1871843,12.5817517 13.1871843,13.54825 C13.1871843,14.5147484 12.4036826,15.29825 11.4371843,15.29825 L0.937184341,15.29825 C-0.0293139713,15.29825 -0.812815659,14.5147484 -0.812815659,13.54825 C-0.812815659,12.5817517 -0.0293139713,11.79825 0.937184341,11.79825 Z"
                                        id="矩形备份" fill="url(#linearGradient-3)"
                                        transform="translate(6.187184, 13.548250) rotate(45.000000) translate(-6.187184, -13.548250) ">
                                    </path>
                                    <path
                                        d="M12.919067,7.84544693 C13.8855653,7.84544693 14.669067,8.62894862 14.669067,9.59544693 C14.669067,10.5619453 13.8855653,11.3454469 12.919067,11.3454469 L2.90970677,11.3449218 C2.3332125,11.4007526 2.11210914,11.6467734 2.2463967,12.0829841 L1.13625523,10.9652337 C0.978812489,10.7524287 0.882030771,10.5889759 0.845910075,10.4748754 C0.787271288,10.2896432 0.730500663,10.1198639 0.720411166,10.0182172 C0.686961868,9.88339692 0.669066988,9.74151171 0.669066988,9.59544693 C0.669066988,8.62894862 1.45256868,7.84544693 2.41906699,7.84544693 L12.919067,7.84544693 Z"
                                        id="形状结合" fill="url(#linearGradient-4)"></path>
                                    <path
                                        d="M13.0387505,7.84606106 C13.1372855,7.85575269 13.2082772,7.86508147 13.2517257,7.87404741 C13.3903921,7.90266226 13.4882149,7.93881145 13.4882149,7.93881145 C13.5396,7.9590554 13.5934269,7.97780359 13.6468142,7.99937349 C14.5429359,8.36143013 14.9758811,9.38138514 14.6138244,10.2775068 L13.3027014,13.5226503 C12.9406447,14.4187719 11.9206897,14.8517171 11.0245681,14.4896605 C10.1284464,14.1276038 9.69550118,13.1076488 10.0575579,12.2115272 L10.929217,10.054222 C10.955101,9.97339898 10.9846787,9.88717067 11.0180322,9.79553276 C11.3240101,8.95486547 11.1479536,8.31008893 10.4898629,7.86120315 L13.0387505,7.84606106 Z"
                                        id="形状结合" fill="url(#linearGradient-5)"></path>
                                    <path
                                        d="M9.14100419,3.00156688 L14.6410042,3.00156688 C15.6075025,3.00156688 16.3910042,3.78506857 16.3910042,4.75156688 C16.3910042,5.71806519 15.6075025,6.50156688 14.6410042,6.50156688 L9.14100419,6.50156688 C8.17450588,6.50156688 7.39100419,5.71806519 7.39100419,4.75156688 C7.39100419,3.78506857 8.17450588,3.00156688 9.14100419,3.00156688 Z"
                                        id="矩形备份-3" fill="url(#linearGradient-6)"
                                        transform="translate(11.891004, 4.751567) rotate(21.000000) translate(-11.891004, -4.751567) ">
                                    </path>
                                    <path
                                        d="M5.4026203,0.510909658 C6.36911862,0.510909658 7.1526203,1.29441135 7.1526203,2.26090966 C7.1526203,3.22740797 6.36911862,4.01090966 5.4026203,4.01090966 C4.43612199,4.01090966 3.6526203,3.22740797 3.6526203,2.26090966 C3.6526203,1.29441135 4.43612199,0.510909658 5.4026203,0.510909658 Z"
                                        id="矩形备份-4" fill="url(#linearGradient-7)"
                                        transform="translate(5.402620, 2.260910) rotate(21.000000) translate(-5.402620, -2.260910) ">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>
