<template>
  <div class="course-player">
    <!-- 顶部工具栏 -->
    <div class="toolbar flex-row">
      <div class="left-section">
        <el-icon class="back-btn" @click="onBack">
          <ArrowLeft />
        </el-icon>
        <div class="title">能力提升</div>
        <div class="flex-spacer"></div>
        <img :src="getOssUrl(isCollapsed ? 'cp_expand.png' : 'cp_fold.png')" alt="toggle-catalog" class="toggle-btn"
          @click="toggleCatalog" />
      </div>

      <div class="split-line"></div>

      <div class="current-title">
        {{ title }}
      </div>
    </div>

    <CourseLoading v-if="loading" />
    <div v-else class="main-content">
      <!-- 左侧课程目录 -->
      <CourseCatalog ref="refCourseCatalog" @callback="cbCatelog" v-show="!isCollapsed" />

      <!-- 右侧视频播放器 -->
      <div class="video-container" :class="{ 'full-width': isCollapsed }" v-if="url">
        <video id="courseVideoPlayer" ref="refVideoPlayer" class="video-js vjs-default-skin video-player" controls
          preload="auto" playsinline></video>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted } from "vue";
import CourseCatalog from "./CourseCatalog.vue";
import { ArrowLeft } from "@element-plus/icons-vue";
import {
  getConferenceSalesImprovement,
  getPlayInfo,
  updateCourseStudyStatus,
} from "@/app_client/tools/api";
import { useRoute, useRouter } from "vue-router";
import CourseLoading from "./CourseLoading.vue";
import { getOssUrl } from "@/js/utils";
import { getConferenceKnowledgePackage } from "@/js/api";
import videojs from "video.js";
import "video.js/dist/video-js.css";

const refCourseCatalog = ref(null);
const route = useRoute();
const refVideoPlayer = ref(null);
const player = ref(null);
const loading = ref(true);
const confId = route.params.id || "";
const router = useRouter();

const onBack = () => {
  router.push({ path: `/postmeet/record/${confId}` });
};

const title = ref("");
const catalogData = ref({ chapterList: [] });
const progressTimer = ref(null);
const isCollapsed = ref(false);
const currentFileId = ref("");
const currentKngId = ref("");

const PROGRESS_STORAGE_KEY = "course_video_progress";
const MAX_CACHE_ITEMS = 50;
const url = ref("");

const toggleCatalog = () => {
  isCollapsed.value = !isCollapsed.value;
};

const cbCatelog = (action, data) => {
  if (action === "click_lesson" && data.fileId) {
    if (player.value) {
      player.value.dispose();
      player.value = null;
    }

    currentFileId.value = data.fileId;
    currentKngId.value = data.id;
    url.value = "";
    getPlayInfo(data.fileId).then((res) => {
      if (res.length > 0) {
        title.value = data.chapterName;
        url.value = res[0].url;
        nextTick(() => {
          initPlayer();
        });
      } else {
        ElMessage.error("获取播放地址失败");
      }
    });
  }
};

const initPlayer = () => {
  const videoElement = document.getElementById("courseVideoPlayer");
  if (!videoElement) {
    console.error("Video element not found");
    return;
  }
  try {
    player.value = videojs("courseVideoPlayer", {
      controls: true,
      autoplay: true,
      preload: "auto",
      sources: [
        {
          src: url.value,
          type: "application/x-mpegURL",
        },
      ],
    });

    const lastProgress = getVideoProgress(currentFileId.value);
    player.value.on("loadedmetadata", () => {
      player.value.currentTime(lastProgress);
    });

    player.value.on("play", onVideoPlay);
    player.value.on("ended", onVideoEnded);
    player.value.on("timeupdate", () => {
      if (currentFileId.value) {
        saveVideoProgress(currentFileId.value, player.value.currentTime());
      }
    });
  } catch (error) {
    console.error("初始化播放器失败:", error);
  }
};

const saveVideoProgress = (fileId, currentTime) => {
  try {
    let progressCache = JSON.parse(localStorage.getItem(PROGRESS_STORAGE_KEY) || "[]");

    // 查找是否已存在该视频的进度
    const index = progressCache.findIndex((item) => item.fileId === fileId);
    const progressItem = {
      fileId,
      currentTime,
      timestamp: Date.now(),
    };

    if (index !== -1) {
      // 更新已存在的进度
      progressCache[index] = progressItem;
    } else {
      // 添加新的进度记录
      progressCache.push(progressItem);
    }

    // 按时间戳排序并限制数量
    progressCache = progressCache
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, MAX_CACHE_ITEMS);

    localStorage.setItem(PROGRESS_STORAGE_KEY, JSON.stringify(progressCache));
  } catch (error) {
    console.error("保存播放进度失败:", error);
  }
};

const getVideoProgress = (fileId) => {
  try {
    const progressCache = JSON.parse(localStorage.getItem(PROGRESS_STORAGE_KEY) || "[]");
    const progressItem = progressCache.find((item) => item.fileId === fileId);
    return progressItem?.currentTime || 0;
  } catch (error) {
    console.error("获取播放进度失败:", error);
    return 0;
  }
};

// 学习状态 1:开始学习 2:完成学习
const onVideoPlay = () => {
  if (currentFileId.value) {
    const packageId = catalogData.value.data.id;
    const kngId = currentKngId.value;
    updateCourseStudyStatus({
      packageId,
      kngId,
      status: 0,
    })
      .then((res) => {
        refCourseCatalog.value.updateCatalogStatus(kngId, 1);
      })
      .catch((err) => {
        ElMessage.error("更新播放状态失败");
      });
  }
};

const onVideoEnded = () => {
  if (currentFileId.value) {
    const packageId = catalogData.value.data.id;
    const kngId = currentKngId.value;
    updateCourseStudyStatus({
      packageId,
      kngId,
      status: 1,
    })
      .then((res) => {
        refCourseCatalog.value.updateCatalogStatus(kngId, 2);
      })
      .catch((err) => {
        ElMessage.error("更新播放状态失败");
      });
  }
};

const init = () => {
  getConferenceKnowledgePackage(confId).then((res) => {
    loading.value = false;
    if (res.code === 0) {
      nextTick(() => {
        catalogData.value = res;
        refCourseCatalog.value.init(res.data.chapterList);
      });
    } else {
      ElMessage.error(res.message);
    }
  });
};

const getProgress = async () => {
  try {
    const res = await getConferenceSalesImprovement(confId);
    if (res.code === 0) {
      if (res.data === "1") {
        clearProgressTimer();
        init();
      } else {
        // 5秒后重新获取进度
        progressTimer.value = setTimeout(() => {
          getProgress();
        }, 5000);
      }
    } else {
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取进度失败:", error);
    // 错误后10秒重试
    progressTimer.value = setTimeout(() => {
      getProgress();
    }, 10000);
  }
};

// 清除定时器
const clearProgressTimer = () => {
  if (progressTimer.value) {
    clearTimeout(progressTimer.value);
    progressTimer.value = null;
  }
};

onMounted(() => {
  getProgress();
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  clearProgressTimer();
  if (player.value) {
    player.value.dispose();
    player.value = null;
  }
});
</script>

<style  lang="scss"  scoped>
.course-player {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.toolbar {
  height: 50px;
  padding: 0;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.flex-row {
  display: flex;
  align-items: center;
}

.back-btn {
  font-size: 20px;
  cursor: pointer;
  color: #606266;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.back-btn:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.title {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  padding: 20px;
  gap: 20px;
}

.video-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 10px;
  }

  .video-container {
    height: 300px;
  }
}

.flex-spacer {
  flex: 1;
}

.toggle-btn {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.toggle-btn:hover {
  opacity: 0.8;
}

.video-container.full-width {
  margin-left: 0;
}

.left-section {
  width: 300px;
  /* 与 CourseCatalog 宽度一致 */
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.split-line {
  width: 1px;
  height: 24px;
  background-color: #e4e7ed;
  margin: 0;
}

.current-title {
  flex: 1;
  padding: 0 20px;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 修改 CourseCatalog 的宽度 */
:deep(.course-catalog) {
  width: 300px;
  flex-shrink: 0;
}

/* 确保 video.js 的样式正确显示 */
:deep(.video-js) {
  width: 100%;
  height: 100%;
}

:deep(.vjs-tech) {
  object-fit: contain;
}
</style>
