<template>
    <div class="header">
        <div class="title">{{ title }}</div>
        <div class="detail" @click="onShowDetail" v-if="show_btn">详情</div>
    </div>
</template>

<script setup>
const emit = defineEmits(['callback'])
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    show_btn: {
        type: Boolean,
        default: true
    }   
})

const onShowDetail = () => {
    emit('callback', 'showDetail')
}
</script>
<style lang="scss" scoped>
.header {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
        font-size: 16px;
        font-weight: 500;
        color: #1D2129;
        position: relative;
        padding-left: 8px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2px;
            height: 14px;
            background: #4080FF;
        }
    }

    .detail {
        color: #4080FF;
        font-size: 14px;
        cursor: pointer;
    }
}
</style>
