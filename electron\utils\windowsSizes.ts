import { keyWindowSizes, getStore } from './store';

const home = {
    width: 374,
    height: 720,
    minWidth: 374,
    minHeight: 720,
}

const bigScreen = {
    width: 1430,
    height: 856,
    minWidth: 1024,
    minHeight: 768,
    resizable: true,
}

export const getPageSize = (url, windowManager) => {
    const allWindowsSizes = getStore(keyWindowSizes)
    let winSize = {}
    const wins = Object.keys(allWindowsSizes);
    if (wins.includes(url)) {
        const param = allWindowsSizes[url];
        winSize = param['param']
        if (param.offset) {
            const mainWindowInfo = windowManager.getWindow('main');
            if (mainWindowInfo) {
                const mainWinPosition = mainWindowInfo.window.getPosition()
                winSize['x'] = mainWinPosition[0] + param.offset.x
                winSize['y'] = mainWinPosition[1] + param.offset.y
            }
        }
    } else if (url == 'home') {
        winSize = home;
    } else {
        winSize = bigScreen;
    }
    return winSize;
}

