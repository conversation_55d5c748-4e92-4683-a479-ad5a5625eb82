<template>
    <div class="cc_tag flex-row" @click="onClick">
        <el-tag type="info" v-if="props.row.length > 0">{{ row[0] }}</el-tag>
        <div v-else>
            -
        </div>
        <el-popover placement="top-start" :width="200" trigger="hover">
            <template #reference>
                <el-tag type="info" v-show="props.row.length > 1" class="more_tag"> +{{ row.length - 1 }}</el-tag>
            </template>
            <el-tag type="info" v-if="props.row.length > 1" v-for="tag in row" :key="tag">{{ tag }}</el-tag>
        </el-popover>

        <div class="flex-grow"></div>
        <el-popover :visible="visible && props.edit" placement="bottom" :width="272">
            <template #reference>
                <div>
                    <div class="edit_icon" @click="onShowEdit" v-if="props.edit">
                        <EditIcon />
                    </div>
                </div>
            </template>
            <div class="tag_edit_pop flex-column">
                <div>
                    编辑客户标签
                </div>
                <ul>
                    <li v-for="(tag, idx) in tags" :key="tag">
                        <el-input v-model="tags[idx]" />
                        <div class="del_icon" @click="onDelete(tag)">
                            <DeleteIcon />
                        </div>
                    </li>
                    <el-input v-model="tagNew" placeholder="请输入" @keyup.enter.native="onAdd" maxlength="20" />
                </ul>
                <div class="link" @click="onAdd">
                    + 添加标签
                </div>
                <div class="b_btns flex-row">
                    <el-button type="default" @click="onCancel" size="small">取消</el-button>
                    <el-button type="primary" @click="onConfirm" size="small">确认</el-button>
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script setup>
import { saveCustomerTags } from "@/app_client/tools/api.js"
import riskCountIcon from "@/app_client/icons/riskCount.vue"
import EditIcon from "@/icons/edit.vue"
import DeleteIcon from "@/icons/delete.vue"

const emit = defineEmits(['callback'])
const visible = ref(false)
const tags = ref([])
const tagNew = ref('')

const onClick = () => {
    emit('callback', 'view', props.row)
}

const props = defineProps(['row', 'edit', 'id']);

const onShowEdit = () => {
    tags.value = props.row;
    visible.value = true;
}

const onDelete = (tag) => {
    tags.value = tags.value.filter(x => x != tag);
}

const onAdd = () => {
    const temp = tagNew.value.trim()
    if (temp && !tags.value.includes(temp)) {
        tags.value.push(tagNew.value);
        tagNew.value = '';
    } else {

    }
}

const onCancel = () => {
    visible.value = false;
}

const onConfirm = () => {
    if (tagNew.value != '') {
        onAdd(false)
    }
    const param = toRaw(tags.value)
    saveCustomerTags(props.id, param).then(resp => {
        if (resp.code == 0) {
            emit('callback', 'update', { id: props.id, value: param });
            ElMessage.success('更新成功')
            onCancel()
        } else {
            ElMessage.error('添加失败，请稍后再试')
        }
    })
}

defineExpose({ riskCountIcon, props, EditIcon, DeleteIcon, visible, onDelete, onCancel, onConfirm })
</script>

<style lang="scss">
.cc_tag {
    cursor: pointer;

    .el-tag {
        margin-right: 2px;
    }

    .more_tag {
        cursor: pointer;
    }

    .edit_icon {
        cursor: pointer;
        margin-top: 4px;
        color: #8c8c8c;
        visibility: hidden;
    }


}

.el-table__row:hover {
    .edit_icon {
        visibility: visible;
    }
}

.tag_edit_pop {
    ul {
        list-style-type: none;

        li {
            list-style-type: none;
            display: flex;
            margin: 12px 0;

            .del_icon {
                margin: 6px;
            }
        }
    }

    .link {
        height: 22px;
        font-size: 14px;
        line-height: 22px;
        margin: 6px 0px;
    }

    .b_btns {
        justify-content: flex-end;
    }
}

.el-popover {
    .el-tag {
        margin: 4px;
    }
}
</style>