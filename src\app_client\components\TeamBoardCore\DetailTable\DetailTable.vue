<template>
  <div class="team-situation flex-col">
    <div class="situation-header flex-row">
      <div class="flex-row">
        <div class="title-line"></div>
        <div class="title">情况</div>
      </div>
    </div>
    <div v-show="viewType === 'team'">
      <TeamTable ref="refTeamTable" @typeChange="onTypeChange" />
    </div>
    <div v-show="viewType === 'member'">
      <MemberTable ref="refMemberTable" @typeChange="onTypeChange" />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import TeamTable from "./TeamTable.vue";
import MemberTable from "./MemberTable.vue";

const viewType = ref("team");
const refTeamTable = ref();
const refMemberTable = ref();
const periodType = computed(() => g.clientBoardStore.periodType);

const queryData = () => {
  g.clientBoardStore.tableType = toRaw(viewType.value);
  nextTick(() => {
    console.log("queryData", viewType.value);
    if (viewType.value === "team") {
      refTeamTable.value.init();
    } else {
      refMemberTable.value.init();
    }
  });
};

const onTypeChange = (value) => {
  console.log("onTypeChange", value);
  viewType.value = value;
  queryData();
};

const init = () => {
  console.log("detail table init");
  viewType.value = g.clientBoardStore.detailNeedShowTeam() ? "team" : "member";
  nextTick(() => {
    queryData();
  })
};

watch([periodType], () => {
  init();
}, { immediate: true });

onMounted(() => {
  g.emitter.on("team_board_region_click", init);
});

onUnmounted(() => {
  g.emitter.off("team_board_region_click", init);
});

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.team-situation {
  margin: 20px 0;
  background: #fff;
  border-radius: 8px;

  .table_wrap {
    padding: 0;
  }

  .situation-header {
    margin-bottom: 20px;

    .title-line {
      width: 3px;
      height: 16px;
      background: #436bff;
      margin: 6px 12px 0 0;
    }

    .title {
      font-size: 20px;
      font-weight: 500;
    }
  }
}
</style>
