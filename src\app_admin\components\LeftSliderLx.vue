<template>
    <el-menu class="admin_left_slider" :default-active="currActive" @select="handleSelect" :collapse="isCollapse"
        :collapse-transition="false" mode="vertical" active-text-color="#436BFF" bg-color="#fff" text-color="#595959">
        <template v-for="menu in menuList" :key="menu.id">
            <el-menu-item v-if="!menu.children || menu.children.length == 0" :index="menu.index">
                <div class="menu_icon">
                    <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
                </div>
                <span>{{ menu.name }}</span>
            </el-menu-item>

            <el-sub-menu v-else :index="menu.id">
                <template #title>
                    <div class="menu_icon">
                        <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
                    </div>
                    <span>{{ menu.name }}</span>
                </template>
                <el-menu-item v-for="child in menu.children" :key="child.id" :index="child.index">
                    {{ child.name }}
                </el-menu-item>
            </el-sub-menu>
        </template>
    </el-menu>
</template>

<script setup>
const menuList = ref([])

// https://element-plus.org/zh-CN/component/icon.html#icon-collection
import { Grid, Edit, Position } from '@element-plus/icons-vue'
import { onMounted } from "vue";
import InlineSvg from 'vue-inline-svg';
const currActive = ref(location.hash.replace("#", ""));
const props = defineProps({ isCollapse: { type: Boolean, default: false } })
const route = useRoute()

watch(() => route.path, (newPath) => {
    currActive.value = newPath
}, { immediate: true })

const handleSelect = (path, keyPath) => {
    g.router.push({ path })
}
const demo_menu = [{
    "id": "1",
    "name": "沟通配置",
    "index": "",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/a7813d7e5c3d4ddb87eac4a95907ceb3.svg",
    "code": "sales_visit",
    "orderIndex": 1,
    "showed": 1,
    "children": [{
        "id": "2",
        "name": "能力模型",
        "index": "/admin/ability_model",
        "icon": "",
        "code": "sales_ability_model",
        "orderIndex": 1,
        "showed": 1,
        "children": []
    }, {
        "id": "3",
        "name": "任务模型",
        "index": "/admin/task_model",
        "icon": "",
        "code": "sales_task_model",
        "orderIndex": 2,
        "showed": 1,
        "children": []
    }, {
        "id": "4",
        "name": "沟通总结",
        "index": "/admin/communication_summary",
        "icon": "",
        "code": "communication_summary",
        "orderIndex": 3,
        "showed": 1,
        "children": []
    }, {
        "id": "5",
        "name": "沟通分析",
        "index": "/admin/communication_analysis",
        "icon": "",
        "code": "communication_analysis",
        "orderIndex": 4,
        "showed": 1,
        "children": []
    }, {
        "id": "7",
        "name": "沟通流程",
        "index": "/admin/communication_process",
        "icon": "",
        "code": "communication_process",
        "orderIndex": 6,
        "showed": 1,
        "children": []
    }]
}, {
    "id": "8",
    "name": "用户管理",
    "index": "",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/a7813d7e5c3d4ddb87eac4a95907ceb3.svg",
    "code": "user_management",
    "orderIndex": 2,
    "showed": 1,
    "children": [{
        "id": "9",
        "name": "字段设置",
        "index": "/admin/field_settings",
        "icon": "",
        "code": "field_settings",
        "orderIndex": 1,
        "showed": 1,
        "children": []
    }, {
        "id": "10",
        "name": "页面设置",
        "index": "/admin/page_settings",
        "icon": "",
        "code": "page_settings",
        "orderIndex": 2,
        "showed": 1,
        "children": []
    }]
}, {
    "id": "11",
    "name": "商机管理",
    "index": "",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/a7813d7e5c3d4ddb87eac4a95907ceb3.svg",
    "code": "opportunity_management",
    "orderIndex": 2,
    "showed": 1,
    "children": [{
        "id": "12",
        "name": "字段设置",
        "index": "/admin/opportunity_field_settings",
        "icon": "",
        "code": "opportunity_field_settings",
        "orderIndex": 1,
        "showed": 1,
        "children": []
    }, {
        "id": "13",
        "name": "页面设置",
        "index": "/admin/opportunity_page_settings",
        "icon": "",
        "code": "opportunity_page_settings",
        "orderIndex": 2,
        "showed": 1,
        "children": []
    }]
}, {
    "id": "14",
    "name": "竞争对手",
    "index": "/admin/competitor",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/f6d57647257f4652afe0d427e3df4c60.svg",
    "code": "competitor",
    "orderIndex": 6,
    "showed": 1,
    "children": []
}, {
    "id": "15",
    "name": "风险预警",
    "index": "/admin/risk_warning",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/f6d57647257f4652afe0d427e3df4c60.svg",
    "code": "risk_warning",
    "orderIndex": 6,
    "showed": 1,
    "children": []
}]

onMounted(() => {
    g.emitter.on('update_admin_menu', (list) => {
        const isSaleShow = g.appStore.getFuncStatus('sales_product_in_app')
        if (!isSaleShow) {
            list = list.filter(x => x.code != 'sales_product_recommend')
        }
        const hasAiApp = g.appStore.getFuncStatus('sales_ai_apps_icon');
        if (!hasAiApp) {
            list = list.filter(x => (!x.children || x.children.length == 0 || x.children && x.children.length > 0 && x.children.some(y => y.code != 'usage_status')));
        }
        menuList.value = [...list, ...demo_menu];
    })
})

onUnmounted(() => {
    g.emitter.off("update_admin_menu");
});

defineExpose({
    isCollapse: props.isCollapse, Grid, Edit, Position, currActive, handleSelect, menuList, InlineSvg
})
</script>

<style lang='scss'>
.admin_left_slider {
    border: none;
    height: calc(100vh - 105px);
    overflow-y: auto;

    .el-sub-menu__title {
        display: flex;
        align-items: center;
    }

    .menu_icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &.el-menu--collapse {
        .menu_icon {
            margin-right: 0;
        }
    }
}
</style>