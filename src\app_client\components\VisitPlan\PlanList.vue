<template>
  <div class="plan-list">
    <div v-for="(plans, date) in groupedPlans" :key="date" class="plan-group">
      <div class="plan-date">
        <span class="date">{{ formatDate(date, "MM月dd日") }}</span>
        <span class="day">{{ formatDate(date, "周w") }}</span>
      </div>
      <div v-for="plan in plans" :key="plan.id" :class="`plan-item ${plan.completed ? 'pi_finish' : 'pi_unfinished'}`">
        <div class="plan_left" @click="handleCommand('visit_detail', plan)">
          <div class="plan-status" v-if="plan.completed">
            <img :src="getOssUrl('client_plan_complete.png')" alt="" />
          </div>
          <div class="plan-time">
            <span>{{ plan.startDt }}</span>
            <div class="time-wrapper">
              <span>{{ plan.endDt }}</span>
              <span v-if="!plan.inSameDay" class="plus-one">+1</span>
            </div>
          </div>
          <div class="plan-company flex-center">
            <el-avatar :src="getCompanyLogoUrl(plan.customerLogoUrl)" :size="32" />
          </div>
          <div class="plan-info">
            <div class="plan-company-name" :title="plan.subject">
              {{ plan.subject }}
              <span v-if="plan.notMe" class="visit-tag">协访</span>
            </div>
            <div class="plan-customer">
              <span>{{ plan.salesMateCustomerName }}</span>
              <span class="location">{{ plan.location }}</span>
              <span :class="`status ${plan.status}`">{{
                getLang(plan.status)
              }} </span>
            </div>
          </div>
        </div>
        <div class="plan-actions">
          <template v-if="plan.showBtnReview">
            <el-button type="primary" @click="handleCommand('review', plan)" class="ch_color_btn">回顾</el-button>
          </template>
          <el-button type="primary" @click="startPlan(plan)" v-if="plan.showBtnStart"
            class="ch_color_btn">开始</el-button>
          <el-button @click="handleCommand('prepare', plan)" class="ch_default_btn">准备</el-button>
          <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, plan)">
            <span class="more-actions">
              <el-icon>
                <MoreFilled />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="prepare" v-if="plan.isExpired && plan.conferenceId && plan.completed">
                  准备
                </el-dropdown-item>
                <el-dropdown-item command="review" v-if="!plan.isExpired && isShowReview(plan)">
                  回顾
                </el-dropdown-item>
                <el-dropdown-item command="visit_detail">查看详情</el-dropdown-item>
                <el-dropdown-item command="edit"
                  v-if="!plan.completed && plan.status != 'ongoing' && !plan.notMe">修改拜访</el-dropdown-item>
                <el-dropdown-item command="delete"
                  v-if="!plan.completed && plan.status != 'ongoing' && !plan.notMe">取消拜访</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div v-if="!hasPlans" class="no-plans">暂无行程安排</div>
  </div>
</template>

<script setup>
import { MoreFilled } from "@element-plus/icons-vue";
import {
  getCompanyLogoUrl,
  formatDate,
  groupPlansByDate,
  getOssUrl,
} from "@/js/utils.js";
import getLang from "@/js/lang.js";

const emit = defineEmits(["callback"]);
const plans = ref([]);
const groupedPlans = ref({});

const hasPlans = computed(() => {
  return Object.keys(groupedPlans.value).length > 0;
});

const isShowReview = (plan) => {
  return plan.conferenceId && plan.completed && !(plan.recognitionPath == '' && plan.recognitionStatus == 'DELETED')
}

function startPlan(plan) {
  g.electronStore.joinMeeting(toRaw(plan));
}

function handleCommand(command, plan) {
  plan = toRaw(plan);
  if (command === "edit") {
    emit("callback", "edit_plan", plan);
  } else if (command === "delete") {
    g.planStore.cancelPlan(plan.scheduleId).then((res) => {
      emit("callback", "reload");
    });
  } else if (command === "visit_detail") {
    emit("callback", "visit_detail", plan);
  } else if (command === "prepare") {
    emit("callback", "prepare", plan);
  } else if (command === "review") {
    g.clientStore.viewPlanRecord(plan);
  } else if (command === "start") {
    startPlan(plan);
  }
}

const init = (list) => {
  plans.value = list;
  groupedPlans.value = groupPlansByDate(plans.value);
};

defineExpose({
  plans,
  groupedPlans,
  hasPlans,
  init,
  formatDate,
});
</script>

<style lang="scss" scoped>
.plan-list {
  padding-bottom: 50px;

  .plan-group {
    margin-bottom: 24px;

    .plan-date {
      display: flex;
      align-items: baseline;
      margin-bottom: 12px;

      .date {
        font-size: 14px;
        font-weight: bold;
        margin-right: 8px;
      }

      .day {
        font-size: 12px;
        color: #999;
      }
    }

    .pi_unfinished {
      border-left: 4px solid #ff7300;
      background: linear-gradient(90deg,
          #fffaf6 0%,
          rgba(255, 250, 246, 0) 100%);
    }

    .pi_finish {
      border-left: 4px solid #d9d9d9;
      background: linear-gradient(90deg,
          #f5f5f5 0%,
          rgba(245, 245, 245, 0) 100%);
    }

    .plan-item {
      display: flex;
      align-items: center;
      margin: 12px 0;
      width: 100%;
      min-height: 84px;
      flex-wrap: wrap;
      gap: 12px;

      .plan_left {
        display: flex;
        align-items: center;
        cursor: pointer;
        flex: 1;
        min-width: 0;
        position: relative;

        .plan-status {
          position: absolute;
          display: flex;
          align-items: center;
          margin: 0 8px;
          top: -20px;
          left: -7px;

          img {
            width: 43px;
            height: 38px;
          }
        }

        .plan-time {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 8px 0 37px;
          font-size: 12px;
          color: #999;
          flex-shrink: 0;

          span {
            margin: 3px 0;
          }

          .time-wrapper {
            position: relative;
            display: inline-flex;
            align-items: center;

            .plus-one {
              position: absolute;
              top: 0;
              right: -16px;
              font-size: 10px;
              color: #ff5219;
              font-weight: 500;
              margin: 0;
            }
          }
        }

        .plan-company {
          margin: 0 12px 0 14px;
          width: 44px;
          height: 44px;
          border-radius: 4px;
          background-color: #fff;
          flex-shrink: 0;
        }

        .plan-info {
          flex: 1;
          min-width: 0;
          overflow: hidden;

          .plan-company-name {
            height: 22px;
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;

            .visit-tag {
              padding: 0 4px;
              height: 18px;
              line-height: 18px;
              background: #FFF7E6;
              border-radius: 2px;
              font-size: 12px;
              color: #FF7300;
              font-weight: normal;
              flex-shrink: 0;
            }
          }

          .plan-customer {
            font-size: 12px;
            color: #666;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;

            .location {
              color: #999;
            }

            .status {
              &.notStarted {
                color: #ff5219;
              }

              &.ongoing {
                color: #02b853;
              }
            }
          }
        }
      }

      .plan-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        padding-right: 12px;
        flex-wrap: wrap;

        .el-button {
          font-size: 14px;
          padding: 4px 8px;
          margin: 0;
        }

        .more-actions {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
        }
      }
    }
  }

  .no-plans {
    text-align: center;
    color: #999;
    padding: 24px 0;
  }

  .ch_default_btn {
    min-width: 60px;
  }

  .ch_color_btn {
    width: 60px;
    height: 32px;
    background: linear-gradient(45deg, #691ffe 0%, #03b8e7 100%);
    border-radius: 4px;
  }

  .ch_btn_disabled {
    opacity: 0.5;
  }
}
</style>
