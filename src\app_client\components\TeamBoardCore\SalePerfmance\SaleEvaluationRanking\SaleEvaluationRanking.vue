<template>
  <div class="sale_evrank_wrap">
    <div class="overview-header flex-row">
      <div class="flex-row title-line-wrap">
        <div class="title-line"></div>
        <div class="title">评估排名</div>
      </div>
    </div>
    <div>
      <averageBarChart ref="refAverageBarChart" />
    </div>
  </div>
</template>

<script setup>
import AverageBarChart from "./averageBarChart.vue";
</script>

<style lang="scss" scoped>
.sale_evrank_wrap {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .overview-header {
    justify-content: space-between;
    margin: 40px 0 10px 0;

  }

}
</style>