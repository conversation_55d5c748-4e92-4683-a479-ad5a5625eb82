<template>
    <div :class="`chat_wrap common_chat_wrap ${isFull ? 'full' : 'nofull'}`">
        <div class="back_pic1"></div>
        <div class="back_pic2"></div>
        <div class="center_box">
            <div class="msgs_list">
                <component :is="getMsgComponent(msg)" :data="msg" v-for="msg in msgs" @callback="cbMsg">
                </component>
                <div class="btn_re_answer" v-if="!isAnswering && msgs.length > 1">
                    <BtnReAnswer ref="refBtnReAnswer" @callback="onReAnswer" />
                </div>
            </div>
            <ChatInput ref="refInput" @callback="cbInput">
                <template #input_top>
                    <slot name="input_top" />
                </template>
                <template #input_inside_top>
                    <slot name="input_inside_top" />
                </template>
            </ChatInput>
        </div>
    </div>
</template>

<script setup>
import { askCozeA<PERSON>, abortCozeRequest } from "@/js/cozeRequest.js"
import MsgMy from "@/components/chatCoze/msg_my.vue"
import MsgAi from "@/components/chatCoze/msg_ai.vue"
import SendIcon from "@/icons/send.vue"
import ChatInput from "@/components/chatCoze/chatInput/chatInput.vue"
import { nextTick, ref, toRaw } from "vue";
import BtnReAnswer from "@/components/chatCoze/BtnReAnswer.vue"
import { mergeHistory, cozeApi2history, splitRawMessage } from "@/js/coze_tools.js"
import { getChatHistory, createGoodsChat, uploadCozeMsg } from "@/js/api.js"
const emit = defineEmits(['callback'])
const isAnswering = ref(false);
const refBtnReAnswer = ref();
const refInput = ref()
const msgs = ref([])
const isFull = ref(false)
const conversation = ref({})
const config = ref({})

let new_msgs = []
let updateing = false;
let chatListDom;
let lastQuestion = '';
let msg_type = '';// 'generate_answer_finish'


const getMsgComponent = (msg) => {
    if (config.value.getMsgComponent && typeof config.value.getMsgComponent == "function") {
        return config.value.getMsgComponent(msg)
    } else {
        return msg.my ? MsgMy : MsgAi
    }
}

const addHelloMsg = () => {
    msgs.value = []
    if (config.value.addHelloMsg && typeof config.value.addHelloMsg == "function") {
        config.value.addHelloMsg()
    } else {
        msgs.value = []

        msgs.value.push({
            my: false,
            type: "hello",
            message: config.value.hello_txt,
            questions: []
        })
    }
}

const requestConvId = () => {
    return new Promise((resolve, reject) => {
        if (conversation.value.id) {
            resolve(true)
        } else {
            const { title, topicType } = config.value;
            const param = {
                "topic": title,
                "type": topicType
            }
            createGoodsChat(param).then(resp => {
                if (resp.code == 0) {
                    conversation.value = resp.data;
                    emit('callback', 'update_conv', resp.data)
                    resolve(true)
                } else {
                    reject(false)
                }
            }).catch(e => {
                console.log('requestConvId error', e)
                reject(false)
            })
        }
    });
}

const setFull = (status) => {
    isFull.value = status
}


const cbMsg = (action, data) => {
    if (action == "send_input") {
        cbInput("send", data)
    } else if (action == "update_conv") {
        conversation.value = data;
        emit('callback', 'update_conv', data)
    } else {
        emit('callback', action, data)
    }
}

const clearHistoryQuetions = () => {
    for (let i = 0; i < msgs.value.length; i++) {
        if (!msgs.value[i].my) {
            msgs.value[i].questions = []
        }
    }
}

const _getBotId = () => {
    if (config.value.param.bot_id) {
        return config.value.param.bot_id
    } else if (config.value.bot_type) {
        return config.value.bots[config.value.bot_type]
    } else {
        console.error("no bot id?")
        return ''
    }
}


const cbInput = (action, data) => {
    if (action === 'stop_answer') {
        abortCozeRequest()
        const lastMsg = msgs.value[msgs.value.length - 1].message;
        if (lastMsg == '') {
            msgs.value[msgs.value.length - 1].message = "已停止回答";
        }
    } else if (action == 'clear') {
    } else if (action == 'send') {
        requestConvId().then(status => {
            if (!status) {
                console.log("fail to create conversation")
                return
            }
            clearHistoryQuetions()
            msg_type = ''
            if (config.value.onBeforeSend && typeof config.value.onBeforeSend == "function") {
                const result = config.value.onBeforeSend(data)
                if (result.status) {
                    data = result.data;
                } else {
                    return;
                }
            }

            msgs.value.push({
                my: true,
                message: data,
                questions: []
            })
            msgs.value.push({
                my: false,
                message: ``,
                questions: []
            })
            const param = toRaw(config.value.param);
            param['conversation_id'] = conversation.value.id;
            param['query'] = data;
            param['user'] = "u";
            param['stream'] = true;
            param['chat_history'] = mergeHistory(msgs.value);
            param['bot_id'] = _getBotId()
            _setIsAnswering(true)
            askCozeApi(param).then(([chatId, resp]) => {
                !!chatId && _uploadCozeData(chatId, resp)
            });
            autoScroll()
        })
    }
}

const _setIsAnswering = (status) => {
    isAnswering.value = status;
    refInput.value.setIsAnswering(status)
    g.emitter.emit("set_answering", status);
}

const _uploadCozeData = (chatId, resp) => {
    const param = splitRawMessage(resp);
    if (chatId && chatId != "chatid") {
        uploadCozeMsg(chatId, param).then(resp => {
        })
    }
}

const onReAnswer = () => {
    if (lastQuestion == '') {
        lastQuestion = msgs.value[msgs.value.length - 2].message.replace("\n", '');
    }
    refInput.value.setQuestion(lastQuestion)
    refInput.value.onSend()
}

const autoScroll = () => {
    nextTick(() => {
        if (chatListDom) {
            chatListDom.scrollTop = chatListDom.scrollHeight;
        }
    })
}

const keyEnter = (event, ctrl) => {
    onSend();
}

const updateMsg = () => {
    if (updateing) {
        return
    }
    const animateText = (line) => {
        return new Promise(resove => {
            if (line.length == 0) {
                resove()
            } else {
                let currentIndex = 0;
                const interval = setInterval(() => {
                    const new_item = line.slice(currentIndex, currentIndex + 1)[0];
                    let line_obj = {}
                    if (new_item && new_item.indexOf('{"') > -1) {
                        try {
                            line_obj = JSON.parse(new_item)
                            if (line_obj.msg_type) {
                                msg_type = line_obj.msg_type
                            }
                        } catch (e) { }
                    }
                    if (msg_type == "") {
                        msgs.value[msgs.value.length - 1].message += new_item;
                    } else if (msg_type == "generate_answer_finish") {
                        if (!line_obj.msg_type) {
                            msgs.value[msgs.value.length - 1].questions.push(toRaw(new_item));
                        }
                    } else {
                        console.log('error unkown type', msg_type, new_item)
                    }
                    currentIndex++;
                    if (currentIndex >= line.length) {
                        clearInterval(interval);
                        resove()
                    }
                }, 30);
            }
        })
    }

    const fn = async () => {
        while (new_msgs.length > 0) {
            updateing = true;
            await animateText(new_msgs.shift())
            autoScroll();
            if (new_msgs.length == 0) {
                updateing = false;
                setTimeout(() => {
                    updateMsg()
                }, 200)
            }
        }
    }
    fn()
}


const setConversation = (data) => {
    conversation.value = data;
    // isAnswering.value = false;
    queryHistory()
}

const addListener = () => {
    g.emitter.on('coze_resp', (data) => {
        new_msgs.push(data)
        updateMsg()
    })
    g.emitter.on('coze_resp_done', () => {
        setTimeout(() => {
            _setIsAnswering(false);
        }, 500)
    })
    chatListDom = document.getElementsByClassName("chat_wrap")[0];
    autoScroll();
}

const queryHistory = () => {
    const convId = conversation.value.id;
    if (convId) {
        getChatHistory(convId).then(resp => {
            if (resp.code == 0) {
                addHelloMsg()
                msgs.value = [...msgs.value, ...cozeApi2history(resp.data)]
                emit("callback", "after_get_history", resp.data)
            }
        })
    } else {
        addHelloMsg()
    }
}

const addMsg = (msg) => {
    msgs.value.push(msg)
}

const getMsgs = () => {
    return msgs.value;
}

const setMsgs = (msgs) => {
    msgs.value = msgs;
}

const updateConfig = (key, value) => {
    config.value[key] = value
}

const init = (cfg) => {
    config.value = cfg;
    if (config.value.title) {
        refInput.value.init({ ...cfg, ...{ show_clear: false, show_stop: true } })
    } else {
        refInput.value.setDisableApp(true)
        g.emitter.emit('show_msg_box', { message: '无权限访问此应用', time: 3, need_icon: true })
    }
    addListener()
}

onUnmounted(() => {
    g.emitter.off('coze_resp');
    g.emitter.off('coze_resp_done');
})

defineExpose({
    refBtnReAnswer, SendIcon, config, MsgMy, MsgAi, cbInput, keyEnter,
    onReAnswer, BtnReAnswer, isFull, setFull, setConversation, updateConfig,
    queryHistory, init, config, getMsgComponent, addMsg, getMsgs, setMsgs
})
</script>

<style lang="scss">
@import url("chat.scss");
</style>
