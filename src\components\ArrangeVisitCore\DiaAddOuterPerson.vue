<template>
  <el-dialog v-model="dialogVisible" title="添加外部参会人" width="500" :before-close="handleClose"
    class="av_add_outer_person_dia">
    <AddContactOuterForm ref="refAddContactOuterForm" @cancel="handleClose" @submit="onSubmit" />
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['callback'])
const dialogVisible = ref(false)
const refAddContactOuterForm = ref()

const show = (p) => {
  dialogVisible.value = true
  nextTick(() => {
    refAddContactOuterForm.value.show(p.id)
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

const onSubmit = (data) => {
  emit('callback', "add_person", data)
  handleClose()
}

defineExpose({
  show, refAddContactOuterForm
})
</script>

<style lang="scss">
.av_add_outer_person_dia {
  .vbp_box {
    margin-bottom: 12px;

    .vbp_title {
      margin-bottom: 12px;

      span {
        color: #F56C6C;
      }
    }
  }
}
</style>
