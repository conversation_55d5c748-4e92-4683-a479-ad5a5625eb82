<template>
  <div class="ai-evaluation" v-ai-tip="'bottom-right'">
    <div class="content">
      <PieChartWithTitle title="目标达成详情" sub-title="点击饼图图例可跳转查看沟通详情" :chart-data="chartData" />
    </div>
  </div>
</template>

<script setup>
import PieChartWithTitle from '@/app_client/components/TeamBoardCore/components/PieChartWithTitle.vue'
let achievementNum = ref(0)
let unAchievementNum = ref(0)
let unDetermineNum = ref(0)
let unResult = ref(0)
const chartData = computed(() => [
  {
    value: achievementNum.value,
    name: '达成',
    type: 'achievement',
    color: '#67C23A'
  },
  {
    value: unAchievementNum.value,
    name: '未达成',
    type: 'unAchievement',
    color: '#F56C6C'
  },
  {
    value: unDetermineNum.value,
    name: '无法判定',
    type: 'unDetermine',
    color: '#909399'
  },
  {
    value: unResult.value,
    name: '结果未评估',
    type: 'unResult',
    color: '#D9D9D9'
  }
])

const init = (data, periodType) => {
  const totalNum = data.totalVisitCount
  achievementNum.value = data.visitTargetAchievedCount
  unAchievementNum.value = data.visitTargetNotAchievedCount
  unDetermineNum.value = data.visitTargetNoCertainAchievedCount
  unResult.value = totalNum - achievementNum.value - unAchievementNum.value - unDetermineNum.value
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.ai-evaluation {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  border: 1px solid #E4E7ED;

  .header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .el-icon {
      img {
        width: 20px;
        height: 20px;
      }
    }

    .ptitle {
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #262626;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      background: linear-gradient(230.96687491132352deg, #4CD2F4 0%, #7A1EFF 100%);
      -webkit-text-fill-color: transparent;
      -webkit-background-clip: text;
    }
  }

  .content {
    height: 300px;

    .description {
      color: #333;
      line-height: 1.5;
      margin-bottom: 16px;
      font-size: 14px;
    }
  }
}
</style>