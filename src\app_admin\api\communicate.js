import { getHttp } from "@/js/request.js";

// 获取能力模型列表
export const getAbilityModelList = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            modelName: "适用能力模型",
                            relatedAbilities: "1. 沟通能力\n2. 问题分析能力\n3. 信息整理能力",
                            testScore: "80 / 100",
                            creator: "李小明",
                            createTime: "2025-07-21 10:30",
                            status: 1
                        }
                    ],
                    totalNum: 1,
                }
            });
        }, 500);
    });
};

// 删除能力模型
export const deleteAbilityModel = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};

// 添加能力项
export const addAbilityModel = (params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "添加成功"
            });
        }, 300);
    });
};

// 更新能力项
export const updateAbilityModel = (params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "更新成功"
            });
        }, 300);
    });
};

// 获取能力项列表
export const getAbilityListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            abilityName: "销售沟通能力",
                            behaviorStandard: "1. 保持专业化沟通：面对客户的需求能够准确理解并给予专业回应\n2. 具备倾听技巧：能够耐心倾听客户需求，准确把握客户意图\n3. 灵活应对异议：遇到客户异议时，能够冷静分析并给出合理解决方案",
                            creator: "王佳佳",
                            createTime: "2025-07-19 14:30",
                            status: 1
                        },
                        {
                            id: 2,
                            abilityName: "问题解决",
                            behaviorStandard: "1. 快速定位问题：能够快速识别问题的核心所在，避免在表面问题上浪费时间\n2. 逻辑思维清晰：问题分析过程中能够运用逻辑思维，条理清晰地分析问题\n3. 专业知识运用：能够运用专业知识和经验，为客户提供有效的解决方案",
                            creator: "李明",
                            createTime: "2025-07-18 16:20",
                            status: 1
                        },
                        {
                            id: 3,
                            abilityName: "问题分析能力",
                            behaviorStandard: "1. 快速定位关键信息：能够从大量信息中快速筛选出关键信息进行分析\n2. 多角度思考问题：从不同角度分析问题，避免思维局限性，全面考虑各种因素\n3. 清晰判断优先级：能够根据问题的紧急程度和重要性，合理安排处理顺序",
                            creator: "张三",
                            createTime: "2025-07-17 10:15",
                            status: 1
                        }
                    ],
                    totalNum: 3,
                }
            });
        }, 500);
    });
};

// 获取任务项列表
export const getTaskListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            taskName: "销售能力",
                            taskDescription: "1. 沟通能力\n2. 问题分析能力\n3. 信息整理能力",
                            creator: "李小明",
                            createTime: "2025-07-21 10:30",
                            status: 1
                        }
                    ],
                    totalNum: 1,
                }
            });
        }, 500);
    });
};

// 获取任务模型列表
export const getTaskModelList = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            modelName: "任务名称",
                            relatedTasks: "1. 保持专业化沟通：面对客户的需求能够准确理解并给予专业回应\n2. 具备倾听技巧：能够耐心倾听客户需求，准确把握客户意图\n3. 灵活应对异议：遇到客户异议时，能够冷静分析并给出合理解决方案",
                            creator: "王佳佳",
                            createTime: "2025-07-19 14:30",
                            status: 1
                        },
                        {
                            id: 2,
                            modelName: "问题解决",
                            relatedTasks: "1. 快速定位问题：能够快速识别问题的核心所在，避免在表面问题上浪费时间\n2. 逻辑思维清晰：问题分析过程中能够运用逻辑思维，条理清晰地分析问题\n3. 专业知识运用：能够运用专业知识和经验，为客户提供有效的解决方案",
                            creator: "李明",
                            createTime: "2025-07-18 16:20",
                            status: 1
                        },
                        {
                            id: 3,
                            modelName: "问题分析能力",
                            relatedTasks: "1. 快速定位关键信息：能够从大量信息中快速筛选出关键信息进行分析\n2. 多角度思考问题：从不同角度分析问题，避免思维局限性，全面考虑各种因素\n3. 清晰判断优先级：能够根据问题的紧急程度和重要性，合理安排处理顺序",
                            creator: "张三",
                            createTime: "2025-07-17 10:15",
                            status: 1
                        }
                    ],
                    totalNum: 3,
                }
            });
        }, 500);
    });
};

// 删除任务模型
export const deleteTaskModel = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};

// 获取维度列表
export const getDimensionListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            name: '会议记录',
                            tag: '系统',
                            tagType: 'primary'
                        },
                        {
                            id: 2,
                            name: '分级摘要',
                            tag: '系统',
                            tagType: 'primary'
                        },
                        {
                            id: 3,
                            name: '问答回顾',
                            tag: '系统',
                            tagType: 'primary'
                        },
                        {
                            id: 4,
                            name: '思维导图',
                            tag: '系统',
                            tagType: 'primary'
                        }
                    ],
                    totalNum: 4,
                }
            });
        }, 500);
    });
};

// 获取模板列表
export const getTemplateListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            templateName: "通用模板",
                            dimensions: "1. 会议记录\n2. 思维导图\n3. 分级摘要",
                            creator: "李小明",
                            createTime: "2025-07-21 10:30",
                            status: 1
                        }
                    ],
                    totalNum: 1,
                }
            });
        }, 500);
    });
};

// 删除模板
export const deleteTemplate = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};// 获取分析维度列表

export const getAnalysisDimensionListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            name: '联系人主张',
                            tag: '编辑',
                            tagType: 'success',
                            creator: '张三',
                            createTime: '2025-07-23 15:30'
                        },
                        {
                            id: 2,
                            name: 'BANT',
                            tag: '系统',
                            tagType: 'primary'
                        },
                        {
                            id: 3,
                            name: 'MEDDIC',
                            tag: '系统',
                            tagType: 'primary'
                        }
                    ],
                    totalNum: 3,
                }
            });
        }, 500);
    });
};

// 获取分析模板列表
export const getAnalysisTemplateListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            templateName: "通用模板",
                            dimensions: "1. 会议记录\n2. 思维导图\n3. 分级摘要",
                            creator: "李小明",
                            createTime: "2025-07-21 10:30",
                            status: 1
                        }
                    ],
                    totalNum: 1,
                }
            });
        }, 500);
    });
};

// 删除分析模板
export const deleteAnalysisTemplate = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};// 获取流程列表
export const getProcessListData = (params) => {
    // 模拟数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            processName: "软件销售",
                            communicationSteps: "1. 客户开发\n2. 产品演示\n3. 报价沟通\n4. 合同确认",
                            type: "销售",
                            creator: "李小明",
                            createTime: "2025-07-21 10:30",
                            status: 1
                        }
                    ],
                    totalNum: 1,
                }
            });
        }, 500);
    });
};

// 删除流程
export const deleteProcess = (id) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                message: "删除成功"
            });
        }, 300);
    });
};