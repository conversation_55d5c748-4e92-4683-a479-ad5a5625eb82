<template>
  <div class="ar_remark_wrap">
    <div class="ar_comments" v-show="formattedComments.length > 0">
      <template v-for="(comment, index) in formattedComments" :key="index">
        <div class="ar_comment">
          <div class="ar_title">
            {{ comment.h1 }}
          </div>
          <div class="ars flex-row">
            <div class="ar_icon">
              <component :is="comment.markIcon" />
            </div>
            <div class="ar_label">{{ comment.h2 }}</div>
          </div>
          <div class="ar_sub" v-show="comment.type == 'risk'">
            {{ comment.h3 }}
          </div>
          <div class="ar_remark">
            {{ comment.h4 }}
          </div>
          <div class="ar_bottom flex-row" v-show="comment.type == 'todo'">
            <div>截止时间: {{ comment.h5 }}</div>
            <div class="line"></div>
            <div>执行人: {{ comment.h6 }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, markRaw, onMounted } from "vue";
import GoodIcon from "./icons/good.vue";
import BadIcon from "./icons/bad.vue";
import RiskHighIcon from "./icons/risk_high.vue";
import RiskMiddleIcon from "./icons/risk_middle.vue";
import RiskLowIcon from "./icons/risk_low.vue";
import TodoIcon from "./icons/todo.vue";
import VsIcon from "./icons/vs.vue";
import { getAnalyseTime, getH1 } from "@/app_postmeet/tools/sale_report.js";

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  filter: {
    type: Object,
    required: true,
  },
});

// 获取图标
const getIcon = (type, data) => {
  const row = data.data;
  let value = "";
  switch (type) {
    case "attitude":
      value = data.label == "积极反馈" ? GoodIcon : BadIcon;
      break;
    case "todo":
      value = TodoIcon;
      break;
    case "competitor":
      value = VsIcon;
      break;
    case "risk":
      const level = row["风险等级"];
      if (level == "高") {
        value = RiskHighIcon;
      } else if (level == "中") {
        value = RiskMiddleIcon;
      } else if (level == "低") {
        value = RiskLowIcon;
      }
      break;
  }
  return markRaw(value);
};

// 获取标题
const getH2 = (type, item) => {
  switch (type) {
    case "attitude":
    case "competitor":
    case "risk":
      return item.label;
    case "todo":
      return "待办";
    default:
      return "";
  }
};

// 获取详细内容
const getH4 = (type, row) => {
  switch (type) {
    case "attitude":
      return row["理由"] || "";
    case "competitor":
      return row["上下文分析"]?.["关注点"] || "";
    case "risk":
      return row["风险识别原因"] || "";
    case "todo":
      return row["待办内容"] || "";
    default:
      return "";
  }
};

// 格式化评论数据
const formattedComments = computed(() => {
  //   console.log("props.item:", props.item); // 调试用
  if (!props.item?.list?.length) return [];
  const filter = props.filter;
  return props.item.list
    .filter(
      (comment) =>
        (comment.type == filter.type && comment.label == filter.label) ||
        filter.type == ""
    )
    .map((comment) => {
      return {
        ...comment,
        h1: getH1(comment.type, comment.data),
        h2: getH2(comment.type, comment),
        h3: comment.data["风险描述"] || "",
        h4: getH4(comment.type, comment.data),
        h5: comment.data["截止时间"] || "待定",
        h6: comment.data["执行人"] || "",
        time: comment.time,
        markIcon: getIcon(comment.type, comment),
        vtime: getAnalyseTime(comment.type, comment.data),
      };
    });
});
</script>

<style lang="scss" scoped>
.ar_remark_wrap {
  width: 355px;
  padding: 8px 12px;
  .ar_comments {
    width: 100%;
    background: #eff2fc;
    border-radius: 8px;
    margin-top: 18px;
    padding: 12px;
    .ar_comment {
      &:not(:last-child) {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e8e8e8;
      }
    }
  }

  .ar_title {
    border-left: 2px solid #bfbfbf;
    padding: 3px 0 3px 10px;
    margin: 0 0 12px 0;
    height: 20px;
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .ars {
    margin: 6px 0;

    .ar_icon {
      margin: 0 8px 0 0;
    }

    .ar_label {
      height: 22px;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
    }
  }

  .ar_sub {
    font-weight: 500;
    font-size: 14px;
    color: #262626;
    line-height: 22px;
    margin-bottom: 7px;
  }

  .ar_remark {
    font-size: 14px;
    color: #262626;
    line-height: 22px;
  }

  .ar_bottom {
    padding: 3px 0;
    margin: 12px 0;
    height: 20px;
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;

    .line {
      width: 1px;
      height: 14px;
      background: #bfbfbf;
      margin: 3px 16px 0px 16px;
    }
  }
}

// 移除fixed定位，改用相对定位
.subtitles-list-right {
  position: relative;
  margin-left: 16px;
}
</style>
