<template>
    <div class="loading-stop">
        <div class="loading_content">
            <h1 class="title">AI处理中</h1>
            <p class="subtitle">预计耗时5～10分钟，请稍后</p>

            <div class="loading-step">
                <template v-for="(step, index) in steps" :key="index">
                    <div class="step-item">
                        <div class="step-node" :class="getNodeStatus(index)">
                            <div class="step-number" v-if="activeStep <= index">{{ index + 1 }}</div>
                            <div class="step-icon" v-else>✓</div>
                        </div>
                        <div class="step-label">{{ step.label }}</div>
                    </div>
                    <div v-if="index < steps.length - 1" class="step-line" :class="{ active: activeStep >= index + 1 }">
                    </div>
                </template>
            </div>
        </div>

        <div class="quote-container">
            <p class="quote-text">"{{ todayQuote.quote }}"</p>
            <div class="quote-source flex-row">
                <p class="author">——{{ todayQuote.author }}</p>
                <p class="book">《{{ todayQuote.source }}》</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { salesQuotes } from '@/app_postmeet/tools/const_value.js'

// 定义步骤数据
const steps = [
    { label: '沟通内容' },
    { label: '总结内容' },
    { label: '分析内容' },
    { label: '辅导内容' }
]

const activeStep = ref(0) // 当前进行到第三步（从0开始计数）

// 获取节点状态
const getNodeStatus = (stepIndex: number) => {
    if (activeStep.value > stepIndex) return { complete: true } // 已完成
    if (activeStep.value === stepIndex) return { active: true } // 进行中
    return {} // 未开始
}

// 获取今天是星期几
const getDayOfWeek = () => {
    const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    return days[new Date().getDay()]
}

// 获取今天的名言，如果没有对应的就随机选一个
const todayQuote = computed(() => {
    const today = getDayOfWeek()
    const todaysQuote = salesQuotes.filter(q => q.day === today)

    // 如果没找到今天的名言（比如周末），随机返回一个
    const randomIndex = Math.floor(Math.random() * todaysQuote.length)
    return todaysQuote[randomIndex]
})

onMounted(() => {
    // 模拟加载过程
    g.emitter.on("update_report_complete_process", (complete) => {
        activeStep.value = Math.trunc(100 * complete / 25);
    })
})

</script>

<style lang="scss" scoped>
.loading-stop {
    padding: 40px 20px;
    text-align: center;

    .loading_content {
        width: 100%;
        height: 202px;
        background: #F9FAFC;
        border-radius: 8px;
        padding-top: 12px;
    }

    .title {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .subtitle {
        font-size: 14px;
        color: #666;
        margin-bottom: 40px;
    }

    .loading-step {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 40px;
        position: relative;

        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .step-node {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #DCDFE6;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: #909399;
            font-size: 14px;

            &.active {
                border-color: transparent;
                background: #436BFF;
                color: #fff;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -6px;
                    left: -6px;
                    right: -6px;
                    bottom: -6px;
                    z-index: -1;
                    background: conic-gradient(#436BFF 0deg 90deg, transparent 90deg 360deg);
                    border-radius: 50%;
                    animation: rotate 2s linear infinite;
                }
            }


            @keyframes rotate {
                from {
                    transform: rotate(0deg);
                }

                to {
                    transform: rotate(360deg);
                }
            }

            &.complete {
                border-color: #436BFF;
                background: #fff;
                color: #436BFF;
            }
        }

        .step-line {
            flex: 1;
            height: 1px;
            background: #DCDFE6;
            margin: 0 4px;
            position: relative;
            top: -11px;

            &.active {
                background: #436BFF;
            }
        }

        .step-label {
            font-size: 14px;
            color: #606266;
            white-space: nowrap;
        }

        .step-number {
            font-weight: 500;
        }

        .step-icon {
            font-size: 14px;
            font-weight: bold;
        }
    }

    .quote-container {
        margin-top: 60px;
        text-align: center;
        width: 100%;
        height: 156px;
        background: #F9FAFC;
        border-radius: 8px;
        padding-top: 24px;
    }

    .quote-text {
        font-size: 18px;
        color: #333;
        margin-bottom: 16px;
        font-weight: 500;
    }

    .quote-source {
        color: #666;
        font-size: 14px;
        justify-content: center;
    }

    .author {
        margin-bottom: 4px;
        font-weight: 500;
        font-size: 14px;
    }

    .book {
        color: #8C8C8C;
    }
}
</style>
