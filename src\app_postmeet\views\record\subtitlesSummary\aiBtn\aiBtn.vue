<template>
  <div class="ai_btn_wrap">
    <div class="icon" @click="onClick" @mouseenter="onMouseEnter(true)" @mouseleave="onMouseEnter(false)">
      <AIBtnIcon />
    </div>
    <div class="ai_hint" v-show="isShowHint">
      <div class="ah1">Ask SalesMate</div>
      <div class="ah2">
        我是您的专属AI助理，有关本次拜访录制内容的问题都可以咨询我，快来试试吧。
      </div>
    </div>
    <div class="ai_chat_box" v-show="isShowChat">
      <div class="header flex-row">
        <div class="left flex-row">
          <img :src="getOssUrl('aibot.png')" />
          <div class="ai_title">AI助理</div>
        </div>
        <img :src="getOssUrl('close.svg')" class="close" @click="isShowChat = false" />
      </div>
      <div class="chat_meet_wrap">
        <RightChat ref="refChat" @callback="cbRight">
          <template #input_top>
            <slot name="input_top" />
          </template>
          <template #input_inside_top>
            <slot name="input_inside_top" />
          </template>
        </RightChat>
      </div>
    </div>
  </div>
</template>

<script setup>
import AIBtnIcon from "@/app_postmeet/icons/ai_btn.vue";
import RightChat from "./rightChat.vue";
import { getOssUrl } from "@/js/utils.js";
import { onMounted } from "vue";

const isShowHint = ref(false);
const isShowChat = ref(false);
const refChat = ref();

const onMouseEnter = (isShow) => {
  isShowHint.value = isShow;
};
const onClick = () => {
  isShowChat.value = true;
  nextTick(() => {
    refChat.value.init();
  });
};

const cbRight = (action, data) => {
  //回调
};

onMounted(() => {
  g.emitter.on('postmeet_show_ai_chat', status => {
    if (!isShowChat.value) {
      onClick()
    } else {
      isShowChat.value = false;
    }
  })
});

defineExpose({
  AIBtnIcon,
  isShowHint,
  isShowChat,
  RightChat,
  cbRight,
  onMouseEnter,
  onClick,
});

</script>

<style lang="scss">
.ai_btn_wrap {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 500;

  .icon {
    position: absolute;
    right: 61px;
    bottom: 55px;
    width: 40px;
    height: 40px;
    z-index: 2;
    cursor: pointer;
  }

  .ai_hint {
    position: absolute;
    right: 96px;
    bottom: 19px;
    width: 253px;
    height: 66px;
    padding: 16px 20px;
    background: linear-gradient(180deg, #f7f8ff 0%, #eef9fe 100%);
    box-shadow: 0px 2px 25px 0px rgba(30, 27, 113, 0.17);
    border-radius: 12px;

    .ah1 {
      width: 129px;
      height: 22px;
      font-weight: 500;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
    }

    .ah2 {
      width: 255px;
      height: 44px;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
    }
  }

  .ai_chat_box {
    width: 400px;
    height: calc(100vh - 200px);
    border-radius: 16px;
    z-index: 999;
    position: fixed;
    right: 0;
    bottom: 0;
    padding: 24px 24px 0 24px;
    background-image: linear-gradient(135deg, #efe9ff 0, #f1f4fd 50%, #d4e8ff 100%),
      linear-gradient(135deg, #c778ff, #76a2ff, #43dbff);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    // pointer-events: none;
    border: 1px solid transparent;
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.12);
    background-origin: padding-box, border-box;
    border-radius: 12px;
    background-clip: padding-box, border-box;

    .header {
      justify-content: space-between;

      .left {
        align-items: center;

        img {
          width: 36px;
          height: 36px;
          margin-right: 10px;
        }

        .ai_title {
          height: 22px;
          font-weight: 500;
          font-size: 16px;
          color: #262626;
        }
      }

      .close {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
