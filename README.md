# 绚星销售助手 
node：18

## 项目使用模版的是
https://github.com/electron-vite/electron-vite-vue

## 安装依赖
```
yarn bootstrap
```

## 项目启动
```
# 启动web项目
yarn dev

# 启动Electron项目
yarn edev

# 打包web项目
yarn build:tf   

# 打包Electron项目,绿色版本
yarn ebuild:tf_green  

# 打包Electron项目,签名版本
yarn ebuild:tf_sign  

# 打包Electron项目,只打包electron index.html资源zip包
yarn ebuild:tf_zip  

# 打包Electron项目,正式版本
yarn ebuild:prod  

# 打包Electron项目,正式版本,只打包electron index.html资源zip包
yarn ebuild:prod_zip  

```

## 复制网站缓存

```
copy(`const local = ${JSON.stringify(localStorage)};
for (const key in local) {
 localStorage.setItem(key, local[key])
}`);

```

## 分支说明：

* electron 是完整代码，本地开发一般在这版本上进行
* electron_release 是electron的正式发布版本
* dev 是为发布web测试环境准备的，此版本里不需要下载electron
* release 是多云web的正式发布版本, 目前只是跳转到x-mate域名，没有业务代码了
* xmate 是产线web版本分支,基于dev分支，不需要去掉public静态文件


## 常用目录

升级缓存目录
```
#MAC
~/Library/Application Support/Caches/novaguidepc-updater/pending/

# Win 使用安装版本升级缓存目录：
%appdata%\..\Local\novaguidepc-updater

```

用户日志目录
```
#Win
%appdata%\novaguidepc

#Mac
~/Library/Application Support/novaguidepc
```

Electron开发环境安装缓存目录

```
#Win
%appdata%\..\Local\electron\Cache

#Mac
~/Library/Caches/electron
```


## 解压asar包
```
#win
cd C:\Program Files\novaguidepc\resources
#mac
cd /Applications/novaguidepc.app/Contents/Resources

asar extract app.asar app
```


## mac 启动日志
```
/Applications/绚星销售助手.app/Contents/MacOS/绚星销售助手 --enable-logging=file --log-file=~/Desktop/lingxi.log --log-level=2 --v=2
```