import packageJson from "../../package.json";
import { isMac, checkInElectron, getClientType, getStore } from "./utils.js";
import { ConstValue } from "./const_value.js"

const isElectron = checkInElectron();


const getViteWebUrl = () => {
  if (feConfig.apiEnv == 'prod') {
    return getStore(ConstValue.keyProdApihost, import.meta.env.VITE_WEB_URL)
  } else {
    return import.meta.env.VITE_WEB_URL
  }
};

const getPublicPath = () => {
  if (isElectron) {
    return getViteWebUrl();
  } else {
    // 需要去掉最后一个 /， 否则会报错
    return `${location.origin}${location.pathname.replace(/\/$/, "")}`;
  }
};

const publicPath = getPublicPath();

//改这段一定要小心，electron（开发和打包后）,web,多云登录，都需要测试下

const getSsoReturnUrl = () => {
  if (isElectron) {
    return `${publicPath}/thirdLogin.html`;
  } else {
    return `${location.origin}${location.pathname}thirdLogin.html`;
  }
};

const meetApiHost = import.meta.env.VITE_LX_API_HOST;

const getPostmeetH5 = () => {
  let base_url = ''
  if (location.origin.includes("localhost") || location.origin.includes("127.0.0.1")) {
    base_url = import.meta.env.VITE_H5_URL;
  } else {
    base_url = getViteWebUrl()
    if (!base_url) {
      base_url = location.origin
    }
  }
  return base_url.replace("www.", "");
};

// 项目的共用配置
export default {
  version: packageJson.version,
  publicPath,
  isElectron,
  clientType: getClientType(),
  isMac: isMac(),
  pptMaxSizeMb: 200,
  productCode: "nova_guide",
  meetApiHost,
  ssoReturnUrl: getSsoReturnUrl(),
  postmeet_h5_customer: `${getPostmeetH5()}/lingxi-m`,
  postmeet_h5_record: `${getPostmeetH5()}/postmeet-xmate-h5`,
  notice_h5_list: `${getPostmeetH5()}/lingxi-m/index.html#/msgCenter/msgList`,
  aiApi: `${meetApiHost}/aibox`,
  defaultSSODomain: "",
  apiEnv: feConfig.apiEnv,
  isDev: location.href.indexOf('http') > -1 && process.env.NODE_ENV === "development",

  downloadPath: import.meta.env.VITE_DOWNLOAD_PATH,
};
