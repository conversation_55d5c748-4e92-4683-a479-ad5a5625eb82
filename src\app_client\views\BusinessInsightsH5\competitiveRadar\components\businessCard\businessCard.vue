<template>
    <div class="business-card" v-loading="loading">
        <listMenu></listMenu>
        <div class="business-card-list">
            <feedback-list :list="competitiveSelectRadarData" :is-finished="finished" :is-loading="loading"
                @load-more="handleLoadMore" />

        </div>
    </div>

</template>

<script setup>
import listMenu from "./listMenu.vue";
import feedbackList from "./feedbackList.vue";
import { onMounted } from "vue";
const store = g.clientBiStore
const totalNum = ref(0)
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)
const competitiveSelectRadarData = ref([])
const periodType = computed(() => store.periodType || '')
const crFilterCondition = computed(() => store.crFilterCondition || {})

const goDetail = (item) => {
    g.clientStore._openMeetRecord(item.conferenceId, 'analyse', 'attitude')
}
const getStatusTagObj = (status) => {
    return g.clientBiStore.getStatusTagObj(status);
}
const checkAndLoadMore = () => {
    nextTick(() => {
        if (document.querySelector('.business-card-list').scrollHeight <= document.querySelector('.business-card-list').clientHeight && !finished.value && !loading.value) {
            queryData(page.value + 1)
        }
    })
}
const queryData = async (pageNum) => {
    try {
        loading.value = true
        const param = {
            pageNumber: pageNum || page.value,
            pageSize: pageSize.value,
            competitorId: store.crFbCompetitorId,
            periodType: periodType.value,
            ...crFilterCondition.value
        }
        const response = await g.clientBiStore.getCompetitorRadarData(param) || {}
        const list = response.datas || []
        if (pageNum === 1) {
            competitiveSelectRadarData.value = list
        } else {
            competitiveSelectRadarData.value.push(...list)
        }
        // 判断是否还有更多数据
        if (list.length < pageSize.value) {
            finished.value = true
        }
        page.value = pageNum || page.value

    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false
        // checkAndLoadMore()

    }
}

// 处理加载更多
const handleLoadMore = () => {
    if (!loading.value && !finished.value) {
        queryData(page.value + 1)
    }
}




watch(
    () => [store.crFbCompetitorId, periodType.value, crFilterCondition.value],
    async (newValue) => {
        page.value = 1
        finished.value = false
        queryData(1)
    }, {
    immediate: true,
    deep: true
}
)
</script>
<style lang="scss" scoped>
.business-card {
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    margin-bottom: 16px;

    .business-card-list {
        padding: 0 16px 16px 16px;
        height: 300px;
        // height: 100px;
        overflow-y: auto;
    }

    .custom-tag {
        display: inline-block;
        background: #e6f7e6;
        color: #13c26b;
        border-radius: 2px;
        padding: 1px 8px;
        font-size: 12px;
        // margin-left: 8px;
        line-height: 22px;

        &.positive {
            color: #04CCA4;
            background: rgba(4, 204, 164, 0.1);
        }

        &.middle {
            color: #595959;
            background: #F5F5F5;
        }

        &.neutral {
            color: #FF6B3B;
            background: rgba(255, 107, 59, 0.1);
        }
    }


    .custom-card-item {
        background: #F9FAFC;
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 16px;
        box-sizing: border-box;


        .custom-card-item-t {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-weight: 400;
            font-size: 14px;
            color: #8C8C8C;
            line-height: 24px;

            .customer-name {
                width: calc(100% - 36px);
            }


            .detail-link {
                color: #436BFF;
                margin-left: 8px;
                cursor: pointer;
                font-size: 14px;
            }


        }

        .custom-card-item-c {
            font-weight: 700;
            font-size: 14px;
            color: #262626;
            line-height: 24px;
            text-align: left;
            margin: 8px 0;
        }

        .footer-info {
            display: flex;
            gap: 8px;
            font-weight: 400;
            font-size: 14px;
            color: #8C8C8C;
            line-height: 24px;

            box-sizing: border-box;
            flex-wrap: wrap;

            span {
                word-break: break-all;
                white-space: normal;
                max-width: 100%;
            }

            span:not(:last-child)::after {
                content: '';
                display: inline-block;
                width: 1px;
                height: 14px;
                background: #BFBFBF;
                margin-left: 8px;
                vertical-align: middle;
                line-height: 24px;
            }
        }
    }



}
</style>