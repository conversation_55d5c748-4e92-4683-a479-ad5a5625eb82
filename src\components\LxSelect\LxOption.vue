<template>
    <div class="lx-option" @click="handleClick" :class="{ 'is-selected': isSelected }">
        <slot>
            {{ label }}
        </slot>
    </div>
</template>

<script setup>
import { inject, computed } from 'vue'

const props = defineProps({
    label: {
        type: [String, Number],
        required: true
    },
    value: {
        type: [String, Number],
        required: true
    }
})

// 注入父组件提供的数据和方法
const { selectedValue, handleSelect } = inject('lxSelect')

// 计算当前选项是否被选中
const isSelected = computed(() => selectedValue === props.value)

const handleClick = () => {
    handleSelect(props.value, props.label)
}
</script>

<style lang="scss" scoped>
.lx-option {
    padding: 0 12px;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;

    &:hover {
        background-color: #f5f7fa;
    }

    &.is-selected {
        color: #409eff;
        font-weight: bold;
        background-color: #f5f7fa;
    }

}
</style>