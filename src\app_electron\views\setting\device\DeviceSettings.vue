<template>
    <div class="device-settings">
        <div class="section-header">工牌设备绑定</div>
        <div class="device-list-section" v-if="deviceInfo">
            <div class="device-info-card">
                <div class="dbs flex-row">
                    <div class="flex-row">
                        <div class="bind-status">已绑定工牌</div>
                        <div :class="`device-status ${localDevice.online ? 'online' : 'offline'}`"
                            v-if="localDevice.batteryPer >= 0">{{ localDevice.online
                                ?
                                '在线' : '离线' }}</div>
                    </div>
                    <div v-if="localDevice.batteryPer >= 0">
                        <BatteryIndicator :battery-per="localDevice.batteryPer" />
                    </div>
                </div>
                <div class="device-sn">
                    序列号: {{ deviceInfo }}
                </div>
                <div class="bind-tip">每位用户仅可绑定一个工牌设备</div>
                <div class="action-buttons">
                    <ButtonChange @callback="onCallback" />
                    <ButtonUnbind :device-sn="deviceInfo" @callback="onCallback" />
                </div>
            </div>
        </div>
        <div class="device-bind-section" v-else>
            <div class="device-status">
                <div class="status-text">暂无绑定设备</div>
                <ButtonAdd @callback="onCallback" />
            </div>
            <div class="bind-instructions">
                <div class="instruction-title">绑定说明:</div>
                <div class="instruction-item">1. 点击"添加设备"按钮</div>
                <div class="instruction-item">2. 输入工牌上的SN序列号</div>
                <div class="instruction-item">3. 确认绑定即可完成</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import ButtonChange from './ButtonChange.vue';
import ButtonUnbind from './ButtonUnbind.vue';
import ButtonAdd from './ButtonAdd.vue';
import BatteryIndicator from '@/app_electron/components/BatteryIndicator.vue';

const deviceInfo = ref();
const localDevice = ref({
    batteryPer: -1
})

const getDeviceInfo = async () => {
    localDevice.value = await g.meetStore.getCardDeviceStatus()
}


const onCallback = (type, sn) => {
    console.log(type, sn);
    if (type === 'add') {
        deviceInfo.value = sn;
    } else if (type === 'change') {
        deviceInfo.value = sn;
    } else if (type === 'unbind') {
        deviceInfo.value = null;
    }
    g.appStore.upateDeviceInfo(deviceInfo.value);
    getDeviceInfo();
}

onMounted(async () => {
    //C16F2448000283,C16F2448000027,C16F2448000223
    const user = g.appStore.user;
    deviceInfo.value = user.audioDeviceCode || '';
    getDeviceInfo();
    console.log('audioDeviceCode', deviceInfo.value)
})

</script>

<style lang="scss" scoped>
.device-settings {

    .section-header {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .device-bind-section {
        background: #F7F8FA;
        border-radius: 8px;
        padding: 20px;

        .device-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;

            .status-text {
                color: #999;
            }

            .bind-button {
                background-color: #436BFF;
            }
        }

        .bind-instructions {
            .instruction-title {
                margin-bottom: 12px;
                font-weight: 500;
            }

            .instruction-item {
                color: #666;
                line-height: 24px;
            }
        }
    }

    .device-list-section {
        background: #F7F8FA;
        border-radius: 8px;
        padding: 20px;

        .section-header {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .device-info-card {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;

            .dbs {
                justify-content: space-between;
                margin-bottom: 12px;

                .bind-status {
                    font-size: 16px;
                    font-weight: 500;

                    margin-right: 8px;
                }

                .device-status {
                    font-size: 13px;
                    border-radius: 4px;
                    padding: 2px 8px;
                    margin-left: 10px;

                    &.online {
                        background: rgba(4, 204, 164, 0.1);
                        color: #04CCA4;
                    }

                    &.offline {
                        background: rgba(245, 34, 45, 0.1);
                        color: #F5222D;
                    }
                }

            }



            .device-sn {
                color: #666;
                margin-bottom: 8px;
            }

            .bind-tip {
                color: #999;
                font-size: 12px;
                margin-bottom: 20px;
            }

            .action-buttons {
                display: flex;
                gap: 12px;

                .el-button {
                    flex: 1;
                }
            }
        }
    }
}
</style>