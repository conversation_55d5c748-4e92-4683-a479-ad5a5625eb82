<template>
    <div class="user_records_wrap">
        <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
            <template #_header_filter>
                <div class="dates_picker">
                    <el-date-picker v-model="refDates" type="daterange" unlink-panels range-separator="~"
                        start-placeholder="开始日期" end-placeholder="结束日期" size="default" @change="onDatesChange" />
                </div>
            </template>
            <template #col_fileName="{ row }">
                <div @click="cbDatas('view_doc', row)" class="column_link">
                    {{ row.fileName }}
                </div>
            </template>
            <template #col_departmentName="{ row }">
                <div>
                    {{ getDept(row) }}
                </div>
            </template>
            <template #col_fileSize="{ row }">
                <div @click="viewDoc(row)">
                    {{ formatFileSize(row.fileSize * 1024) }}
                </div>
            </template>
            <template #_link_pre="scope">
                <el-button type="primary" text @click="cbDatas('view_input', scope.row)">
                    查看输入项
                </el-button>
                <el-button type="primary" text @click="cbDatas('download', scope.row)">
                    下载
                </el-button>
            </template>
        </MyTable>
        <ViewUseInput ref="refDvi" />
    </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getPptTemplateDocument } from "@/app_admin/tools/api.js"
import { getNDaysAgo } from "@/app_admin/tools/utils.js"
import { formatDate, formatFileSize, jsOpenNewWindow } from "@/js/utils.js"
import ViewUseInput from "@/app_admin/components/ViewUseInput"
import { useRoute } from 'vue-router'
import config from '@/js/config';

const route = useRoute()
const refDates = ref('')
const refDvi = ref()

const refTable = ref(null);
const datas = reactive({
    tableid: 'template_use_record',
    param: {
        templateId: route.params.id,
        startTime: getNDaysAgo(30),
        endTime: getNDaysAgo(0)
    },
    need_header: true,
    need_init_load: true,
    form: {},
    show_link_column: true,
    show_link_edit: false,
    show_link_view: false,
    columns: ["fileName", "createdUserName", "departmentName", "companyName", "slideSize", "fileSize", "createdTime"],
    template: ["fileName", "fileSize", "departmentName"],
    urlGet: getPptTemplateDocument,
});

const cbDatas = (action, data) => {
    if (action == "view_doc") {
        const templateId = route.params.id;
        const docId = data.id
        const url = `${config.publicPath}/index.html#/vp/${templateId}/${docId}`
        jsOpenNewWindow(url)
    } else if (action == 'view_input') {
        refDvi.value.show(data)
    } else if (action == 'download') {
        jsOpenNewWindow(data.downloadPath)
    }
}

const onDatesChange = () => {
    const [start, end] = refDates.value
    datas.param['startTime'] = formatDate(start)
    datas.param['endTime'] = formatDate(end)
    refTable.value.search()
}

const getDept = (row) => {
    if (row.departmentName) {
        return row.departmentName.replace("云学堂->", "").replaceAll("->", ">")
    } else {
        return ""
    }
}

defineExpose({
    refDates,
    MyTable,
    refDvi,
    refTable,
    ViewUseInput,
    cbDatas,
    getDept
})
</script>

<style lang='scss'>
.user_records_wrap {
    .table_box .table_class {
        height: calc(100vh - 185px) !important;
    }

    .dates_picker {
        margin-right: 10px;
    }
}
</style>