<template>
    <div class="competitive-radar-evaluation-h5">
        <div class="title-text">
            竞品评价
        </div>
        <competitorsFilter v-model="competitorId" :options="options" />
        <businessCard v-if="selectCategoriesObj.id"></businessCard>
    </div>
</template>

<script setup>
import competitorsFilter from "./competitorsFilter.vue";
import businessCard from "./businessCard/businessCard.vue";
const selectCategoriesObj = computed(() => g.clientBiStore.crDrdimensionList[0] || {})
const store = g.clientBiStore
const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.alternativeName || i.commonName,
    value: i.competitorId,
})))
const competitorId = ref('')
watch(() => competitorId.value, (newValue) => {
    if (newValue) {
        store.getCrDimensionList(competitorId.value)
    }
})
watch(() => options.value, () => {
    competitorId.value = options.value[0]?.value || ""
    store.crFbCompetitorId = competitorId.value;
})

watch(() => selectCategoriesObj.value, () => {
    store.setCrFilterCondition({
        parentId: selectCategoriesObj.value.id
    })
    store.crFbCompetitorId = competitorId.value;

})


</script>

<style lang="scss" scoped>
.competitive-radar-evaluation-h5 {
    padding: 24px 0;
    background: #f7f8fa;
}
</style>