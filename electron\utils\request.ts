import axios from 'axios';
import config from './config';
import { getStore, keySystemInfo, getUserInfo } from './store';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { app } from 'electron';
import { errorWriteLocal } from './errorWriteLocal';
// 创建一个 CancelToken.source 实例
let source

export function abortAxiosRequest() {
    try {
        source && source.cancel('请求取消的原因');
    } catch (e) {
        console.log('abortRequest error', e)
    }
}

export default function request(url, opt: any) {
    const init = {
        cache: 'no-cache',
        method: 'GET',
        mode: 'cors',
        redirect: 'follow',
        referrer: 'no-referrer',
        timeout: 300000
    };

    const options = {
        ...init,
        ...opt,
        headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Your-Custom-User-Agent',
            ...opt.headers,
        },
    };

    if (opt.body) {
        options.data = JSON.stringify(opt.body);
    }

    const userInfo: any = getUserInfo();
    const apiToken: any = userInfo.token || '';

    if (apiToken) {
        options.headers['token'] = apiToken;
    }

    const domain: any = userInfo.ssoDomain || ''
    if (domain) {
        options.headers['yxt-orgdomain'] = domain
    }

    return new Promise((resolve, reject) => {
        source = axios.CancelToken.source();
        options['cancelToken'] = source.token;
        axios(url, options)
            .then(response => {
                if (response.status >= 200 && response.status < 300) {
                    resolve(response.data);
                } else {
                    reject(new Error(`http status:${response.status}`));
                }
            })
            .catch((e) => {
                console.log('request error', e)
                reject(e)
            });
    });
}

function getFullUrl(_host, str) {
    if (str.indexOf('http') === 0) {
        return str;
    } else {
        const host = _host || `${config.meetApiHost}/rest`
        return `${host}/${str}`
    }
}

export const getHttp = (_host = '') => {
    const cmap = ['get', 'post', 'put', 'patch', 'delete'];
    const _http = {}
    for (let fn of cmap) {
        _http[fn] = (str, data = {}) => {
            const url = getFullUrl(_host, str)
            const option = {
                method: fn.toUpperCase(),
                body: data
            };
            return request(url, option)
        }
    }
    _http['download'] = (str, data = {}) => request(getFullUrl(_host, str), {
        method: 'POST',
        body: data,
        responseType: 'blob'
    })
    return _http;
}

export const uploadLogFile = async (url: string, formData: FormData) => {
    return new Promise((resolve, reject) => {
        const fullUrl = getFullUrl(`${config.meetApiHost}/rest`, url)
        const systemInfo = getStore(keySystemInfo, { platform: config.platform, version: config.version });
        const systemInfoStr = Object.values(systemInfo).join('|');

        axios.post(fullUrl, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'User-Agent': systemInfoStr
            },
            timeout: 30000,
        }).then((response) => {
            resolve(response.data)
        }).catch((err) => {
            // 修改错误对象的序列化方式
            const errorInfo = {
                message: err.message,
                code: err.code,
                status: err.response?.status,
                responseData: err.response?.data,
                // 添加更多有用的错误信息，但避免循环引用
                type: err.type,
                name: err.name
            };
            errorWriteLocal('uploadLogFile error', errorInfo)
            reject(errorInfo)
        })
    })
}

// 添加新的图片缓存函数
export async function downloadAndCacheImage(imageUrl: string): Promise<string> {
    if (!imageUrl) {
        errorWriteLocal('downloadAndCacheImage error', 'imageUrl is empty')
        return ''
    }
    const imageCacheDir = path.join(app.getPath("appData"), config.name, 'image_cache');

    // 确保缓存目录存在
    if (!fs.existsSync(imageCacheDir)) {
        fs.mkdirSync(imageCacheDir, { recursive: true });
    }

    // 使用 URL 的 MD5 作为文件名
    const fileName = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filePath = path.join(imageCacheDir, fileName);

    // 如果文件已存在，直接返回路径
    if (fs.existsSync(filePath)) {
        return `file://${filePath}`;
    }

    // 使用 axios 下载图片
    try {
        const response = await axios({
            method: 'get',
            url: imageUrl,
            responseType: 'stream'
        });

        // 将响应流写入文件
        const writer = fs.createWriteStream(filePath);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        return `file://${filePath}`;
    } catch (error) {
        console.error('下载图片失败:', error);
        return imageUrl; // 如果下载失败，返回原始 URL
    }
}