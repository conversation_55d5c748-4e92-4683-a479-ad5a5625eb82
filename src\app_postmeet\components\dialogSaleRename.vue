<template>
  <el-dialog v-model="is_show" width="480px" class="dialog_sale_rename_wrap">
    <div @click="onClose">
      <closeIcon class="btnClose"></closeIcon>
    </div>
    <RenameSpeaker ref="refRenameSpeaker" @callback="onCallback"> </RenameSpeaker>
    <DialogEditRole ref="refEditRole" @callback="onCallback"> </DialogEditRole>
  </el-dialog>
</template>

<script>
import RenameSpeaker from "@/components/RenameSpeaker/RenameSpeaker.vue";
import DialogEditRole from "@/app_postmeet/components/dialogEditRole.vue";
import closeIcon from "@/app_postmeet/icons/close.vue";

export default {
  name: "DiaSaleRename",
  components: { RenameSpeaker, closeIcon, DialogEditRole },
  data() {
    return {
      item: {},
      is_show: false,
    };
  },
  methods: {
    onClose() {
      this.is_show = false;
    },
    show(item) {
      this.item = item;
      this.is_show = true;
      this.$nextTick(() => {
        const confId = g.postmeetStore.data.confId;
        this.$refs.refRenameSpeaker.init(item, confId);
      });
    },
    onCallback(action, data) {
      if (action == "show_edit_role") {
        data['ui'] = this.item.ui;
        this.$refs.refEditRole.show(data);
      } else if (action == "close") {
        this.is_show = false;
      }
    },
    cbList(action, data) {
      const { ui, name } = this.item;
      if (action == "show_add") {
        this.$refs.refRenameSpeaker.show({ ui, name, type: "customer" });
      } else if (action == "eidt_role") {
        // this.$refs.refEditRole.show(data);
      } else if (action == "close") {
        this.is_show = false;
      }
    },
  },
};
</script>

<style lang="scss">
.dialog_sale_rename_wrap {
  padding: 0;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .btnClose {
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    z-index: 999;
  }

  .suggested-contacts {
    margin-top: 0;
  }

  .btnClose:hover {
    color: #436bff;
  }
}
</style>
