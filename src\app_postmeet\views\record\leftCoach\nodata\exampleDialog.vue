<template>
  <el-dialog
    title="生成内容示意图"
    v-model="isShow"
    width="608px"
    :append-to-body="true"
    :modal-append-to-body="false"
    class="coach_example_wrap"
  >
    <div class="cd_main">
      <img :src="getOssUrl('coach_pic2.png')" />
    </div>
    <template #footer class="dialog-footer">
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { getOssUrl } from "@/js/utils";

const isShow = ref(false);

const show = () => {
  isShow.value = true;
};

const onCancel = () => {
  isShow.value = false;
};

const onConfirm = () => {
  isShow.value = false;
};

// 暴露方法供外部调用
defineExpose({
  show,
  getOssUrl,
});
</script>

<style lang="scss">
.coach_example_wrap {
  .cd_main {
    img {
      width: 576px;
    }
  }
}
</style>
