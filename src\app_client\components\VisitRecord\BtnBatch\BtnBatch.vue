<template>
    <div class="visit_btn_batch">
        <el-dropdown @command="handleCommand" type="default">
            <span class="el-dropdown-link flex-row">
                <div class="vbb_title">
                    批量操作
                </div>
                <el-icon class="el-icon--right">
                    <ArrowDown />
                </el-icon>
            </span>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="delete" :disabled="checkedData.length == 0">删除</el-dropdown-item>
                    <el-dropdown-item command="edit" :disabled="checkedData.length == 0">编辑</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <DiaEditRecord ref="refDiaEdit" @callback="handleCommand" />
    </div>
</template>

<script setup>

import { deleteRecordByConfId } from "@/app_client/tools/api.js"
import { ArrowDown } from '@element-plus/icons-vue'
import DiaEditRecord from "@/components/DiaEditRecord.vue";

const checkedData = ref([])
const refDiaEdit = ref();
const emit = defineEmits(['reload']);

const _deleteConfirm = () => {
    const ids = checkedData.value.map(x => x.conferenceId).join(',');
    deleteRecordByConfId(ids).then(resp => {
        if (resp.code == 0) {
            ElMessage.success("删除成功");
            emit('reload', '')
        } else {
            ElMessage.error(
                `删除失败! 错误信息 ${resp.message}`
            );
        }
    })
}

const setChecked = (checked) => {
    checkedData.value = checked;
}

const showEdit = (row) => {
    refDiaEdit.value.show(row);
}

const handleCommand = (cmd) => {
    if (cmd == 'delete') {
        _deleteConfirm()
    } else if (cmd == "edit") {
        const conferenceIds = checkedData.value.map(x => x.conferenceId).join(',');
        const first = checkedData.value[0];
        refDiaEdit.value.show({ ...first, conferenceIds });
    } else if (cmd == 'reload') {
        emit('reload', '')
    }
}


defineExpose({ setChecked, handleCommand, checkedData, refDiaEdit, DiaEditRecord, showEdit })
</script>

<style lang="scss">
.visit_btn_batch {

    .el-dropdown-link {
        .vbb_title {
            margin-top: 3px;
        }

        .el-icon {
            margin-top: 3px;
        }
    }



    .el-dropdown {
        height: 18px;
        padding: 6px 12px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
        border-radius: 4px;
    }

    .vbp_title {
        margin-bottom: 12px;

        span {
            color: red;
            margin-left: 4px;
        }
    }
}
</style>