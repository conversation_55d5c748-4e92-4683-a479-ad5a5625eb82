<template>
    <div class="av_item_icon">
        <img :src="getOssUrl(`${iconName}.svg`)" :alt="iconName">
        <div class="star" v-if="isStar">
        </div>
    </div>
</template>

<script setup>
import { getOssUrl } from '@/js/utils'

const props = defineProps({
    iconName: {
        type: String,
        required: true
    },
    isStar: {
        type: Boolean,
        default: false
    }
})

defineExpose({
    getOssUrl
})
</script>

<style lang="scss" scoped>
.av_item_icon {
    width: 30px;
    margin-right: 10px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;

    .star {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #F56C6C;
        right: 0;
        top: 0;
        position: absolute;
        z-index: 2;
    }
}
</style>
