<template>
    <div class="dept_input_box" @click="onClick">
        <div>
            {{ hint }}
        </div>
        <div class="dept_input_box_icon">
            •••
        </div>
    </div>
    <el-drawer v-model="isShow" direction="rtl" class="dept_select_wrap" append-to-body>
        <template #header>
            <div class="vd_title">
                选择部门
            </div>
        </template>
        <template #default>
            <div class="dept_tree_body">
                <DeptSelectLeft v-model:selectedNodes="selectedNodes" ref="refDeptLeft" />
                <div class="dt_right">
                    <div class="dt_right_header">
                        <div class="drh_choosed">已选 ：{{ selectedNodes.length }}</div>
                        <div class="drh_btn" @click="onClear">清空</div>
                    </div>
                    <div class="dt_right_main">
                        <el-tag v-for="tag in selectedNodes" :key="tag.value" @close="onDelTag(tag)" closable
                            type="info">
                            {{ tag.label }}
                        </el-tag>
                    </div>
                </div>
            </div>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">保存</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import DeptSelectLeft from './components/DeptSelectLeft.vue'

const emit = defineEmits(['callback']);
const isShow = ref(false)
const default_hint = '有权限的部门';
const hint = ref(default_hint)
const selectedNodes = ref([])
const refDeptLeft = ref()

const onClick = () => {
    isShow.value = true
}

const onCancel = () => {
    emit('callback', [])
    isShow.value = false;
}

const _updateHint = () => {
    hint.value = selectedNodes.value.length > 0
        ? `已选${selectedNodes.value.length}个部门`
        : default_hint;
}

const onConfirm = () => {
    emit('callback', selectedNodes.value.map(x => x.value))
    isShow.value = false;
}

const onDelTag = (item) => {
    selectedNodes.value = selectedNodes.value.filter(x => x.value != item.value)
    refDeptLeft.value.refTree.setCheckedKeys(selectedNodes.value.map(x => x.value))
}

const onClear = () => {
    selectedNodes.value = []
    refDeptLeft.value.reset()
}

const reset = () => {
    selectedNodes.value = []
    refDeptLeft.value?.reset()
    _updateHint()
}

watch(selectedNodes, () => {
    _updateHint()
})

defineExpose({
    isShow,
    selectedNodes,
    onCancel,
    onConfirm,
    reset,
    onDelTag,
    onClear
})
</script>

<style lang="scss">
.dept_input_box {
    width: 200px;
    height: 32px;
    border-radius: 5px;
    border: 1px solid #e9e9e9;
    padding: 0 10px;
    padding: 0 10px 0px 10px;
    text-align: right;
    cursor: pointer;
    color: #8c8c8c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 12px;

    .dept_input_box_icon {
        font-size: 11px;
        color: #8c8c8c;
    }
}

.dept_select_wrap {
    width: 50vw !important;

    .el-drawer__header {
        margin-bottom: 0 !important;
    }

    .el-drawer__body {
        padding: 0;

        .dept_tree_body {
            height: calc(100vh - 125px);
            overflow-y: hidden;
            display: flex;
            flex-direction: row;

            .dt_left {
                width: 70%;
                display: flex;
                flex-direction: column;
                border-right: 1px solid #e9e9e9;
                padding: 20px;

                .dt_left_header {
                    height: 44px;

                    .search_dept {
                        width: 40%;
                        min-width: 200px;
                    }
                }

                .dt_left_main {
                    height: calc(100vh - 201px);
                    overflow-y: auto;

                    .custom-tree-node {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding-right: 8px;

                        .icon_op {
                            visibility: hidden;
                            display: flex;
                            align-items: center;

                            .el-icon {
                                cursor: pointer;
                                font-size: 16px;
                                padding: 4px;
                            }
                        }
                    }

                    .custom-tree-node:hover {
                        .icon_op {
                            visibility: visible;
                        }
                    }
                }
            }

            .dt_right {
                width: 30%;
                display: flex;
                flex-direction: column;
                padding: 20px;


                .dt_right_header {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    height: 44px;

                    .drh_btn {
                        color: #436BFF;
                        cursor: pointer;
                    }
                }

                .dt_right_main {
                    height: calc(100vh - 201px);
                    overflow-y: auto;

                    .el-tag {
                        margin: 4px;
                    }
                }
            }
        }
    }

    .el-drawer__footer {
        border-top: 1px solid #e9e9e9;
        padding-top: 20px;
    }

}
</style>