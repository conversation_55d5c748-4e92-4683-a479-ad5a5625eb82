const fs = require('fs')
const path = require('path')
const archiver = require('archiver')
const packageJson = require('../package.json')

const zipDist = (env, buildTime) => {
  return new Promise((resolve, reject) => {

    const subFolder = env === 'prod' ? 'prod' : 'test';
    const rootDir = path.join(__dirname, '..')
    const distDir = path.join(rootDir, 'dist')
    const distElectronDir = path.join(rootDir, 'dist-electron')
    const outputDir = path.join(rootDir, 'release', subFolder, packageJson.version, 'web-resources', buildTime)

    const output = fs.createWriteStream(path.join(outputDir, `${packageJson.version}.zip`))
    const archive = archiver('zip', {
      zlib: { level: 9 }
    })

    output.on('close', () => {
      console.log(`Web resource package generated: ${archive.pointer()} bytes`)
      resolve()
    })

    archive.on('error', (err) => {
      reject(err)
    })

    archive.pipe(output)

    // 检查目录是否存在
    if (!fs.existsSync(distDir)) {
      reject(new Error('dist directory does not exist. Please run build first.'))
      return
    }
    if (!fs.existsSync(distElectronDir)) {
      reject(new Error('dist-electron directory does not exist. Please run build first.'))
      return
    }

    // 添加 dist 目录，保持目录结构
    archive.directory(distDir, 'dist')

    // 添加 dist-electron 目录，保持目录结构
    archive.directory(distElectronDir, 'dist-electron')

    archive.finalize()
  })
}

module.exports = zipDist 