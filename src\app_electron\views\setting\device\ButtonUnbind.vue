<template>
    <div class="dia-device-unbind">
        <el-button type="danger" plain @click="onShow">解绑设备</el-button>

        <el-dialog v-model="dialogVisible" title="确认解绑设备" width="400px" :show-close="false" class="unbind-dialog">
            <div class="dialog-content">
                <div class="confirm-text">您确定要解绑此工牌设备吗？</div>
                <div class="device-sn">序列号：{{ deviceSn }}</div>
                <div class="unbind-tip">解绑后需要重新绑定才能使用。</div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="danger" @click="confirmUnbind">确认解绑</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { unbindDevice } from '@/app_electron/tools/api.js';

const props = defineProps({
    deviceSn: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['unbind']);

const dialogVisible = ref(false);

const onShow = () => {
    dialogVisible.value = true;
};

const confirmUnbind = () => {
    unbindDevice().then(res => {
        emit('callback', 'unbind');
        ElMessage.success('解绑成功');
        dialogVisible.value = false;
    }).catch(() => {
        ElMessage.error('解绑失败，请稍后再试');
    });
};
</script>

<style lang="scss" scoped>
.dia-device-unbind {
    display: inline-block;
}

:deep(.unbind-dialog) {
    .dialog-content {
        padding: 0 20px;

        .confirm-text {
            font-size: 16px;
            color: #333;
            margin-bottom: 12px;
        }

        .device-sn {
            color: #666;
            margin-bottom: 8px;
        }

        .unbind-tip {
            color: #999;
            font-size: 14px;
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: center;
        gap: 12px;
        padding-top: 8px;
    }

    .el-dialog__header {
        margin-right: 0;
        text-align: center;
    }
}
</style>