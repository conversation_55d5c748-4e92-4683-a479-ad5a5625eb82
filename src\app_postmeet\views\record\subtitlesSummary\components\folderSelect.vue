<template>
  <div class="pr">
    <el-select ref="select" v-model="selectId" popper-class="yxtbiz-joinlib_selectpop" class="width-percent-100"
      @visible-change="visibleChangeHandle" @change="selectChange">
      <template #fixed-header>
        <Search ref="search" :readonly="loading" @search="search">
          <i v-if="hasCreatePermission" class="yxtbiz-joinlib_addfolder ph8 lh32 text-bf hand yxt-icon-plus"
            :class="{ 'yxtbiz-joinlib_notallowed': breadcrumbs.length === 0 }" @click.stop="addFolder" />
        </Search>
        <Breadcrumbs v-if="breadcrumbs.length > 1" ref="breadcrumbs" :list="breadcrumbs" @change="breadcrumbHandle" />
      </template>
      <DataEmpty #empty :loading="loading" :empty-text="emptyText" />
      <el-option v-for="item in list" v-show="!item.hide" :key="item.id" :value="item.id" :label="item.label"
        class="yxtbiz-joinlib_flex yxtbiz-joinlib_flexcenter">
        <div class="yxtbiz-joinlib_flex1 pr">
          <i>&nbsp;&nbsp;</i>
          <label class="pa nowrap ellipsis">{{ item.label }}</label>
        </div>
        <i v-if="item.children" class="yxtbiz-joinlib_right-icon ml24 yxt-icon-arrow-right text-center color-gray-6"
          @click.stop="loadChildList(item)" />
      </el-option>
    </el-select>
  </div>
</template>

<script>
import Search from './search.vue';
import Breadcrumbs from './breadcrumbs.vue';
import DataEmpty from './dataEmpty.vue';
import { getKngLibCatalogtree } from './service.js';
export default {
  name: '',
  components: { Search, Breadcrumbs, DataEmpty },
  model: {
    prop: 'id',
    event: 'change'
  },
  props: {
    libId: String, // 知识库 id
    hasCreatePermission: Boolean // 新建目录权限
  },
  data() {
    return {
      loading: false,
      selectId: '',
      list: [],
      breadcrumbs: [],
      selectLabel: '',
      isSearch: false // 当前是否在搜索中
    };
  },
  computed: {
    emptyText() {
      return this.isSearch ? '' : '暂无数据'
    }
  },
  methods: {
    // 外部设置选择项
    initData(item) {
      this.selectId = item.id;
      this.selectLabel = item.label;
      this.list = [];
      this.breadcrumbs = [];
      this.tree = null;
    },
    // 整个 tree 搜索
    search(val) {
      this.isSearch = !!val;
      // 有搜索内容
      if (val) {
        this.breadcrumbs = [];
        let reg = new RegExp(val);
        let l = [];
        const searchAll = function (data) {
          data.forEach(item => {
            if (reg.test(item.label)) {
              l.push(item);
            }
            if (item.children && item.children.length) {
              searchAll(item.children);
            }
          });
        };
        searchAll(this.tree);
        this.list = l;
      } else {
        // 设置面包屑
        this.setBreadcrumbs();
        this.setList();
      }
    },
    // 设置面包屑
    setBreadcrumbs() {
      let flag = false;
      let l = [];
      const findFolder = (data, index) => {
        data.some((item, i) => {
          if (flag) return true;
          if (l.length !== index) l.splice(index);
          if (item.id === this.selectId) {
            flag = true;
            return true;
          }
          l.push(item);
          if (item.children && item.children.length) {
            return findFolder(item.children, index + 1);
          }
          return false;
        });
      };
      findFolder(this.tree, 0);
      // 添加根节点
      l.unshift(this.root);
      this.breadcrumbs = l;
    },
    // 设置下拉列表
    setList() {
      let l = this.breadcrumbs[this.breadcrumbs.length - 1].children;
      if (!l) {
        l = this.tree;
      }
      this.list = l;
    },
    // 显示/隐藏下拉列表
    visibleChangeHandle(val) {
      if (val) {
        if (this.tree) {
          this.setBreadcrumbs();
          this.setList();
          return;
        }
        // 加载目录 tree
        this.loading = true;
        getKngLibCatalogtree({ libId: this.libId }).then(res => {
          const dealTreeNode = function (data) {
            if (!data) return [];
            return data.map((item) => {
              return {
                label: item.label,
                id: item.id,
                children: dealTreeNode(item.children)
              };
            });
          };
          // 将根节点单独记录，将根节点加入下一级目录，setList 可以精简
          this.root = { id: res.id, label: res.label };
          if (!res.children) {
            res.children = [];
          }
          res.children.unshift(this.root);
          this.tree = dealTreeNode(res.children);
          this.tree[0].children = null;
          this.setBreadcrumbs();
          this.setList();
        }).catch(e => {
          console.log(e)
        }).then(() => {
          this.loading = false;
        });
      } else {
        // this.$refs.search.searchText = '';
      }
    },
    // 下拉选择
    selectChange(val) {
      this.selectLabel = this.list.find(item => item.id === val).label;
      this.$emit('change', val);
    },
    // 点击箭头，加载子列表
    loadChildList(item) {
      this.breadcrumbs.push(item);
      this.setList();
    },
    // 点击面包屑
    breadcrumbHandle(data, i) {
      this.breadcrumbs.splice(i + 1);
      this.setList();
    },
    // 新建目录
    addFolder() {
      let breadcrumb = this.breadcrumbs[this.breadcrumbs.length - 1];
      if (!breadcrumb) return;
      this.selectHandle();
      this.$emit('add', breadcrumb, (res) => {
        let { id, libName: label } = res;
        this.list.push({ id, label, children: [] });
        this.selectId = id;
        this.selectChange(id);
      });
    },
    // 触发下拉显示/隐藏事件
    selectHandle() {
      this.$refs.select.toggleMenu();
    }
  }
};
</script>
