<template>
    <div class="uploading_wrap" v-show="is_show">
        <div class="win_title">
            <div class="left">
                {{ title }}
            </div>
            <div class="flex-grow"> </div>
            <div class="upload_right">
                <el-tooltip content="最小化" placement="top-end" v-if="is_show_list">
                    <img :src="getOssUrl('min.svg')" class="icon" @click="onMin" />
                </el-tooltip>
                <el-tooltip content="最大化" placement="top-end" v-else>
                    <img :src="getOssUrl('max.svg')" class="icon" @click="onMax" />
                </el-tooltip>
                <el-tooltip content="取消上传" placement="top-end">
                    <img :src="getOssUrl('close.svg')" class="icon" @click="onClose" />
                </el-tooltip>
            </div>
        </div>
        <div class="files_box" v-for="row in list" :key="row.startTime" v-show="is_show_list">
            <div class="file_up">
                <div class="file_icon">
                    <component :is="fileIcons[row.fileIcon]" />
                </div>
                <div class="up_mid flex-grow">
                    <div class="title">{{ row.subject }}</div>
                    <div class="size">{{ row.size }}</div>
                </div>
                <div class="up_right">
                    <el-tooltip content="取消上传" placement="top-end">
                        <img :src="getOssUrl('delete.svg')" class="icon" @click="onDelete(row)" title="取消上传" />
                    </el-tooltip>
                </div>
            </div>
            <div class="file_bottom">
                <div class="process_box">
                    <div class="process" :style="{ width: procStr(row) }"></div>
                </div>
                <div class="note"> {{ getProcessText(row) }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import videoIcon from "./videoIcon.vue"
import { getOssUrl } from '@/js/utils';

const is_show = ref(false);
const is_show_list = ref(true);
const loading = ref(false);
const cancelTokenSource = {};
const processes = ref({});
const list = ref([]);
const totalCount = ref(0);
const title = ref('上传中，请不要关闭浏览器页面');


let intervalId;
const fileIcons = ref({
    video: markRaw(videoIcon)
})


const procStr = computed(() => {
    return function (row) {
        if (processes.value.hasOwnProperty(row.startTime)) {
            return processes.value[row.startTime] + '%';
        } else {
            return ''
        }
    }
})

const getProcessText = computed(() => {
    const statusMap = {
        waiting: "排队中", uploading: "正在上传", uploaded: "上传完成", delete: "已取消", error: "上传错误"
    }
    return function (row) {
        return statusMap[row.status];
    }
})

const onMin = () => {
    is_show_list.value = false;
};

const onMax = () => {
    is_show_list.value = true;
};

const onClose = () => {
    ElMessageBox.confirm(
        '文件还在上传中，取消后不可恢复',
        '确定取消上传吗？',
        {
            confirmButtonText: '取消上传',
            cancelButtonText: '继续上传',
            type: 'warning',
        }
    )
        .then(() => {
            const startTimes = g.clientFileStore.get_files().map(x => x.startTime)
            for (let i = 0; i < startTimes.length; i++) {
                _deleteFile(startTimes[i]);
            }
        })
        .catch(() => { });
};

const onDelete = (row) => {
    ElMessageBox.confirm(
        '文件还在上传中，取消后不可恢复',
        '确定取消上传吗？',
        {
            confirmButtonText: '取消上传',
            cancelButtonText: '继续上传',
            type: 'warning',
        }
    )
        .then(() => {
            _deleteFile(row.startTime);
        })
        .catch(() => { });
};

const _deleteFile = (startTime) => {
    cancelTokenSource[startTime].abort();
    processes.value[startTime] = -1;
    g.clientFileStore.update_file_status(startTime, 'delete')
    updateList();
    ElMessage({ type: 'success', message: '取消成功' });
};

const show = () => {
    is_show.value = true;
    function upload_func(param) {
        const { startTime, status } = param;
        if (['waiting', 'uploading'].includes(status)) {
            processes.value[startTime] = 0;
            cancelTokenSource[startTime] = null;
            g.clientFileStore.update_file_status(startTime, 'uploading')
            upload(param);
        }
    }

    let index = 0;
    function executeFunc() {
        if (index < g.clientFileStore.upload_files.length) {
            const param = g.clientFileStore.upload_files[index];
            upload_func(param);
            index++;
        }

        if (index == g.clientFileStore.upload_files.length) {
            intervalId && clearInterval(intervalId);
            intervalId = null;
        }
        updateList();
    }
    executeFunc();
    intervalId = setInterval(executeFunc, 800);
};

const updateList = () => {
    list.value = g.clientFileStore.get_files()
    totalCount.value = list.value.length;
    title.value = `正在上传${totalCount.value}个文件,请不要关闭浏览器页面!`;
    is_show.value = totalCount.value > 0 || !!intervalId;
};

const upload = (param) => {
    const { uploadMethod, file, startTime, page } = param;

    const onProgress = (xhr, process) => {
        cancelTokenSource[startTime] = xhr;
        processes.value[startTime] = process;
    };

    const onSuccess = (data) => {
        g.clientFileStore.update_file_status(startTime, 'uploaded')
        updateList();
        g.emitter.emit('file_uploaded', page);
        ElMessage({ type: 'success', message: '上传成功' });
    };

    const onFail = (error) => {
        console.log('onFail', error);
        g.clientFileStore.update_file_status(startTime, 'error')
        if (error.code == 20) {
            console.log('Request canceled', error);
        } else {
            ElMessage.error(`上传错误${error.code}-${error.message}`);
        }
    };
    uploadMethod && uploadMethod(file, onProgress, onSuccess, onFail);
};

onMounted(() => {
    g.emitter.on('file_add_new', show);
});

onUnmounted(() => {
    g.emitter.off('file_add_new');
});

defineExpose({
    is_show, is_show_list, loading, processes, list, totalCount, title, getProcessText,
    procStr, onMin, onMax, onClose, onDelete, show, updateList, upload, fileIcons
});
</script>

<style lang="scss">
@import url("./UploadingBox.scss");
</style>