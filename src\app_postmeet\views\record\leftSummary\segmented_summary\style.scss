.overview {

  ul,
  ol {
    line-height: 25px;
    padding-left: 21px;

  }

  .has_bt {
    li {
      display: flex;

      .time {
        cursor: pointer;
        color: #436bff;
      }

      .li_title {
        cursor: pointer;
      }

      .li_title_edit {
        width: 100%;
        cursor: pointer;
      }

      .li_title:hover {
        color: #436bff;
      }


    }

    li.active {
      color: #436bff;
    }
  }

  .topic {
    list-style: revert;


    li {
      list-style: revert;
      display: flex;
      flex-direction: column;
      cursor: pointer;

      .ov_main {
        display: flex;
      }

      .btn-ov-more {
        margin-left: 10px;
        color: #436bff;
      }

      .ov_summary {
        margin-left: 44px;
        color: #999;
      }

      .time {
        color: #436bff;
        margin-right: 8px;
        font-family: monospace;
      }
    }
  }

  .summary {
    li {
      list-style: square;
      margin-left: 6px;
      cursor: pointer;
    }
  }

  .ov_item_summary {
    cursor: pointer;

    input {
      width: 99%;
    }
  }

  .topic {
    .diy_box {
      input {
        width: 100%;
      }
    }
  }

  .sentence {
    list-style: revert;

    li {
      display: list-item;
      list-style: revert;
      margin-left: 5px;
    }
  }

  .action-list {
    list-style: revert;

    li {
      display: list-item;
      cursor: pointer;
      list-style: revert;

      input[type=checkbox] {
        margin-top: 7px;
      }

      .diy_box {
        width: 100%;

        input:focus {
          width: 98%;
        }
      }
    }
  }

  .action-lab {
    padding-left: 3px;

    li {
      display: list-item;
      cursor: pointer;
      display: flex;
      align-items: flex-start;

      input[type=checkbox] {
        margin-top: 7px;
      }

      .diy_box {
        width: 100%;
        margin-left: 8px;

        input:focus {
          width: 98%;
        }
      }
    }
  }

  .header {
    display: flex;
    line-height: 42px;
    border-top: 1px solid #eee;
    margin-top: 5px;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    .blank {
      flex-grow: 1;
    }
  }

  .ovagend {
    padding: 0 20px;

    p {
      margin: 0;
    }
  }

  .no_data {
    margin: 30px 0;
  }
}