<template>
    <div class="analys_nodata_wrap">
        <div class='an_header flex-row' v-if="!loading">
            <span class="icon">
                <hintIcon />
            </span>
            <span class="an1">
                拜访记录中所有发言人均需要标注身份，需标注后才可生成对应内容
            </span>
            <span class="an2" @click="onShowExample">
                生成内容示意图
            </span>
        </div>
        <exampleDialog ref="refExample" :show_hint="false" />
        <setupRole ref="refSetupRole" @callback="cbSetupRole" type="coach" />
    </div>
</template>

<script>
import hintIcon from "./hint.vue"
import setupRole from '@/app_postmeet/components/setup_role/setupRole.vue';
import exampleDialog from "./exampleDialog.vue";

export default {
    components: {
        hintIcon, setupRole, exampleDialog
    },
    data() {
        return {
            loading: false,
            isHost: false,
            isNodata: true,
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
    },
    methods: {
        init() {
            this.isHost = !g.postmeetStore.isReadonly();
            this.$refs.refSetupRole && this.$refs.refSetupRole.init();
        },
        cbSetupRole(action, data) {
            if (action == "loading") {
                this.loading = data;
            }
        },
        onShowExample() {
            this.$refs.refExample.show()
        }
    }
}

</script>

<style lang="scss">
.analys_nodata_wrap {
    .an_header {
        line-height: 18px;
        margin: 12px 0;

        .an1 {
            margin-left: 10px;
            line-height: 26px;
        }

        .an2 {
            height: 22px;
            line-height: 26px;
            font-size: 14px;
            color: #436BFF;
            cursor: pointer;
            margin: 0 10px;
        }
    }

}
</style>