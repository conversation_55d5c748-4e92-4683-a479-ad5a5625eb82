import { defineStore } from 'pinia'
import { createLive<PERSON>eet, endLiveMeet, updateCloudRecord, update<PERSON>r, renameSpeaker, getRoom, updateDeviceR<PERSON>ord, getCardStatus } from '@/app_electron/tools/api'
import { Room, RoomEvent, Track, setLogLevel, LogLevel } from 'livekit-client';
import { simplifyLabel, pingRequest, MeetStatus } from '@/app_electron/tools/utils'
import { startLocalRecorder, stopLocalRecorder, pauseLocalRecord, resumeLocalRecord, combineAudioTracks } from '@/app_electron/tools/localRecord'
import { ElMessage } from 'element-plus';
import { setStore, getStore, removeStore, getUser, deepCopy } from '@/js/utils'
import { ConstValue } from '@/js/const_value'


// import { mockDataReceived } from '@/app_electron/tools/mock_data'

const MeetStore = defineStore('meet', {
    state: () => ({
        conferenceId: '',
        participantId: '',
        meetType: '',
        cardId: '',
        cardInfo: {},
        plan: getStore(ConstValue.keyMeetPlan, {}),
        room: null,
        meetingOptions: getStore(ConstValue.keyMeetOptions, {}),
        meetingInfo: {},
        remoteVideos: null,
        eventListeners: [],
        renameData: {},
        errorId: 0,
        isQuickMeeting: false,
        checkerTimer: null,
        error: '',
        isNetworkConnected: true,
        networkStatusInterval: null,
        hasNetworkDisconnect: false,
        connectedCount: 0,
        needCheckNet: false,
        isChecking: false,
        isEndingMeet: false,
        targetStatus: MeetStatus.na,
        cardOpStatus: '',
        onGoingMeeting: {},
        fromStore: false,
        isLocalMeeting: false,
    }),
    actions: {
        setOngoingConference(info) {
            this.conferenceId = info.conferenceId;
            this.participantId = g.appStore.user.id;
            this.meetType = info.recordType == 'APP' ? 'computer' : 'card';
            this.onGoingMeeting = info;
        },
        setPlan(plan) {
            this.plan = plan;
            this.conferenceId = plan.conferenceId || '';
            setStore(ConstValue.keyMeetPlan, plan);
        },
        reStoreCardConferenceId() {
            this.conferenceId = getStore(ConstValue.KeyCardConferenceId, '')
            g.elog.start(this.conferenceId);
            g.elog.log('reStoreCardConferenceId', this.conferenceId)
        },
        openCardMeetWindow(scheduleId = '', continueStr = 'false') {
            const url = `${g.config.postmeet_h5_customer}/index.html#/badge/record?scheduleId=${scheduleId}&continue=${continueStr}`;
            const user = localStorage.getItem('yxtlm_userInfo');
            const openParam = {
                urlParam: {
                    id: 'dummy_cardmeet',
                    url
                },
                newPageData: {
                    "yxt-userInfo": user
                }
            }
            g.electronStore.createWindows(openParam);
        },
        _updateCardStatus(action) {
            const that = this;
            g.elog.log('_updateCardStatus', that.plan)
            return new Promise((resolve) => {
                let response = {}
                if (this.isNetworkConnected) {
                    updateDeviceRecord(action, that.conferenceId).then((res) => {
                        g.elog.log('_updateCardStatus resp:', res)
                        response = res;
                        if (res.code == 0) {
                            that.cardOpStatus = action;
                        }
                        if (action === 'stop') {
                            g.elog.stop()
                        }
                    }).catch(err => {
                        g.elog.error('_updateCardStatus error', err)
                        response = { code: -1, message: '网络错误或更新失败' }
                    }).finally(() => {
                        resolve(response)
                    })
                } else {
                    resolve({ code: -2, message: '网络连接异常，请检查网络' })
                }
            });
        },
        endCardMeeting() {
            return this._updateCardStatus('stop')
        },
        createMeeting(plan, options) {
            console.log('createMeeting', plan, options)
            this.plan = plan;
            const { upcomingId, scheduleId } = plan;
            this.isQuickMeeting = !!upcomingId;
            this._updateMeetingOptions(options)
            return new Promise(async (resolve) => {
                try {
                    this.error = ''
                    const param = {
                        upcomingId,
                        scheduleId
                    }
                    if (this.conferenceId) {
                        param['conferenceId'] = this.conferenceId;
                    }
                    // to show more livekit logs
                    // setLogLevel(LogLevel.debug);
                    console.log('createLiveMeet param', param, this.meetingOptions)
                    createLiveMeet(param).then(async resp => {
                        this.meetingInfo = resp;
                        console.log('createLiveMeet resp', resp)
                        if (resp.code == 0) {
                            this.meetType = 'computer';
                            this.conferenceId = resp.room.conferenceId;
                            this.participantId = resp.participantId;
                            g.elog.start(this.conferenceId);
                            await this._logInfo()
                            let status = await this._initRoom()
                            status && (status = await this._uploadLocalRecord('start'))
                            status && (status = await this._publishMixedAudioTrack())
                            // this._enableMockAsr()
                            // resolve(true)
                            if (this.fromStore) {
                                status && (status = await this._updateAsr('resume'))
                            } else {
                                status && (status = await this._updateAsr('start'))
                            }
                            status && (status = await this._updateCloudRecord('start'))
                            if (status) {
                                this.targetStatus = MeetStatus.started;
                                this.startCheckNetwork()
                                this._saveMeetingInfo()
                            } else {
                                this.endOrderMeeting()
                            }
                            this.isLocalMeeting = true;
                            g.elog.log('createLiveMeet result', status)
                            resolve(status)
                        } else {
                            g.elog.error('startMeeting error', resp)
                            this.conferenceId = resp.data.conferenceId;
                            this.participantId = resp.data.hostUserId;
                            this.errorId = resp.code;
                            this.error = resp.message
                            resolve(false)
                        }
                    })
                } catch (err) {
                    g.elog.error('startMeeting error2', err)
                    this.error = err.message
                    resolve(false)
                }
            })
        },
        _saveMeetingInfo() {
            const info = {
                conferenceId: this.conferenceId,
                participantId: this.participantId,
                meetType: this.meetType,
                cardId: this.cardId,
                cardInfo: this.cardInfo,
                plan: this.plan,
                meetingInfo: this.meetingInfo,
                meetingOptions: this.meetingOptions
            }
            g.appStore.setStore(g.cv.keyMeetingInfo, info)
        },
        restoreMeetingInfo() {
            const info = g.appStore.getStore(g.cv.keyMeetingInfo)
            if (info && info.conferenceId) {
                this.conferenceId = info.conferenceId;
                this.participantId = info.participantId;
                this.meetType = info.meetType;
                this.cardId = info.cardId;
                this.cardInfo = info.cardInfo;
                this.plan = info.plan;
                this.meetingInfo = info.meetingInfo;
                this.meetingOptions = info.meetingOptions;
                this.fromStore = true;
                this.isLocalMeeting = true;
            }
        },
        async _logInfo() {
            g.elog.log('startMeeting conferenceId', this.conferenceId, 'meetType', this.meetType)
            if (this.meetType == 'computer') {
                g.elog.log('participantId', this.participantId)
                g.elog.log('meetingOptions', this.meetingOptions)
            } else {
                g.elog.log('cardId', this.cardId)
            }
            g.elog.log('systemInfo', await g.electronStore.getStore('systemInfo'))
            g.elog.log('clientInfo', await g.electronStore.getStore('localVersionInfo'))
            const { id, name, orgId } = getUser()
            g.elog.log('userInfo', { id, name, orgId })
        },
        endOrderMeeting() {
            return new Promise((resolve) => {
                try {
                    const that = this;
                    g.elog.log('endMeeting start isNetworkConnected:', this.isNetworkConnected)
                    if (this.isNetworkConnected) {
                        let status = false;
                        this.isEndingMeet = true;
                        endLiveMeet(that.conferenceId, { operator: that.participantId }).then(async (res) => {
                            g.elog.log("endLiveMeet result", res)
                            status = res.code == 0
                            this.isLocalMeeting = false;
                            if (status) {
                                await that._uploadLocalRecord('stop')
                                that._disconnectRoom()
                                that.cleanup()
                                setTimeout(() => {
                                    that.isEndingMeet = false;
                                }, 500)
                            }
                        }).catch(err => {
                            g.elog.error('endMeeting error1', err)
                        }).finally(() => {
                            g.appStore.removeStore(g.cv.keyMeetingInfo)
                            resolve(status)
                        })
                    } else {
                        resolve(false)
                    }
                } catch (err) {
                    g.elog.error('endMeeting error2', err)
                    resolve(false)
                }
            })
        },
        pauseMeeting() {
            return new Promise(async (resolve) => {
                try {
                    g.elog.log('begin pauseMeeting')
                    let result = true;
                    if (this.isNetworkConnected) {
                        result && (result = await this._updateAsr('pause'))
                        result && (result = await this._updateCloudRecord('stop'))
                    }
                    result && (result = await this._uploadLocalRecord('pause'))
                    if (result) {
                        this.targetStatus = MeetStatus.paused;
                    }
                    g.elog.log('pauseMeeting result', result)
                    resolve(result)
                } catch (err) {
                    resolve(false)
                }
            })
        },
        resumeMeeting() {
            return new Promise(async (resolve) => {
                try {
                    g.elog.log('begin resumeMeeting')
                    let result = true;
                    if (this.isNetworkConnected) {
                        result && (result = await this._updateAsr('resume'))
                        result && (result = await this._updateCloudRecord('start'))
                    }
                    result && (result = await this._uploadLocalRecord('resume'))
                    if (result) {
                        this.targetStatus = MeetStatus.resumed;
                    }
                    g.elog.log('resumeMeeting result', result)
                    resolve(result)
                } catch (err) {
                    resolve(false)
                }
            })
        },
        rename(data) {
            return new Promise(async (resolve) => {
                try {
                    const { orgId } = getUser()
                    g.elog.log('rename', orgId, this.conferenceId, data)
                    renameSpeaker(orgId, this.conferenceId, data).then(res => {
                        g.elog.log('renameSpeaker result', res)
                        resolve(res.code == 0)
                    }).catch(err => {
                        g.elog.error('renameSpeaker error1', err)
                        resolve(false)
                    })
                } catch (err) {
                    g.elog.error('renameSpeaker error2', err)
                    resolve(false)
                }
            })
        },
        // 获取音频输入设备
        async getAudioInputs(includeVirtual = false) {
            try {
                g.elog.log('begin getAudioInputs')
                const rawDevices = await Room.getLocalDevices('audioinput')
                let devices = []
                rawDevices.forEach(device => {
                    const { deviceId, groupId, label, kind } = device;
                    devices.push({ deviceId, groupId, label: simplifyLabel(label), kind });
                });
                if (!includeVirtual) {
                    devices = devices.filter(device => !(device.label.includes('虚拟') || device.label.toLowerCase().includes('virtual')));
                }
                g.elog.log('getAudioInputs', devices)
                return devices;
            } catch (err) {
                g.elog.error('getAudioInputs error', err)
                return []
            }
        },
        startCheckNetwork() {
            const that = this;
            if (that.needCheckNet && !this.isChecking) {
                this.isChecking = true;
                pingRequest().then(resp => {
                    that.isNetworkConnected = resp;
                    g.elog.log('isNetworkConnected', that.isNetworkConnected)
                    g.emitter.emit('networkStatus', that.isNetworkConnected)
                    if (that.isNetworkConnected) {
                        that.needCheckNet = false;
                        if (that.hasNetworkDisconnect) {
                            that.checkRoom()
                        }
                        this.hasNetworkDisconnect = false;
                    } else {
                        this.hasNetworkDisconnect = true;
                    }
                    that.restartCheckNetwork()
                }).catch(e => {
                    that.restartCheckNetwork()
                })
            } else {
                that.restartCheckNetwork()
            }
        },
        restartCheckNetwork() {
            const that = this;
            that.isChecking = false;
            if (this.networkStatusInterval) {
                clearTimeout(this.networkStatusInterval)
                this.networkStatusInterval = null
            }
            that.networkStatusInterval = setTimeout(() => {
                that.startCheckNetwork()
            }, 5000)
        },
        _updateMeetingOptions(options) {
            this.meetingOptions = options
            setStore(ConstValue.keyMeetOptions, options)
        },
        updateMeetingOptions(options) {
            g.elog.log('_updateMeetingOptions', options)
            this._updateMeetingOptions(options)
            this._uploadLocalRecord('update')
        },
        // _enableMockAsr() {
        //     let index = 0;
        //     const timer = setInterval(() => {
        //         this._sendAsrResult(mockDataReceived[index])
        //         index++
        //         if (index >= mockDataReceived.length) {
        //             index = 0
        //             clearInterval(timer)
        //         }
        //     }, 500)
        // },
        _sendAsrResult(data) {
            g.elog.log('sendAsrResult', data)
            g.emitter.emit('livekitData', data)
        },
        _updateAsr(status) {
            //status: start,pause,resume
            return new Promise((resolve) => {
                try {
                    g.elog.log('begin updateAsr', status)
                    const param = {}
                    if (status == 'start') {
                        param.diarizationEnabled = true;
                    }
                    updateAsr(this.conferenceId, this.participantId, status, param).then(res => {
                        g.elog.log('updateAsr ', status, res)
                        const { code, curStatus } = res;
                        // 在调用asr和录制时会返回response中有个字段 curStatus，表示当前状态。分别为
                        // StatusNone    0
                        // StatusStop    1
                        // StatusStart   2
                        // StatusPause   3
                        // StatusResume  4

                        if (code == 4) {
                            g.emitter.emit('forceEndMeeting', 'roomNotExists');
                        } else if (code !== 0) {
                            const isPause = status == 'pause' && curStatus == 3
                            const isStart = status == 'start' && curStatus == 2
                            const isResume = status == 'resume' && curStatus == 4
                            if (isPause || isStart || isResume) {
                                return resolve(true)
                            } else {
                                ElMessage.error(res.message)
                            }
                        }
                        resolve(res.code == 0)
                    }).catch(err => {
                        g.elog.error('updateAsr error', status, err)
                        resolve(false)
                    })
                } catch (err) {
                    g.elog.error('updateAsr error', status, err)
                    resolve(false)
                }
            })
        },
        _updateCloudRecord(status) {
            return new Promise(async (resolve) => {
                try {
                    g.elog.log('begin updateCloudRecord', status)
                    if (!this.meetingOptions.recording.cloudRecording) {
                        resolve(true)
                        return
                    }
                    updateCloudRecord(this.conferenceId, this.participantId, status).then(res => {
                        g.elog.log('updateCloudRecord result', status, res)
                        if (res.code !== 0) {
                            ElMessage.error(res.message)
                        }
                        resolve(res.code == 0)
                    }).catch(err => {
                        g.elog.error('updateCloudRecord error', status, err)
                        resolve(false)
                    })
                } catch (err) {
                    g.elog.error('updateCloudRecord error2', status, err)
                    resolve(false)
                }
            })
        },
        _uploadLocalRecord(status) {
            return new Promise(async (resolve) => {
                try {
                    g.elog.log('begin uploadLocalRecord', status)
                    if (!this.meetingOptions.recording.localRecording) {
                        resolve(true)
                    } else {
                        let result = true;
                        if (status == 'start') {
                            result = await startLocalRecorder(this.meetingOptions)
                        } else if (status == 'stop') {
                            result = await stopLocalRecorder()
                        } else if (status == 'pause') {
                            result = await pauseLocalRecord()
                        } else if (status == 'resume') {
                            result = await resumeLocalRecord()
                        } else if (status == 'update') {
                            result = await startLocalRecorder(this.meetingOptions)
                            if (result && this.room) {
                                result = await this._publishMixedAudioTrack()
                            }
                        }
                        g.elog.log('uploadLocalRecord result', status, result)
                        resolve(result)
                    }
                } catch (err) {
                    g.elog.error('uploadLocalRecord error', status, err)
                    resolve(false)
                }
            })
        },
        // 初始化房间
        async _initRoom() {
            return new Promise(async (resolve) => {
                try {
                    const { wsUrl, token } = this.meetingInfo
                    g.elog.log('begin initRoom')
                    this._disconnectRoom()
                    this.room = new Room({
                        adaptiveStream: true,
                        dynacast: true,
                        reconnectPolicy: {
                            nextRetryDelayInMs: (context) => {
                                return 3000;
                            }
                        }
                    });

                    this.room
                        .on(RoomEvent.DataReceived, this._handleDataReceived)
                        .on(RoomEvent.Connected, () => this._boradcastWsStatus('connected'))
                        .on(RoomEvent.Reconnected, () => this._boradcastWsStatus('reconnected'))
                        .on(RoomEvent.Disconnected, (e) => this._boradcastWsStatus('disconnected', e))
                        .on(RoomEvent.Reconnecting, () => this._boradcastWsStatus('reconnecting'))
                        .on(RoomEvent.SignalReconnecting, () => this._boradcastWsStatus('reconnecting'))
                        .on(RoomEvent.MediaDevicesError, (e) => this._OnError('mediaDevicesError', e))
                        .on(RoomEvent.TrackMuted, (e) => this._OnError('TrackMuted', e))
                        .on(RoomEvent.TrackSubscriptionFailed, (e) => this._OnError('TrackSubscriptionFailed', e))
                        .on(RoomEvent.EncryptionError, (e) => this._OnError('encryptionError', e))
                        .on(RoomEvent.LocalAudioSilenceDetected, (e) => this._OnError('LocalAudioSilenceDetected', e))
                        .on(RoomEvent.ConnectionQualityChanged, (quality) => {
                            g.elog.log('ConnectionQualityChanged', quality)
                        })
                        .on(RoomEvent.SignalConnected, () => {
                            g.elog.log('SignalConnected')
                        })
                        .on(RoomEvent.ParticipantConnected, () => {
                            g.elog.log('ParticipantConnected')
                        })

                    await this.room.prepareConnection(wsUrl, token)
                    await this.room.connect(wsUrl, token)

                    g.elog.log('room connected')
                    resolve(true)
                } catch (err) {
                    g.elog.error('initRoom error', err)
                    resolve(false)
                }
            })
        },
        _OnError(type, e) {
            g.elog.error('OnError', type, e)
        },
        async _publishMixedAudioTrack() {
            return new Promise(async (resolve) => {
                try {
                    g.elog.log('begin publishMixedAudioTrack')
                    let isNeedRePublish = false;
                    // 确保 room 和 localParticipant 都已经准备好
                    if (this.room?.localParticipant?.trackPublications) {
                        const tracks = Array.from(this.room.localParticipant.trackPublications.values());
                        g.elog.log('Current published tracks:', tracks);

                        if (tracks.length > 0) {
                            for (const trackPublication of tracks) {
                                // if (trackPublication.source === Track.Source.Microphone) {
                                // if (trackPublication.track.streamState !== 'active') {
                                const res = await this.room.localParticipant.unpublishTrack(trackPublication.track);
                                g.elog.log('unpublishTrack result', res)
                                isNeedRePublish = true;
                                // }
                                // }
                            }
                        } else {
                            isNeedRePublish = true;
                        }
                    } else {
                        isNeedRePublish = true;
                    }

                    if (isNeedRePublish) {
                        const mixedAudioTrack = await combineAudioTracks(this.meetingOptions);
                        g.elog.log('mixedAudioTrack', mixedAudioTrack)
                        if (this.room?.localParticipant) {
                            const res = await this.room.localParticipant.publishTrack(mixedAudioTrack, {
                                name: 'mixedAudioTrack',
                                simulcast: true,
                                source: Track.Source.Microphone,
                            });
                            g.elog.log('Mixed audio track published', res);
                        } else {
                            g.elog.error('Room or localParticipant not ready for publishing');
                        }
                    } else {
                        g.elog.log('no need to re-publish mixed audio track')
                    }
                    resolve(true);
                } catch (err) {
                    g.elog.error('publishMixedAudioTrack error', err)
                    resolve(false)
                }
            });
        },
        _handleDataReceived(message) {
            const decoder = new TextDecoder();
            const text = decoder.decode(message);
            const data = JSON.parse(text)
            try {
                if (data.MsgType == 'TwMsg') {
                    delete data.Data.words
                    delete data.Data.message_id
                    const logData = deepCopy(data.Data);
                    logData.count = logData.asr_result.length;
                    delete logData.asr_result
                    g.elog.log('get TwMsg', logData)
                } else {
                    g.elog.log('_handleDataReceived', data)
                }
            } catch (e) {
                g.elog.error('_handleDataReceived error', e)
            }
            g.emitter.emit('livekitData', data)
        },
        checkRoom: function () {
            g.elog.log('begin _checkRoom');
            const param = {
                operator: this.participantId,
            };
            getRoom(this.conferenceId, param).then(async (res) => {
                g.elog.log('getRoom resp', res);
                if (res.code == 0) {
                    this._reconnectRoom()
                } else {
                    g.emitter.emit('forceEndMeeting', 'roomNotExists');
                }
            }).catch(() => {
                g.elog.error('getRoom error');
            });
        },
        _boradcastWsStatus(status, e) {
            this.ws_status = status;
            g.elog.log('ws status change', status, e || '', this.isEndingMeet);
            if (status == 'disconnected' && e == 5 && !this.isEndingMeet) {
                //被服务器踢出
                g.emitter.emit('forceEndMeeting', 'endByServer');
            }
            g.emitter.emit('wsStatus', status);
            if (status == 'reconnecting') {
                this.needCheckNet = true;
            } else if (status == 'connected') {
                this.connectedCount++;
                g.elog.log('connectedCount', this.connectedCount)
                this.needCheckNet = false;
            }
        },
        _reconnectRoom() {
            const { wsUrl, token } = this.meetingInfo
            this.room.prepareConnection(wsUrl, token).then(() => {
                return this.room.connect(wsUrl, token, { reconnect: true });
            }).then(res => {
                g.elog.log('room connect result', res || '')
                setTimeout(() => {
                    this._publishMixedAudioTrack().then(res2 => {
                        g.elog.log('publishMixedAudioTrack result', res2 || '')
                    })
                }, 1000);
            }).catch(err => {
                g.elog.error('reconnectRoom error', err)
                this.needCheckNet = true;
            })
        },
        _disconnectRoom() {
            g.elog.log('begin disconnectRoom', !this.room)
            if (this.room) {
                this.room
                    .off(RoomEvent.DataReceived, this._handleDataReceived)
                    .off(RoomEvent.Connected, this._boradcastWsStatus)
                    .off(RoomEvent.Reconnected, this._boradcastWsStatus)
                    .off(RoomEvent.Disconnected, this._boradcastWsStatus)
                    .off(RoomEvent.Reconnecting, this._boradcastWsStatus)
                    .off(RoomEvent.SignalReconnecting, this._boradcastWsStatus)
                this.room.disconnect()
                this.room = null
            }
        },
        cleanup() {
            g.elog.log('begin cleanup')
            this.conferenceId = ''
            this.participantId = ''
            this.meetingOptions = {}
            this.meetingInfo = {}
            this.remoteVideos = null
            this.startTime = null
            removeStore(ConstValue.keyMeetPlan)
            removeStore(ConstValue.keyMeetOptions)
            removeStore(g.cv.keyMeetTimer)
            g.emitter.off('livekitData')
            g.emitter.off('wsStatus')
            g.elog.stop()
        },

        getCardDeviceStatus() {
            return new Promise(async (resolve) => {
                try {
                    const cardId = g.appStore.user.audioDeviceCode;
                    if (!cardId) {
                        resolve({})
                    }
                    this.cardId = cardId;
                    const resp = await getCardStatus(cardId);
                    //   {
                    //     "code": 0,
                    //     "data": [
                    //         {
                    //             "autoRecord": 0,
                    //             "batteryPer": 100,
                    //             "deviceType": "L3",
                    //             "gpsStatus": 0,
                    //             "imei": "866234071274573",
                    //             "leftSpace": 3782752,
                    //             "limitBatteryPer": 5,
                    //             "online": 0,
                    //             "recordIgnoreTime": 15,
                    //             "recordStatus": 0,
                    //             "snCode": "C16F2448000223",
                    //             "totalSpace": 3784224,
                    //             "version": "1.3.8",
                    //             "version4G": "0.2.0"
                    //         }
                    //     ],
                    //     "message": "success"
                    // }
                    let info = {}
                    if (resp.code == 0) {
                        info = resp.data[0];
                    }
                    resolve(info);
                } catch (error) {
                    g.elog.error("Error fetching card status:", error);
                    resolve({});
                }
            })
        }
    }
})

export default MeetStore
