<template>
  <div class="arrange_visit_wrap">
    <ArrangeVisitCore ref="refArrangeVisitCore" @callback="onCallback" />
    <div class="av_footer flex-center">
      <el-button type="primary" @click="onConfirm" :disabled="loading">确定</el-button>
    </div>
  </div>
</template>

<script setup>
const refArrangeVisitCore = ref(null);
const loading = ref(false);

const onConfirm = () => {
  loading.value = true;
  refArrangeVisitCore.value.onConfirm();
};

const onCallback = (action, data) => {
  if (action === "reload") {
    ElMessage.success("提交成功");
    setTimeout(() => {
      g.electronStore.sendMessage("main", "reload_schedule", 1000);
      g.electronStore.closeWin("arrange_visit");
    }, 500);
  } else if (action === "param_error") {
    loading.value = false;
  } else if (action === "api_error") {
    ElMessage.error("提交失败");
    loading.value = false;
  } else if (action === "has_error") {
    setTimeout(() => {
      g.electronStore.closeWin("arrange_visit");
    }, 2000);
  } else if (action === "uploading") {
    loading.value = data;
  }
};

onMounted(() => {
  g.electronStore
    .waitPageReady()
    .then((data) => {
      refArrangeVisitCore.value.showEdit(data);
    })
    .catch((err) => {
      console.error("meet waitPageReady error", err);
    });
});

defineExpose({
  onConfirm,
  refArrangeVisitCore,
  loading,
});
</script>

<style lang="scss">
.arrange_visit_wrap {
  padding: 2px 6px;
  display: flex;
  flex-direction: column;

  .ed_main {
    .av_item {
      .av_item_value {
        width: 80%;
      }
    }
  }

  .av_footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: #fff;
    padding: 12px 0;

    .el-button {
      width: 90%;
    }
  }
}

.mac-titlebar {
  background-color: #fff;
}

.mac-content {
  .ed_main {
    height: calc(100vh - 92px);

    .arrange_visit_core_wrap {
      height: calc(100vh - 75px);
    }
  }
}

.win-content {
  .ed_main {
    height: calc(100vh - 50px);

    .arrange_visit_core_wrap {
      height: calc(100vh - 45px);
    }
  }
}
</style>
