<template>
  <div class="customer_name_picker_wrap">
    <el-select v-model="localValue" placeholder="请输入并选择客户名称" filterable remote :remote-method="handleRemoteSearch"
      :loading="loading" style="width: 100%" @change="onSelect" @focus="handleFocus" @blur="handleBlur"
      remote-show-suffix popper-class="customer_name_selector">
      <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item">
        <div class="rdn_item">
          <div class="name-text">{{ item.name }}</div>
          <el-popover placement="left" :width="300" trigger="hover" popper-class="customer-detail-popover">
            <template #reference>
              <el-icon class="cnp_icon">
                <ArrowRight />
              </el-icon>
            </template>
            <template #default>
              <CustomerDetail :item="item" />
            </template>
          </el-popover>
        </div>
      </el-option>
      <template #footer>
        <div class="cp_footer flex-row" @click="onAddCustomer">
          <div class="fn_icon flex-center">
            <el-icon :size="16">
              <Plus />
            </el-icon>
          </div>
          <div class="fn_txt">
            添加客户
          </div>
        </div>
      </template>
    </el-select>
    <div v-if="!hasSelected && localValue && !modelValue && !isAllowInput" class="no-customer-selected">
      请从下拉列表中选择客户
    </div>
  </div>
</template>

<script setup>
import { Plus, ArrowRight } from '@element-plus/icons-vue'
import { jsOpenNewWindow } from '@/js/utils.js'
import { searchCustomerAPI } from "@/js/api.js";
import CustomerDetail from './CustomerDetail.vue';
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  customerId: {
    type: String,
    default: "",
  }
});

const emit = defineEmits(["update:modelValue", "update:customerId", "change"]);
const hasSelected = ref(false);
const localValue = ref(props.modelValue);
const customerId = ref(props.customerId);
const options = ref([]);
const loading = ref(false);
const isFocus = ref(false)
const isAllowInput = ref(false);
const searchQuery = ref('');
let lastKeyword = undefined;
let hideTimer = null;

watch(
  () => props.modelValue,
  (val) => {
    localValue.value = val;
  }
);
watch(
  () => props.customerId,
  (val) => {
    customerId.value = val;
  }
);

const onSelect = (item) => {
  hasSelected.value = true;
  emit("update:modelValue", item.name);
  emit("update:customerId", item.customerId);
  emit("change", item);
};

const onAddCustomer = () => {
  const url = "/client/customer?action=create_customer"
  if (g.config.isElectron) {
    g.electronStore.openUrl(url);
  } else {
    jsOpenNewWindow(`/#${url}`, "_blank");
  }
}

const reset = (default_value = "") => {
  localValue.value = default_value;
  customerId.value = "";
  hasSelected.value = false;
  emit("update:modelValue", default_value);
};


const _searchUseCompany = async (keyword) => {
  if (keyword === lastKeyword || loading.value) {
    return;
  }
  try {
    loading.value = true;
    const param = {
      companyName: keyword,
      pageNumber: 1,
      pageSize: 10
    }
    const resp = await searchCustomerAPI(param);
    if (resp.code == 0 && resp.data && resp.data.length > 0) {
      hasSelected.value = false;
      options.value = resp.data;
    } else {
      options.value = [];
    }
  } catch (error) {
    console.error("_searchUseCompany error", error);
    options.value = [];
  } finally {
    lastKeyword = keyword;
    loading.value = false;
  }
}

const handleRemoteSearch = (keyword) => {
  searchQuery.value = keyword;
  _searchUseCompany(keyword);
};

const handleFocus = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
  isFocus.value = true
  _searchUseCompany(searchQuery.value)
}

const handleBlur = () => {
  hideTimer = setTimeout(() => {
    isFocus.value = false
  }, 500);
}


defineExpose({
  reset,
});
</script>

<style lang="scss">
.customer_name_picker_wrap {
  position: relative;
}

.customer_name_selector {
  .el-select-dropdown__item {
    height: 40px !important;
    padding: 8px 10px;

    .rdn_item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      .el-icon {
        font-size: 14px;
        color: #595959;
        margin-right: 12px;
        margin-top: 8px;
      }
    }
  }

  .is-selected,
  .is-hovering {
    .rdn_item {
      .name-text {
        margin-left: 16px;
        font-weight: 400;
        font-size: 14px;
        color: #595959 !important;
      }
    }
  }

  .el-select-dropdown__footer {
    .cp_footer {
      padding: 2px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;

      .fn_icon {
        width: 20px;
        height: 20px;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fn_txt {
        margin-left: 0;
        font-size: 14px;
        color: var(--el-color-primary);
        line-height: 20px;
      }
    }
  }

  .no-customer-selected {
    color: red;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
