<template>
    <div class="meeting-participant-attributes">
        <div class="attributes-section">
            <div class="section-header">
                <div class="header-content">
                    <div class="title-group">
                        <div class="title">参会人属性设置</div>
                        <div class="settings-link" @click="handleSettings">设置</div>
                    </div>
                    <div class="subtitle">拜访后在拜访记录页面标注签入人信息等要填写相关信息。</div>
                </div>
            </div>

            <div>客户参会人属性</div>
            <div class="attributes-list">
                <div class="attribute-item" v-for="(item, index) in outerFields" :key="index">
                    <div class="attribute-name">
                        {{ item.fieldName }}
                        <p v-if="item.isRequired == 1" class="adot has-dot">
                        </p>
                        <p v-else class="adot no-dot"></p>
                    </div>
                </div>
            </div>

            <div>内部参会人属性</div>
            <div class="attributes-list">
                <div class="attribute-item" v-for="(item, index) in innerFields" :key="index">
                    <div class="attribute-name">
                        {{ item.fieldName }}
                        <p v-if="item.isRequired == 1" class="adot has-dot">
                        </p>
                        <p v-else class="adot no-dot"></p>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>

<script setup>
import { getFormFieldsWrap } from '@/app_admin/views/globalConfig/misc';
const emit = defineEmits(['callback']);

const defaultFields = [{
    fieldName: '姓名',
    fieldType: 'String',
    fieldStatus: 1,
    sortOrder: 1,
    isRequired: 1,
}];
const innerFields = ref(defaultFields);
const outerFields = ref(defaultFields);

const handleSettings = () => {
    emit('callback', 'open_config', {
        INTERNAL_PARTNER: innerFields.value,
        OUTER_PARTNER: outerFields.value,
    });
};

const getData = () => {
    getFormFieldsWrap('INTERNAL_PARTNER').then(res => {
        innerFields.value = res;
    });
    getFormFieldsWrap('OUTER_PARTNER').then(res => {
        outerFields.value = res;
    });
};

onMounted(() => {
    getData();
});

defineExpose({
    handleSettings,
});
</script>

<style lang="scss" scoped>
.meeting-participant-attributes {
    .attributes-section {
        background-color: #fff;

        .section-header {
            padding: 16px 0;

            .header-content {
                display: flex;
                flex-direction: column;

                .title-group {
                    display: flex;
                    align-items: center;

                    .title {
                        font-size: 14px;
                        color: #1f2329;
                        margin-bottom: 8px;
                    }

                    .settings-link {
                        color: #436BFF;
                        cursor: pointer;
                        font-size: 14px;
                        margin-left: 10px;
                        margin-bottom: 6px;

                        &:hover {
                            opacity: 0.8;
                        }
                    }
                }

                .subtitle {
                    font-size: 12px;
                    color: #86909c;
                    line-height: 1.5;
                }
            }
        }

        .attributes-list {
            margin-top: 16px;

            .attribute-item {
                display: inline-flex;
                align-items: center;
                margin-right: 12px;
                margin-bottom: 12px;

                .attribute-name {
                    display: inline-flex;
                    align-items: center;
                    position: relative;
                    color: #4e5969;
                    font-size: 13px;
                    background-color: #f5f6f7;
                    padding: 1px 10px;
                    border-radius: 4px;

                    .adot {
                        width: 4px;
                        height: 4px;
                        border-radius: 50%;
                        margin-left: 6px;
                    }

                    .has-dot {
                        background-color: #f53f3f;
                    }
                }

                .attribute-value {
                    color: #86909c;
                    font-size: 13px;
                    margin-left: 8px;
                }
            }
        }
    }
}
</style>