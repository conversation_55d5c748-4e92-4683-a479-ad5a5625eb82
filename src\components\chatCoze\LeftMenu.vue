<template>
    <div class="left_menu_wrap" v-if="isShow">
        <div class="head column-center">
            <div class="logo">
                <PrBigIcon />
            </div>
            <h1>{{ config.title }}</h1>
            <div class="btn flex-row flex-center" @click="onClick('new', {})">
                <AddIcon />
                <div class="txt">
                    新建对话
                </div>
            </div>
            <div class="record flex-row flex-center">
                <div class="line">
                </div>
                <div class="record_txt">
                    推荐记录
                </div>
                <div class="line">
                </div>
            </div>
        </div>

        <div class="prod_box_list">
            <div :class="`prod_bg flex-row  ${item.pinned ? 'pinned' : ''}`" v-for="item in lists">
                <div class="prod_box" :class="`prod_bg flex-row ${currItem.id == item.id ? 'active' : ''}`">
                    <div class="p_logo flex-center" v-show="!isEdit">
                        <PRIcon v-if="currItem.id == item.id" />
                        <PRGrayIcon v-else />
                    </div>
                    <el-checkbox class="item_cb" v-show="isEdit" v-model="item.checked" size="large"
                        @change="onCheckedChange(item)" @click.stop="() => { }" />
                    <div class="p_right flex-col">
                        <div class="edit_title flex-row" v-if="currEditItem.id == item.id">
                            <input type="text" v-model="currEditItem.topic" />
                            <div class="icon" @click="onClick('save', item)">
                                <SaveIcon />
                            </div>
                            <div class="icon" @click="onClick('cancel', item)">
                                <CancelIcon />
                            </div>
                        </div>
                        <div class="title" v-else @click="onClick('click', item)">
                            <el-tooltip class="box-item" effect="dark" :content="item.topic" placement="top"
                                :show-after="400">
                                {{ item.topic }}
                            </el-tooltip>
                        </div>
                        <div class="dt flex-row">
                            <div @click="onClick('click', item)">
                                {{ item.createdTime.substring(0, 16) }}
                            </div>
                            <div class="icons flex-row" v-if="currEditItem.id != item.id && !!item.id">
                                <div class="icon pin_icon" @click="onClick('pin', item)">
                                    <PinIcon />
                                </div>
                                <div class="icon" @click="onClick('edit', item)">
                                    <EditIcon />
                                </div>

                                <el-popconfirm title="删除后无法恢复,是否继续删除?" confirm-button-text="删除" cancel-button-text="取消"
                                    width="220" icon-color="#FA8C16" @confirm="onClick('delete', item)">
                                    <template #reference>
                                        <div class="icon">
                                            <DeleteIcon />
                                        </div>
                                    </template>
                                </el-popconfirm>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="footer flex-row" v-if="!isEdit">
            <div class="fleft flex-row" @click="onShowEdit">
                <div class="setup_icon">
                    <SetupIcon />
                </div>
                <div class="txt">
                    批量管理
                </div>
            </div>
            <div class="pack_up_icon" @click="onPackup">
                <PackUpIcon />
            </div>
        </div>
        <div class="footer flex-row" v-if="isEdit">
            <el-checkbox class="custom-checkbox" v-model="cp_checkAll" :indeterminate="cp_indeterminate" label="全选"
                size="large" @change="onCheckAllChange" />
            <div class="foot-view-edit-group">
                <div style="color: #595959" @click="onClickCancelEdit">取消</div>
                <el-divider direction="vertical" />
                <el-popconfirm title="删除后无法恢复,是否继续删除?" confirm-button-text="删除" cancel-button-text="取消" width="220"
                    icon-color="#FA8C16" @confirm="doDeleteItems()">
                    <template #reference>
                        <div :style="(cp_indeterminate || cp_checkAll) ? 'color: #595959' : ''">确认删除</div>
                    </template>
                </el-popconfirm>
            </div>
        </div>
    </div>
</template>

<script setup>
import PrBigIcon from "@/icons/pr_big.vue"
import PRIcon from "@/icons/pr.vue"
import PRGrayIcon from "@/icons/pr_gray.vue"
import AddIcon from "@/icons/add.vue"
import PinIcon from "@/icons/pin.vue"
import EditIcon from "@/icons/edit.vue"
import DeleteIcon from "@/icons/delete.vue"
import PackUpIcon from "@/icons/packUp.vue"
import SetupIcon from "@/icons/setup.vue"
import SaveIcon from "@/icons/save.vue"
import CancelIcon from "@/icons/cancel.vue"
import { getGoodsChatList, deleteGoodsChatList, updateChat, deleteChat } from "@/js/api.js"
import { now } from "@/js/utils.js"

// createdTime
// "2024-03-18 14:15:45"
// id
// "1769608609127206912"
// pinned
// false
// topic
// "专用测试商品"

const emit = defineEmits(['callback'])
const isShow = ref(true)
const currItem = ref({})
const currEditItem = ref({})
const lists = ref()
const isEdit = ref(false);
const cp_indeterminate = ref(false)
const cp_checkAll = ref(false)
const config = ref({})
const isAnswering = ref(false);

const onPackup = () => {
    isShow.value = false;
    emit('callback', 'packup', {})
}

const show = () => {
    isShow.value = true;
}

const onClick = (action, item) => {
    if (isAnswering.value) {
        ElMessage({
            message: '请等待右边会话完成后再操作',
            type: 'warning',
        })
        return
    }
    const id = item.id || '';
    switch (action) {
        case 'new':
            currItem.value = { createdTime: now(), id: '', pinned: false, topic: '新会话' }
            break
        case 'click':
            currItem.value = item;
            break
        case 'save':
            const { topic } = currEditItem.value;
            if (topic) {
                updateChat(id, { topic }).then(resp => {
                    if (resp.code == 0) {
                        for (let i = 0; i < lists.value.length; i++) {
                            if (lists.value[i].id == currEditItem.value.id) {
                                lists.value[i].topic = currEditItem.value.topic;
                            }
                        }
                    }
                    currEditItem.value = {}
                })
            }
            break
        case 'cancel':
            currEditItem.value = {}
            break
        case 'pin':
            const newPinned = !item.pinned;
            updateChat(id, { pinned: newPinned }).then(resp => {
                if (resp.code == 0) {
                    for (let i = 0; i < lists.value.length; i++) {
                        if (lists.value[i].id == id) {
                            lists.value[i].pinned = newPinned;
                        }
                    }
                    _sortData(lists.value);
                }
                currEditItem.value = {}
            })
            break
        case 'edit':
            currEditItem.value = item;
            break
        case 'delete':
            deleteGoodsChatList(item.id).then(resp => {
                if (resp.code == 0) {
                    lists.value = lists.value.filter(x => x.id !== item.id)
                    onClick('new', {})
                }
            })
            break
        default:
            break
    }
    emit('callback', action, toRaw(item))
}

const _sortData = (data) => {
    let pinned = data.filter(x => x.pinned)
    let unpinned = data.filter(x => !x.pinned)
    pinned = pinned.sort((a, b) => a.createdTime > b.createdTime ? -1 : 1)
    unpinned = unpinned.sort((a, b) => a.createdTime > b.createdTime ? -1 : 1)
    const merged = [...pinned, ...unpinned];
    merged.map(x => {
        x['checked'] = false
    })
    lists.value = merged;
}


const queryList = () => {
    const param = {
        searchKey: "", //搜索内容
        pageSize: 100, //每页显示个数
        pageNumber: 1, //第几页,
        topicType: config.value.topicType
    };
    getGoodsChatList(param).then(resp => {
        if (resp.code == 0) {
            _sortData(resp.data.datas);
            onClick('new', {})
        }
    })
}

const updateConv = (data) => {
    currItem.value = data;
    lists.value.unshift(data)
}

const onShowEdit = () => {
    isEdit.value = true;
}

const onCheckAllChange = (v) => {
    cp_checkAll.value = v;
    cp_indeterminate.value = false;
    lists.value.map((item) => {
        item.checked = v;
    });
}

const onCheckedChange = (v) => {
    const hasCheckCot = lists.value.filter(x => x.checked).length;
    cp_checkAll.value = hasCheckCot == lists.value.length;
    cp_indeterminate.value = hasCheckCot > 0 && !cp_checkAll.value;
}

const doDeleteItems = () => {
    const checkeds = lists.value.filter(x => x.checked);
    if (checkeds.length == 0) {
        g.emitter.emit('show_msg_box', { message: '删除数量为空，请至少选择一个', time: 3, need_icon: false })
        return
    }
    const ids = checkeds.map(x => x.id);
    deleteChat(ids).then((resp) => {
        if (resp.code == 0) {
            isEdit.value = false
            queryList()
        }
    })
}

const onClickCancelEdit = () => {
    isEdit.value = false
}

const updateConfig = (key, value) => {
    config.value[key] = value
}

const init = (cfg) => {
    config.value = cfg;
    queryList();
    g.emitter.on('set_answering', (status) => {
        isAnswering.value = status;
    })
}

onUnmounted(() => {
    g.emitter.off('set_answering');
})

defineExpose({
    init, show, isShow, currItem, lists, PRGrayIcon, PrBigIcon,
    PRIcon, AddIcon, PackUpIcon, SetupIcon, onClick, isEdit, updateConfig,
    PinIcon, EditIcon, DeleteIcon, SaveIcon, CancelIcon, updateConv,
    onCheckAllChange, onCheckedChange, cp_indeterminate, cp_checkAll, isAnswering
})
</script>

<style lang="scss">
.left_menu_wrap {
    width: 328px;
    height: 100vh;
    background-color: #F5F9FE;
    padding: 0;
    box-sizing: border-box;

    .head {
        z-index: 9;
        margin-top: 55px;

        .logo {
            padding: 16px;
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 14px;
            font-size: 40px;
            height: 72px;
            width: 72px;
        }

        h1 {
            height: 28px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #262626;
            line-height: 28px;
            text-align: right;
            font-style: normal;
        }

        .btn {
            width: 116px;
            height: 32px;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 16px;
            border: 1px solid #436BFF;
            margin-top: 32px;
            cursor: pointer;
            user-select: none;

            .txt {
                height: 22px;
                font-family: PingFangSC, PingFang SC;
                font-size: 14px;
                color: #436BFF;
                line-height: 22px;
            }
        }

        .record {
            width: 100%;
            margin-bottom: 13px;

            .line {
                width: 93px;
                height: 1px;
                background: #D8D8D8;
                border-radius: 1px;
                margin-top: 20px;
            }

            .record_txt {
                height: 22px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #8C8C8C;
                line-height: 22px;
                text-align: justify;
                font-style: normal;
                padding: 0 10px;
                margin-top: 19px;
            }
        }
    }

    .prod_box_list {
        height: calc(100vh - 345px);
        overflow-y: auto;
        overflow-x: hidden;

        .prod_bg {
            padding: 5px 0;

            .prod_box {
                width: 256px;
                height: 62px;
                border-radius: 8px;
                margin-left: 12px;
                cursor: pointer;

                .p_logo {
                    width: 50px;
                    font-size: 28px;
                }

                .item_cb {
                    width: 50px;
                    display: flex;
                    justify-content: center;
                }

                .p_right {
                    display: flex;
                    justify-content: center;

                    .title {
                        height: 22px;
                        font-size: 14px;
                        color: #262626;
                        line-height: 22px;
                        text-align: justify;

                        width: 200px;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                        white-space: nowrap;
                    }

                    .edit_title {
                        input {
                            width: 152px;
                            height: 32px;
                            background: #FFFFFF;
                            border-radius: 4px;
                            border: 1px solid #436BFF;
                            padding: 5px 12px;
                        }

                        .icon {
                            margin: 7px;
                            cursor: pointer;
                        }

                        .icon:hover {
                            color: #436BFF;
                        }
                    }

                    .dt {
                        height: 20px;
                        font-size: 12px;
                        color: #8C8C8C;
                        line-height: 20px;
                        text-align: justify;
                        justify-content: space-between;
                        width: 200px;

                        .icons {
                            .icon {
                                display: none;
                                margin: 0 5px;
                                cursor: pointer;
                            }

                            .icon:hover {
                                color: #436BFF;
                            }
                        }
                    }
                }
            }

            .active {
                border: 1px solid #436BFF;
                background: #ECF1FF;
            }



            .prod_box:hover {
                background-color: #eaefff;

                .p_right {
                    .dt {
                        .icons {
                            .icon {
                                display: block;
                            }
                        }
                    }
                }
            }
        }

        .pinned {
            background: #E8ECF5;

            .pin_icon {
                color: #436BFF;
            }
        }
    }

    .footer {
        width: 244px;
        height: 42px;
        background: #F6F9FF;
        box-shadow: inset 0px 1px 0px 0px #E9E9E9;
        position: fixed;
        bottom: 0;
        padding: 6px 16px 0 16px;
        align-items: center;
        justify-content: space-between;

        .fleft {
            .setup_icon {
                margin-top: 1px;
            }

            .txt {
                width: 56px;
                height: 22px;
                font-size: 14px;
                color: #595959;
                line-height: 22px;
                margin-left: 6px;
                cursor: pointer;
            }

        }

        .pack_up_icon {
            cursor: pointer;
        }

        .foot-view-edit-group {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 100px;
            font-size: 14px;
            font-weight: 400;
            color: #bfbfbf;
            line-height: 22px;
            min-width: 120px;

            div {
                cursor: pointer;
            }

            div:hover {
                opacity: 0.6;
            }
        }


    }
}
</style>
