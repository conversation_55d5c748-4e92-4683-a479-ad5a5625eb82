<template>
  <div :class="`cc_vist_news `" @click="onClick">
    <div class="nv_header_date flex-row">
      <div v-if="row._index == 0">
        {{ formDt(props.data.min_dt) }}
      </div>
      <div v-if="row._index == 0">
        {{ formDt(props.data.middle_dt) }}
      </div>
      <div v-if="row._index == 0">
        {{ formDt(props.data.max_dt) }}
      </div>
    </div>
    <div class="time_line"></div>
    <div class="cicles flex-row">
      <div v-for="meet in meetsInfo.data.filter((x) => x.date_isfirst)"
        :class="`cicle ${meet.date_count > 1 ? 'big_clcle' : 'cicle_box'}`" :style="getStyle(meet)" :key="meet.id">
        <el-popover :width="355" trigger="hover">
          <template #reference>
            <div class="cicle_box" @mouseenter="onHover(meet)"></div>
          </template>
          <div>
            <div class="ccv_pop_main" v-if="isLoading[currentRecord.id]"></div>
            <div :class="`ccv_pop_main ${hasGoal ? 'has_goal' : 'no_goal'}`" v-else>
              <div class="nav-controls flex-row" v-if="currentDayRecords.length > 1">
                <el-button :disabled="currentIndex === 0" @click="showPrevRecord" class="nav-btn">
                  &lt;
                </el-button>
                <span class="record-count">{{ currentIndex + 1 }}/{{ currentDayRecords.length }}</span>
                <el-button :disabled="currentIndex === currentDayRecords.length - 1" @click="showNextRecord"
                  class="nav-btn">
                  &gt;
                </el-button>
              </div>
              <div class="ccv_header flex-row">
                <div class="ccvh_right flex-column">
                  <div class="cr1 flex-row">
                    <div class="cr1_subject" :title="getInfo(currentRecord, 'subject')">
                      {{ getInfo(currentRecord, "subject") }}
                    </div>
                    <div class="cr1_link" @click="onDetail(currentRecord)">详情 ></div>
                  </div>
                  <div class="cr2 flex-row">
                    <div class="ct">
                      {{ getInfo(currentRecord, "startTime") }}
                    </div>
                    <div class="line"></div>
                    <div class="ct">
                      时长:{{ getInfo(currentRecord, "recordDurationSeconds") }}分钟
                    </div>
                    <div class="line"></div>
                    <div class="ct">
                      {{ getInfo(currentRecord, "salesMateTags") }}
                    </div>
                  </div>
                </div>
              </div>
              <ul v-if="hasSaleResult[currentRecord.id] && getInfo(currentRecord, 'salesAchievementStatus')">
                <li class="first_li">
                  <div class="bt">拜访目标达成情况</div>
                  <div class="bv" :class="getAchievementClass(getInfo(currentRecord, 'salesAchievementStatus'))">
                    {{ getInfo(currentRecord, "salesAchievementStatus") }}
                  </div>
                </li>
              </ul>

              <ul v-if="hasSaleResult[currentRecord.id]">
                <li class="first_li">
                  <div class="bt">客户态度</div>
                  <div class="bv">
                    <faceIcon :status="getInfo(currentRecord, 'customerMood')" />
                  </div>
                </li>
                <li>
                  <div class="bt">风险项</div>
                  <div class="bv">{{ getInfo(currentRecord, "riskCount") }}个</div>
                </li>
                <li>
                  <div class="bt">竞争对手</div>
                  <div class="bv">{{ getInfo(currentRecord, "competitorCount") }}个</div>
                </li>
                <li class="last_li">
                  <div class="bt">待办</div>
                  <div class="bv">{{ getInfo(currentRecord, "pendingTaskCount") }}个</div>
                </li>
              </ul>

              <ul v-if="hasSaleResult[currentRecord.id]">
                <li class="last_li">
                  <div class="bt">能力评估</div>
                  <div class="bv" :class="getScoreClass(getInfo(currentRecord, 'salesAbilityScore'))">
                    {{ getInfo(currentRecord, "salesAbilityScore") }}
                  </div>
                </li>
                <li class="last_li">
                  <div class="bt">任务达成</div>
                  <div class="bv" :class="getScoreClass(getInfo(currentRecord, 'taskCompleteRatio'))">
                    {{ getInfo(currentRecord, "taskCompleteRatio") }}
                  </div>
                </li>
              </ul>

              <div v-if="!hasSaleResult[currentRecord.id]" class="vn_no_data">
                <img :src="getOssUrl('rc_no_data.png', 2)" alt="No Data" />
                <div class="no_data_text">
                  <span class="ndt_1">
                    当前未标注发言人，暂无法生成对应总结、分析与辅导建议。
                  </span>
                  <span class="ndt_2" @click="onDetail(currentRecord)">前往标注&nbsp; &gt;</span>
                </div>
              </div>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getConfAnalyseRisks } from "@/app_client/tools/api.js";
import { formatDate, getOssUrl } from "@/js/utils.js";
import { calcProcessToData } from "./misc.js";
import faceIcon from "@/app_client/components/faceIcon.vue";
import LogoIcon from "@/app_client/icons/logo2.vue";
import { watch } from "vue";
import { onMounted } from "vue";

const props = defineProps(["row", "data"]);
const meetsInfo = ref({ data: [] });
const riskInfos = ref({});
const isLoading = ref({});
const emit = defineEmits(["callback"]);
const hasGoal = ref(false);
const hasSaleResult = ref({});
const currentDayRecords = ref([]);
const currentIndex = ref(0);
const currentRecord = ref({ id: null, date: null });

const init = () => {
  meetsInfo.value = calcProcessToData(props.row.meets, props.data);
};

watch(
  props,
  () => {
    init();
  },
  { deep: true }
);

onMounted(() => {
  init();
});

const onDetail = (meet) => {
  g.clientStore.viewPlanRecord({
    conferenceId: meet.id,
    recognitionPath: meet.recognitionPath,
  });
};

const formDt = (dt) => {
  return formatDate(new Date(dt), "MM/dd");
};

const getStyle = (meet) => {
  return { left: meet.process + "%", backgroundColor: meet.color };
};

const _checkHasSaleResult = (data) => {
  const relevantFields = [
    "competitorCount",
    "customerMood",
    "longestSalesMinutes",
    "patienceSeconds",
    "pendingTaskCount",
    "riskCount",
    "salesAchievementStatus",
    "speakingRate",
    "salesAbilityScore",
    "taskCompleteRatio",
  ];

  return relevantFields.some((field) => {
    const value = data[field];
    return (
      (typeof value === "number" && value > 0) ||
      (typeof value === "string" && value.trim() !== "")
    );
  });
};

const showPrevRecord = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    currentRecord.value = currentDayRecords.value[currentIndex.value];
    onHover(currentRecord.value);
  }
};

const showNextRecord = () => {
  if (currentIndex.value < currentDayRecords.value.length - 1) {
    currentIndex.value++;
    currentRecord.value = currentDayRecords.value[currentIndex.value];
    onHover(currentRecord.value);
  }
};

const onHover = (meet) => {
  const { id, date } = meet;
  // Filter records for the current date
  currentDayRecords.value = meetsInfo.value.data.filter(m => m.date === date);
  // Find index of current record
  currentIndex.value = currentDayRecords.value.findIndex(m => m.id === id);
  currentRecord.value = meet;

  if (!riskInfos.value[id]) {
    getConfAnalyseRisks(id).then((resp) => {
      if (resp.code == 0) {
        isLoading.value[id] = false;
        riskInfos.value[id] = resp.data;
        hasGoal.value = !!resp.data.salesGoal;
        hasSaleResult.value[id] = _checkHasSaleResult(resp.data);
      }
    });
  }
};

const getClass = (meet, key) => {
  const { id } = meet;
  let result = "";
  if (riskInfos.value[id]) {
    if (key == "salesPerformance") {
      const map = {
        需改进: "s0",
        达到预期: "s1",
        优秀: "s2",
      };
      result = map[meet["salesPerformance"]];
    }
  }
  return result;
};

const getInfo = (meet, key) => {
  const id = meet.id;
  let result = "";
  if (riskInfos.value[id]) {
    isLoading.value[id] = false;
    result = riskInfos.value[id][key];
    if (key == "startTime") {
      result = result.substring(0, 16);
    } else if (key == "recordDurationSeconds") {
      result = Math.round(result / 60);
    } else if (key == "salesAchievementStatus") {
      if (hasSaleResult.value[id]) {
        if (hasGoal.value) {
          // 使用原来的值
        } else {
          result = "目标未填写";
        }
      } else {
        hasGoal.value = false;
      }
    } else if (key == 'salesAbilityScore') {
      result = result ? (result + '分') : '结果未评估';
    } else if (key == 'taskCompleteRatio') {
      result = result ? (result + '%') : '结果未评估';
    }
  } else {
    isLoading.value[id] = true;
  }
  return result;
};

const onClick = () => {
  emit("callback", props.row, "visitRecord");
};

const getScoreClass = (value) => {
  const score = Number(value);
  if (score >= 80) return 'score-high';
  if (score < 60) return 'score-low';
  return 'score-medium';
};

const getAchievementClass = (status) => {
  switch (status) {
    case '达成':
      return 'score-high';
    case '未达成':
      return 'score-low';
    case '无法判定':
      return 'score-medium';
    default:
      return '';
  }
};

defineExpose({
  props,
  onHover,
  LogoIcon,
  riskInfos,
  getInfo,
  isLoading,
  getClass,
  faceIcon,
  meetsInfo,
  formDt,
  onDetail,
  onClick,
  hasGoal,
});
</script>

<style lang="scss">
.cc_vist_news {
  position: absolute;
  width: 100%;
  padding-right: 22px;
  cursor: pointer;

  .nv_header_date {
    position: relative;
    justify-content: space-between;
    top: -27px;
    height: 16px;

    div {
      height: 18px;
      font-size: 10px;
      color: #8c8c8c;
      line-height: 18px;
      font-weight: 400;
    }
  }

  .time_line {
    position: relative;
    width: 100%;
    height: 1px;
    background: #f0f1f5;
    top: -17px;
  }

  .cicles {
    padding: 0 36px 0 0;
    width: 89%;
    position: absolute;

    .cicle {
      position: absolute;
      border-radius: 50%;
      cursor: pointer;

      .cicle_box {
        width: 100%;
        height: 100%;
      }
    }

    .cicle_box {
      width: 8px;
      height: 8px;
      top: -22px;
    }

    .big_clcle {
      width: 14px;
      height: 14px;
      top: -24px;
    }
  }
}

.has_goal {
  // height: 616px;
}

.no_goal {
  height: 200px;
}

.ccv_pop_main {
  margin-left: 6px;
  overflow-y: auto;

  .nav-controls {
    padding: 8px 0;
    justify-content: left;
    align-items: center;
    border-bottom: 1px solid #f0f1f5;
    margin-bottom: 12px;

    .nav-btn {
      font-size: 12px;
      padding: 4px 12px;
      border: none
    }

    .record-count {
      margin: 0 2px;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .ccv_header {
    margin-bottom: 12px;

    .icon {
      margin-right: 16px;
    }

    .ccvh_right {
      .cr1 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 24px;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;

        .cr1_subject {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #262626;
          margin-right: 8px; // Add some space between subject and link
          width: 168px;
        }

        .cr1_link {
          flex-shrink: 0; // Prevent the link from shrinking
          color: #436bff;
          cursor: pointer;
          font-size: 14px;
        }
      }

      .cr2 {
        font-size: 12px;
        width: 316px;

        .line {
          width: 1px;
          height: 12px;
          background: #d9d9d9;
          margin: 2px 4px 0 4px;
        }
      }
    }
  }

  ul {
    border-radius: 8px;
    margin-top: 16px;

    li {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 52px;
      background: #f9fafc;
      padding: 0 16px;
      align-items: center;

      .s0 {
        color: #ff4d4f;
      }

      .s1 {
        color: #436bff;
      }

      .s2 {
        color: #52c41a;
      }

      .bv {
        &.score-high {
          color: #52c41a;
        }

        &.score-medium {
          color: #436bff;
        }

        &.score-low {
          color: #ff4d4f;
        }
      }
    }

    li.first_li {
      border-radius: 8px 8px 0 0;
    }

    li.last_li {
      border-bottom: none;
      border-radius: 0 0 8px 8px;
    }
  }

  .vn_no_data {
    width: 316px;
    height: 342px;
    background: #F9FAFC;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .no_data_text {
      width: 269px;
      flex-wrap: wrap;

      .ndt_2 {
        color: #436bff;
        cursor: pointer;
      }
    }
  }
}
</style>
