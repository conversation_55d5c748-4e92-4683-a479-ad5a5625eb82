<template>
    <el-dialog title="商品详情" v-model="dialogVisible" width="650px" :before-close="handleClose" class="goodDeail_wrap"
        :fullscreen="true">
        <ul>
            <li v-for="item in data">
                <div class="t1">{{ item[0] }}</div>
                <div class="t2">{{ item[1] }}</div>
            </li>
        </ul>
    </el-dialog>
</template>

<script setup>
const data = ref({})
const dialogVisible = ref(false)

const show = (rowData) => {
    const notShowColumns = ['is_checkable_', 'id', 'categoryId', 'listId', "_label"]
    data.value = Object.entries(rowData).filter(x => !notShowColumns.includes(x[0]))
    dialogVisible.value = true
}

const handleClose = () => {
    dialogVisible.value = false
}

defineExpose({
    show,
    handleClose,
    data,
    dialogVisible
})
</script>

<style lang="scss">
.goodDeail_wrap {

    .el-dialog {
        margin-top: 52px;
    }

    ul {
        overflow-y: auto;

        li {
            display: flex;
            flex-direction: row;

            .t1 {
                width: 120px;
                height: 22px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #8C8C8C;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .t2 {
                width: 389px;
                color: #262626;
            }
        }
    }

}
</style>
