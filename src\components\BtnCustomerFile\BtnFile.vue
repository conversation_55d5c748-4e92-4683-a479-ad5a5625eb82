<template>
    <div class="customer_btn_file" v-if="hasImportAccess || hasExportAccess">
        <el-dropdown @command="handleCommand" type="default">
            <span class="el-dropdown-link flex-row">
                <div class="vbb_title">
                    导入导出
                </div>
                <el-icon class="el-icon--right">
                    <ArrowDown />
                </el-icon>
            </span>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="file_import"
                        v-if="hasImportAccess">导入&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</el-dropdown-item>
                    <el-dropdown-item command="file_export" v-if="hasExportAccess">导出</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <input class="hidden" type="file" ref="refFile" multiple @change="changeHandle" @click="fileUploadCheck" />
        <DrawerUpload ref="refDrawerUpload" :isAdmin="isAdmin" />
    </div>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import DrawerUpload from "./DrawerUpload.vue";
import { exportCustomer } from "@/js/api";
import { getCustomerTypeList } from "@/js/api.js";
import { exportCustomerList } from "@/app_client/tools/api.js"

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    },
    param: {
        type: Object,
        required: false,
    },
    team: {
        type: Boolean,
        required: false,
        default: false
    }
});

const refDiaEdit = ref();
const emit = defineEmits(['reload']);
const refFile = ref(null);
const refDrawerUpload = ref(null);
const pageAccess = ref({})
const hasImportAccess = ref(false);
const hasExportAccess = ref(false);

const showEdit = (row) => {
    refDiaEdit.value.show(row);
}

const setAccess = (access) => {
    pageAccess.value = access;
    if (props.isAdmin) {
        hasImportAccess.value = pageAccess.value.customer_import_opr;
        hasExportAccess.value = pageAccess.value.customer_export_opr;
    }
}

const _onExportAdminCustomer = () => {
    const data = {
        "customerTypeId": "",
        "createType": 0,
    }
    data.filename = '客户列表_' + new Date().getTime();
    exportCustomer(data)
}

const onExportUserCustomer = () => {
    const data = toRaw(props.param);
    data.filename = '客户管理_' + new Date().getTime();
    exportCustomerList(props.team, data)
}



const handleCommand = (cmd) => {
    if (cmd == 'file_import') {
        refDrawerUpload.value.show();
    } else if (cmd == 'file_export') {
        if (props.isAdmin) {
            _onExportAdminCustomer()
        } else {
            onExportUserCustomer()
        }
    }
}

const getCustomerTypes = () => {
    getCustomerTypeList({ status: 1 }).then(res => {
        let list = res.data;
        // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
        hasImportAccess.value = list.filter(x => x?.permissionsStr && x?.permissionsStr?.indexOf('2') > -1).length > 0;
        hasExportAccess.value = hasImportAccess.value;

    });
};

onMounted(() => {
    if (!props.isAdmin) {
        getCustomerTypes();
    }
});


defineExpose({ handleCommand, refDiaEdit, showEdit, setAccess })
</script>

<style lang="scss">
.customer_btn_file {

    .el-dropdown-link {

        .vbb_title {
            margin-top: 3px;
        }

        .el-icon {
            margin-top: 3px;
        }
    }

    .el-dropdown {
        height: 18px;
        padding: 6px 12px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
        border-radius: 4px;

        .el-dropdown-item {
            width: 300px;
        }
    }

    .vbp_title {
        margin-bottom: 12px;

        span {
            color: red;
            margin-left: 4px;
        }
    }
}
</style>