<template>
    <el-popover placement="bottom-start" :width="400" popper-class="speaker-type-popover" trigger="contextmenu"
        v-model:visible="isShowAnalysisType">
        <template #reference>
            <div class="custom-button" :class="{ 'is-primary': rdViewType == 'normal_meet' }" @click="onClickTitle">
                <span class="label-text">
                    {{ localAnalysisType?.label || '' }}
                </span>
                <el-icon class="icon-arrow" @click.stop="openPopover">
                    <ArrowDown />
                </el-icon>
            </div>
        </template>
        <div class="menu-container">
            <div class="menu-item" v-for="item in analysisTypes" :key="item.value" @click="cbAnalysisType(item)">
                <div class="item-title">{{ item.label }}</div>
                <div class="item-desc">{{ item.description }}</div>
            </div>
        </div>
    </el-popover>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'

const rdViewType = ref('')
const emit = defineEmits(['update:analysisType', 'change']);
const localAnalysisType = ref({ label: '', value: '' });
const analysisTypes = ref([]);
const isShowAnalysisType = ref(false);

const init = () => {
    analysisTypes.value = g.cacheStore.salesMethodology;
    if ((!localAnalysisType.value?.value || localAnalysisType.value.value === '') && analysisTypes.value?.length > 0) {
        localAnalysisType.value = analysisTypes.value[0];
    }
}

const onClickTitle = () => {
    rdViewType.value = 'normal_meet';
    emit("change", localAnalysisType.value)
}

const openPopover = () => {
    isShowAnalysisType.value = true;
}

const cbAnalysisType = (selectType) => {
    rdViewType.value = 'normal_meet';
    localAnalysisType.value = selectType;
    isShowAnalysisType.value = false;
    emit("update:analysisType", selectType)
    emit("change", selectType)
}

const setAnalysisType = (analysisType) => {
    if (analysisType && analysisTypes.value?.length) {
        const foundType = analysisTypes.value.find(x => x.value == analysisType);
        if (foundType) {
            localAnalysisType.value = foundType;
        }
    }
}

const setRdType = (rdType) => {
    rdViewType.value = rdType;
}

onMounted(() => {
    init();
});

defineExpose({
    setAnalysisType,
    setRdType
})

</script>

<style lang="scss" scoped>
.custom-button {
    width: 80px;
    height: 32px;
    padding: 0 12px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s;
    margin: 12px 12px 0 0;
}

.custom-button:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
}

.custom-button.is-primary {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: #fff;
}

.custom-button.is-primary:hover {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: #fff;
}

.label-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.icon-arrow {
    margin-left: 8px;
    font-size: 14px;
}

.menu-container {
    .menu-item {
        padding: 12px 16px;
        cursor: pointer;

        &:hover {
            background-color: #f5f7fa;
        }

        .item-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .item-desc {
            font-size: 12px;
            color: #909399;
        }
    }
}
</style>