<template>
    <div class="settings-container">
        <div class="settings-nav">
            <div v-for="(item, index) in navItems.filter(x => x.show)" :key="index" class="nav-item"
                :class="{ active: currentNav === item.key }" @click="currentNav = item.key">
                <component :is="item.icon" />
                <span>{{ item.label }}</span>
            </div>
        </div>
        <div class="settings-content">
            <component :is="currentComponent" />
        </div>
    </div>
</template>

<script setup>
import GeneralSettings from './GeneralSettings.vue';
import AudioSettings from './AudioSettings.vue';
import RecordingSettings from './RecordingSettings.vue';
import DeviceSettings from './device/DeviceSettings.vue';
import AboutSettings from './AboutSettings.vue';
import NoticeSettings from './NoticeSettings.vue';

import GeneralIcon from '@/app_electron/icons/setting_general.vue';
import AudioIcon from '@/app_electron/icons/setting_audio.vue';
import RecordingIcon from '@/app_electron/icons/setting_recording.vue';
import AboutIcon from '@/app_electron/icons/setting_about.vue';
import EcardIcon from '@/app_electron/icons/setting_ecard.vue';
import NoticeIcon from '@/app_electron/icons/setting_notice.vue';


const navItems = ref([]);
const currentNav = ref('general');

const currentComponent = computed(() => {
    const componentMap = {
        general: GeneralSettings,
        audio: AudioSettings,
        recording: RecordingSettings,
        device: DeviceSettings,
        about: AboutSettings,
        notice: NoticeSettings,
    };
    return componentMap[currentNav.value] || GeneralSettings;
});

onMounted(async () => {
    await g.appStore.getAllFuncPoints();
    await g.appStore.reflashUserInfo();
    const isShowDevice = g.appStore.getFuncStatus('bind_badge')
    navItems.value = [
        { key: 'general', label: '通用设置', icon: markRaw(GeneralIcon), show: true },
        { key: 'audio', label: '音频', icon: markRaw(AudioIcon), show: true },
        { key: 'recording', label: '录制', icon: markRaw(RecordingIcon), show: true },
        { key: 'notice', label: '消息通知', icon: markRaw(NoticeIcon), show: true },
        { key: 'device', label: '工牌设备绑定', icon: markRaw(EcardIcon), show: isShowDevice },
        { key: 'about', label: '关于', icon: markRaw(AboutIcon), show: true },
    ]
})

</script>

<style lang="scss" scoped>
.settings-container {
    display: flex;
    height: 556px;
    border-top: 1px solid #E0E0E0;

    .settings-nav {
        width: 183px;
        padding: 20px;
        border-right: 1px solid #E0E0E0;

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            color: #595959;

            &.active {
                color: #436BFF;
            }
        }
    }

    .settings-content {
        flex-grow: 1;
        padding: 20px;
    }
}
</style>
