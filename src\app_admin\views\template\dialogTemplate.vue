<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close>
    <el-form
      ref="refForm"
      :model="formData"
      label-width="auto"
      label-position="top"
      size="default"
      :rules="rules"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="formData.name" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="应用场景" prop="scenario">
        <el-input v-model="formData.scenario" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="场景说明" prop="description">
        <el-input
          type="textarea"
          v-model="formData.description"
          maxlength="200"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { reactive, ref, toRaw } from "vue";
import { createPptTemplate, updatePptTemplate } from "@/app_admin/tools/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
const refModal = ref();
const title = ref("");
const refForm = ref();
const emit = defineEmits(["callback"]);
let templateId = "";

const defaultForm = {
  name: "",
  scenario: "",
  description: "",
};

const formData = ref(defaultForm);

const cfg = {
  width: "480px",
};

const show_add = () => {
  templateId = "";
  cfg["title"] = "新建模板";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (id, data) => {
  templateId = id;
  formData.value = { ...data };
  cfg["title"] = "编辑模板";
  refModal.value.show(cfg);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;
  refForm.value.validate((valid, fields) => {
    if (valid) {
      if (!templateId) {
        const data = toRaw(formData.value);
        apiHintWrap(createPptTemplate(data), cfg["title"]).then(
          ({ status, resp }) => {
            if (status) {
              formData.value = defaultForm;
              emit("callback", "create_ok", resp.data);
              refModal.value.hide();
            }
          }
        );
      } else {
        const data = toRaw(formData.value);
        apiHintWrap(updatePptTemplate(templateId, data), cfg["title"]).then(
          ({ status }) => {
            if (status) {
              emit("callback", "reload", formData.value);
              formData.value = defaultForm;
              refModal.value.hide();
            }
          }
        );
      }
    }
  });
};

const rules = reactive({
  name: [
    { required: true, message: "请输入模板名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度需要在2到50之间", trigger: "blur" },
  ],
  scenario: [
    { required: true, message: "请输入应用场景", trigger: "blur" },
    { min: 2, max: 50, message: "长度需要在2到50之间", trigger: "blur" },
  ],
});

defineExpose({ title, show_add, show_edit, cbModal, formData, rules });
</script>

<style lang="scss">
.el-dialog__body {
  padding: 15px 24px 5px 24px;
}
</style>
