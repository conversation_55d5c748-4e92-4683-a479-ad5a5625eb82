<template>
    <div class="checkSource_wrap_cu">
        <div class="cvh_title">
            客户反馈
        </div>
        <div class="content_section flex-col">
            <FilterCondition />
            <div class="content_inner flex-row">
                <CategoryList />
                <div class="feedback_list">
                    <FeedbackList :data="cvCustomerFeedbackList" :loading="loading" />
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" background
                        layout="total,  prev, pager, next" :total="parseInt(totalNum)"
                        @current-change="handleCurrentChange" class=checkSource_wrap_pagination
                        v-show="totalNum > pageSize" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import FilterCondition from './components/FilterCondition.vue';
import CategoryList from './components/CategoryList.vue';
import FeedbackList from './components/FeedbackList.vue';

const store = g.clientBiStore
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(4)
const totalNum = ref(0)

const cvCustomerFeedbackList = computed(() => {
    const listP = store.cvCustomerFeedbackListP;
    totalNum.value = listP.totalNum;
    return listP.datas;
})


const handleCurrentChange = (val) => {
    currentPage.value = val
    init()
}

const init = async () => {
    loading.value = true
    const param = {
        pageNumber: currentPage.value,
        pageSize: pageSize.value,
    }
    await store.getCvCustomerFeedbackList(param)
    loading.value = false
}

onMounted(async () => {
    currentPage.value = 1
    init()
})

watch(() => [g.clientBiStore.periodType, g.clientBiStore.cvSearchVersion], () => {
    currentPage.value = 1
    init()
}, { immediate: true })

</script>

<style lang="scss">
.checkSource_wrap_cu {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .cvh_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }

    .filter_section {
        margin-bottom: 16px;
    }

    .content_section {
        gap: 16px;
        min-height: 0;
        padding: 24px;
        background: #F9FAFC;
        border-radius: 8px;
        margin: 24px 15px 24px 0;

        .content_inner {
            padding: 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .feedback_list {
                width: calc(100% - 240px);

                .el-pagination {
                    // margin: 12px 24px;
                }
            }
        }
    }

    .checkSource_wrap_pagination {
        display: flex;
        justify-content: flex-end;
        // margin: 12px 24px;
    }
}

.bottom_details_filter {
    margin: 24px 0 24px 15px;
}
</style>