<template>
  <div class="media-editor flex-col">
    <Toolbar />
    <div class="md_main flex-row">
      <LeftMenu />
      <TimeLine ref="refTimeLine"> </TimeLine>
    </div>
  </div>
</template>

<script setup>
import Toolbar from "@/app_clipper/components/Toolbar.vue";
import TimeLine from "@/app_clipper/components/TimeLine.vue";
import LeftMenu from "@/app_clipper/components/LeftMenu.vue";
const refTimeLine = ref(null);

const init = () => {
  refTimeLine.value.init();
};

defineExpose({
  init,
  TimeLine,
  Toolbar,
  LeftMenu,
});
</script>

<style lang="scss" scoped>
.media-editor {
  height: 100%;

  .md_main {
    height: 100%;

    .right_menu {
      flex: 1;
    }
  }
}
</style>
