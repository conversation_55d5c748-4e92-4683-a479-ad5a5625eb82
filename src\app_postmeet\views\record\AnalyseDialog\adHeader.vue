<template>
  <div class="adv_header_wrap" :class="{ folded: isFolded }">
    <div class="adv_header_wrap_inner" v-for="[key, items] in Object.entries(list)" :key="key" v-show="!isFolded">
      <div class="padv_title">
        {{ key }}
      </div>
      <div class="adv_items flex-row">
        <div :class="`adt ${row.label == currId && currType == key ? 'adt_active' : 'adt_normal'
          }`" v-for="row in items" @click="onClick(key, row)" :key="row.label">
          {{ getLabel(row) }}
        </div>
      </div>
    </div>

    <div class="keyword_box" v-show="!isFolded">
      <div class="padv_title">关键词</div>
      <div class="kb_bottom flex-row">
        <div :class="`adv_items kw_items ${isShowAll ? '' : 'kw_items_1'} flex-row`" ref="keywordsContainer">
          <div :class="`adt ${currKeyword == keyword ? 'adt_active' : 'adt_normal'}`" v-for="keyword in keywords"
            @click="onClickKeyword(keyword)" :key="keyword">
            {{ keyword }}
          </div>
        </div>
        <div class="showArrowBtn" v-if="hasMultipleLines" @click="onShowAll">
          <ArrowUp v-if="!isShowAll" />
          <ArrowDown v-else />
        </div>
      </div>
    </div>

    <div class="word_nav flex-row" v-show="item.label">
      <div>{{ item.type == "todo" ? "待办" : item.label }}</div>
      <div class="wn_right flex-row">
        <div class="wn_icon" @click="onPrev">
          <PrevIcon />
        </div>
        <div>
          {{ `${index}/${count}` }}
        </div>
        <div class="wn_icon" @click="onNext">
          <NextIcon />
        </div>
        <div class="wn_icon" @click="onClear">
          <ClearIcon />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PrevIcon from "@/app_postmeet/components/rightHeader/btnSearch/icons/prev.vue";
import NextIcon from "@/app_postmeet/components/rightHeader/btnSearch/icons/next.vue";
import ClearIcon from "@/app_postmeet/components/rightHeader/btnSearch/icons/clear.vue";
import ArrowUp from "@/icons/arrow_up.vue";
import ArrowDown from "@/icons/arrow_down.vue";
import { toRaw } from "vue";
export default {
  components: { PrevIcon, NextIcon, ClearIcon, ArrowUp, ArrowDown },
  data() {
    return {
      list: [],
      item: {},
      currKeyword: "",
      index: 1,
      count: 0,
      currType: "",
      keywords: [],
      isShowAll: false,
      currId: "summary",
      isFolded: false,
      hasMultipleLines: false,
    };
  },
  mounted() {
    this.init();
    this.checkKeywordsOverflow();
    window.addEventListener("resize", this.checkKeywordsOverflow);
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
    g.emitter.on("after_analyse_dia", () => {
      this.init();
    });
    g.emitter.on("right_header_search", ([action, data]) => {
      if (action == "clear") {
        this.currKeyword = "";
      }
    });
    g.emitter.on("setAnalyseTag", (data) => {
      const recordViewPermission = g.postmeetStore.getRecordViewPermission();
      if (!recordViewPermission) {
        return;
      }
      if (data.length > 0) {
        const [item, index, type] = data;
        this.currType = type;
        this.onClick(type, item, index);
      }
    });
    g.emitter.on("toggle_analyse_fold", (folded) => {
      this.isFolded = folded;
    });
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.checkKeywordsOverflow);
  },
  methods: {
    init() {
      this.list = g.postmeetStore.data.saleAnalyseList;
      this.keywords = g.postmeetStore.data.keywords;
      this.$nextTick(() => {
        this.checkKeywordsOverflow();
      });
      for (let [key, items] in Object.entries(this.list)) {
        for (row in items) {
          if (row.label == this.currId) {
            this.onClick(this.currType, row, this.index);
          }
        }
      }
      this.$forceUpdate();
    },
    onClickKeyword(keyword) {
      this.onClear();
      if (this.currKeyword == keyword) {
        this.currKeyword = "";
        g.emitter.emit("onSearchKeyword", "");
      } else {
        this.currKeyword = keyword;
      }
      g.emitter.emit("onSearchKeyword", this.currKeyword);
    },
    getLabel(row) {
      if (row.count && row.type != "todo") {
        return `${row.label}(${row.count})`;
      } else {
        return row.label;
      }
    },
    _emit() {
      this.$emit("callback", this.index - 1, toRaw(this.item), this.currType);
    },
    onClick(key, row, index = 1) {
      console.log("onClick", key, row, index);
      row = toRaw(row);
      this.currId = row.label;
      this.item = row;
      this.currType = key;
      this.index = index;
      this.count = row.count;
      this.currKeyword = "";
      this._emit();
    },
    onPrev() {
      if (this.index > 1) {
        this.index -= 1;
        this._emit();
      }
    },
    onNext() {
      if (this.index < this.count) {
        this.index += 1;
        this._emit();
      }
    },
    onShowAll() {
      this.isShowAll = !this.isShowAll;
    },
    onClear() {
      this.index = 0;
      this.currId = "";
      this.currType = "";
      this.item = {};
      this._emit();
      g.emitter.emit("toggle_analyse_fold", false);
      g.emitter.emit("filterAnalyseType", { type: "" });
    },
    checkKeywordsOverflow() {
      this.$nextTick(() => {
        const container = this.$refs.keywordsContainer;
        if (container) {
          const containerHeight = container.getBoundingClientRect().height;
          // 假设单行高度为35px（与 kw_items_1 的高度一致）
          this.hasMultipleLines = containerHeight > 35;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.adv_header_wrap {
  z-index: 3;
  position: relative;
  background-color: #f7f8fc;
  padding: 12px;
  transition: height 0.3s ease;

  &.folded {
    height: auto;
    padding: 0 15px 12px 0;
    background-color: transparent;

    .word_nav {
      background: #f7f8fc;
      box-shadow: none;
    }
  }

  .padv_title {
    font-weight: 500;
    font-size: 14px;
    color: #262626;
    line-height: 24px;
    padding: 6px 4px;
  }

  .adv_items {
    .adt {
      font-size: 14px;

      line-height: 20px;
      padding: 4px 8px;
      margin: 0 6px;

      border-radius: 4px;
      user-select: none;
      cursor: pointer;
    }

    .adt_normal {
      color: #436bff;
      background: #f1f3fd;
    }

    .adt_active {
      color: #fff;
      background: #436bff;
    }
  }

  .keyword_box {
    .adt {
      margin: 2px 5px 2px 0;
    }

    .kb_bottom {
      .kw_items {
        flex-wrap: wrap;
        overflow: hidden;
        width: 295px;
        display: flex; // 确保flex布局生效
        gap: 8px; // 使用gap属性统一设置间距
      }

      .kw_items_1 {
        height: 35px;
      }

      .showArrowBtn {
        color: #436bff;
        margin-top: 7px;
        user-select: none;
        cursor: pointer;
        padding-left: 4px; // 添加左侧内边距，与关键词保持一定距离
      }
    }
  }

  .word_nav {
    width: 276px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 4px 20px 0px #ffffff;
    border-radius: 8px;
    align-items: center;
    padding: 0 16px;
    margin-top: 12px;
    justify-content: space-between;

    .wn_right {
      align-items: center;

      .wn_icon {
        margin: 6px 6px 0 6px;
        cursor: pointer;
      }

      .wn_icon:hover {
        color: #436bff;
        cursor: pointer;
      }
    }
  }

  &.folded {
    height: auto;
    padding: 0 15px 12px 0;
  }
}
</style>
