<template>
  <div class="record_header_wrap">
    <div class="rh_left">
      <div class="rh_logo" @click="goPremeet">
        <img :src="getOssUrl('logo_mini.png')" />
      </div>
      <div class="rh_meet_info">
        <DivEdit v-model="roomInfo.subject" class="rh_title" @callback="onCbSubject">
        </DivEdit>
        <div class="rh_sub_title" v-show="roomInfo && roomInfo.startTime">
          <template v-for="(item, index) in getSubTitle()" :key="index">
            <span class="subtitle-text">{{ item }}</span>
            <div v-if="index !== getSubTitle().length - 1" class="subtitle-separator"></div>
          </template>
        </div>
      </div>
    </div>
    <div class="_blank"></div>
    <div class="rh_right">
      <div class="ebtn" @click="onMoreClick('share')" v-show="!readonly || isTeamLeader">
        <shareIcon />
      </div>
      <div class="ebtn" @click="onMoreClick('export_subtitle')" v-show="recordViewPermission">
        <exportIcon />
      </div>
      <div v-if="showMailBtn || !readonly && recordViewPermission">
        <el-dropdown @command="onMoreClick" trigger="click">
          <div class="ebtn" @click="onMoreClick('more')">
            <moreIcon />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="mail" v-if="showMailBtn">生成沟通纪要邮件</el-dropdown-item>
              <el-dropdown-item command="delete" v-if="!readonly && recordViewPermission">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="rh_name">
        <usericon :usemy="true"></usericon>
      </div>
    </div>
    <btnShare ref="refShare"></btnShare>
    <btnRePerson ref="refReperson"></btnRePerson>
    <btnExport ref="refExport"></btnExport>
    <DeleteConfirm ref="deleteConfirmRef" :title="`确定彻底删除吗会议录制？`" content="注意！删除沟通记录后录制文件与访后生成内容均会删除" confirm-text="删除"
      cancel-text="取消" @confirm="handleDeleteConfirm" />
  </div>
</template>
<script>
import {
  timeDiffSecond,
  parseRecordsTag,
  formateTime,
  downloadTxt,
} from "@/app_postmeet/tools/tools";
import DivEdit from "@/app_postmeet/components/div_edit.vue";
import { updateSubject, getIsUserLeader } from "@/app_postmeet/tools/api";
import btnExport from "../subtitlesSummary/btnExport.vue";
import btnShare from "../subtitlesSummary/btnShare.vue";
import btnRePerson from "../subtitlesSummary/btnRePerson.vue";
import usericon from "@/app_postmeet/components/usericon.vue";
import exportIcon from "@/app_postmeet/icons/exportIcon.vue";
import shareIcon from "@/app_postmeet/icons/share.vue";
import moreIcon from "@/app_postmeet/icons/moreIcon.vue";
import { getOssUrl } from "@/js/utils.js";
import { deleteRecordByConfId } from "@/app_client/tools/api.js"
import DeleteConfirm from "@/components/DeleteConfirm.vue";

export default {
  name: "RecordHeader",
  components: {
    DivEdit,
    exportIcon,
    btnShare,
    btnRePerson,
    btnExport,
    usericon,
    moreIcon,
    shareIcon,
    DeleteConfirm,
  },
  data() {
    return {
      roomInfo: { subject: "" },
      hostName: "",
      hasAsr: "",
      username: "",
      activeName: "",
      activeName3: "",
      currVidioTime: "",
      records: [],
      currRecord: {},
      readonly: true,
      isTeamLeader: false,
      hasChat: false,
      showMailBtn: false,
      recordViewPermission: false,
      from_share: location.href.indexOf("from=share") > -1,
      iconClass: "yxtf-icon-arrow-down yxtf-icon--right",
      hasInitShare: false
    };
  },
  mounted() {
    g.emitter.on("open_dia_reperson", () => {
      this.$refs.refReperson.show();
    });
    g.emitter.on('set_play_record_tab', (item) => {
      this.handleClick(item)
    })
  },
  methods: {
    getOssUrl,
    init() {
      this.showMailBtn =
        !g.postmeetStore.isReadonly() &&
        g.postmeetStore.data.asrRaw.mailTemplate &&
        g.postmeetStore.data.needAsrMail;
      this.username = g.appStore.user.userName;
      this.hasAsr = g.postmeetStore.getHasAsr();
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      this.toPlay(this.currRecord);
      const that = this;
      g.emitter.on("gen_sale_mail", () => {
        that._downSaleMail();
      });
    },
    getIsUserLeaderWrap() {
      if (this.hasInitShare) {
        return
      }
      const user = g.appStore.user;
      const hostId = g.postmeetStore.data.playItem.hostUserId;
      if (!user.id && !hostId) {
        return;
      }
      if (user.id == hostId) {
        this.isTeamLeader = true;
        return;
      }

      if (hostId && user?.token) {
        getIsUserLeader(hostId).then((resp) => {
          if (resp.code == 0) {
            this.isTeamLeader = resp.data;
          }
          this.hasInitShare = true;
        })
      }
    },
    getSubTitle() {
      const parts = [];
      if (this.currRecord?.customerName) {
        parts.push(this.currRecord.customerName);
      }

      if (g.postmeetStore.data.asrRaw.tags) {
        parts.push(g.postmeetStore.data.asrRaw.tags);
      }

      if (this.roomInfo?.startTime) {
        parts.push(this.roomInfo.startTime);
      }

      parts.push(`时长：${this.currVidioTime}`);

      if (this.currRecord?.hostName) {
        parts.push(this.currRecord.hostName);
      }

      // 过滤掉空值
      return parts.filter((item) => item);
    },
    onMoreClick(e) {
      if (e == "share") {
        this.$refs.refShare.show();
      } else if (e == "reperson") {
        this.$refs.refReperson.show();
      } else if (e == "export_subtitle") {
        this.$refs.refExport.onExport();
      } else if (e == "export_kng_lib") {
        this.$refs.refExportLib.show();
      } else if (e == "mail") {
        this._downSaleMail();
      } else if (e == "delete") {
        this._delete();
      }
    },
    _delete() {
      this.$refs.deleteConfirmRef.show();
    },
    handleDeleteConfirm() {
      deleteRecordByConfId(this.roomInfo.confId).then((resp) => {
        if (resp.code == 0) {
          ElMessageBox.alert('隔3秒后跳转至「我的沟通记录」列表页面', '删除成功', {
            confirmButtonText: '现在跳转',
            callback: (action) => {
              console.log('action', action)
              this.goPremeet();
            },
          })
          setTimeout(() => {
            this.goPremeet();
          }, 3000);
        }
      });
    },
    _downSaleMail() {
      const mail = g.postmeetStore.getMailBody();
      const {
        recordInfo,
        asrRaw: { customerName },
      } = g.postmeetStore.data;
      const fname = `${customerName}_客户沟通纪要_${recordInfo.startTime
        .replace(/:/g, "")
        .replace(/ /g, "_")}.txt`;
      downloadTxt(fname, mail);
    },
    changeExpIcon(v) {
      if (v) {
        this.iconClass = "yxtf-icon-arrow-up yxtf-icon--right";
      } else {
        this.iconClass = "yxtf-icon-arrow-down yxtf-icon--right";
      }
    },
    setRoomInfo(info) {
      if (info?.needCalRealTime) {
        info['startTime'] = info.recordList[0].startTime;
      }
      this.roomInfo = info;
      this.readonly = g.postmeetStore.isReadonly();

      this.$nextTick(() => {
        if (!this.readonly) {
          // this.$refs.refExportLib && this.$refs.refExportLib.init(info)
        }
        this.convertRecord(info.recordList);
        if (info.recordList.length > 0) {
          this.hostName = info.recordList[0].hostName;
        }
      });
    },
    goPremeet() {
      window.location.href = g.config.publicPath;
    },
    handleClick(tab) {
      if (tab) {
        this.activeName = tab.label;
        this._handleClick(tab);
      }
    },
    _handleClick(tab) {
      if (tab) {
        g.postmeetStore.setValue("playIdx", tab.index);
        this.toPlay(this.records[tab.index]);
      }
    },
    convertRecord(list) {
      if (list.length > 0) {
        this.records = list.reduce((a, b, index) => {
          let startInSecord = 0;

          if (this.roomInfo?.needCalRealTime) {
            let preSecords = 0
            for (let idx = 0; idx < index; idx++) {
              preSecords += list[idx].duration / 1000;
            }
            startInSecord = preSecords
          }

          const shareRecords = parseRecordsTag(
            b.shareRecords,
            this.roomInfo.startTime,
            startInSecord
          );
          const splitTags = b.scdetOffsets.map(
            (x) => timeDiffSecond(x, this.roomInfo.startTime) - startInSecord
          );
          const c = {
            ...this.roomInfo,
            ...b,
            ...{
              startInSecord,
              title: list.length > 1 ? `录制文件${index + 1}` : "录制文件",
              url: g.appStore.getRecordUrl(b.recordingPath),
              recordId: b.id,
              format: b.format.toLowerCase(),
              id: index,
              shareRecords,
              splitTags,
            },
          };
          a.push(c);
          return a;
        }, []);
        this.activeName = this.records[0].title + "";
        if (this.records.length > 2) {
          this.activeName3 = this.records[2].title;
        }
        const tab = {
          label: this.records[0].title,
          index: 0,
        };
        this.handleClick(tab);
      }
    },
    toPlay(record) {
      this.currRecord = record;
      const durationList = g.postmeetStore.data.recordInfo.recordList.map(x => x.duration)
      const totalDuration = durationList.reduce((a, b) => a + b, 0)
      this.currVidioTime = formateTime(totalDuration / 1000);
      g.postmeetStore.setValue("playItem", record);
      g.emitter.emit("setPlayItem", record);
      g.emitter.emit("videoStatus", "pause");
      this.getIsUserLeaderWrap();
    },
    onCbSubject(val) {
      updateSubject(this.roomInfo.confId, { subject: val }).then((res) => {
        const { recordInfo } = g.postmeetStore.data;
        recordInfo["subject"] = val;
        g.postmeetStore.setValue("recordInfo", recordInfo);
        g.emitter.emit("updateSubject", val);
      });
    },
    handleSelectClick() {
      this.handleSelectChange(this.activeName3);
    },
    handleSelectChange(value) {
      this.activeName = '';
      const selectedRecord = this.records.find(record => record.title === value);
      const index = this.records.indexOf(selectedRecord);
      this._handleClick({ label: value, index });
    },
  },
};
</script>

<style lang="scss">
@use './record_header.scss';

.rh_sub_title {
  display: flex;
  align-items: center;
  font-size: 14px;

  .subtitle-text {
    color: #262626;
  }

  .subtitle-separator {
    width: 1px;
    height: 12px;
    background: #d9d9d9;
    margin: 0 8px;
  }
}

.tabs-select-container {
  display: flex;
  align-items: center;

  .record-select {
    margin-left: 24px;
    margin-top: 12px;
    width: 150px;
    position: relative;

    :deep(.el-select-dropdown__item) {
      &.selected {
        color: var(--el-color-primary);
        font-weight: bold;
      }
    }

    :deep(.el-input__inner) {
      color: var(--el-color-primary);
    }

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -12px;
      width: 100%;
      height: 2px;
      background-color: var(--el-color-primary);
      display: none;
    }

    &.el-select--small {
      &:has(.el-input__inner:not(:placeholder-shown)) {
        &::after {
          display: block;
        }
      }
    }

    &.has-value::after {
      display: block;
    }
  }
}
</style>
