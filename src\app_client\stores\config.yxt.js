import config from "@/js/config.js"
import { getUser, getFuncStatus } from "@/js/utils.js"

const getYxtConfig = () => {
    const { token } = getUser();
    let list = {
        name: '销售赋能大单滚滚',
        data: [
            {
                title: '产业信息挖掘机',
                id: "industrial_info_excavator",
                note: '洞察产业链，挖掘客户商机',
                url: `api_iie`,
                show: getFuncStatus('sales_industry_in_app')
            },
            {
                id: "customer",
                title: '企业信息侦察机',
                note: '获取客户信息，轻松不止一点点',
                url: `${config.publicPath}/postmeet-xmate-h5/#/customer?token=${token}`,
                show: getFuncStatus('sales_company_in_app')
            },
            {
                title: '智能商品推荐师',
                id: "product_recommender_c",
                note: '根据客户需求，个性化推荐最佳商品',
                url: `recommend2`,
                show: getFuncStatus('sales_product_in_app')
            }
        ]
    };

    list.data = list.data.filter(x => x.show)

    return list

}

export default getYxtConfig;