<template>
    <el-drawer v-model="visible" size="800px" direction="rtl" :with-header="true" show-close @close="handleClose"
        class="fragment-faq-drawer-wrap">
        <template #header>
            <div class="vd_title flex-row">
                <div class="vd_title_name">
                    {{ detailData.question || '详情' }}
                </div>
            </div>
        </template>
        <div v-if="detailData">
            <!-- 问题与标准答复 -->
            <p class="faq-question single-line-ellipsis-2">
                <span class="icon-question">问</span>
                <span class="question-title">{{ detailData.question }}</span>
            </p>
            <p class="faq-answer single-line-ellipsis-2">
                <span class="icon-answer">答</span>
                <span class="answer-content">{{ detailData.answer }}</span>
            </p>
            <!-- 问答原文 -->
            <div class="faq-origin">
                <div class="origin-title">问答原文 <span class="origin-title-count">相关问答：199个（截止时间：2025-06-04）</span></div>
                <QAList :qaList="detailData.originList" />
            </div>
        </div>
        <template #footer>
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, watch } from 'vue'
import { View, Clock } from '@element-plus/icons-vue'
import QAList from "@/components/QAList.vue"

const emits = defineEmits(['update:visible'])
const visible = ref(false)
const detailData = ref({})
const init = (obj) => {
    visible.value = true
    detailData.value = obj || {}
}
function handleClose() {
    emits('update:visible', false)
}

defineExpose({
    init
})
</script>

<style lang="scss">
.fragment-faq-drawer-wrap {
    p {
        margin: 0;
    }

    .el-drawer__header {
        padding: 16px 28px 16px 4px !important;
        height: auto;

        .vd_title {
            .vd_title_name {
                color: #262626;
                font-weight: 500;
            }
        }

    }

    .el-drawer__body {
        padding: 24px;
    }

    .el-drawer__footer {
        padding: 12px 24px;
        box-shadow: inset 0px 1px 0px 0px #E9E9E9;

        .el-button--primary {
            margin-left: 16px;
        }
    }



    .detail-content {
        .feedback_list_wrap {
            padding: 24px;

            .feedback_item {
                background: #F9FAFC;
                border-radius: 8px;
            }
        }
    }

    .faq-detail-header {
        background: #EEF1FD;
        border-radius: 8px;
        padding: 18px 20px;
        margin-bottom: 18px;
    }

    .faq-question {
        font-weight: bold;
        margin-bottom: 10px;

        .question-title {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            line-height: 24px;
        }
    }

    .icon-question {
        background: #04CCA4;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        display: inline-block;
        margin-right: 8px;
    }

    .faq-answer {
        margin-bottom: 12px;

        .answer-content {
            font-size: 14px;
            color: #757575;
            line-height: 22px;
            width: calc(100% - 34px);
        }
    }

    .icon-answer {
        background: #436BFF;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        display: inline-block;
        margin-right: 8px;
    }



    .faq-origin {
        background: #F9FAFC;
        border-radius: 8px;
        margin-top: 12px;
        padding: 24px;

        .origin-title {
            margin-bottom: 20px;
            font-weight: 500;
            font-size: 16px;
            color: #262626;
            line-height: 26px;
            display: flex;
            align-items: center;

            .origin-title-count {
                font-weight: 400;
                font-size: 12px;
                color: #8C8C8C;
                line-height: 18px;
                margin-left: 12px;
            }

            &::before {
                content: '';
                display: inline-block;
                width: 3px;
                height: 14px;
                background: #436BFF;
                margin-right: 8px;
                border-radius: 2px;
            }
        }


    }
}
</style>