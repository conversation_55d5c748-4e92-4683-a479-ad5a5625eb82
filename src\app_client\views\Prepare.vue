<template>
    <PrevisitPrepare ref="refPrevisitPrepare" />
</template>

<script setup>
import PrevisitPrepare from '@/components/PrevisitPrepare/PrevisitPrepare.vue'
const refPrevisitPrepare = ref()
import { useRoute } from "vue-router"
const route = useRoute();

onMounted(() => {
    const data = {
        scheduleId: route.params.id
    }
    refPrevisitPrepare.value.init(data);
})

defineExpose({
    refPrevisitPrepare
})

</script>

<style lang="scss">
.previsit_prepare_wrap {
    .ed_main {
        height: calc(100vh - 100px);

        .av_item {
            .av_item_value {
                width: 90%;
            }
        }
    }
}
</style>
