<template>
  <div class="library-search">
    <div class="title">{{ nodeData.name }}</div>
    <div class="search-input">
      <el-input v-model="searchText" placeholder="输入关键词搜索" :prefix-icon="Search" clearable @clear="handleClear"
        @input="handleSearch">
      </el-input>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

const nodeData = ref({ name: "" });
const searchText = ref("");
const emit = defineEmits(["callback"]);

const handleClear = () => {
  emit("callback", "search-input", "");
};

const handleSearch = (value) => {
  emit("callback", "search-input", value);
};

const init = (data) => {
  nodeData.value = data;
};

const init_my = () => {
  nodeData.value = { name: "我的剪辑" };
};

defineExpose({
  init,
  init_my,
});
</script>

<style lang="scss" scoped>
.library-search {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 16px;
    color: #262626;
    font-weight: 500;
  }

  .search-input {
    width: 300px;

    :deep(.el-input__wrapper) {
      border-radius: 20px;
    }
  }
}
</style>
