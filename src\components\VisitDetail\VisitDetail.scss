.visit-detail {
    .section {
        display: flex;
        margin-bottom: 20px;

        .item2 {
            display: flex;
            justify-content: space-between;

            .tag {
                height: 20px;
                background: #E6EEFF;
                border-radius: 2px;
                padding: 2px 8px;
                font-weight: 400;
                font-size: 12px;
                color: #436BFF;
                line-height: 20px;
                width: 53px;
            }

            .time {
                font-weight: 400;
                font-size: 14px;
                color: #595959;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                margin-left: 12px;
            }

            .btns {
                .icon-container {
                    margin-left: 12px;
                    cursor: pointer;
                    color: #595959;

                    &:hover {
                        color: #436BFF;
                    }
                }
            }
        }

        .item {
            display: flex;
            margin-bottom: 16px;

            .detail-text {
                font-size: 14px;
                color: #262626;
            }

            .subject {
                font-weight: 500;
                font-size: 20px;
                color: #262626;
                line-height: 28px;
            }


            .visit-tag {
                padding: 0 4px;
                height: 18px;
                line-height: 18px;
                background: #FFF7E6;
                border-radius: 2px;
                font-size: 12px;
                color: #FF7300;
                font-weight: normal;
                margin-top: 5px;
                margin-left: 8px;
                flex-shrink: 0;
            }


            .participants {
                .par_row {
                    width: 100%;

                    .title {
                        width: 100px;
                        color: #262626;
                    }

                    .content {
                        font-size: 14px;
                        color: #bfbfbf;
                        line-height: 22px;
                    }
                }
            }
        }

        .company-link {
            .detail-text {
                .join-button {
                    width: 80px;
                }

                .url-line {
                    margin-top: 12px;
                    display: flex;
                    align-items: flex-start;

                    .url-text {
                        font-size: 14px;
                        color: #262626;
                        word-break: break-all;
                        white-space: normal;
                        line-height: 20px;
                        flex: 1;
                    }

                    .copy-btn {
                        margin-left: 10px;
                        cursor: pointer;
                        flex-shrink: 0;

                        &:hover {
                            color: #436BFF;
                        }
                    }
                }
            }
        }
    }

    .section-btns {
        margin-top: 16px;
        justify-content: center;
        position: fixed;
        width: 92%;
        bottom: 12px;

        .el-button {
            width: 45%;
        }
    }

    .divider {
        margin: 12px 0;
        border-top: 1px solid #E9E9E9;
        width: 100%;
    }

    .buttons {
        .el-button {
            margin-right: 10px;
            width: 100px;
        }
    }

    .electron-times {
        padding: 20px 8px;
        background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);
        border-radius: 8px;

        .time-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .time-block {
                text-align: center;

                .time {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                }

                .date {
                    font-size: 14px;
                    color: #666;
                    margin-top: 4px;
                }
            }

            .duration {
                padding: 4px 12px;
                background: #f0f2f5;
                border-radius: 12px;
                font-size: 14px;
                color: #666;
                max-width: 60px;
                text-align: center;
            }
        }
    }

    .description-wrapper {
        position: relative;
        width: 100%;

        .description {
            max-height: 88px;
            overflow: hidden;
            line-height: 22px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            text-overflow: ellipsis;
            word-break: break-all;

            &.expanded {
                max-height: none;
                display: block;
                -webkit-line-clamp: unset;
            }
        }

        .expand-btn {
            color: #3370ff;
            cursor: pointer;
            padding: 4px 0;
            font-size: 14px;
            display: flex;
            align-items: center;

            .fold-icon {
                margin-left: 4px;
                width: 16px;
                height: 16px;
            }

            &:hover {
                opacity: 0.8;
            }
        }
    }
}