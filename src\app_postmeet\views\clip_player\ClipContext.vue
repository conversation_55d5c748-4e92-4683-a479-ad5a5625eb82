<template>
  <div class="clip-context custom-scrollbar" v-ai-tip="'center'" v-loading="loading">
    <div class="context-title" v-if="contextData.overview.situation">情境背景</div>
    <div class="summary-content" v-if="contextData.overview.situation">
      <div class="context-content">
        <div class="section">
          <div class="section-title">会话概要</div>
          <div class="section-content">{{ contextData.overview.situation }}</div>
        </div>

        <div class="section">
          <div class="section-title">客户类型</div>
          <div class="section-content">{{ contextData.overview.customer_type }}</div>
        </div>

        <div class="section">
          <div class="section-title">客户需求</div>
          <div class="section-content">{{ contextData.overview.customer_needs }}</div>
        </div>

        <div class="section">
          <div class="section-title">会话目标</div>
          <div class="section-content">{{ contextData.overview.key_objective }}</div>
        </div>
      </div>
    </div>

    <div class="speech-list" v-if="contextData.sales_speeches.length > 0">
      <div class="speech-title">优秀话术</div>
      <div class="speech-items">
        <div v-for="(item, index) in contextData.sales_speeches" :key="index" class="speech-item">
          <div class="item-index">
            <div class="index-circle">{{ index + 1 }}</div>
            <div class="type-tag">{{ item.type }}</div>
          </div>
          <div class="item-content">
            <div class="content-row">
              <div class="text">{{ item.improvedSpeech }}</div>
            </div>
            <div class="content-row">
              <div class="label">使用场景：</div>
              <div class="text">{{ item.context }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="error_msg" v-else>
      <div class="error_text">{{ errorMsg }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const errorMsg = ref("");
const hasInit = ref(false);
const loading = ref(true);
const contextData = ref({
  overview: {
    situation: "",
    customer_type: "",
    customer_needs: "",
    key_objective: "",
  },
  sales_speeches: [],
});

const emit = defineEmits(["retry"]);
let timer = null;

const init = (rawData) => {
  if (hasInit.value) {
    return;
  }
  if (rawData.aiReportStatus == 1) {
    loading.value = true;
    timer = setTimeout(() => {
      emit("retry");
    }, 10 * 1000);
  } else if (rawData.aiReportStatus == 2) {
    if (rawData.aiReportContent) {
      contextData.value = JSON.parse(rawData.aiReportContent);
      hasInit.value = true;
    }
    loading.value = false;
    errorMsg.value = "AI报告生成失败";
  } else {
    loading.value = false;
    errorMsg.value = "AI报告生成失败";
  }
};

defineExpose({
  init,
});

onUnmounted(() => {
  timer && clearTimeout(timer);
});
</script>

<style lang="scss" scoped>
.clip-context {
  width: 50%;
  background: #ffffff;
  height: calc(100vh - 140px);
  padding: 24px;
  overflow-y: auto;

  .summary-content {
    background: #f7f8fa;
    border-radius: 8px;
    padding: 16px;
  }

  .context-title {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
    margin-bottom: 16px;
  }

  .context-content {
    .section {
      margin-bottom: 16px;

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #1d2129;
        margin-bottom: 8px;
      }

      .section-content {
        font-size: 14px;
        color: #4e5969;
        line-height: 22px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .speech-list {
    margin-top: 24px;

    .speech-title {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 16px;
    }

    .speech-items {
      .speech-item {
        background: #f7f8fa;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;

        .item-index {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .index-circle {
            width: 20px;
            height: 20px;
            background: #e8f3ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #165dff;
            margin-right: 8px;
          }

          .type-tag {
            background: #e8f3ff;
            border-radius: 4px;
            padding: 2px 8px;
            font-size: 12px;
            color: #165dff;
          }
        }

        .item-content {
          margin-left: 22px;

          .content-row {
            display: flex;
            margin-bottom: 8px;

            .label {
              flex-shrink: 0;
              font-size: 14px;
              font-weight: 500;
              color: #1d2129;
              margin-right: 4px;
            }

            .text {
              font-size: 14px;
              color: #4e5969;
              line-height: 22px;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>
