<template>
    <div class="icon">
        {{ status }}
        <!-- <component :is="getFaceCom()" /> -->
    </div>
</template>

<script setup>
// import faceActive from "@/app_client/icons/face/face_active.vue"
// import faceMiddle from "@/app_client/icons/face/face_middle.vue"
// import faceInactive from "@/app_client/icons/face/face_inactive.vue"

const props = defineProps(['status']);

// const getFaceCom = () => {
//     const map = {
//         "积极": faceActive,
//         "中性": faceMiddle,
//         "消极": faceInactive,
//     }
//     return map[props.status];
// }

defineExpose({
    // getFaceCom
})

</script>

<style lang="scss">
.icon {
    // margin: 4px 0 0 12px;
}
</style>