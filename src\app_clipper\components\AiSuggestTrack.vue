<template>
  <div class="ai-suggest-track">
    <div class="is_loading" v-if="hintText">{{ hintText }}</div>
    <div class="sb_list" v-if="!hintText">
      <div v-for="item in segments" :key="item.startTime" :style="getItemStyle(item, 12)" class="ai_item flex-center"
        @click="handleSubtitleClick(item)">
        <el-popover placement="top" title="对话总结3" :width="400" trigger="click" popper-class="astp_popover">
          <template #reference>
            <div class="astp_text">
              <img :src="getOssUrl('sb_star.png')" alt="ai_suggest" />
              <span>{{ item.topic }}</span>
            </div>
          </template>
          <template #default>
            <div v-ai-tip="'bottom-right'">
              <div class="astp_main custom-scrollbar">
                <div class="astp_summary">
                  {{ item.summary }}
                </div>
                <div class="astp_title" v-if="item.aiSuggestion.goodSentence.length > 0">
                  优秀话术
                </div>
                <div class="astp_content" v-for="(subItem, idx) in item.aiSuggestion.goodSentence">
                  <div class="astp_line1 flex-row">
                    <div class="astp_idx flex-center">{{ idx + 1 }}</div>
                    <div class="astp_name">{{ subItem.type }}</div>
                  </div>
                  <div class="astp_body">
                    {{ subItem.improvedSpeech }}
                  </div>
                  <div class="astp_line2">
                    <span class="astpl_title"> 使用场景： </span>
                    {{ subItem.context }}
                  </div>
                </div>
              </div>
            </div>

          </template>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getItemStyle } from "@/app_clipper/tools/utils.js";
import { getOssUrl } from "@/js/utils.js";
import { getConferenceClipLib } from "@/app_clipper/tools/api";
const QueryIntervalTime = 3000;
const hintText = ref("正在根据音频内容智能分析中…");
const segments = ref([]);

let pollingTimer = null;

const _get_ai_suggest = (item) => {
  try {
    return JSON.parse(item.aiSuggestion);
  } catch (error) {
    console.error("萃取建议生成失败:", error, item);
    return { goodSentence: [] };
  }
};

const pollClipLibStatus = async () => {
  try {
    const res = await getConferenceClipLib(g.clipperStore.confId);
    if (res.status === 0 || res.status === 1) {
      // 继续轮询
      pollingTimer = setTimeout(pollClipLibStatus, QueryIntervalTime);
    } else if (res.status === 3) {
      hintText.value = '萃取建议生成失败';
      clearTimeout(pollingTimer);
    } else {
      // 状态为2或3时停止轮询
      clearTimeout(pollingTimer);
      if (res.status === 2) {
        // 处理成功获取到的数据
        let list = res.segments || [];
        const { startDt, endDt, uploadedRecord } = g.clipperStore.recordInfo;
        if (!uploadedRecord) {
          list = list.filter((item) => {
            // 过滤掉不在录制时间范围内的字幕
            return item.end_time >= startDt && item.start_time <= endDt;
          });
        }
        segments.value = list.map((item) => {
          return {
            ...item,
            aiSuggestion: _get_ai_suggest(item),
            startSeconds: g.clipperStore.timeToSeconds(item.start_time),
            endSeconds: g.clipperStore.timeToSeconds(item.end_time),
          };
        });
        if (segments.value.length === 0) {
          hintText.value = "当前拜访中无优秀话术片段建议";
        } else {
          hintText.value = "";
        }
      }
    }
  } catch (error) {
    console.error("轮询失败:", error);
    hintText.value = "萃取建议生成失败";
    clearTimeout(pollingTimer);
  }
};

// 添加字幕点击处理函数
const handleSubtitleClick = (item) => {
  // 更新当前时间为字幕的开始时间
  g.emitter.emit("set_audio_time", item.startSeconds);

  // 发送字幕选择事件
  g.emitter.emit("subtitle_select", item);
};

const init = async () => {
  pollClipLibStatus();
};

onMounted(() => {
  g.emitter.on("audio_ready", (duration) => {
    init();
  });
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (pollingTimer) {
    clearTimeout(pollingTimer);
  }
  g.emitter.off("file_uploaded");
});

defineExpose({ init, getItemStyle, segments });
</script>

<style lang="scss">
.ai-suggest-track {
  height: 50px;
  padding-left: 20px;

  .is_loading {
    background: linear-gradient(227.9970555714091deg, #4cd2f4 0%, #7a1eff 100%);
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
    font-size: 16px;
  }

  .sb_list {
    .ai_item {
      width: 250px;
      height: 32px;
      background: linear-gradient(318deg,
          rgba(76, 210, 244, 0.1) 0%,
          rgba(122, 30, 255, 0.1) 100%);
      border-radius: 4px;
      white-space: nowrap; // 防止文字换行
      overflow: hidden; // 超出部分隐藏
      text-overflow: ellipsis; // 显示省略号
      cursor: pointer;
      display: flex;
      align-items: center; // 确保垂直居中对齐
      padding: 0 12px; // 添加左右内边距

      .astp_text {
        width: 100%;
        text-align: center;

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
          vertical-align: middle; // 图片垂直居中
        }

        span {
          color: rgba(38, 38, 38, 0.8);
          line-height: 32px; // 与容器高度相同，确保文字垂直居中
          flex: 1; // 让文字占据剩余空间
          overflow: hidden;
          font-size: 14px;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.astp_popover {
  padding: 16px !important;

  .el-popover__title {
    font-weight: 500;
    font-size: 14px;
    color: #262626;
  }

  .astp_main {
    max-height: 450px;
    overflow-y: auto;
    padding-right: 7px;

    .astp_summary {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
    }

    .astp_title {
      height: 22px;
      font-weight: 500;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
      margin-top: 12px;
    }

    .astp_line1 {
      margin: 12px 0;

      .astp_idx {
        height: 20px;
        width: 20px;
        border: 1px solid #426bff;
        border-radius: 50%;
        font-weight: 500;
        font-size: 14px;
        color: #436bff;
        line-height: 20px;
      }

      .astp_name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #436bff;
        width: 64px;
        height: 20px;
        background: #e6eeff;
        line-height: 20px;
        border-radius: 2px;
        margin-left: 12px;
        padding: 2px 4px;
        text-align: center;
      }
    }

    .astp_body {
      margin-left: 41px;
      color: #262626;
    }

    .astp_line2 {
      color: #262626;
      margin-top: 12px;
      margin-left: 42px;

      .astpl_title {
        color: #262626;
        font-weight: 500;
      }
    }
  }
}
</style>
