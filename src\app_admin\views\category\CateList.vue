<template>
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
        <template #_link_pre="{ row }">
            <el-button type="primary" text @click="cbDatas('init_view', row)"
                :disabled="row.status.indexOf('PROCESS') > -1">查看解析
            </el-button>
        </template>
        <template #col_fileName="{ row }">
            <div @click="viewDoc(row)" class="column_link">
                {{ row.fileName }}
            </div>
        </template>

        <template #col_fileSize="{ row }">
            <div @click="viewDoc(row)">
                {{ formatFileSize(row.fileSize) }}
            </div>
        </template>
        <template #col_status="{ row }">
            <div class="cate_tab_status_wrap">
                <div class="dot" :style="{ background: getDocColor(row.status) }"></div>
                <div class="txt">
                    {{ trans(row.status) }}
                </div>
            </div>
        </template>
    </MyTable>
    <UploadModal ref="refUpload" />
    <ViewDoc ref="refView" />
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getPptDocs, deletePpt, getPptCateDocInfo } from "@/app_admin/tools/api.js";
import { getDocColor } from '@/app_admin/tools/utils.js'
import { formatFileSize, jsOpenNewWindow } from "@/js/utils.js"
import UploadModal from "@/app_admin/components/UploadModal.vue";
import ViewDoc from "@/app_admin/components/ViewDoc"
import trans from "@/js/lang.js";
import { useRoute } from 'vue-router'
import config from '@/js/config';

const route = useRoute()
const refUpload = ref();
const refView = ref(null);
const refTable = ref(null);

const datas = reactive({
    tableid: 'category_list',
    param: {
        "categoryIds": [],
        "searchKey": "",
    },
    template: ["status", "fileName", "fileSize"],
    need_init_load: false,
    show_search: true,
    need_header: true,
    show_btn_add: true,
    form: {},
    modal_type: "link",
    add_txt: "上传",
    delete_hint_column: 'fileName',
    show_link_column: true,
    show_link_view: false,
    show_link_delete: true,
    columns: ["categoryName", "fileName", "fileSize", "slideSize", "createdTime", "status"],
    urlGet: getPptDocs,
    urlDelete: deletePpt
});
const cbDatas = (action, data) => {
    if (action === "init_view") {
        const { categoryId, id } = data;
        getPptCateDocInfo(categoryId, id).then(resp => {
            if (resp.code == 0) {
                refView.value.init(resp.data);
            }
        })
    } else if (action == 'before_create') {
        if (refUpload.value.onOk()) {
            refTable.value.btnModalCancel()
        }
    } else if (action == "init_add") {
        g.adminFileStore.get_config('ppt').then(config => {
            refUpload.value.show(config, route.params.id);
        })
    }
}

const viewDoc = (row) => {
    if (row.fileSize < 100 * 1024 * 1024) {
        const url = config.publicPath + row.downloadPath;
        const ext = row.fileName.split('.').pop()
        g.emitter.emit('app_preview_file', { url, ext });
    } else {
        const docId = row.id
        const url = `${config.publicPath}/#/vp/0/${docId}`
        jsOpenNewWindow(url)
    }
}

onMounted(() => {
    const categoryId = route.params.id;
    datas.param['categoryIds'] = [categoryId]
    refTable.value.search();
    g.emitter.on('file_uploaded', () => {
        !!refTable.value && refTable.value.search()
    })
})

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    MyTable,
    refUpload,
    refView,
    ViewDoc,
    UploadModal,
    refTable,
    getDocColor,
    trans,
    viewDoc,
    cbDatas
})
</script>

<style lang='scss'>
.cate_tab_status_wrap {
    display: flex;
    flex-direction: row;

    .dot {
        width: 6px;
        height: 6px;
        margin: 9px 9px;
        border-radius: 50%;
    }
}
</style>