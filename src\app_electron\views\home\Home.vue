<template>
    <div class="home-container custom-scrollbar" v-loading="loading">
        <div class="home-container-top">
            <div class="header">
                <div class="avatar" @click="onWin('person')">
                    <UserIcon :name="userInfo.name" :photo="userInfo.photo" :showname="true" />
                </div>
                <div class="flex-row">
                    <div class="right-icon message" @click="openMeg">
                        <img :src="getAssetUrl('inmeet_message.svg')" alt="消息中心" />
                        <div class="red_dot" v-if="unReadCount > 0"></div>
                    </div>
                    <div class="right-icon setting" @click="onWin('setting')">
                        <img :src="getAssetUrl('inmeet_set.svg')" alt="设置" />
                    </div>
                    <div class="right-icon ai" @click="onWin('ai_app')" v-if="hasAiApp">
                        <img :src="getAssetUrl('inmeet_ai.svg')" alt="AI助手" />
                    </div>
                </div>
            </div>
            <MenuList ref="refMenuList" />
        </div>
        <PlanList ref="refPlanList" @callback="onCallabck" />
        <DiaConfirm2 ref="refAbnExit" leftText="结束" rightText="继续" @callback="onAbnormalExit"
            hint="检测到您上次异常退出，是否要继续录制？" />
        <DiaConfirm2 ref="refApiHint" leftText="取消" rightText="查看" @callback="onAutoEndDia" :hint="apiHint" />
    </div>
</template>

<script setup>
import UserIcon from '@/components/userIcon.vue';
import PlanList from './HomePlanList.vue';
import MenuList from './MenuList.vue';
import { getAssetUrl, now } from '@/js/utils';
import { getUnReadMsgNum, pauseLiveMeet, getCloseMsg } from '@/app_electron/tools/api';
import DiaConfirm2 from '@/app_electron/components/DiaConfirm2.vue';

let allWindowNames = []
let autoCheckTimer = null;
let reloadTimer = {};
const loading = ref(false);
const userInfo = ref({ name: '', photo: '' });
const refPlanList = ref(null);
const refMenuList = ref(null);
const refAbnExit = ref(null);
const refApiHint = ref(null);
const apiHint = ref('')
const hasAiApp = ref(false)
const unReadCount = ref(0);


const getCloseMsgWrap = async () => {
    try {
        const res = await getCloseMsg()
        if (res.code === 0 && res?.data?.content) {
            apiHint.value = res?.data?.content || ''
            refApiHint.value.show_dialog();
        }
    } catch (error) {
        console.error('获取关闭会议提示失败:', error);
    }
}


const reloadSchedule = () => {
    if (refPlanList.value) {
        refPlanList.value.reload();
    } else {
        console.log('no planlist?')
    }
    getCloseMsgWrap()
}

const checkAbnormalExit = () => {
    try {
        return new Promise(async (resolve, reject) => {
            try {
                const meetingInfo = g.appStore.getStore(g.cv.keyMeetingInfo, {})
                if (meetingInfo && meetingInfo.conferenceId) {
                    if (meetingInfo.meetType == 'computer') {
                        const resp = await pauseLiveMeet(meetingInfo.conferenceId)
                        console.log('pauseLiveMeet resp', resp)
                    }
                    refAbnExit.value.show_dialog()
                    resolve(true)
                } else {
                    resolve(false)
                }
            } catch (error) {
                console.error('获取主屏幕信息失败:', error);
                resolve(false)
            }
        })
    } catch (error) {
        console.error('检查异常退出失败:', error);
        resolve(false)
    }
}


const onAbnormalExit = async (action) => {
    if (action === 'left') {
        //end meeting
        g.meetStore.restoreMeetingInfo()
        await g.meetStore.endOrderMeeting()
        reloadSchedule()
    } else {
        //continue meeting
        g.electronStore.openWin("meet", { fromStore: true });
        reloadSchedule()
    }
}

const onAutoEndDia = (action) => {
    if (action === 'right') {
        g.electronStore.openUrl("/client/visit?tab=visit_record");
    }
}

const onLink = (url) => {
    g.electronStore.openUrl(url);
};

const getUnReadMsg = async () => {
    if (!g.electronStore.settings.message.enableDotMessage) return;
    try {
        const res = await getUnReadMsgNum();
        if (res.code === 0) {
            unReadCount.value = res.data.unReadCount;
        } else {
            unReadCount.value = 0
        }
    } catch (error) {
        console.error('获取未读消息数失败:', error);
    }
}

const onCallabck = (action, data) => {
    if (action === 'reload') {
        getUnReadMsg()
    }
}

const openMeg = () => {
    const url = `${g.config.postmeet_h5_customer}/index.html#/msgCenter/msgList`;
    const user = localStorage.getItem('yxtlm_userInfo');
    const openParam = {
        urlParam: {
            id: 'dummy_window',
            url
        },
        newPageData: {
            "yxt-userInfo": user
        }
    }
    g.electronStore.createWindows(openParam);
};

const onWin = (win, closeWinName) => {
    if (closeWinName) {
        g.electronStore.closeWin(closeWinName);
    }
    g.electronStore.openWin(win);
};


const addListener = () => {
    g.ipcRenderer.on('forward_message', (_, { action, data }) => {
        console.log('forward_message', new Date(), action, data)
        if (action === 'reload_schedule') {
            if (reloadTimer[data]) clearTimeout(reloadTimer[data]);
            reloadTimer[data] = setTimeout(() => {
                reloadSchedule()
            }, data || 50)
        } else if (action == 'ws_connected') {
            console.log('home ws_connected', data)
        }
    })
    g.emitter.on('XMATE_CONF_SUMMARY_MSG', (data) => {
        getUnReadMsg()
    })
}

const autoCheckUpdate = () => {
    if (g.config.isDev) return;

    const lastUpdateRemindTime = g.appStore.getStore(g.cv.keyLastUpdateRemindTime);
    const today = now('yyyy-MM-dd hh');

    if (lastUpdateRemindTime !== today) {
        autoCheckTimer = setTimeout(async () => {
            try {
                if (await g.electronStore.checkUpdateBase(true)) {
                    g.appStore.setStore(g.cv.keyLastUpdateRemindTime, now('yyyy-MM-dd hh'))
                }
            } catch (error) {
                console.error('自动检查更新失败:', error);
            }
        }, 3000);
    }
}

const init = () => {
    g.appStore.reloadUserInfo().then(async (status) => {
        if (status) {
            addListener()
            userInfo.value = g.appStore.user;
            g.electronStore.sendMessage('hide_window', 'reconnect_ws', {})
            allWindowNames = await g.ipcRenderer.invoke('get-all-window-names');
            if (!allWindowNames.includes('hide_window')) {
                g.electronStore.openWin('hide_window')
            }
            if (!allWindowNames.includes('meet')) {
                g.ipcRenderer.send('update_old_logs', '')
                await checkAbnormalExit()
            }
            reloadSchedule()
            hasAiApp.value = g.appStore.getFuncStatus('sales_ai_apps_icon');
            refMenuList.value.init();
            getUnReadMsg()
            autoCheckUpdate();
        } else {
            g.appStore.logout()
        }
    })
}

onMounted(async () => {
    loading.value = true;
    const user = await g.electronStore.getStore('userInfo');
    let clientId = await g.electronStore.getStore('clientId');
    if (!clientId) {
        clientId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    }
    await g.electronStore.getMainScreenInfo();
    g.electronStore.updateWindowSize()
    g.appStore.setStore(g.cv.keyClientId, clientId);
    if (user && user.token && user.userInfo) {
        await g.appStore.electronLogin(user);
        init();
    } else {
        g.ipcRenderer.send('removeStore', 'userInfo');
        g.router.push({ path: '/electron/login' })
        return;
    }
    loading.value = false;
    g.ipcRenderer.send('check_hot_update', '')
    // to debug some window
    // g.electronStore.openWin('card_inmeet', {});
});

onBeforeUnmount(() => {
    g.ipcRenderer.removeAllListeners('forward_message');
    autoCheckTimer && clearTimeout(autoCheckTimer);
    reloadTimer && clearTimeout(reloadTimer);
})

defineExpose({
    UserIcon,
    onLink,
    hasAiApp,
    PlanList,
    refPlanList,
    refMenuList,
    getAssetUrl
})
</script>

<style lang="scss">
.electron-layout {
    .mac-titlebar {
        background: #E9F1FF;
    }
}

.home-container {
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);
    height: 100%;
    overflow-y: auto;

    .home-container-top {
        padding: 20px;

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;

            .avatar {
                display: flex;
                align-items: center;
                cursor: pointer;
            }

            .right-icon {
                cursor: pointer;
                margin-left: 20px;
                position: relative;

                img {
                    width: 20px;
                    height: 20px;
                }

                .red_dot {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 5px;
                    height: 5px;
                    border-radius: 50%;
                    background: #FF0000;
                }
            }
        }


    }

}
</style>
