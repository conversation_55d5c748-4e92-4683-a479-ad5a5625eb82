<template>
  <div class="btn_export_wrap">
    <el-dialog title="请选择需要导出的内容" v-model="centerDialogVisible" width="480px" :append-to-body="true"
      :modal-append-to-body="true" class="btn_export_modal_wrap">
      <div>
        <div v-if="saleDiyReports.length == 0">
          <el-checkbox v-model="ck_normal_meet">拜访纪要</el-checkbox>
          <el-checkbox v-model="ck_segmented_summary">分段摘要</el-checkbox>
          <el-checkbox v-model="ck_speecher_summary">发言总结</el-checkbox>
          <el-checkbox v-model="ck_key_points">问答回顾</el-checkbox>
        </div>
        <div v-if="saleDiyReports.length > 0" class="saleDiy">
          <el-checkbox v-model="cks[item.id]" v-for="item in saleDiyReports" :key="item.id">{{ item.name
            }}</el-checkbox>
        </div>
        <el-checkbox v-model="ck_need_name">说话人姓名</el-checkbox>
        <el-checkbox v-model="ck_need_time">时间戳</el-checkbox>
        <div class="pi_ckbox" v-if="data_all.length > 0">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
            @change="handleCheckAllChange">下面说话人数据</el-checkbox>
        </div>
        <el-checkbox-group v-model="data_checked" @change="handleCheckedChange" class="cbl_users"
          v-if="data_all.length > 0">
          <el-checkbox v-for="user in data_all" :key="user.uid" :label="user.uid" :value="user.uid">{{ user.name
            }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <template #footer>
        <el-button @click="centerDialogVisible = false" class="dialog-footer">取消</el-button><el-button type="primary"
          @click="doExport">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { downloadTxt } from '@/app_postmeet/tools/tools.js'
import { normal_export, sale_export } from "./btn_export_tools.js"
import { mdToText } from '@/js/md.js'

export default {
  name: 'BtnExport',
  data() {
    return {
      centerDialogVisible: false,

      ck_normal_meet: true,
      ck_segmented_summary: true,
      ck_speecher_summary: true,
      ck_key_points: true,

      ck_need_name: true,
      ck_need_time: true,

      cks: {},
      value: '',
      checkAll: false,
      check_viewall: true,
      data_all: [{ uid: 1, name: 'a' }],
      data_pre: [],
      data_checked: [],
      saleDiyReports: [],
      isIndeterminate: true,
      saleTags: []
    }
  },
  methods: {
    checkselect(e, action) {
      let checked = []
      this.data_checked = checked.map(x => x.uid)
    },
    handleCheckAllChange(val) {
      this.data_checked = val ? this.data_all.map(x => x.uid) : []
      this.isIndeterminate = false
    },
    handleCheckedChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.data_all.length
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.data_all.length
    },
    onExport() {
      const saleDiyReports = g.postmeetStore.data.saleReport.analysisReports || [];
      for (let i = 0; i < saleDiyReports.length; i++) {
        this.cks[saleDiyReports[i].id] = true
      }
      this.saleDiyReports = saleDiyReports.filter(x => x.name != '思维导图');
      this.data_all = g.postmeetStore.data.names.map(x => { return { uid: x, name: x } })
      this.centerDialogVisible = true;
    },
    doExport() {
      const { recordInfo } = g.postmeetStore.data
      const isSaleReport = this.saleDiyReports.length > 0;
      let txt = []
      if (isSaleReport) {
        txt = sale_export(this)
      } else {
        txt = normal_export(this)
      }

      if (this.data_checked.length > 0) {
        const messages = g.postmeetStore.data.subtitles
        if (messages) {
          let subtitles = ''
          const _f = (x) => {
            let txt = '';
            if (this.isIndeterminate || this.checkAll || this.ck_need_time) {
              if (this.ck_need_name) {
                txt += `${x.name} `
              }
              if (this.ck_need_time) {
                txt += `${x.time}`
              }
              txt += `\n`
            }
            txt += `${x.txt}`
            return txt
          }
          let exportMsg = []
          if (this.isIndeterminate) {
            exportMsg = messages.filter(x => this.data_checked.includes(x.name))
          } else {
            exportMsg = messages;
          }
          if (exportMsg.length > 0) {
            subtitles = '【字幕】\n'
            subtitles += exportMsg.map(_f).join('\n\n')
          }
          txt.push(subtitles)
        }
      }

      let fname = recordInfo.subject + '_' + recordInfo.startTime.replace(/:/g, '').replace(/ /g, '_') + '.txt';
      let body = mdToText(txt.join('\n\n'));

      downloadTxt(fname, body);
      this.centerDialogVisible = false;
    }
  }
}
</script>

<style lang='scss'>
.btn_export_modal_wrap {
  .el-dialog__body {
    display: flex;
    flex-direction: column;
    padding: 0 20px;

    .saleDiy {
      display: flex;
      flex-wrap: wrap;
    }

    .el-checkbox {
      margin: 6px 0;
      display: flex;
      width: 49%;

      .el-checkbox__label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 380px;
        line-height: 13px;
      }
    }

    .cbl_users {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .el-checkbox {
        width: 146px;
      }
    }
  }
}
</style>
