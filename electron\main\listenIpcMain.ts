import { ipcMain, dialog, desktopCapturer, powerSaveBlocker } from 'electron'
import { shell, app } from 'electron'
import windowManager from './windowManager'
import { createSubWindows } from './listenSubWindow'
import { ffmpegFixDuration } from '../utils/record'
import * as store from '../utils/store'
import { ensurePermissions, PermissionType } from '../utils/systemPermisions'
import { writeLog, uploadLog, uploadOldLogs } from '../utils/errorWriteLocal'
import { checkMacVirtualAudio, installMacVirtualAudio, killVirtualAudio } from '../utils/macInstall'
import config from '../utils/config'
import * as fs from 'fs'
import * as path from 'path'
import { startMainWindow } from './createMainWindow'
import { errorWriteLocal } from '../utils/errorWriteLocal'
import { downloadAndCacheImage } from '../utils/request'
import { getMainScreenInfo } from '../utils/tools'
import { WebResourceManager } from '../utils/webResource'

export const listenIpcMain = () => {
    ipcMain.on('ssoLogin', (_, res) => {
        console.log('ssoLogin', res)
        if (res.code == 0) {
            store.setStore(store.keyUserInfo, res.data)
            windowManager.closeAllWindows();
            startMainWindow();
        } else {
            console.log('ssoLogin error', res)
        }
    })

    ipcMain.handle('mainPageReady', (event) => {
        const win = windowManager.getWindow('main')
        if (win && win.window && !win.window.isDestroyed()) {
            const userInfo = store.getUserInfo();
            return { code: 0, data: userInfo }
        } else {
            console.log('mainPageReady win is destroyed')
            return { code: 1, data: null }
        }
    })

    ipcMain.on('forward_message', (_, data) => {
        console.log('forward_message', data)
        windowManager.sendMessage(data)
    })

    ipcMain.on('setStore', (_, key, value) => {
        store.setStore(key, value);
    })

    ipcMain.on('removeStore', (_, key) => {
        store.removeStore(key);
    })

    ipcMain.handle('getStore', (_, key) => {
        const data = store.getStore(key);
        return JSON.stringify(data);
    })

    ipcMain.on('updateWindowSize', (_, sizes) => {
        store.setStore(store.keyWindowSizes, sizes)
    })

    // choose-local-recording-path
    // 选择本地录制路径，并返回
    ipcMain.handle('choose-local-recording-path', async (event) => {
        const result = await dialog.showOpenDialog({
            properties: ['openDirectory']
        })

        if (result.canceled) {
            return null; // 用户取消了选择
        } else {
            return result.filePaths[0]
        }
    })

    ipcMain.on('open-local-recording-path', (_, path) => {
        console.log("open-local-recording-path", path)
        shell.openPath(path)
    })

    //add hide win
    ipcMain.on('hide_window', (_, winName) => {
        console.log('hide_window', winName)
        windowManager.hideWindow(winName)
    })

    ipcMain.on('close_window', (_, winName) => {
        console.log('close_window', winName)
        windowManager.closeWindow(winName)
    })

    ipcMain.on('create_window', (_, arg) => {
        console.log('create_window', arg)
        createSubWindows(arg)
    })

    ipcMain.on('close_all_windows', () => {
        console.log('close_all_windows')
        windowManager.closeAllWindows()
    })

    ipcMain.on('minimize_window', (_, winName) => {
        console.log('minimize_window', winName)
        windowManager.minimizeWindow(winName)
    })

    ipcMain.on('open_main', () => {
        console.log('open_main')
        startMainWindow()
    })

    ipcMain.on('update_closeable', (_, winName, closeable) => {
        console.log('update_closeable', winName, closeable)
        windowManager.updateCloseable(winName, closeable)
    })

    ipcMain.handle('ensurePermissions', async (_, type: PermissionType) => {
        errorWriteLocal('ensurePermissions', type)
        return ensurePermissions(type);
    })

    ipcMain.handle('get_desktopCapturer_sources', async () => {
        errorWriteLocal('get_desktopCapturer_sources')
        return await desktopCapturer.getSources({ types: ['screen'] })
    })

    // 添加处理视频的方法
    ipcMain.on('process-recording', async (_, videoPath, isVideo, confId) => {
        errorWriteLocal('process-recording', videoPath, isVideo, confId)
        ffmpegFixDuration(videoPath, isVideo, confId)
    })

    ipcMain.handle('check-recording-path', async (_, recordPath) => {
        try {
            // 如果路径为空，设置默认路径为 我的文档\SalesMateRecord
            if (!recordPath) {
                const documentsPath = app.getPath('documents');
                recordPath = path.join(documentsPath, 'SalesMateRecord');
            }

            // 检查目录是否存在，不存在则创建
            if (!fs.existsSync(recordPath)) {
                fs.mkdirSync(recordPath, { recursive: true });
            }

            return recordPath;
        } catch (error) {
            console.error('Error checking/creating recording path:', error);
            return '';
        }
    })

    ipcMain.handle('check-mac-virtual-audio', async () => {
        errorWriteLocal('check-mac-virtual-audio')
        return await checkMacVirtualAudio()
    })

    ipcMain.handle('install-mac-virtual-audio', async () => {
        errorWriteLocal('install-mac-virtual-audio')
        return await installMacVirtualAudio()
    })

    ipcMain.on('kill-virtual-audio', () => {
        killVirtualAudio()
    })

    ipcMain.on('write-elog', (_, logId, ...args) => {
        console.log('write-elog', logId, args)
        if (!logId) return;
        writeLog(logId, ...args)
    })

    ipcMain.on('upload-log', (_, logId) => {
        const userInfo: any = store.getUserInfo()
        uploadLog(logId, userInfo.id)
    })

    ipcMain.handle('toggle_dev_tools', () => {
        const isOpen = store.getStore(store.keyDevToolsOpen, false);
        store.setStore(store.keyDevToolsOpen, !isOpen)
        return !isOpen
    })

    ipcMain.handle('get_app_version', () => {
        return config.version
    })

    ipcMain.on('open_url', (_, url) => {
        shell.openExternal(url)
    })

    ipcMain.on('update_user_agent', (_, winName, userAgent) => {
        windowManager.updateUserAgent(winName, userAgent)
    })

    // 修改获取图片缓存路径的处理
    ipcMain.handle('get-image-cache', async (_, imageUrl) => {
        return await downloadAndCacheImage(imageUrl);
    });

    ipcMain.handle('get-all-window-names', async () => {
        return windowManager.getAllWindowNames()
    })

    //   在页面里 使用demo
    //   const opacity = await g.ipcRenderer.invoke('execute_win_method', {
    //     id: 'main',
    //     method: 'getOpacity',
    //     args: []
    // });
    // await g.ipcRenderer.invoke('execute_win_method', {
    //     id: 'main',
    //     method: 'setOpacity',
    //     args: [0.5]
    // });
    ipcMain.handle("execute_win_method", async (_, { id, method, args } = {}) => {
        try {
            errorWriteLocal('execute win', id, method, args);
            const win = windowManager.getWindow(id);
            const result = await win.window[method](...args);
            return { success: true, data: result };
        } catch (error) {
            errorWriteLocal('execute win error', error);
            return { success: false, error: error.message };
        }
    })

    //获取主屏幕信息
    ipcMain.handle('get-main-screen-info', async () => {
        return getMainScreenInfo()
    })

    ipcMain.on("update_old_logs", () => {
        uploadOldLogs()
    })

    // 检查热更新
    ipcMain.on("check_hot_update", () => {
        const webResourceManager = WebResourceManager.getInstance()
        const win = windowManager.getWindow('main');
        if (!win || win.window.isDestroyed()) return;
        webResourceManager.checkAndApplyHotUpdate(win.window)
    })

    ipcMain.on('update_power_save_blocker', (_, isPreventSleep) => {
        let isNowPreventSleep = false;
        const currentId = parseInt(store.getStore(store.keyPreventSaveBlockerId, '0'))
        if (currentId && currentId > 0) {
            isNowPreventSleep = powerSaveBlocker.isStarted(currentId);
        }
        errorWriteLocal('update_power_save_blocker', currentId, isPreventSleep, isNowPreventSleep);

        if (isPreventSleep) {
            if (isNowPreventSleep) return;
            try {
                const newId = powerSaveBlocker.start('prevent-app-suspension')
                errorWriteLocal("get id", newId)
                store.setStore(store.keyPreventSaveBlockerId, newId)
            } catch (error) {
                errorWriteLocal("powerSaveBlocker.start error", error)
            }
        } else {
            if (!isNowPreventSleep) return;
            try {
                if (currentId && currentId > 0) {
                    powerSaveBlocker.stop(currentId)
                }
                store.setStore(store.keyPreventSaveBlockerId, 0)
            } catch (error) {
                errorWriteLocal("powerSaveBlocker.stop error", error)
            }
        }
    })
}
