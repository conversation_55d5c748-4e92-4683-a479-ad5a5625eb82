<template>
    <div class="quadrant-chart">
        <p class="quadrant-chart-title">Top 6 积极反馈&消极反馈</p>

        <div class="legend">
            <span class="legend-item positive"></span> 积极
            <span class="legend-item negative"></span> 消极
        </div>


        <div class="qchart_main" v-loading="loading"
            v-if="quadrantData?.positiveFeedbacks?.length > 0 || quadrantData?.negativeFeedbacks?.length > 0">
            <div class="positive-chart" v-if="quadrantData?.positiveFeedbacks?.length > 0">
                <!-- <div class="positive-chart-title">
                    积极评价
                </div> -->
                <div class="positive-item">
                    <p v-for="(item, index) in quadrantData?.positiveFeedbacks" :key="`${index}I`"
                        class="single-line-ellipsis">
                        {{ item }}
                    </p>
                    <div class="positive-img" :style="{ backgroundImage: `url(${getOssUrl('positive.png', 3)})` }">
                        <span>高</span>
                        <span>低</span>
                    </div>
                </div>
            </div>
            <div class="negative-chart" v-if="quadrantData?.negativeFeedbacks?.length > 0">
                <!-- <div class="negative-chart-title">
                    消极评价
                </div> -->
                <div class="negative-item">
                    <p v-for="(item, index) in quadrantData?.negativeFeedbacks" :key="index"
                        class="single-line-ellipsis">
                        {{ item }}
                    </p>
                    <div class="negative-img" :style="{ backgroundImage: `url(${getOssUrl('negative.png', 3)})` }">
                        <span>高</span>
                        <span>低</span>
                    </div>
                </div>
            </div>

            <div class="quadrant_empty" v-else>
                <el-empty description="暂无数据" style="margin: 0 auto;" :image="getOssUrl('no-data.png', 3)">
                </el-empty>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getOssUrl } from '@/js/utils.js';
const quadrantData = computed(() => g.clientBiStore.cvAlQuadrantData);
const loading = ref(true)

const init = async () => {
    loading.value = true;
    await g.clientBiStore.getQuadrantData()
    loading.value = false;
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

onMounted(() => {
    init()
})

</script>

<style lang="scss" scoped>
.quadrant-chart {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 24px;

    background: #FFFFFF;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px;


    .quadrant-chart-title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        margin: 0;
        margin: 0 0 12px 0;
    }

    .legend {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        color: #8A8B8E;

        .legend-item {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
            margin-left: 24px;

            &.positive {
                background: #04CCA4;
            }

            &.negative {
                background: #FF6B3B;
            }

            &.neutral {
                background: #D9D9D9;
            }
        }

        .legend-item:first-child {
            margin-left: 0;
        }
    }

    .qchart_main {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 24px;

        .quadrant_empty {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .positive-chart,
    .negative-chart {
        // margin-top: 20px;
        width: 48%;

        .positive-chart-title,
        .negative-chart-title {
            font-size: 14px;
            color: #262626;
            line-height: 24px;


        }


        .negative-item {
            position: relative;
            padding-bottom: 24px;

            >p {
                font-size: 14px;
                color: #FF6B3B;
                line-height: 22px;
                background: rgba(255, 107, 59, 0.10);
                margin: 0;
                margin-top: 16px;
                padding: 4px 8px;
                box-sizing: border-box;
                width: calc(100% - 26px);

            }

            &::after {
                position: absolute;
                content: '';
                display: block;
                width: 100%;
                height: 100%;
                background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
                margin-top: 16px;
                top: -16px;
            }
        }

        .positive-item {
            position: relative;
            padding-bottom: 24px;

            >p {
                font-size: 14px;
                color: #04CCA4;
                line-height: 22px;
                width: calc(100% - 26px);
                background: rgba(4, 204, 164, 0.10);
                margin: 0;
                margin-top: 16px;
                padding: 4px 8px;
                box-sizing: border-box;
            }

            &::after {
                position: absolute;
                content: '';
                display: block;
                width: 100%;
                height: 100%;
                background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
                margin-top: 16px;
                top: -16px;
            }
        }

        .negative-img,
        .positive-img {
            position: absolute;
            top: -8px;
            right: 2px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            width: 24px;
            height: 100%;
            background-size: contain;
            background-repeat: no-repeat;
            text-align: center;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;

            span {
                margin-right: 3px;
            }
        }
    }
}
</style>