<template>
  <div class="cu_page_list_wrap">
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
      <template #col_page_name="scope">
        <span>{{ scope.row.name }}创建与详情页配置</span>
      </template>
    </MyTable>
    <DiaDiyPage ref="refDiaField" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getCustomerTypeSearch } from "@/js/api.js"
import DiaDiyPage from "./DiaDiyPage.vue";
const refDiaField = ref()

const getCustomerTypeListWrap = (param) => {
  return new Promise((resolve, reject) => {
    const data = { ...param }
    data['pageNumber'] = data['pageNumber'] - 1
    getCustomerTypeSearch(data)
      .then((resp) => {
        resolve(resp)
      })
      .catch((err) => {
        resolve(err)
      })
  })
}

const refTable = ref(null);
const datas = reactive({
  tableid: 'admin_customer_field_page',
  param: {
    status: 1,
  },
  need_header: false,
  need_init_load: false,
  form: {},
  columns: ["page_name", "description"],
  template: ["page_name"],
  show_link_column: true,
  show_link_edit: true,
  urlGet: getCustomerTypeListWrap,
});

const cbDatas = (action, data) => {
  if (action == 'init_edit') {
    refDiaField.value.show_edit(data.id, data)
  }
}

const initLoad = () => {
  refTable.value.search()
}


defineExpose({
  MyTable,
  initLoad,
  refDiaField,
  refTable,
  cbDatas,
})
</script>

<style lang='scss'>
.cu_page_list_wrap {
  padding: 24px 0;

  .table_box .table_class {
    height: calc(100vh - 178px) !important;

    .col_operation_ {
      width: 104px
    }
  }
}
</style>