<template>
  <el-calendar v-model="currentDate" ref="refCalendar" class="plan-calendar">
    <template #header="{ date }">
      <span>{{ date }}</span>
      <el-button-group>
        <el-button size="small" @click="selectDate('prev-month')"> &lt; </el-button>
        <el-button size="small" @click="selectDate('next-month')"> &gt; </el-button>
      </el-button-group>
    </template>
    <template #date-cell="{ data }">
      <div :class="['date-item', { active: isCurrentDay(data.day) }]">
        {{ data.day.split("-")[2] }}
        <div v-if="hasMeeting(data.day)" class="meeting-dot"></div>
      </div>
    </template>
  </el-calendar>
</template>

<script setup>
import { getWeekOfMonth } from "@/app_client/tools/utils";
import { formatDate, getDay06 } from "@/js/utils";
import { getScheduleList } from "@/js/api.js";

const currentDate = ref();
const emit = defineEmits(["callback"]);
const startOfWeek = ref();
const endOfWeek = ref();
const selectedWeek = ref([]);
const refCalendar = ref();
const planList = ref([]);
let lastMonthEnd = null;

const selectDate = (val) => {
  refCalendar.value.selectDate(val);
};

const handleDateClick = (date) => {
  const currDate = new Date(date);
  const [_startOfWeek, _endOfWeek] = getDay06(currDate);
  startOfWeek.value = _startOfWeek;
  endOfWeek.value = _endOfWeek;
  selectedWeek.value = [_startOfWeek, _endOfWeek];

  //计算出当前日期是当前月份第几周
  const weekOfMonth = getWeekOfMonth(currDate);

  // 动态添加样式
  const styleId = "dynamic-row-highlight";
  let styleElement = document.getElementById(styleId);

  if (!styleElement) {
    styleElement = document.createElement("style");
    styleElement.id = styleId;
    document.head.appendChild(styleElement);
  }

  styleElement.textContent = `
    .el-calendar-table tbody tr:nth-child(${weekOfMonth}) {
      background-color: #F0F6FF !important;
    }
    .el-calendar-table tbody tr:nth-child(${weekOfMonth}) td:first-child {
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
    }
    .el-calendar-table tbody tr:nth-child(${weekOfMonth}) td:last-child {
      border-top-right-radius: 15px;
      border-bottom-right-radius: 15px;
    }
  `;
  const result = {
    startTime: formatDate(startOfWeek.value) + " 00:00:00",
    endTime: formatDate(endOfWeek.value) + " 23:59:59",
  };
  emit("callback", result);
};

const getScheduleListByDate = (monthFirst) => {
  monthFirst.setDate(1);
  const monthLast = new Date(Date.UTC(monthFirst.getFullYear(), monthFirst.getMonth() + 1, 0));
  const param = {
    startTime: formatDate(monthFirst) + " 00:00:00",
    endTime: formatDate(monthLast) + " 23:59:59",
    completed: null,
    showAssistMeeting: true,
    dptIds: [],
  };
  if (lastMonthEnd !== param.endTime) {
    lastMonthEnd = param.endTime;
    getScheduleList(param).then((res) => {
      planList.value = res.data.datas.map(x => {
        return {
          ...x,
          startDate: formatDate(x.startTime || x.scheduleStartTime, 'yyyy-MM-dd'),
        }
      })
    });
  } else {
  }
};

const setCurrentDate = (date) => {
  currentDate.value = date;
};

watch(currentDate, (newVal, oldVal) => {
  getScheduleListByDate(new Date(newVal));
  handleDateClick(newVal);
});

const reload = () => {
  getScheduleListByDate(currentDate.value || new Date());
}

const init = () => {
  handleDateClick(new Date());
}

onMounted(() => {
  init();
});

defineExpose({
  selectDate,
  refCalendar,
  handleDateClick,
  setCurrentDate,
  reload,
});

const isCurrentDay = (day) => {
  return formatDate(new Date(day)) === formatDate(currentDate.value);
};

const hasMeeting = (day) => {
  return planList.value.some(plan => plan.startDate === day);
};
</script>

<style lang="scss">
.plan-calendar {
  .el-calendar__header {
    padding: 12px 0;
  }

  .el-calendar__body {
    padding: 0;

    .el-calendar-table {
      border: none;
      width: 277px;
      height: 200px;

      th {
        border: none;
      }

      td {
        border: none !important;
      }

      .el-calendar-day {
        height: auto;
        padding: 0;
      }

      .is-today {
        .date-item {
          border: 1px solid #436bff;
          border-radius: 50%;
        }
      }

      .date-item {
        position: relative;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;

        .meeting-dot {
          position: absolute;
          bottom: 2px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #436bff;
        }
      }

      .el-calendar-day:hover {
        background-color: var(--el-calendar-selected-bg-color);
        cursor: pointer;
        border-radius: 50%;
      }

      thead {
        background-color: #fff !important;
      }

      tbody.tr.selected-week {
        background-color: #f0f6ff !important;
        border-radius: 15px;

        td:first-child {
          border-top-left-radius: 15px;
          border-bottom-left-radius: 15px;
        }

        td:last-child {
          border-top-right-radius: 15px;
          border-bottom-right-radius: 15px;
        }
      }
    }
  }
}
</style>
