<template>
  <div class="avg_bar_chart_wrap">
    <template v-if="isStandardNumber && chartData.length > 0">
      <div class="header-title">
        <span>整体均值</span>
        <el-tooltip class="box-item" effect="dark" content="各人员平均得分之和 / 人数" placement="top">
          <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon_min" />
        </el-tooltip>
        <span class="standard-score">{{ Number(averageValue || 0).toFixed(2) }} {{ mark ? mark : '分' }}</span>
      </div>
    </template>
    <div class="chart-container" v-if="chartData.length > 0">
      <template v-if="isShowAverage">
        <!-- 平均值显示 -->
        <div class="average-info">
          达标值 {{ formatBarValue(isStandard, "", 2)
          }} {{ mark ? mark : '分' }}
          <div class="bar" :style="{
            width: `${6 + (isStandard ? isStandard : averageValue) * lineZoom}px`,
          }"></div>
        </div>

        <!-- 添加垂直平均线 -->
        <div class="vertical-average-line" :style="{
          left: `${155 + (isStandard ? isStandard : averageValue) * lineZoom}px`
        }"></div>
      </template>
      <!-- 柱状图 -->
      <div class="chart">
        <div v-for="(item, index) in chartData" :key="item.label + selectDimension + item.value" :class="[
          'chart-item',
          item.value < isStandard ? 'bad_bar' : 'good_bar',
          item.label === selectedRegion ? 'selected' : ''
        ]" @click="handleRegionClick(item)">
          <div class="label" :title="item.label">{{ item.label }}</div>
          <div class="bar-container">
            <div class="bar" :style="{
              width: `${item.value * lineZoom}px`,
            }"></div>
          </div>
          <div class="value">{{ formatBarValue(item.value, selectDimension, 2) }} <span v-if="mark">{{ mark }}</span>
          </div>
        </div>
      </div>
      <!-- <div class="expand flex-row" @click="onExpand" v-if="chartData.length > 5">
        <div class="expand-text">
          {{ iSExpand ? '收起' : '展开' }}
        </div>
        <el-icon size="14">
          <ArrowUp v-if="iSExpand" />
          <ArrowDown v-else />
        </el-icon>
      </div> -->
    </div>

    <div class="no_data" v-if="chartData.length == 0">
      <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)"></el-empty>
    </div>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import { formatBarValue } from '@/app_client/tools/utils.js';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
const props = defineProps({
  isShowAverage: {
    type: Boolean,
    default: true
  },
  innerWidth: {
    default: 0
  },
  mark: {
    type: String,
    default: ''
  },
  standardScore: {
    type: [Number, String],
    default: ''
  },
  isEndDept: {
    type: Boolean,
    default: false
  },
  isStandardNumber: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['callback'])
const selectDimension = ref('');
const lineZoom = ref(0);
const selectedRegion = ref('');
const averageValue = ref(0);
const iSExpand = ref(false)
const showTopN = ref(5);
const chartData = ref([]);
const onExpand = () => {
  iSExpand.value = !iSExpand.value;
  if (iSExpand.value) {
    showTopN.value = chartData.value.length;
  } else {
    showTopN.value = 5;
  }
}

const isStandard = computed(() => {
  return props.standardScore ? props.standardScore : averageValue.value
})
const formatAverageValue = computed(() => {
  return formatBarValue(averageValue.value, selectDimension.value, 2);
});
const handleRegionClick = (item) => {
  selectedRegion.value = item.label;
  emit('callback', 'setUser', item)
}
// 计算最大柱子宽度和缩放比例
const calculateZoom = () => {
  if (chartData.value.length === 0) {
    averageValue.value = 0;
    lineZoom.value = 0;
    return;
  }

  // 确保所有 value 都是有效的数字
  const validData = chartData.value.filter(item => {
    const value = Number(item.value);
    return !isNaN(value) && isFinite(value);
  });
  console.log('validData:', validData);
  if (validData.length === 0) {
    averageValue.value = 0;
    lineZoom.value = 0;
    return;
  }

  const sum = validData.reduce((acc, curr) => {
    const value = Number(curr.value) || 0;
    return acc + value;
  }, 0);
  averageValue.value = sum / validData.length;

  // 找出最大值
  const maxValue = Math.max(...validData.map(item => Number(item.value) || 0), props.standardScore || 60);
  if (maxValue === 0) {
    lineZoom.value = 0;
    return;
  };

  console.log('maxValue:', maxValue);
  // 获取容器宽度（减去左侧标签宽度和右侧数值宽度的空间）
  const containerWidth = props.innerWidth - 300
  // 期望最大柱子的宽度是容器的70%
  const targetMaxWidth = containerWidth * 0.7;
  console.log('targetMaxWidth:', targetMaxWidth, 'maxValue:', maxValue, containerWidth);

  // 计算新的缩放比例
  lineZoom.value = targetMaxWidth / maxValue;
}

const init = (data) => {
  chartData.value = data;
}

watch(() => [props.innerWidth, chartData.value], () => {
  nextTick(() => {
    calculateZoom()
  })
}, {
  immediate: true
})

defineExpose({
  init,
  selectedRegion
});
</script>

<style lang="scss" scoped>
.avg_bar_chart_wrap {
  width: 100%;
  // height: 100%;
  min-height: 300px;
  display: flex;
  background: #FFFFFF;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  flex-direction: column;

  .header-title {
    padding: 24px 24px 0 24px;
    font-weight: 400;
    font-size: 12px;
    color: #8C8C8C;
    line-height: 20px;
    display: flex;
    align-items: center;

    .standard-score {
      margin-left: 2px;
      font-weight: 400;
      font-size: 12px;
      color: #262626;
      line-height: 20px;
    }

  }

  .no_data {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .chart-container {
    flex: 1;
    padding: 20px 0;
    position: relative;

    .average-info {
      color: #0FAD72;
      margin-bottom: 24px;
      margin-left: 150px;
      font-size: 14px;

      .bar {
        height: 100%;
        height: 3px;
        margin-top: 10px;
        background: #0FAD72;
        transition: width 0.3s ease;
        border-radius: 4px;
      }
    }

    .vertical-average-line {
      position: absolute;
      top: 52px;
      bottom: 40px;
      width: 1px;
      z-index: 2;
      background: #0FAD72;
      transition: left 0.3s ease;
    }

    .chart {
      display: flex;
      flex-direction: column;
      gap: 24px;
      max-height: 380px;
      overflow-y: auto;
      padding-right: 4px;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      // Firefox 滚动条样式
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;

      .chart-item {
        cursor: pointer;
        padding: 8px 20px;
        border-radius: 4px;
        font-size: 14px;

        &.selected {
          background-color: #F5F7FA;
        }

        display: flex;
        align-items: center;
        gap: 9px;

        .label {
          width: 120px;
          color: #666;
          font-size: 14px;
          text-align: right;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .bar-container {
          position: relative;
          display: flex;
          height: 14px;
          border-radius: 0px 100px 100px 0px;
          align-items: center;

          .bar {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
          }
        }

        .value {
          // font-weight: bold;
          text-align: right;
        }
      }

      .bad_bar {
        .bar-container {
          background: #FF6B3B;
        }

        .value {
          z-index: 3;
          color: #FF6B3B;
        }
      }

      .good_bar {
        .bar-container {
          background: #04CCA4;
        }

        .value {
          color: #0FAD72;
        }
      }
    }

    .expand {
      margin-top: 20px;
      width: 100%;
      cursor: pointer;
      justify-content: center;

      .expand-text {
        color: #595959;
        font-size: 14px;
        font-weight: 400;
        margin-right: 6px;

      }

      .el-icon {
        margin-top: 3px;
      }
    }
  }
}
</style>
