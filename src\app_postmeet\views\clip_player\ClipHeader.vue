<template>
  <div class="cright_header_wrap flex-row">
    <div class="flex-row">
      <div class="left-section">
        <div class="rh_logo" @click="goPremeet">
          <img :src="getOssUrl('logo_mini.png')" />
        </div>
        <div class="company-name">{{ info.companyName }}</div>
      </div>

      <div class="right-section flex-col" v-show="!loading">
        <div class="top-row flex-row">
          <div class="clip-name">{{ info.title }}</div>
        </div>
        <div class="bottom-row flex-row">
          <div class="info-group">
            <span>{{ info.salesMateCustomerName }}</span>
          </div>
          <div class="separator">|</div>
          <div class="info-group">
            <span>{{ info.salesMateTags }}</span>
          </div>
          <div class="separator">|</div>
          <div class="info-group">
            <span>{{ info.startTime }}</span>
          </div>
          <div class="separator">|</div>
          <div class="info-group">
            <span>{{ info.hostName }}</span>
          </div>
          <div class="separator">|</div>
          <div class="info-group">
            <span>{{ info.date }}</span>
            <span class="duration"
              >时长：{{ getSecondsShowTime(info.duration / 1000) }}</span
            >
          </div>
        </div>
      </div>
    </div>

    <div class="rh_name">
      <usericon :usemy="true"></usericon>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getOssUrl, getSecondsShowTime, formatDate } from "@/js/utils";
import usericon from "@/app_postmeet/components/usericon.vue";

const info = ref({});
const loading = ref(true);

const goPremeet = () => {
  const url = `${g.config.publicPath}`;
  window.open(url, "_blank");
};

const init = (data) => {
  data.startTime = formatDate(new Date(data.createdTime), "yyyy-MM-dd hh:mm:ss");
  info.value = data;
  loading.value = false;
};

defineExpose({
  init,
  getOssUrl,
});
</script>

<style lang="scss" scoped>
.cright_header_wrap {
  height: 72px;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  align-items: center;
  justify-content: space-between;
  gap: 20px;

  .left-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .rh_logo {
      width: 32px;
      height: 32px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .company-name {
      font-size: 14px;
      color: #1d2129;
      white-space: nowrap;
    }
  }

  .right-section {
    flex: 1;
    gap: 8px;

    .top-row {
      align-items: center;

      .clip-name {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
      }
    }

    .bottom-row {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #4e5969;
      font-size: 14px;

      .info-group {
        display: flex;
        align-items: center;
        gap: 4px;

        .label {
          color: #86909c;
        }
      }

      .separator {
        color: #e5e6eb;
        padding: 0 4px;
      }
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}
</style>
