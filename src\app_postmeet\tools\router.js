const postmeetRouter = [
  {
    path: "/postmeet",
    name: "postmeet_instantMeeting",
    component: () => import("@/app_postmeet/views/record/index.vue"),
    redirect: "/record",
  },
  {
    path: "/postmeet/record/:confId",
    name: "postmeet_record",
    component: () => import("@/app_postmeet/views/record/index.vue"),
  },
  {
    path: "/postmeet/share/:shareId",
    name: "postmeet_share",
    component: () => import("@/app_postmeet/views/share_pwd/index.vue"),
  },
  {
    path: "/share/:shareId",
    name: "share",
    component: () => import("@/app_postmeet/views/share_pwd/index.vue"),
  },
  {
    path: "/postmeet/chat/:confId",
    name: "postmeet_chat",
    component: () => import("@/app_postmeet/views/chat/index.vue"),
  },
  {
    path: "/postmeet/no_access",
    name: "postmeet_no_access",
    component: () => import("@/app_postmeet/views/share_pwd/no_access.vue"),
  },
  {
    path: "/postmeet/clipplayer/:clipId",
    name: "postmeet_clipplayer",
    component: () => import("@/app_postmeet/views/clip_player/ClipPlayer.vue"),
  },
];

export default postmeetRouter;
