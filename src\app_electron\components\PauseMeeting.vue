<template>
    <el-button class="ebtn continue-btn" @click="onClick" :loading="isLoading">{{ isPaused ? '继续' : '暂停' }}
    </el-button>
</template>

<script setup>
import { MeetStatus } from '@/app_electron/tools/utils';

const emit = defineEmits(['callback']);
const isPaused = ref(false);
const isLoading = ref(false);

const onClick = () => {
    if (!g.meetStore.isNetworkConnected) {
        emit('callback', MeetStatus.no_network);
        return;
    }

    if (isLoading.value) {
        return;
    }
    isLoading.value = true;
    const targetstatus = isPaused.value ? MeetStatus.resumed : MeetStatus.paused;
    isPaused.value = !isPaused.value;
    emit('callback', targetstatus);
};


const updateStatus = (status) => {
    isPaused.value = status;
    isLoading.value = false;
}

defineExpose({
    isPaused,
    updateStatus,
    onClick
})
</script>

<style scoped lang="scss"></style>
