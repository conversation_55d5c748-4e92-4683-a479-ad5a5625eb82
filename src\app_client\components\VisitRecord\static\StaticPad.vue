<template>
    <div class="vline4 flex-row">
        <staticItem label="沟通总次数" :value="result.meetingNum" :loading="loading" />
        <staticItem label="沟通覆盖客户数" :value="result.customerNum" :loading="loading" />
        <staticItem label="客户沟通总时长（小时）" :value="result.duration" :loading="loading" />
    </div>
</template>

<script setup>
import staticItem from "./staticItem.vue";
const loading = ref(true)

const result = ref({
    meetingNum: 0,
    customerNum: 0,
    recordDurationSeconds: 0,
    duration: 0
})

const getHours = (v) => {
    try {
        if (v) {
            return (v / 3600).toFixed(1);
        } else {
            return 0;
        }
    } catch (e) {
        return 0;
    }
}

const init = (param) => {
    param.duration = getHours(param.recordDurationSeconds)
    result.value = param;
    loading.value = false
}

defineExpose({
    staticItem, init
})
</script>