<template>
    <component :is="coms[props.type]"></component>
</template>
<script setup>
import customer from "./customer.vue"
import sale from "./sale.vue"
import visit from "./visit.vue"
import yxt_ai from "./yxt_ai.vue";
import yxt_app from "./yxt_app.vue"
import myteam from "./myteam.vue";

const props = defineProps(['type']);

const coms = ref({
    customer: markRaw(customer), sale: markRaw(sale), visit: markRaw(visit), yxt_ai: markRaw(yxt_ai),
    yxt_app: markRaw(yxt_app), myteam: markRaw(myteam)
})

defineExpose({ coms, props })
</script>