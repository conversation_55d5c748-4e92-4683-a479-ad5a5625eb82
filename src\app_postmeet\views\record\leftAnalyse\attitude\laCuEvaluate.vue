<template>
    <div class="la_cu_evaluate_wrap flex-column" v-show="list.length > 0">
        <div v-for="item in list">
            <div class="la_header flex-row">
                <div class="la_left flex-row">
                    <div class="la_title">
                        {{ item['姓名'] }}
                    </div>
                    <faceIcon :status="getStatus(item)" :showface="false" />
                </div>
                <div class="la_detail" v-if="recordViewPermission" @click="onReview(item)">
                    回顾
                </div>
            </div>
            <div class="la_md">
                {{ item['客户侧整体态度'] && item['客户侧整体态度']['理由'] || '' }}
            </div>
            <div class="la_tags flex-row" v-if="ifShowStatus(item)">
                <div class="tag" @click="onClick(item, '积极反馈')" v-if="ifShowStatus1(item)">
                    积极反馈({{ item['客户反馈']['积极反馈'].length }})
                </div>
                <div class="tag" @click="onClick(item, '负面反馈')" v-if="ifShowStatus2(item)">
                    负面反馈({{ item['客户反馈']['消极反馈'].length }})
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getLaCuEvaluate } from "@/app_postmeet/tools/sale_report.js"
import faceIcon from "@/app_postmeet/components/faceIcon.vue"
export default {
    components: { faceIcon },
    data() {
        return {
            list: [],
            recordViewPermission: false
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
    },
    methods: {
        init() {
            this.recordViewPermission = g.postmeetStore.getRecordViewPermission()
            this.list = getLaCuEvaluate()
        },
        onReview(item) {
            const row = g.postmeetStore.data.saleAnalyseList[item['姓名']][0];
            g.emitter.emit('setAnalyseTag', [row, 1, item['姓名']])
        },
        onClick(item, label) {
            const row = g.postmeetStore.data.saleAnalyseList[item['姓名']].find(x => x.label == label);
            g.emitter.emit('setAnalyseTag', [row, 1, item['姓名']])
        },
        getStatus(item) {
            return item['客户侧整体态度'] && item['客户侧整体态度']['整体情绪'] || '';
        },
        ifShowStatus(item) {
            return this.ifShowStatus1(item) || this.ifShowStatus2(item);
        },
        ifShowStatus1(item) {
            if (!this.recordViewPermission) {
                return false;
            }
            let isshow = false;
            if (item['客户反馈']) {
                isshow = item['客户反馈']['积极反馈'] && item['客户反馈']['积极反馈'].length > 0;
            }
            return isshow;
        },
        ifShowStatus2(item) {
            if (!this.recordViewPermission) {
                return false;
            }
            let isshow = false;
            if (item['客户反馈']) {
                isshow = item['客户反馈']['消极反馈'] && item['客户反馈']['消极反馈'].length > 0;
            }
            return isshow;
        }
    }
}

</script>

<style lang="scss">
.la_cu_evaluate_wrap {
    position: relative;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 20px;

    .la_header {
        justify-content: space-between;
        margin-top: 12px;

        .la_left {
            width: 300px;

            .la_title {
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #262626;
                line-height: 26px;
                text-align: left;
                font-style: normal;
            }
        }

        .atti_tag {
            margin: 4px 0 0 12px;
            padding: 0 5px;
            height: 20px;
            //background: #52C41A;
            border-radius: 2px;
            height: 20px;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 20px;
            text-align: center;
        }

        .la_detail {
            display: none;
        }
    }

    .la_md {
        margin: 12px 0;
    }

    .la_tags {
        .tag {
            border-radius: 4px;
            border: 1px solid #D9D9D9;
            height: 24px;
            font-size: 14px;
            color: #262626;
            line-height: 24px;
            margin-right: 12px;
            padding: 5px 12px;
            cursor: pointer;
        }

        .tag:hover {
            border: 1px solid #436BFF;
            color: #436BFF;
        }
    }
}

.la_cu_evaluate_wrap:hover {
    background: #F1F3FB;

    .la_header {
        .la_detail {
            display: block;
            height: 22px;
            font-size: 14px;
            color: #436BFF;
            line-height: 22px;
            cursor: pointer;
        }
    }
}
</style>