<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close class="sale_dis_dim4_wrap">
    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules">
      <el-form-item label="分析维度" prop="name">
        <el-input v-model.trim="formData.name" maxlength="50" show-word-limit placeholder="请输入" />
      </el-form-item>
      <el-form-item label="默认分析类型" prop="defaultSalesMethodology">
        <el-select v-model="formData.defaultSalesMethodology" placeholder="请选择分析类型" style="width: 100%">
          <el-option v-for="item in analysisTypes" :key="item.value" :label="item.label" :value="item.value">
            <span style="float: left">{{ item.label }}</span>
            <span style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              ">
              {{ item.description }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { nextTick, reactive, ref, toRaw } from "vue";
import {
  updateXmTopicDimension,
} from "@/app_admin/tools/api.js";
import Modal from "@/components/Modal.vue";
import QuestionIcon from "@/app_admin/icons/question.vue";
const emit = defineEmits(["callback"]);

const refModal = ref();
const title = ref("");
const refTemplate = ref();
const refForm = ref("");
const currId = ref("");
const dimensionType = ref("");
const defaultForm = {
  systemPreset: false, //是否系统内置维度
  systemId: 0, //系统内置维度ID， 如果不是系统内置维度，固定为0
  content: "", //提示词内容
  name: "", //系统内置维度名称
  defaultSalesMethodology: "", // Add this line
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "600px",
};

const onTypeChange = (value) => {
  formData.value["systemPreset"] = value;
  formData.value["systemId"] = 0; //value ? 1 : 0;
  if (value) {
    nextTick(() => {
      // refTemplate.value.setChoosed(formData.value["systemId"]);
    });
  }
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const show_add = (pid) => {
  currId.value = pid;
  dimensionType.value = g.saleStore.dimensionType;
  formData.value = { ...defaultForm };
  cfg["title"] = "添加维度4";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (pid, data) => {
  currId.value = pid;
  dimensionType.value = g.saleStore.dimensionType;
  formData.value = { ...data };
  cfg["title"] = "编辑维度";
  refModal.value.show(cfg);
  if (formData.value.systemPreset) {
    nextTick(() => {
      // refTemplate.value.setChoosed(formData.value.systemId);
    });
  }
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const cbTemplate = (item) => {
  if (!item) {
    return;
  }
  formData.value.systemId = item.id;
  formData.value.name = item.name;
  formData.value.content = "";
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;

  const { systemPreset } = formData.value;
  const fn = () => {
    const data = toRaw(formData.value);
    updateXmTopicDimension(currId.value, data.id, data)
      .then((resp) => {
        if (resp.code == 0) {
          ElMessage.success(`${cfg["title"]}成功`);
          emit("callback", "reload");
          btnCancel();
        } else {
          ElMessage.error(`${resp.message}`);
        }
      })
      .catch((e) => {
        ElMessage.error(`${cfg["title"]}失败`);
      });
  };
  if (systemPreset) {
    fn();
  } else {
    refForm.value.validate((valid, fields) => {
      if (valid) {
        fn();
      } else {
        console.log("not valid", fields);
      }
    });
  }
};
const analysisTypes = ref(g.cacheStore.salesMethodology);

const rules = reactive({
  name: [{ required: true, message: "请输入分析维度", trigger: "blur" }],
  defaultSalesMethodology: [{ required: true, message: "请选择默认分析类型", trigger: "change" }],
});

defineExpose({
  title,
  onTypeChange,
  cbTemplate,
  refTemplate,
  show_add,
  show_edit,
  cbModal,
  analysisTypes,
  formData,
  rules,
  dimensionType,
  QuestionIcon,
});
</script>

<style lang="scss">
.sale_dis_dim4_wrap {
  .dim_title {
    margin: 4px 0;
  }

  .wd_radio {
    margin-bottom: 8px;
  }

  .el-dialog__body {
    padding: 15px 24px 5px 24px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    .promot_question_icon {
      position: absolute;
      top: -31px;
      left: 107px;
      cursor: pointer;
    }

    .right_top_btn {
      position: absolute;
      top: -28px;
      right: -5px;
      z-index: 3;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
        margin: 0 6px;
      }

      .ai_btn {
        width: 75px;
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }
    }
  }

  .option-content {
    .option-label {
      font-size: 14px;
      font-weight: 500;
    }

    .option-description {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }

    .el-select-dropdown__item {
      padding: 8px 12px;

      .flex {
        display: flex;
        flex-direction: column;
      }

      .text-gray-400 {
        color: #999;
        font-size: 12px;
      }

      .text-sm {
        font-size: 12px;
      }

      .mt-1 {
        margin-top: 4px;
      }
    }
  }
}
</style>
