<template>
    <el-drawer v-model="isShow" :title="company.name" :with-header="true" size="640" show-close @close="onClose"
        class="drawer_customer">
        <template #header>
            <div :class="`vd_title flex-row ${!isAllowInput ? 'pointer' : ''}`" @click="onClickTitle">
                <div class="vd_title_name">
                    {{ company.name }}
                </div>
                <el-icon class="vd_title_arrow" v-if="!isAllowInput">
                    <RightArrowIcon />
                </el-icon>
            </div>
            <div class="header_right" v-if="!props.team">
                <el-button type="primary" @click="handleCommand('visit')">安排沟通</el-button>
                <el-dropdown @command="handleCommand" type="default" v-if="showEditBtn || showDeleteBtn">
                    <el-button type="default" class="more_btn">
                        <el-icon>
                            <MoreFilled />
                        </el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="edit" v-if="showEditBtn"> 编辑客户 </el-dropdown-item>
                            <el-dropdown-item command="delete" v-if=showDeleteBtn> 删除客户 </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </template>
        <iframe ref="refIframe" :src="url" width="620px" height="100%" frameborder="no" border="0" marginwidth="0"
            marginheight="0" @load="onIframeLoad"></iframe>
    </el-drawer>
    <drawerCustomerCompany ref="refDrawerCustomerCompany" />
    <DrawerCustomerForm ref="refDiaCustomer" @reload="onSearch" :isAdmin="false" />
</template>

<script setup>
import { generateUrl, jsOpenNewWindow, confirmDelete } from "@/js/utils.js"
import RightArrowIcon from "@/icons/right_arrow.vue"
import drawerCustomerCompany from "@/components/drawerCustomerCompany.vue"
import DrawerCustomerForm from "@/components/BtnAddCustomer/DrawerCustomerForm.vue";
import { MoreFilled } from '@element-plus/icons-vue'
import { deleteCustomer } from "@/js/api.js"
import { getCustomerTypeDetail } from "@/js/api.js";

const props = defineProps({
    team: {
        type: Boolean,
        required: false,
        default: false,
    },
});

const emit = defineEmits(['callback'])
const company = ref({})
const refDrawerCustomerCompany = ref()
const url = ref('');
const isShow = ref(false)
const isAllowInput = ref(false)
const refIframe = ref()
const refDiaCustomer = ref()
const customerTypeInfo = ref({})
const showEditBtn = ref(false)
const showDeleteBtn = ref(false)

const getDeptIds = (dptIds) => {
    if (typeof dptIds === 'string') {
        return dptIds
    } else if (Array.isArray(dptIds) && dptIds.length > 0) {
        return dptIds[0]
    }
    return dptIds
}

const onClickTitle = () => {
    if (!isAllowInput.value) {
        refDrawerCustomerCompany.value.show(company.value)
    }
}

const show = (row, page, param, team) => {
    isShow.value = true;
    isAllowInput.value = !row.enableBusinessRule;
    company.value = row;
    const { id, name, companyId, customerTypeId } = row;
    const iframeurl = generateUrl(`${g.config.postmeet_h5_customer}/#/customerDetail`, {
        companyId, name,
        customerId: id,
        token: g.appStore.user.token,
        st: param.startTime.substr(0, 10),
        et: param.endTime.substr(0, 10),
        tabType: page,
        myTeam: team,
        dptIds: getDeptIds(param.dptIds)
    })
    url.value = '';
    getCustomerTypeDetail(customerTypeId).then((res) => {
        console.log('客户类型详情', res.data);
        customerTypeInfo.value = res.data;
        if (res.data.permissions) {
            // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
            showEditBtn.value = res.data?.permissions.includes(3);
            showDeleteBtn.value = res.data?.permissions.includes(4);
        } else {
            showEditBtn.value = false;
            showDeleteBtn.value = false;
        }

    })
    setTimeout(() => {
        url.value = iframeurl
    }, 1);
}

const onClose = () => {
    if (refIframe.value) {
        refIframe.value.src = 'about:blank';
    }
    url.value = '';
    company.value = {};
}

const handleCommand = (command) => {
    if (command === 'visit') {
        jsOpenNewWindow(`/#/client/visit?action=create_plan&company=${company.value.name}&id=${company.value.id}`, "_blank");
    } else if (command === 'edit') {
        refDiaCustomer.value.show_edit_byid(company.value.id)
    } else if (command === 'delete') {
        const title = company.value.name;
        confirmDelete(title, (status) => {
            if (status) {
                deleteCustomer({
                    source: 1,
                    ids: [company.value.id]
                }).then(resp => {
                    if (resp.code == 0) {
                        emit('callback', 'reload')
                        isShow.value = false;
                    } else {
                        ElMessage.error(resp.message || "删除客户失败")
                    }
                }).catch(e => {
                    ElMessage.error(e.message || "删除客户失败")
                })
            }
        })
    }
}

const onIframeLoad = () => {
}

const onSearch = () => {
    emit('callback', 'reload')
}

defineExpose({ onClose, show, company, url, isShow, refIframe, RightArrowIcon, onSearch })

</script>

<style lang="scss">
.drawer_customer {
    .el-drawer__header {
        margin-bottom: 6px;
        padding: 0 12px;
        justify-content: space-between;

        .vd_title {

            .vd_title_name {
                color: #262626;
                font-weight: 500;
            }

            .vd_title_arrow {
                margin-left: 6px;
                margin-top: 3px;
            }

        }

        .el-drawer__title {
            color: #262626;
            font-weight: 500;
        }

        .header_right {
            margin-right: 20px;

            .more_btn {
                width: 32px;
                margin-left: 10px;
                padding: 0 10px;
            }
        }
    }

    .el-drawer__body {
        margin-bottom: 0 !important;
        padding: 0 !important;
    }
}
</style>