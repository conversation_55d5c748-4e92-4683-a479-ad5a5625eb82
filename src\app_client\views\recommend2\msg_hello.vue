<template>
    <div class="msg_ai msg_line">
        <div class="mbody">
            <div>
                {{ props.data.message }}
            </div>
            <div class="prod_list" v-show="props.data.showCate">
                <div v-for="item in lists.slice(0, itemCount)" @click="onClick(item)" :title="item.label"
                    :class="currItem.id == item.id ? 'active btn' : 'btn'" :key="item.id">
                    {{ item.label }}
                </div>
                <div class="btn" v-show="currCot > maxLen && lists.length > line2Idx && itemCount == line2Idx"
                    @click="onMore">
                    更多
                    <div class="icon">
                        <MoreIcon />
                    </div>
                </div>
                <div class="btn" v-show="currCot > maxLen && lists.length > line2Idx && itemCount == lists.length"
                    @click="onMore">
                    收起
                    <div class="icon">
                        <LessIcon />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import AiIcon from "@/icons/ai.vue"
import CopyIcon from "@/icons/copy.vue"
import MoreIcon from "@/app_client/icons/more.vue"
import LessIcon from "@/app_client/icons/less.vue"
import DropDownList from "@/app_client/components/dropDown.vue"
import { createGoodsChat } from "@/js/api.js"
import { getGoodsCategoryOnshelf, getCateoryCount, getCustomerRequirement } from "@/app_client/tools/api.js"
import { now } from "@/js/utils.js"
import { useRoute } from "vue-router"
import { onMounted } from "vue"

const route = useRoute();
const emit = defineEmits(['callback'])
const props = defineProps(['data']);
const itemCount = ref(3)
const currItem = ref({})
const lists = ref([])
let currCot = ref(0);
let line2Idx = ref(3)
const maxLen = ref(60)
let hasChoosed = false;
let isNeedAutoSend = false;
let meetingInfo = {}


const onMore = () => {
    if (itemCount.value === lists.value.length) {
        itemCount.value = line2Idx.value;
    } else {
        itemCount.value = lists.value.length;
    }
}

const onClick = (item) => {
    if (hasChoosed) {
        return
    }
    getCateoryCount(item.id).then(respCount => {
        if (respCount.code == 0 && respCount.data > 0) {
            hasChoosed = true;
            const param = {
                "topic": item.label, //对话主题
                "categoryId": item.id //商品分类id
            }
            createGoodsChat(param).then((resp) => {
                if (resp.code == 0) {
                    const { id, topic } = resp.data;
                    const conv = {
                        categoryId: item.id,
                        createdTime: resp.data.createdTime || now()
                        , id, topic,
                        pinned: false,
                    }
                    emit('callback', 'update_conv', conv)
                    emit("callback", 'set_bot_type', item.label == "软件产品" ? "softs" : 'product')
                    _autoSelectMeeting(item)
                } else {
                    emit('callback', 'create_chat_error', resp.message)
                }
            })
        } else {
            g.emitter.emit('show_msg_box', { message: '本分类下的商品清单还没有上架，请上架后再试', time: 3, need_icon: true })
        }
    })
}

const _autoSelectMeeting = (item) => {
    if (isNeedAutoSend) {
        getCustomerRequirement(meetingInfo.conferenceId).then(resp => {
            if (resp.code == 0) {
                meetingInfo['requirement'] = resp.data
                emit("callback", 'choose_mm', meetingInfo)
                emit('callback', 'send_input', item.label)
            }
        })
    } else {
        emit('callback', 'send_input', item.label)
    }
}


const _autoSelectCate = () => {
    const { category, confId, customerName, subject } = route.query;
    const url = location.href;
    const hasConfId = url.indexOf("confId") > -1;
    if (!!category && !!confId && hasConfId) {
        const item = lists.value.find(x => x.label == route.query.category);
        if (item) {
            meetingInfo = { category, conferenceId: confId, salesMateCustomerName: customerName, subject };
            isNeedAutoSend = true;
            onClick(item)
        }
    } else {
        meetingInfo = {}
        isNeedAutoSend = false;
    }
}


const init = () => {
    if (props.data.showCate) {
        currCot.value = 0;
        getGoodsCategoryOnshelf().then(resp => {
            if (resp.code == 0) {
                if (resp.data.length == 0) {
                    props.data.message = 'Hello，我是你的智能商品推荐伙伴，当前管理后台暂未配置可售卖商品，请联系管理员操作。'
                } else {
                    lists.value = resp.data.map(x => { return { id: x.id, label: x.name } })
                    itemCount.value = lists.value.length;
                    for (let i = 0; i < lists.value.length; i++) {
                        const row = lists.value[i]
                        currCot.value += row.label.length;
                        if (currCot.value > maxLen.value) {
                            line2Idx.value = i;
                            itemCount.value = i
                            break
                        }
                    }
                    hasChoosed = false;
                    _autoSelectCate()
                }
            }
        })
    }
}


watch(() => props.data, (newValue, oldValue) => {
    init()
})

onMounted(() => {
    init()
})

defineExpose({
    init, props, lists, CopyIcon, AiIcon, itemCount, LessIcon,
    MoreIcon, currItem, DropDownList, onMore
})
</script>

<style lang="scss">
.msg_ai {
    justify-content: flex-start;
    position: relative;

    .mbody {
        border-radius: 0px 12px 12px 12px;
        text-align: left;
        background-color: #FFFFFF;

        .prod_list {
            display: flex;
            flex-direction: row;
            width: 856px;
            flex-wrap: wrap;

            .btn {
                height: 36px;
                line-height: 36px;
                color: #262626;
                background: #F4F5F6;
                border-radius: 21px;
                padding: 0 20px;
                cursor: pointer;
                max-width: 358px;
                text-overflow: ellipsis;
                font-size: 14px;
                overflow: hidden;
                white-space: nowrap;
                margin: 8px;
                user-select: none;
                display: flex;
                flex-direction: row;

                .icon {
                    margin-top: 2px;
                }
            }

            .btn:hover {
                background: #ECF3FF;
                color: #436BFF;

                .icon {
                    color: #436BFF;
                }
            }

            .active {
                color: #436BFF;
            }

        }
    }
}
</style>
