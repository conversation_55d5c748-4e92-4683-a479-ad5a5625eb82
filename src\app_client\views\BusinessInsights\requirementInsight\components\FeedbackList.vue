<template>
    <div class="feedback_list_wrap" v-loading="loading">
        <template v-if="feedbackList.length">
            <div class="feedback_item" v-for="(item, index) in feedbackList" :key="item.id" @click="onDetail(item)">
                <div class="item_header">
                    <div class="customer_info">
                        <span class="company">{{ item.customerName }}</span>
                    </div>
                    <div class="header_right">
                        <div class="hr_txt">
                            详情
                        </div>
                    </div>
                </div>
                <div class="item_content">
                    <el-tooltip :content="item.original" :show-after="500" placement="top" :disabled="isHide"
                        popper-class="custom-tooltip">
                        <div class="single-line-ellipsis" @mouseover="isShowTooltip(item.original, $event)">
                            {{ item.original }}
                        </div>
                    </el-tooltip>
                </div>
                <div class="qa-footer">
                    <span>创建人: {{ item.hostName }}</span>
                    <span class="qa-footer-split" />
                    <span> 发言人: {{ item.feedbackName }}</span>
                    <span class="qa-footer-split" />
                    <span>创建时间: {{ item.createdTime }}</span>
                </div>

            </div>
        </template>
        <el-empty v-else style="display: flex;justify-content: flex-start;" :image="getOssUrl('no-data.png', 3)"
            description="暂无数据" />
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
const feedbackList = computed(() => props.data || [])
const isHide = ref(true)
const props = defineProps({
    data: {
        type: Array,
        default: () => ([])
    },
    loading: {
        type: Boolean,
        default: false
    }
})
const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 1, 40);
}

const onDetail = (item) => {
    g.clientStore._openMeetRecord(item.conferenceId, 'analyse', 'attitude')
}

const getStatusTagObj = (status) => {
    return g.clientBiStore.getStatusTagObj(status);
}
</script>

<style lang="scss" scoped>
.feedback_list_wrap {
    border-radius: 4px;
    flex: 1;
    // padding-left: 12px;
    overflow: scroll;



    .feedback_item {
        padding: 20px 24px;
        border-radius: 8px;
        margin-bottom: 16px;
        background: #FFFFFF;

        .status_tag {
            padding: 1px 8px;
            border-radius: 4px;
            font-size: 12px;

            &.positive {
                color: #04CCA4;
                background: rgba(4, 204, 164, 0.1);
            }

            &.middle {
                color: #595959;
                background: #F5F5F5;
            }

            &.neutral {
                color: #FF6B3B;
                background: rgba(255, 107, 59, 0.1);
            }
        }

        .item_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;

            .customer_info {
                display: flex;
                align-items: center;
                gap: 12px;

                .company {
                    font-size: 14px;
                    color: #8C8C8C;
                }

                .time {
                    font-size: 14px;
                    color: #8C8C8C;
                }


            }

            .header_right {
                .hr_txt {
                    color: #436BFF;
                    font-size: 14px;
                    margin-bottom: 2px;
                    display: flex;
                    align-items: center;
                    gap: 2px;
                    cursor: pointer;
                }
            }
        }

        .item_content {
            font-size: 14px;
            color: #262626;
            line-height: 24px;
            padding-bottom: 8px;
            cursor: pointer;
            margin: 0;
            font-weight: 700;
        }

        .qa-footer {
            display: flex;
            gap: 12px;
            color: #8C8C8C;
            font-size: 14px;
            align-items: center;

            // margin-top: 8px;
            .qa-footer-split {
                display: inline-block;
                width: 1px;
                height: 12px;
                background: #8C8C8C;
            }
        }

        &:hover {
            box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);

            .item_content {
                color: #436BFF;
            }
        }
    }
}
</style>