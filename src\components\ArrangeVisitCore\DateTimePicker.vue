<template>
    <div class="av_item_value datetime-picker flex-col">
        <div class="dt_item flex-row">
            <el-date-picker v-model="localParam.startDate" type="date" placeholder="开始日期" value-format="YYYY-MM-DD"
                :disabledDate="disabledStartDate" @change="onStartDateChange" />
            <el-time-select v-model="localParam.startTime" style="width: 240px" :start="timePickerStart" step="00:15"
                end="23:49" placeholder="开始时间" format="HH:mm" @change="onStartTimeChange" />
        </div>
        <div class="dt_item flex-row">
            <el-date-picker v-model="localParam.endDate" type="date" placeholder="结束日期" value-format="YYYY-MM-DD"
                :disabledDate="disabledEndDate" @change="onEndDateChange" />
            <el-time-select v-model="localParam.endTime" style="width: 240px" :start="endTimePickerStart" step="00:15"
                end="23:59" placeholder="结束时间" format="HH:mm" @change="onEndTimeChange" />
        </div>
    </div>
</template>

<script setup>
// import { nowMNAfter } from '@/js/utils'
import { ElMessage } from 'element-plus'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})
const localParam = ref(props.modelValue)

const emit = defineEmits(['update:modelValue'])

const timePickerStart = computed(() => {
    // if (localParam.value.startDate) {
    //     const today = new Date().toISOString().split('T')[0]
    //     if (localParam.value.startDate === today) {
    //         return nowMNAfter('hh:mm', 30)
    //     }
    // }
    return '00:00'
})

const endTimePickerStart = computed(() => {
    // if (localParam.value.endDate && localParam.value.startDate) {
    //     if (localParam.value.endDate === localParam.value.startDate) {
    //         const [hours, minutes] = (localParam.value.startTime || '00:00').split(':')
    //         const endTime = new Date()
    //         endTime.setHours(parseInt(hours), parseInt(minutes) + 60)
    //         return `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`
    //     }
    // }
    return '00:00'
})

const onStartDateChange = (val) => {
    localParam.value.startDate = val
    if (val === new Date().toISOString().split('T')[0]) {
        // 设置为下一个整半点时间
        localParam.value.startTime = getNextHalfHour()

        // 自动设置结束时间为开始时间后1小时
        const [hours, minutes] = localParam.value.startTime.split(':')
        const endTime = new Date()
        endTime.setHours(parseInt(hours), parseInt(minutes))
        endTime.setTime(endTime.getTime() + 60 * 60 * 1000)
        localParam.value.endTime = `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`
    }
    if (localParam.value.endDate && localParam.value.endDate < val) {
        localParam.value.endDate = val
    }
    emit('update:modelValue', localParam.value)
}

const onStartTimeChange = (val) => {
    localParam.value.startTime = val

    // 检查如果是当天，且选择的时间小于当前时间
    if (localParam.value.startDate === new Date().toISOString().split('T')[0]) {
        const now = new Date()
        const currentTime = now.getTime()

        const [hours, minutes] = val.split(':')
        const selectedTime = new Date(localParam.value.startDate)
        selectedTime.setHours(parseInt(hours), parseInt(minutes))

        if (selectedTime.getTime() < currentTime) {
            // 计算下一个整半点时间
            const currentMinutes = now.getMinutes()
            const nextHalfHour = new Date(now)

            if (currentMinutes < 30) {
                nextHalfHour.setMinutes(30, 0, 0)
            } else {
                nextHalfHour.setHours(now.getHours() + 1, 0, 0, 0)
            }

            const nextTime = `${String(nextHalfHour.getHours()).padStart(2, '0')}:${String(nextHalfHour.getMinutes()).padStart(2, '0')}`

            ElMessage.warning('开始时间不能小于当前时间，已自动调整为最近的整半点时间')
            localParam.value.startTime = nextTime
        }
    }

    // 自动设置结束时间为开始时间后1小时
    if (localParam.value.startTime) {
        const [hours, minutes] = localParam.value.startTime.split(':')
        const endTime = new Date()
        endTime.setHours(parseInt(hours), parseInt(minutes))
        endTime.setTime(endTime.getTime() + 60 * 60 * 1000) // 增加1小时
        localParam.value.endTime = `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`
    }

    emit('update:modelValue', localParam.value)
}

// 添加一个方法来获取下一个整半点时间
const getNextHalfHour = (date) => {
    const now = date || new Date()
    const currentMinutes = now.getMinutes()
    const nextHalfHour = new Date(now)

    if (currentMinutes < 30) {
        nextHalfHour.setMinutes(30, 0, 0)
    } else {
        nextHalfHour.setHours(now.getHours() + 1, 0, 0, 0)
    }

    return `${String(nextHalfHour.getHours()).padStart(2, '0')}:${String(nextHalfHour.getMinutes()).padStart(2, '0')}`
}

const onEndDateChange = (val) => {
    localParam.value.endDate = val
    if (val === localParam.value.startDate && localParam.value.startTime) {
        if (!localParam.value.endTime || localParam.value.endTime <= localParam.value.startTime) {
            const [hours, minutes] = localParam.value.startTime.split(':')
            const endTime = new Date()
            endTime.setHours(parseInt(hours), parseInt(minutes) + 60)
            localParam.value.endTime = `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`
        }
    }
    emit('update:modelValue', localParam.value)
}

const onEndTimeChange = (val) => {
    localParam.value.endTime = val
    emit('update:modelValue', localParam.value)
}

const disabledStartDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7
}

const disabledEndDate = (time) => {
    const startDate = localParam.value.startDate ? new Date(localParam.value.startDate) : null
    if (!startDate) return false

    const timeDate = new Date(time.setHours(0, 0, 0, 0))
    const startDateTime = new Date(startDate.setHours(0, 0, 0, 0))
    const nextDay = new Date(startDateTime)
    nextDay.setDate(nextDay.getDate() + 1)

    return timeDate.getTime() < startDateTime.getTime() ||
        timeDate.getTime() > nextDay.getTime()
}

watch(() => props.modelValue, (newVal) => {
    localParam.value = newVal
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.av_item_value {
    .dt_item {
        margin: 5px 0;

        :deep(.el-date-editor) {
            margin: 0 5px !important;
            width: 280px !important;
        }
    }
}
</style>
