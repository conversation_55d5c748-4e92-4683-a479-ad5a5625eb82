// @see https://www.electron.build/configuration/configuration
{
  "$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  "appId": "com.yunxuetang.novaguidemac.test",
  "afterSign":"build/notarize.js",
  "asar": true,
  "productName": "绚星销售助手test",
  "directories": {
    "output": "release/test/${version}"
  },
  "files": [
    "dist",
    "dist-electron"
  ],
  "protocols": [
    {
      "name": "com.yunxuetang.novaguidemac.test",
      "schemes": [
        "yxtnovaguidetest"  
      ]
    }
  ],
  "mac": {
    "icon": "build/favicon.icns",
    "target": {
      "target": "default",
      "arch": [
        "universal"
      ]
    },
    "artifactName": "${productName}-Mac-${version}-Installer.${ext}",
    "extraResources": [
      "./build/extraResources/mac.zip"
    ],
    "publish": [
      {
        "provider": "generic",
        "url": "https://meetcdn.yxt.com/download-novaguide/mac/"
      }
    ],
    "entitlements": "build/entitlements.mac.plist",
    "entitlementsInherit": "build/entitlements.mac.plist",
    "hardenedRuntime": true,
    "gatekeeperAssess": false
  },
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": [
          "x64"
        ]
      },
      "zip"
    ],
    "icon": "public/favicon.ico",
    "artifactName": "${productName}-Windows-${version}-Installer.${ext}",
    "publish": [
      {
        "provider": "generic",
        "url": "https://meetcdn.yxt.com/download-novaguide/windows/"
      }
    ],
    "verifyUpdateCodeSignature": false,
    "signingHashAlgorithms": [
      "sha256"
    ],
    "certificatePassword": "88888888",
    "certificateSha1": "2fc94b1e8ec3dfb91c0b1db8d8aa87bebe3792f1",
    "rfc3161TimeStampServer": "http://timestamp.comodoca.com/rfc3161",
    "extraResources": [
      {
        "from": "build/extraResources/win",
        "to": "extraResources/win",
        "filter": [
          "**/*"
        ]
      }
    ],
    "signDlls": true
  },
  "nsis": {
    "oneClick": false,
    "perMachine": true,
    "allowToChangeInstallationDirectory": true,
    "deleteAppDataOnUninstall": false,
    "createDesktopShortcut": true,
    "installerHeaderIcon": "public/favicon.ico",
    "installerIcon": "public/favicon.ico",
    "uninstallerIcon": "public/favicon.ico",
    "include": "build/urlProtoco-test.nsh"
  },
  "linux": {
    "target": [
      "AppImage"
    ],
    "artifactName": "${productName}-Linux-${version}.${ext}"
  }
}