<template>
    <div class="meeting_lists" v-if="isShow">
        <div class="ml_box">
            <div class="ml_title">
                {{ item.subject }}
            </div>
            <div class="ml_sub">
                {{ item.salesMateCustomerName }}
            </div>
            <div class="ml_icon" @click="onClose">
                <CloseIcon />
            </div>
        </div>
    </div>
</template>

<script setup>
import CloseIcon from "@/app_client/icons/meetClose.vue"

const emit = defineEmits(["callback"]);
const isShow = ref(false);
const item = ref({})

const show = (_item) => {
    isShow.value = true;
    item.value = _item;
}

const ClearItem = () => {
    item.value = {}
    isShow.value = false;
}

const onClose = () => {
    ClearItem();
    emit("callback", "clear_meeting_list")
}

defineExpose({
    CloseIcon, isShow, onClose, show, ClearItem
})

</script>

<style lang="scss">
.meeting_lists {
    display: flex;
    flex-direction: row;


    .ml_box {
        width: 242px;
        height: 64px;
        background: #F1F2FA;
        border-radius: 12px;
        border: 1px solid #E9E9E9;
        opacity: 0.5;
        padding: 12px;
        margin-bottom: 20px;
        position: relative;
        margin-right: 12px;

        .ml_title {
            height: 17px;
            font-size: 12px;
            color: #262626;
            line-height: 17px;
        }

        .ml_sub {
            height: 17px;
            font-size: 12px;
            color: #8C8C8C;
            line-height: 17px;
        }

        .ml_icon {
            position: absolute;
            top: -10px;
            right: -10px;
            cursor: pointer;
        }
    }
}
</style>