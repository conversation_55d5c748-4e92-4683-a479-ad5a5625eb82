<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <rect id="path-1" x="0" y="0" width="432" height="50"></rect>
            <filter x="-0.1%" y="-1.0%" width="100.2%" height="102.0%" filterUnits="objectBoundingBox" id="filter-2">
                <feOffset dx="0" dy="-1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1"
                    result="shadowInnerInner1"></feComposite>
                <feColorMatrix values="0 0 0 0 0.91372549   0 0 0 0 0.91372549   0 0 0 0 0.91372549  0 0 0 1 0"
                    type="matrix" in="shadowInnerInner1"></feColorMatrix>
            </filter>
        </defs>
        <g id="需求调研报告助手" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="主题管理-未关联商品-维度排序" transform="translate(-1200.000000, -147.000000)">
                <rect fill="#F6F7FA" x="0" y="0" width="1280" height="850"></rect>
                <g id="编组-8">
                    <g id="6.反馈-/2.Drawer-抽屉-/-960-/-页签">
                        <rect id="矩形" fill-opacity="0.7" fill="#000000" x="0" y="5.68434189e-14" width="1280"
                            height="850"></rect>
                        <rect id="矩形" fill="#FFFFFF" x="800" y="5.68434189e-14" width="480" height="812"></rect>
                    </g>
                    <g id="编组-17" transform="translate(824.000000, 80.000000)">
                        <g id="矩形" transform="translate(0.000000, 50.000000)">
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        </g>
                        <g id="拖动排序" transform="translate(376.000000, 67.000000)" fill="#595959">
                            <path
                                d="M6,4 C6.55228475,4 7,3.55228475 7,3 C7,2.44771525 6.55228475,2 6,2 C5.44771525,2 5,2.44771525 5,3 C5,3.55228475 5.44771525,4 6,4 Z M6,9 C6.55228475,9 7,8.55228475 7,8 C7,7.44771525 6.55228475,7 6,7 C5.44771525,7 5,7.44771525 5,8 C5,8.55228475 5.44771525,9 6,9 Z M6,14 C6.55228475,14 7,13.5522847 7,13 C7,12.4477153 6.55228475,12 6,12 C5.44771525,12 5,12.4477153 5,13 C5,13.5522847 5.44771525,14 6,14 Z M10,4 C10.5522847,4 11,3.55228475 11,3 C11,2.44771525 10.5522847,2 10,2 C9.44771525,2 9,2.44771525 9,3 C9,3.55228475 9.44771525,4 10,4 Z M10,9 C10.5522847,9 11,8.55228475 11,8 C11,7.44771525 10.5522847,7 10,7 C9.44771525,7 9,7.44771525 9,8 C9,8.55228475 9.44771525,9 10,9 Z M10,14 C10.5522847,14 11,13.5522847 11,13 C11,12.4477153 10.5522847,12 10,12 C9.44771525,12 9,12.4477153 9,13 C9,13.5522847 9.44771525,14 10,14 Z"
                                id="形状结合"></path>
                        </g>
                        <g id="拖动排序" transform="translate(376.000000, 67.000000)" fill="#595959">
                            <path
                                d="M6,4 C6.55228475,4 7,3.55228475 7,3 C7,2.44771525 6.55228475,2 6,2 C5.44771525,2 5,2.44771525 5,3 C5,3.55228475 5.44771525,4 6,4 Z M6,9 C6.55228475,9 7,8.55228475 7,8 C7,7.44771525 6.55228475,7 6,7 C5.44771525,7 5,7.44771525 5,8 C5,8.55228475 5.44771525,9 6,9 Z M6,14 C6.55228475,14 7,13.5522847 7,13 C7,12.4477153 6.55228475,12 6,12 C5.44771525,12 5,12.4477153 5,13 C5,13.5522847 5.44771525,14 6,14 Z M10,4 C10.5522847,4 11,3.55228475 11,3 C11,2.44771525 10.5522847,2 10,2 C9.44771525,2 9,2.44771525 9,3 C9,3.55228475 9.44771525,4 10,4 Z M10,9 C10.5522847,9 11,8.55228475 11,8 C11,7.44771525 10.5522847,7 10,7 C9.44771525,7 9,7.44771525 9,8 C9,8.55228475 9.44771525,9 10,9 Z M10,14 C10.5522847,14 11,13.5522847 11,13 C11,12.4477153 10.5522847,12 10,12 C9.44771525,12 9,12.4477153 9,13 C9,13.5522847 9.44771525,14 10,14 Z"
                                id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'QuestionIcon',
}
</script>