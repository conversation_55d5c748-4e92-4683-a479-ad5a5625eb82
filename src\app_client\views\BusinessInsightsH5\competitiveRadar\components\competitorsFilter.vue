<template>
    <div class="competitors-filter-h5 hide-scrollbar">
        <div v-for="(item) in options" :key="item.value"
            :class="['option-btn', { active: localSelectedValue === item.value }]" @click="handleSelect(item.value)">
            {{ item.label }}
        </div>
    </div>
</template>

<script setup>
const localSelectedValue = ref('')
const props = defineProps({
    options: {
        type: Array,
        required: true,
        default: () => []
    },
    modelValue: {
        type: [String, Number, Boolean],
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

const handleSelect = (value) => {
    localSelectedValue.value = value
    emit('update:modelValue', value)
}

watch(() => props.modelValue, (newValue) => {
    localSelectedValue.value = newValue
}, { immediate: true })
</script>

<style lang="scss" scoped>
.competitors-filter-h5 {
    margin: 16px 0;
    display: flex;
    gap: 12px;
    width: 100%;
    overflow: scroll;

    .option-btn {
        padding: 4px 12px;
        border-radius: 14px;
        background: #E9E9E9;
        font-size: 14px;
        color: #595959;
        cursor: pointer;
        transition: all 0.2s;
        word-break: keep-all;


        &.active {
            background: #E6EEFF;
            color: #436BFF;
            // font-weight: bold;
        }
    }
}
</style>
