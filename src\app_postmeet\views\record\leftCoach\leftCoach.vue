<template>
  <div class="left_coach_wrap" v-ai-tip="'center'">
    <div v-if="isShowSetup">
      <noData v-if="!isLoadingReport" />
    </div>
    <lessData v-else-if="!hasSaleData" />
    <template v-else>
      <template v-if="tags">
        <btnRadio ref="refTags" :active-id="activeId" :tags="tags" @callback="cbMenu" />
        <div class="left_main">
          <MdViewer :md="tags[activeId].data.report" v-if="tags[activeId].diys" />
          <component :is="tags[activeId].page" v-else />
        </div>
      </template>
      <div v-else-if="!hasSalePositon">
        <el-empty>
          <div class="empty_desc flex-col">
            <div class="empty_desc_title">沟通创建人「未发言」，无法显示辅导内容</div>
            <div class="empty_desc_tip">请在「会议纪要」中发言，以便辅导内容显示</div>
          </div>
        </el-empty>
      </div>
      <lessData v-else />
    </template>
  </div>
</template>

<script>
import btnRadio from "@/app_postmeet/components/btnRadio.vue";
import lcOverview from "./lcOverview/lcOverview.vue";
import lcSalePerformance from "./lcSalePerformance/lcSalePerformance.vue";
import CapabilityAssessment from "./CapabilityAssessment/lcSalePerformance.vue";
import lcTaskAchievement from "./lcTaskAchievement/lcTaskAchievement.vue";
import noData from "./nodata/nodata.vue";
import MdViewer from "@/components/Markdown/MdViewer.vue"
import lessData from "@/app_postmeet/components/nodata.vue";
import { markRaw } from "vue";
export default {
  components: { btnRadio, noData, MdViewer, lessData },
  data() {
    return {
      hasSalePositon: false,
      isLoadingReport: false,
      hasSaleData: false,
      isShowSetup: false,
      activeId: "overview",
      tags: {},
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
    g.emitter.on("update_coach_menu", (tag) => {
      this.cbMenu(tag);
    });
  },
  methods: {
    init() {
      this.isLoadingReport = g.postmeetStore.data.isLoadingReport;
      this.isShowSetup = g.postmeetStore.getIsShowSetup();
      const ar = g.postmeetStore.data.saleReport.salesCounsellingReports;
      const hasCapabilityAssessment = ar && ar.find((x) => x.systemId == 205);
      const hasPerformance = ar && ar.find((x) => x.systemId == 200);
      const hasTaskAchievement = ar && ar.find((x) => x.systemId == 206);
      this.tags = {
        overview: {
          page: markRaw(lcOverview),
          name: "概览",
          show: true,
          disabled: false,
        },
        performance: {
          page: markRaw(lcSalePerformance),
          name: "销售表现",
          show: hasPerformance,
          disabled: false,
        },
        capabilityAssessment: {
          page: markRaw(CapabilityAssessment),
          name: "能力评估",
          show: hasCapabilityAssessment,
          disabled: false,
        },
        taskAchievement: {
          page: markRaw(lcTaskAchievement),
          name: "任务达成",
          show: hasTaskAchievement,
          disabled: false,
        },
      };
      if (!this.isShowSetup && ar) {
        this.hasSaleData = true;
        const new_dims = ar.filter((x) => x.systemId == 0);
        if (new_dims.length > 0) {
          this.$nextTick(() => {
            for (let dim of new_dims) {
              this.tags[dim.id] = {
                name: dim.name,
                show: true,
                disabled: false,
                diys: true,
                data: dim,
              };
            }
            this.$refs.refTags && this.$refs.refTags.setTags(this.tags);
          });
        }
      }
      this._checkHasSale();
    },
    _checkHasSale() {
      const rolelist = g.postmeetStore.getRoleList();
      const id = g.postmeetStore.data.asrRaw.hostSsoUserId;
      this.hasSalePositon = rolelist.map(x => x.id).includes(id);
    },
    handleClick() {
      this.$emit("callback", this.activeId);
    },
    cbMenu(tag) {
      this.activeId = tag;
    },
  },
};
</script>

<style lang="scss">
.left_coach_wrap {
  margin-bottom: 20px;
}

.empty_desc_tip {
  color: #999;
}
</style>
