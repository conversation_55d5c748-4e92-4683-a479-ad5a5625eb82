<template>
    <AddContactInnerForm ref="formRef" @submit="onSubmit" @cancel="onCancel" />
</template>

<script setup>
const formRef = ref()

const onSubmit = (data) => {
    console.log('onSubmit', data)
    g.electronStore.sendMessage('arrange_visit', 'add-contact-inner', data)
    g.electronStore.closeWin('add_inner_contact')
}

const onCancel = () => {
    g.electronStore.closeWin('add_inner_contact')
}

onMounted(() => {
    formRef.value?.setFormData({})
})
</script>