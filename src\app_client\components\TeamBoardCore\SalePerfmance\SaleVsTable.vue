<template>
  <div class="overview-sp flex-col" v-loading="loading" element-loading-text="数据加载中... 请稍等">
    <div class="overview-header flex-row">
      <div class="flex-row title-line-wrap">
        <div class="title-line"></div>
        <div class="title">销售表现对比</div>
      </div>
    </div>
    <div class="user_selector flex-row">
      <div class=" flex-row">
        <DrawerSelectMixed @callback="onSelectUser" ref="refUser" type="user" :rootDeptId="rootDeptId"
          :enableClose="true" :maxTagCount="5" :maxSelect="5" />
        <!-- <el-button type="primary" @click="onClick" :disabled="selectSsoUserIds.length == 0">开始对比 ({{
          selectSsoUserIds.length }}/5)</el-button> -->
      </div>
      <RadioGroup v-model="perfType" class="fch_radio" :options="options" />
    </div>
    <div class="overview-body">
      <div class="table-container" v-if="tableData.length > 0">
        <el-table :data="tableData" style="width: 100%; min-width: 100%" header-cell-class-name="table-header">
          <el-table-column fixed prop="name" label="人员" min-width="120" />
          <el-table-column sortable v-for="column in columns" :key="column" :prop="column" :label="column"
            min-width="210" />
        </el-table>
      </div>
      <template v-else>
        <el-empty description="请选择销售人员" :image="getOssUrl('no-data.png', 3)" />
      </template>
    </div>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import RadioGroup from '@/app_client/components/RadioGroup.vue'
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import { getUsersAssessCompareData } from "@/app_client/tools/api.js"

const props = defineProps({
  isReport: {
    type: Boolean,
    default: false
  },

});
const options = g.clientBoardStore.perfTypeOptions;
const tableData = ref([])
const columns = ref([]);

const loading = ref(false);
const perfType = ref('');
const perfTypeRanking = computed(() => {
  return g.clientBoardStore.perfTypeRanking;
})
const overviewDept = computed(() => {
  return g.clientBoardStore.overviewDept;
});
const periodType = computed(() => {
  return g.clientBoardStore.periodType;
})
const rootDeptId = computed(() => {
  return g.clientBoardStore.overviewDept.value;
});
const selectSsoUserIds = ref([])

const perfTypeLocal = ref('ability');
const onSelectUser = (action, data) => {
  console.log('onSelectUser', action, data)
  if (action == "confirm") {
    selectSsoUserIds.value = data.users.map(item => item.ssoUserId);
    init()
  }
  if (action == "confim") {
    tableData.value = [];
  }
};
const refUser = ref(null);
const onClick = () => {
  init()
}

// 默认选中前两项用户
const setDefaultUsers = async () => {
  try {
    // 使用API获取用户列表
    const { getUserListPager } = await import('@/js/api');
    const response = await getUserListPager({
      pageNum: 1,
      pageSize: 10,
      deptIds: [rootDeptId.value]
    });

    if (response.code === 0 && response.data.datas) {

      const defaultUsers = response.data.datas.slice(0, 2);
      // 设置默认选中的用户
      refUser.value?.init({ users: defaultUsers });
      selectSsoUserIds.value = defaultUsers.map(item => item.ssoUserId);
      init()
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

const converFunc = (data, settings) => {
  // 根据当前perfType获取对应的评估维度配置
  let assessmentConfig = null;
  if (perfType.value === 'ability') {
    assessmentConfig = settings.salesAbilityAssesses;
  } else if (perfType.value === 'taskCompletion') {
    assessmentConfig = settings.taskCompleteAssesses;
  }

  // 获取所有启用的评估维度名称
  const allAssessments = [];
  if (assessmentConfig) {
    // 添加系统评估维度
    if (assessmentConfig.systemAssessments) {
      assessmentConfig.systemAssessments.forEach(assessment => {
        if (assessment.enabled) {
          allAssessments.push(assessment.name);
        }
      });
    }
    // 添加自定义评估维度
    if (assessmentConfig.customizedAssessments) {
      assessmentConfig.customizedAssessments.forEach(assessment => {
        if (assessment.enabled) {
          allAssessments.push(assessment.name);
        }
      });
    }
  }

  // 构建表格数据 - 第一列是人员姓名，每一列是评估维度
  const tableRows = data.map(user => {
    // 创建基础行数据，第一列是人员姓名
    const row = {
      name: user.name
    };

    // 为每个评估维度添加得分列
    allAssessments.forEach(assessmentName => {
      const score = user.scores.find(s => s.assessmentName === assessmentName);
      // 如果没有找到分数或者分数为空，显示"-"
      row[assessmentName] = score && score.score ? Number(score.score).toFixed(2) : '-';
    });
    return row;
  });

  columns.value = allAssessments.sort((x, y) => x > y ? 1 : -1);
  return tableRows;
}

const init = async () => {
  tableData.value = [];
  if (selectSsoUserIds.value.length == 0) return;

  loading.value = true;
  const { orgId, deptId, dataType, startTime, endTime, periodType } = g.clientBoardStore.getApiParam(perfType.value)
  await getUsersAssessCompareData(orgId, deptId, dataType, {
    startTime: startTime,
    endTime: endTime,
    ssoUserIds: toRaw(selectSsoUserIds.value)
  }).then(res => {
    if (res.code == 0) {
      tableData.value = converFunc(res.data.datas, res.data.settings);
    }
  }).finally(() => {
    loading.value = false;
  })

}

watch(() => g.clientBoardStore.perfType, () => {
  perfType.value = g.clientBoardStore.perfType;
}, { immediate: true })

watch(() => [perfType.value, periodType.value, overviewDept.value], () => {
  init()
}, { immediate: true });

watch(() => [overviewDept.value], () => {
  refUser.value?.reset(true, true)
  // 当部门变化时，重新设置默认用户
  nextTick(() => {
    setDefaultUsers();
  });
}, { immediate: true });

// 组件挂载后设置默认用户
onMounted(() => {
  nextTick(() => {
    setDefaultUsers();
  });
});

defineExpose({
  perfTypeLocal,
});
</script>

<style lang="scss">
.overview-sp {
  .no_select_col {
    width: 100%;
    margin: 20px 0;
    font-size: 16px;
    color: #262626;
    height: 250px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .overview-header {
    justify-content: space-between;
    margin: 40px 0 20px 0;


  }

  .user_selector {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;

    .mixed_input_box {
      width: 600px;
    }

    .el-button {
      margin-left: 12px;
    }
  }

  .overview-body {
    // display: flex;
    margin-bottom: 40px;
    width: 100%;

    .table-container {
      overflow-x: auto;
      width: 100%;
    }

    thead {
      background-color: #E9E9E9;
      color: #262626;

      th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 表格标头背景色样式
.table-header {
  background-color: #F9FAFC !important;
  color: #262626 !important;
  font-weight: 500 !important;
  border-bottom: none !important;
}
</style>
