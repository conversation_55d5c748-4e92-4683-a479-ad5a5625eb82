<template>
  <div class="diabody_create_wrap">
    <div class="h_main">
      <div class="dia_header" v-if="!isElectron">发言者身份标注</div>
      <div class="dia_main">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" label-position="top">
          <el-form-item prop="userType" label="发言人角色">
            <el-radio-group v-model="formData.userType" @change="onChange">
              <el-radio value="customer">客户角色</el-radio>
              <el-radio value="internal">内部伙伴</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="companyName" label="公司名称" v-if="formData.userType == 'customer' && isElectron"
            :required="isElectron">
            <CustomerNamePicker v-model="formData.companyName" v-model:customerId="formData.customerId"
              ref="refCustomerNamePicker" @change="onChangeCompanyName" />
          </el-form-item>

          <el-form-item prop="editMode" label="发言人类型" v-if="formData.userType == 'customer'">
            <el-radio-group v-model="formData.editMode" @change="onChange">
              <el-radio value="select">选择已有发言人</el-radio>
              <el-radio value="edit">{{ isAddNew ? "新建发言人" : "编辑发言人" }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="editMode" label="发言人类型" v-if="formData.userType == 'internal'">
            <el-radio-group v-model="formData.editMode" @change="onChange">
              <el-radio value="select">组织架构内人员</el-radio>
              <el-radio value="edit">新建外部人员</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <CustomerSelect ref="refCustomerSelect" v-if="formData.userType == 'customer' && formData.editMode == 'select'"
          @callback="cbSubForm" />
        <InternalSelect ref="refInternalSelect"
          v-if="formData.userType == 'internal' && formData.editMode == 'select'" />
        <FieldAdd ref="refCustomerAdd" v-if="formData.userType == 'customer' && formData.editMode == 'edit'" />
        <FieldAdd ref="refInternalAdd" v-if="formData.userType == 'internal' && formData.editMode == 'edit'" />
      </div>
    </div>
    <div class="footer">
      <div class="blank"></div>
      <el-button type="default" @click="onCancel">取消 </el-button>
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { getFormFields, updateInmeetCompanyName } from '@/js/api';
import { saveCustomRole, saveInternalRole, getAsrStoreUpdated, updateParName } from "@/app_postmeet/tools/api";
import CustomerSelect from "./CustomerSelect.vue";
import InternalSelect from "./InternalSelect.vue";
import FieldAdd from "./FieldAdd.vue";
import CustomerNamePicker from '@/components/CustomerNamePicker'

const emit = defineEmits(["callback"]);
const refCustomerSelect = ref(null);
const refInternalSelect = ref(null);
const refCustomerAdd = ref(null);
const refInternalAdd = ref(null);
const internalFields = ref([]);
const customerFields = ref([]);
const isAddNew = ref(true);
const formRef = ref(null);
const confId = ref('');
const refCustomerNamePicker = ref()
const isQuickMeeting = ref(true)
const isElectron = ref(g.config.isElectron);
const roleList = ref([]);
const formData = reactive({
  userType: 'customer',
  editMode: 'select',
  companyName: '',
  customerId: '',
  selectPerson: {}
});

const rules = {
  userType: [
    { required: true, message: '请选择发言人角色', trigger: 'change' }
  ],
  editMode: [
    { required: true, message: '请选择发言人类型', trigger: 'change' }
  ]
};

const cbSubForm = (action, data) => {
  emit("callback", action, data);
}

const onChange = () => {
  nextTick(() => {
    _init_ui();
  });
};

const _reloadCostomerContactSelect = () => {
  if (formData.userType == 'customer' && formData.editMode == 'select') {
    refCustomerSelect.value.init(confId.value);
  }
}

const onChangeCompanyName = (val) => {
  if (!val.trim()) return
  updateInmeetCompanyName(confId.value, val.trim()).then((resp) => {
    if (resp.code == 0) {
      g.appStore.setStore(g.cv.keyInmeetCompanyName, val.trim())
      _reloadCostomerContactSelect()
    } else {
      ElMessage.error(resp.message)
    }
  })
}

const _get_data = async () => {
  let param = {};
  try {
    if (formData.userType == "customer") {
      if (formData.editMode == "select") {
        param = refCustomerSelect.value.get_data();
      } else {
        param = await refCustomerAdd.value.get_data();
      }
    } else {
      if (formData.editMode == "select") {
        param = refInternalSelect.value.get_data();
      } else {
        param = await refInternalAdd.value.get_data();
      }
    }
    param['ui'] = formData.selectPerson.ui;
    return param;
  } catch (error) {
    throw error; // Propagate validation errors up
  }
}

const _saveCustomRole = async () => {
  try {
    const param = await _get_data();
    const resp = await saveCustomRole(confId.value, param);
    if (resp.code == 0) {
      _send_emit(param);
    } else {
      ElMessage.error(resp.message);
    }
  } catch (e) {
    console.log("has error", e);
    if (e.status == 400) {
      const resp = e.response.data;
      ElMessage.error(resp.message);
    } else {
      ElMessage.error(e.message);
    }
  }
}

const _send_emit = (param) => {
  if (!g.postmeetStore.data.confId) {
    //  in electron in meeting, we need to close the window
    param['speaker_type'] = formData.userType == 'customer' ? 'CUSTOMER' : 'PARTNER'
    emit("callback", "submit", param);
    return
  }
  const data = { id: param.ui, value: param.name };
  updateParName(confId.value, data).then((resp) => {
    if (resp.code == 0) {
      getAsrStoreUpdated().then((resp2) => {
        if (resp2.code == 0) {
          if (resp2.data.hasUpdatedContent) {
            g.postmeetStore.setUpdatedContent(resp2.data.asrUpdatedContent);
            g.postmeetStore.setValue("allowSaleReGen", true);
            g.emitter.emit("updatedAsrContent", "");
            ElMessage.success("更新成功");
            emit("callback", "close");
          }
        }
      });
    }
  });
}

const _saveInternalRole = async () => {
  try {
    const param = await _get_data();
    const resp = await saveInternalRole(confId.value, param);
    if (resp.code == 0) {
      _send_emit(param);
    } else {
      ElMessage.error(resp.message);
    }
  } catch (e) {
    console.log("has error", e);
    if (e.status == 400) {
      const resp = e.response.data;
      ElMessage.error(resp.message);
    } else {
      ElMessage.error(e.message);
    }
  }
}

const onCancel = () => {
  emit("callback", "close");
}

const onConfirm = async () => {
  try {
    await formRef.value.validate();
    if (formData.userType == "customer") {
      _saveCustomRole();
    } else {
      _saveInternalRole();
    }
  } catch (error) {
    console.log('Form validation failed');
    return false;
  }
};

const _init_ui = () => {
  nextTick(() => {
    if (formData.userType == "customer") {
      if (formData.editMode == "select") {
        refCustomerSelect?.value.init(confId.value, formData.selectPerson, customerFields.value);
      } else {
        refCustomerAdd?.value.init(customerFields.value);
      }
    } else {
      if (formData.editMode == "select") {
        refInternalSelect?.value.init();
      } else {
        refInternalAdd?.value.init(internalFields.value);
      }
    }
  })
}

const init = async (item, id) => {
  console.log("rename init", item, id)
  confId.value = id;
  formData.selectPerson = item;
  roleList.value = g.postmeetStore.data.roleList;
  const findUser = roleList.value.find(item => item.ui == formData.selectPerson.ui);
  console.log('findUser', findUser)
  if (findUser) {
    formData.userType = findUser.type || 'internal';
  }
  const respInternal = await getFormFields('INTERNAL_PARTNER');
  if (respInternal.code == 0) {
    internalFields.value = respInternal.data.filter(item => item.fieldStatus == 1);
  }
  const respCustomer = await getFormFields('OUTER_PARTNER');
  if (respCustomer.code == 0) {
    customerFields.value = respCustomer.data.filter(item => item.fieldStatus == 1);
  }
  _init_ui();
}

const init_meet = (param, confId, _isQuickMeeting) => {
  console.log("rename init_meet", param, confId, _isQuickMeeting)
  init(param, confId)
  isQuickMeeting.value = _isQuickMeeting
  if (_isQuickMeeting) {
    param.companyName = g.appStore.getStore(g.cv.keyInmeetCompanyName, '')
    rules.companyName = [{ required: true, message: '请输入公司名称', trigger: 'change' }]
  }
}

defineExpose({
  init,
  init_meet
});

</script>

<style lang="scss">
.diabody_create_wrap {
  .h_main {
    padding: 16px 0;
    display: flex;
    flex-direction: column;

    .dia_header {
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      padding: 0 24px 10px 24px;
      font-size: 16px;
      color: #262626;
      line-height: 24px;
      border-bottom: 1px solid #e9e9e9;
    }

    .dia_main {
      margin: 12px 24px;

      .rd_box_inner {
        position: relative;
      }

      .rd_box {
        .rd_title {
          display: flex;
          align-items: center;
          margin: 0;

          p {
            width: 4px;
            height: 4px;
            background-color: red;
            border-radius: 50%;
            margin-left: 6px;
          }
        }

        .rd_input {
          margin: 10px 0;
        }

        .el-select {
          width: 100%;
          margin-top: 12px;
        }

        .rd_tip {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 20px;
          margin-top: 10px;
        }
      }

      .rd_name {
        margin-top: 12px;

        .rnd_icon {
          margin-top: 2px;
          margin-left: 6px;
          cursor: pointer;
          color: #436bff;
        }
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    padding: 19px;
    border-top: 1px solid #e9e9e9;

    .el-checkbox {
      margin: 5px 0;
    }

    .blank {
      flex-grow: 1;
    }
  }
}
</style>
