<template>
    <div :class="`text-wrapper_14 flex-col ${props.choosed == 2 ? 'active' : ''}`" @click="onClick(2)">
        <div class="text_27">问答回顾</div>
        <dashline />
        <div class="qareview_wrap">
            <page :id="2" />
            <div class="image-wrapper_1 flex-row" v-if="props.choosed">
                <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
            </div>
        </div>
    </div>

</template>


<script setup>
import dashline from './dashline.vue';
import page from '../pages/page.vue';
import { r_png } from './image.js';
const props = defineProps(['choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])

const onClick = (value) => {
    if (props.enableChoose) {
        emit('update:choosed', value)
    }
}

defineExpose({ onClick, page, dashline })

</script>

<style lang="scss">
.qareview_wrap {
    display: flex;
    flex-direction: column;
    padding-left: 8px;
}
</style>
