{"name": "novaguidepc", "version": "0.2.4", "main": "dist-electron/main/index.js", "description": "先进销售团队，先用绚星销售助手", "author": "江苏云学堂网络科技有限公司", "license": "MIT", "private": true, "keywords": ["electron", "vite", "vue3"], "scripts": {"bootstrap": "yarn install --registry=http://npm.yunxuetang.com.cn/repository/yxt-npm/", "dev": "yarn dev:tf", "dev:tf": "cross-env VUE_APP_APIENV=tf-tc-01 vite --mode test_tf", "dev:di": "cross-env VUE_APP_APIENV=di-hw-01 vite --mode test_di", "dev:prod": "cross-env VUE_APP_APIENV=prod vite --mode test_prod", "build:tf": "cross-env NODE_ENV=production VUE_APP_APIENV=tf-tc-01 vite build --mode web_tf", "build:tf_local": "cross-env NODE_ENV=production VUE_APP_APIENV=tf-tc-01 vite build --mode web_tf_local", "build:prod_local": "cross-env NODE_ENV=production VUE_APP_APIENV=prod vite build --mode web_prod_local", "build:di": "cross-env NODE_ENV=production VUE_APP_APIENV=di-hw-01 vite build --mode web_di", "build:prod": "cross-env NODE_ENV=production VUE_APP_APIENV=prod vite build --mode web_prod", "build:blue": "cross-env NODE_ENV=production VUE_APP_APIENV=prod vite build --mode web_blue", "build": "node ./build/build.js", "edev": "yarn edev:tf", "edev:tf": "cross-env NODE_ENV=development VUE_APP_APIENV=tf-tc-01 vite --mode e_dev_tf", "edev:prod": "cross-env NODE_ENV=development VUE_APP_APIENV=prod vite --mode e_dev_prod", "ebuild": "yarn ebuild:tf_green", "ebuild:tf_green": "cross-env BUILD_ENV=tf_green node ./build/ebuild.js", "ebuild:tf_sign": "cross-env BUILD_ENV=tf_sign node ./build/ebuild.js", "ebuild:tf_zip": "cross-env BUILD_ENV=tf_green ONLY_ZIP=true node ./build/ebuild.js", "ebuild:prod": "cross-env BUILD_ENV=prod node ./build/ebuild.js", "ebuild:prod_zip": "cross-env BUILD_ENV=prod ONLY_ZIP=true node ./build/ebuild.js", "report": "cross-env VUE_APP_APIENV=tf-tc-01 vite build --mode web_tf", "preview": "cross-env VUE_APP_APIENV=tf-tc-01 vite preview --mode web_tf", "u": "ncu && ncu -u && yarn install --registry=http://npm.yunxuetang.com.cn/repository/yxt-npm/", "fix:winlm": "chcp 65001"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@petrel/editor": "1.0.5", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "4.6.2", "@vue/reactivity": "^3.5.12", "@vue/shared": "^3.5.12", "archiver": "^6.0.1", "cross-env": "^7.0.3", "custom-protocol-check": "^1.4.0", "d3-svg-to-png": "^0.3.1", "datepicker-enhanced": "^2.2.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "electron": "32.2.1", "electron-builder": "23.6.0", "element-plus": "^2.9.10", "html2pdf.js": "^0.10.3", "fs": "^0.0.1-security", "livekit-client": "2.12.0", "marked": "15.0.12", "markmap": "^0.6.1", "markmap-common": "^0.17.1", "markmap-lib": "^0.17.2", "markmap-view": "^0.17.2", "mitt": "^3.0.1", "node-sass-install": "^1.0.2", "oss-webpack-plugin": "^0.4.51", "pinia": "^2.2.6", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.80.7", "typescript": "^5.6.3", "unplugin-auto-import": "^19.2.0", "unplugin-icons": "^0.20.1", "unplugin-vue-components": "^28.5.0", "vant": "^4.9.19", "video.js": "^8.21.0", "vite": "4.5.5", "vite-plugin-electron": "^0.28.8", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-html": "2.1.2", "vue": "^3.5.12", "vue-cal": "^4.10.0", "vue-inline-svg": "^3.1.4", "vue-router": "^4.4.5", "vue-tsc": "^2.1.10", "vue3-lottie": "^3.3.1", "vue3-sortablejs": "^1.0.7", "wavesurfer.js": "^7.9.4", "yxt-fe-center": "^0.3.3"}, "dependencies": {"avrecorder-canvas": "0.1.35", "avrecorder-recorder": "0.1.35", "axios": "^1.7.7", "electron-notarize": "^1.2.2", "electron-store": "8.2.0", "electron-updater": "^6.3.9", "extract-zip": "^2.0.1", "js-base64": "^3.7.7", "salesmate-ui": "file:../salesmate-ui", "sudo-prompt": "^9.2.1", "web-to-node-readablestream": "^3.0.3"}}