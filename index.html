<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="https://stc.yxt.com/ufd/989eac/favicon2.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>绚星销售助手</title>
  <script>this.globalThis || (this.globalThis = this)</script>
  <!-- 加载运行时配置 -->
  <%_ for (let item of feConfig.runtimeConfig) { if(/^http/.test(item)) { _%>
    <script src="<%= item %>"></script>
    <%_ } else { _%>
      <%- item %>
        <%_ }} _%>
</head>

<body>
  <div id="app" class="custom-scrollbar"></div>
  <script type="module" src="/src/main.js"></script>
  <script src="https://meetcdn.yxt.com/lingxi-static-resources/vconsole.min.js"></script>
  <script>
    setTimeout(() => {
      try {
        if (typeof g === 'undefined') return
        const { apiEnv, clientType } = g.config;
        if (apiEnv !== 'prod' && clientType == 'mobile') {
          new window.VConsole();
        }
      } catch (e) {
        console.log(e)
      }
    }, 2000)
  </script>
</body>

</html>