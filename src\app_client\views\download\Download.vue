<template>
  <div class="downlod_wrap">
    <div class="download_page flex-col">
      <div class="box1 flex-col">
        <div class="main1 flex-col">
          <div class="main2 flex-row">
            <img class="pic1" :src="getOssUrl('logo2.svg')" @click="toPage" />
          </div>
        </div>
        <div class="main3 flex-col">
          <div class="wrap1 flex-col">
            <span class="word1">下载绚星销售助手客户端</span>
            <span class="txt1">同时支持&nbsp;iOS，Android，macOS，Windows版本</span>
            <span class="txt12">支持Chrome 89+,&nbsp;Edge 89+ 浏览器</span>
            <div class="mod1 flex-row">
              <div class="section1 flex-col" @mouseover="mouseOverIos" @mouseleave="mouseLeaveIos">
                <div class="wrap2 flex-col" v-if="!show_ios_qr">
                  <div class="layer1 flex-col">
                    <div class="outer1 flex-row">
                      <div class="outer2 flex-col"></div>
                      <div class="outer3 flex-col">
                        <img class="icon1" :src="getOssUrl('dl_1.png')" />
                      </div>
                    </div>
                    <div class="outer4 flex-row">
                      <div class="mod2 flex-col"></div>
                    </div>
                  </div>
                  <span class="info1">iOS客户端</span>
                  <span class="info4">iOS&nbsp;11.0+</span>
                </div>
                <div class="wrap2 flex-col" v-else>
                  <img class="qr" :src="getOssUrl('dl_mobile.png')" />
                  <span class="info1">扫码下载</span>
                </div>
              </div>

              <div class="section2 flex-col" @mouseover="mouseOverAndroid" @mouseleave="mouseLeaveAndroid">
                <div class="group1 flex-col" v-if="!show_android_qr">
                  <div class="section3 flex-col">
                    <div class="wrap3 flex-col">
                      <img class="icon2" :src="getOssUrl('dl_3.png')" />
                    </div>
                    <div class="wrap4 flex-col">
                      <div class="group2 flex-col">
                        <div class="section4 flex-col"></div>
                      </div>
                    </div>
                  </div>
                  <span class="txt2">Android客户端</span>
                  <span class="info5">Android&nbsp;5+</span>
                </div>

                <div class="group1 flex-col" v-else>
                  <img class="qr" :src="getOssUrl('dl_mobile.png')" />
                  <span class="txt2">扫码下载</span>
                </div>
              </div>
              <ElTooltip content="点击下载Mac客户端" placement="top">
                <div class="section5 flex-col">
                  <div class="box2 flex-col">
                    <div class="box3 flex-col" @click="download('mac')">
                      <div class="mac_note">已支持M1/M2芯片</div>
                      <img class="pic2" :src="getOssUrl('dl_5.png')" />
                    </div>
                    <span class="info2">MacOS客户端</span>
                    <span class="txt3"> MacOS&nbsp;11+ </span>
                  </div>
                </div>
              </ElTooltip>
              <ElTooltip content="点击下载Windows客户端" placement="top">
                <div class="section6 flex-col">
                  <div class="mod3 flex-col">
                    <div class="layer2 flex-col pointer" @click="download('win')">
                      <div class="box4 flex-col">
                        <div class="mod5 flex-col"></div>
                      </div>
                    </div>
                    <span class="info3">Windows客户端</span>
                    <span class="word2">Windows&nbsp;10+</span>
                  </div>
                </div>
              </ElTooltip>
            </div>
          </div>
        </div>
        <div class="word4">
          江苏绚星智慧科技有限公司 2011-{{ year }}. All rights reserved.
        </div>
      </div>
      <div class="box5 flex-col">
        <img class="img1" :src="getOssUrl('dl_6_4.png')" />
      </div>
    </div>
  </div>
</template>
<script>
import { getOssUrl, jsOpenNewWindow, isPC } from "@/js/utils.js";
import { ElTooltip } from "element-plus";

export default {
  components: {
    ElTooltip,
  },
  data() {
    return {
      show_android_qr: false,
      show_ios_qr: false,
      year: new Date().getFullYear(),
    };
  },
  mounted() {
    if (!isPC()) {
      g.appStore.toMobileDownload()
    }
  },
  methods: {
    getOssUrl,
    toPage() {
      window.open(g.config.publicPath, "_self");
    },
    download(type) {
      const baseurl = g.config.downloadPath;
      let ymlurl = "";
      if (type == "win") {
        ymlurl = baseurl + "windows/latest.yml";
      } else {
        ymlurl = baseurl + "mac/latest-mac.yml";
      }
      fetch(ymlurl)
        .then((res) => {
          res
            .text()
            .then((text) => {
              // Parse the YAML content
              const lines = text.split("\n");
              let version = "";
              let filename = "";

              for (const line of lines) {
                if (line.startsWith("version:")) {
                  version = line.split(":")[1].trim();
                } else if (
                  line.includes("url:") &&
                  (line.includes(".exe") || line.includes(".dmg"))
                ) {
                  filename = line.split(":")[1].trim();
                }
              }

              if (version && filename) {
                // Construct the full download URL
                const downloadUrl = `${baseurl}${type === "win" ? "windows" : "mac"
                  }/${filename}`;
                jsOpenNewWindow(downloadUrl);
              } else {
                console.error("Failed to parse version or filename from yml");
              }
            })
            .catch((err) => {
              console.error("Failed to parse yml content:", err);
            });
        })
        .catch((err) => {
          console.error("Failed to fetch yml file:", err);
        });
    },
    mouseOverAndroid() {
      this.show_android_qr = true;
    },
    mouseLeaveAndroid() {
      this.show_android_qr = false;
    },
    mouseOverIos() {
      this.show_ios_qr = true;
    },
    mouseLeaveIos() {
      this.show_ios_qr = false;
    },
  },
};
</script>

<style scoped lang="scss">
@import url("./index.scss");
</style>
