<template>
    <TeamLayout @callback="onCallback">
        <div class="report_container">
            <div class="report_title report_title1">一、团队情况</div>
            <TeamBoardCore ref="refTeamBoardCore" :isReport="true" />
        </div>
        <div class="report_container">
            <div class="report_title report_title2">二、客户情况</div>
            <CustomerList />
        </div>
    </TeamLayout>
</template>

<script setup>
import TeamLayout from "@/app_client/components/TeamLayout.vue";
import TeamBoardCore from "@/app_client/components/TeamBoardCore/TeamBoardCore.vue";
import CustomerList from "./CustomerInfo/CustomerList.vue";
import { reportTypeMap } from "@/app_client/tools/utils.js"
import { getUrlParam } from "@/js/utils";

const refTeamBoardCore = ref(null);


const setTitle = () => {
    const startDate = getUrlParam('startDate');
    const endDate = getUrlParam('endDate');
    const route = useRoute();
    const periodType = route.params.periodType;
    g.clientBoardStore.reportDateTitle = `每${reportTypeMap[periodType]}队效能分析报告（${startDate} ～ ${endDate}）`
}
const onCallback = (action, data) => {
    if (action === "updateDept") {
        refTeamBoardCore.value.setDept(data);
    }
}

onMounted(() => {
    setTitle();
});

defineExpose({
    TeamLayout,
    refTeamBoardCore,
    CustomerList,
    TeamBoardCore,
});

</script>

<style lang="scss">
.report_container {
    .report_title {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0;
    }

    .report_title1 {
        margin: 0 0 20px 0;
    }

    .report_title2 {
        margin: 20px 0;
    }
}
</style>
