<template>
    <div class="download-container">
        <div class="logo">
            <img :src="getAssetUrl('logo2.svg')" alt="logo">
        </div>
        <div class="progress-info">
            <div class="progress-text">{{ processText }}</div>
            <div class="progress-bar" v-if="downloadStatus == 'downloading'">
                <div class="progress" :style="{ width: progress + '%' }"></div>
            </div>
        </div>
        <div class="actions flex-row" v-if="downloadStatus == 'downloaded'">
            <el-button type="default" @click="handleCancel">稍后提醒</el-button>
            <el-button type="primary" @click="handleRestart">重启更新</el-button>
        </div>
    </div>
</template>

<script setup>
import { getAssetUrl } from '@/js/utils'

const progress = ref(0)
const downloadStatus = ref('downloading')
const processText = ref('正在下载更新... 0%');

const handleCancel = () => {
    g.electronStore.closeWin('download')
}

const handleRestart = () => {
    g.ipcRenderer.send('update-install')
    g.electronStore.closeWin('download')
}

const handleUpdateStatus = ({ status, percent, version }) => {
    downloadStatus.value = status
    progress.value = percent
    if (status == 'downloaded') {
        processText.value = `新版本 ${version} 已准备就绪，重新启动应用后即可体验。`
    } else if (status == 'downloading') {
        processText.value = `正在下载更新... ${Math.floor(percent)}%`
    } else if (status == 'error') {
        processText.value = '下载更新失败'
    }
}


const addListener = () => {
    g.ipcRenderer.on('forward_message', (_, { action, data }) => {
        console.log('forward_message', action, data)
        if (action === 'update_status') {
            handleUpdateStatus(data)
        }
    })
    g.ipcRenderer.send('update-download');
}

onMounted(() => {
    addListener();
});

onBeforeUnmount(() => {
    g.ipcRenderer.removeAllListeners('forward_message');
})

defineExpose({
    handleCancel, getAssetUrl, processText
})
</script>

<style lang="scss">
.download-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);

    .logo {

        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 16px 0 40px 0;
        width: 196.8px;
        height: 32px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .progress-info {
        width: 80%;
        max-width: 400px;

        .progress-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #E8E8E8;
            border-radius: 2px;
            overflow: hidden;

            .progress {
                height: 100%;
                background: #2B6BFF;
                border-radius: 2px;
                transition: width 0.3s ease;
            }
        }
    }

    .actions {
        margin-top: 24px;
        width: 100%;
        justify-content: center;
    }
}

.mac-content {
    .download-container {
        height: calc(100vh - 38px);
    }
}

.win-content {
    .download-container {
        height: 100vh;
    }
}
</style>
