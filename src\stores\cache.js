import { defineStore } from 'pinia'
import { getAdminNavPermission, getOrgMymgtdprt, getOrgDprt, getPositionConfig, getSalesConfigure, getXMTopicDetail } from "@/js/api.js"
import { isAdmin, cacheApiFn, extractDeptIdNames, mergeMenuNew, convertTreeName, setStore, getStore } from "@/js/utils.js"

export default defineStore('cache', {
    state: () => ({
        cache: {
        },
        loading: {},
        sale_labels: {},
        userMenu: {},// admin,manage,client
        deptIdNames: {},
        accessCache: {},
        salesMethodology: [
            { value: 'BANT', label: 'BANT', description: '预算、决策、需求、时间线' },
            { value: 'MEDDIC', label: 'MEDDIC', description: '衡量标准、经济决策、决策标准、决策流程、痛点、教练' },
            { value: 'SPICED', label: 'SPICED', description: '现状、痛点、影响、关键事件、决策' },
            { value: 'NEAT', label: 'NEAT', description: '核心需求、经济影响、接触决策者、有时间限制的' },
        ]
    }),
    getters: {
        isAdmin(state) {
            return isAdmin(state.menuPermission);
        },
    },
    actions: {
        getSalesConfigure() {
            return new Promise((resolve, reject) => {
                if (JSON.stringify(this.sale_labels) != "{}") {
                    resolve(this.sale_labels)
                } else {
                    getSalesConfigure().then(resp => {
                        if (resp.code == 0) {
                            this.sale_labels = resp.data;
                            resolve(resp.data)
                        } else {
                            resolve({})
                        }
                    }).catch(() => {
                        resolve({})
                    })
                }
            })
        },
        getUserMenu(type) {
            return new Promise((resolve) => {
                this._getUserMenu().then(resp => {
                    resolve(toRaw(this.userMenu[type]))
                }).catch(() => {
                    resolve([])
                })
            })
        },
        _getUserMenu() {
            return new Promise((resolve) => {
                const param = {
                    // "type": 2, //1:学员端，2：管理端，不传，所有
                    "productCode": "nova_guide",
                }
                const key = 'menuPermission'
                const fn = () => getAdminNavPermission(param)
                const handleCacheFn = (resp) => {
                    if (resp.code == 0 && resp.data.datas) {
                        const permissions = resp.data.datas //.filter(x => x.showed == 1);
                        this.cache[key] = permissions
                        this.userMenu['admin'] = mergeMenuNew(permissions, 'admin')
                        this.userMenu['client'] = mergeMenuNew(permissions, 'client')
                        this.userMenu['electron'] = mergeMenuNew(permissions, 'electron')
                        g.appStore.setStore(g.cv.keyMenuPermission, permissions)
                        return permissions
                    } else {
                        return []
                    }
                }
                cacheApiFn(fn, handleCacheFn, this.cache, this.loading, key).then(resolve)
            })
        },
        getDeptTreeData() {
            return new Promise((resolve, reject) => {
                const key = 'myDeptTreeData'
                if (this.cache[key] && this.cache[key]?.length > 0) {
                    resolve(toRaw(this.cache[key]))
                    return
                }
                const fn = () => getOrgMymgtdprt()
                const handleCacheFn = (resp) => {
                    if (resp.code == 0) {
                        const data = convertTreeName([resp.data])
                        this.cache[key] = data
                        this.deptIdNames = extractDeptIdNames(data);
                        return data
                    } else {
                        return []
                    }
                }
                cacheApiFn(fn, handleCacheFn, this.cache, this.loading, key).then(resolve)
            })
        },
        getDeptTreeDataAll() {
            return new Promise((resolve, reject) => {
                const key = 'myDeptTreeDataAll'
                const fn = () => getOrgDprt()
                const handleCacheFn = (resp) => {
                    if (resp.code == 0) {
                        const data = convertTreeName([resp.data])
                        this.cache[key] = data
                        this.deptIdNames = extractDeptIdNames(data);
                        return data
                    } else {
                        return []
                    }
                }
                cacheApiFn(fn, handleCacheFn, this.cache, this.loading, key).then(resolve)
            })
        },
        getPositionConfig() {
            return new Promise((resolve, reject) => {
                const key = 'positionConfig'
                const fn = () => getPositionConfig()
                const handleCacheFn = (resp) => {
                    if (resp.code == 0) {
                        this.cache[key] = resp.data
                        return resp.data
                    } else {
                        return {}
                    }
                }
                cacheApiFn(fn, handleCacheFn, this.cache, this.loading, key).then(resolve)
            })
        },
        getXMTopicDetail(topicId) {
            return new Promise((resolve) => {
                if (topicId) {
                    getXMTopicDetail(topicId).then((resp) => {
                        if (resp.code == 0) {
                            resolve(resp.data.categoryTemplates)
                        } else {
                            resolve([])
                        }
                    }).catch(e => {
                        console.log('error getXMTopicDetail', e)
                        resolve([])
                    })
                } else {
                    resolve([])
                }
            })
        },
        checkPermission(code) {
            //检查是否某个菜单权限
            if (this.accessCache[code] !== undefined) {
                return this.accessCache[code]
            }
            const list = this.cache['menuPermission']
            const hasAccess = list.filter(x => x.code == code && x.showed == 1).length > 0
            this.accessCache[code] = hasAccess
            return hasAccess
        },
        checkPointActionByCode(code, action) {
            // {
            // 	"code":"case_library",
            // 	"functionCode":"case_library",
            // 	"pointActions":[
            // 		"case_manage_opr",
            // 		"folder_manage_opr",
            // 		"script_extraction_opr",
            // 		"sub_folder_manage_opr"
            // 	],
            // 	"showed":1,
            // },
            const list = this.cache['menuPermission']
            const hasAccess = list.filter(x => x.code == code && x.showed == 1 && x.pointActions.includes(action)).length > 0
            return hasAccess
        }
    }
})

