<template>
  <TeamLayout @callback="onCallback" :isShowTimeRange="true">
    <TeamBoardCore ref="refTeamBoardCore" :isReport="false" />
  </TeamLayout>
</template>

<script setup>
import TeamBoardCore from "@/app_client/components/TeamBoardCore/TeamBoardCore.vue";
import TeamLayout from "@/app_client/components/TeamLayout.vue";

const refTeamBoardCore = ref();

const onCallback = (action, data) => {
  if (action === "updateDept") {
    refTeamBoardCore.value.setDept(data);

  }
};
defineExpose({
  TeamLayout,
  TeamBoardCore,
});
</script>

<style lang="scss"></style>
