<template>
    <div class="role_tag_wrap">
        <div :class="`dot d_${getUserClass}`"></div>
        <div :class="`tag t_${getUserClass}`">{{ getUserRole }}</div>
    </div>
</template>

<script>
export default {
    props: ['roleType'],
    computed: {
        getUserRole: function () {
            const role = this.roleType;
            if (role == "customer") {
                return "客户"
            } else if (role == "internal") {
                return "伙伴"
            } else {
                return "未知"
            }
        },
        getUserClass: function () {
            const role = this.roleType;
            if (role == "customer") {
                return "customer"
            } else if (role == "internal") {
                return "internal"
            } else {
                return "na"
            }
        },
    }
}
</script>

<style lang="scss">
.role_tag_wrap {
    display: flex;
    flex-direction: row;

    .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin: 9px 4px 0 0;
    }

    .tag {
        margin: 0 8px 0 0;
    }

}
</style>