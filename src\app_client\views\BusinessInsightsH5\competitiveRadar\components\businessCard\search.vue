<template>
    <van-popup v-model:show="isSelect" round position="bottom" :close-on-click-overlay="false" teleport="body">
        <div class="action-sheet-button">
            <span class="action-sheet-button-p">选择客户</span>
            <span class="action-sheet-button-s" @click="onCancel">
                <van-icon name="cross" @click="onCancel" size="16" />
            </span>
        </div>
        <div class="action-sheet-main-box">
            <van-search v-model="searchValue" show-action placeholder="搜索" @search="onSearch" @clear="onClear">
                <template #action>
                    <div @click="onSearch">搜索</div>
                </template>
            </van-search>
            <ul class="action-sheet-main-box-list">
                <van-checkbox-group v-model="checked" ref="checkboxGroup" icon-size="16px">
                    <van-checkbox v-for="item in customerList" :key="item.customerId" :name="item.customerId">{{
                        item.customerName
                    }}</van-checkbox>

                </van-checkbox-group>
            </ul>
            <div class="action-sheet-bottom">
                <van-checkbox v-model="isCheckAll" @change="checkAll" icon-size="16px">
                    全选
                </van-checkbox>


                <van-button type="primary" block @click="onConfirm">
                    确认
                </van-button>
            </div>
        </div>
    </van-popup>
</template>

<script setup>
import { watch, ref, computed } from 'vue'
import { debounce } from "@/js/utils.js";

const emit = defineEmits(['callback', 'close'])
const isSelect = ref(false)
const searchValue = ref('')
const checked = ref([])
const checkboxGroup = ref(null);
const isCheckAll = ref(false);
const customerList = computed(() => g.clientBiStore.crCustomerList || [])

const onSearch = async () => {
    await g.clientBiStore.getCrCustomerList(searchValue.value)
}

// 创建防抖版本的搜索函数
const debouncedSearch = debounce(onSearch, 500);

// 监听搜索值变化，触发防抖搜索
watch(searchValue, () => {
    debouncedSearch();
});

const checkAll = () => {
    checkboxGroup.value.toggleAll(true);
}

const checkAllChange = (val) => {
    val && checkAll()
}
// 选中
const toggle = item => {
    checked.value = item.value
}
// 取消
const onCancel = () => isSelect.value = false

const onConfirm = () => {
    isSelect.value = false
    const checkItems = customerList.value.filter(item => checked.value.includes(item.customerId))
    emit('callback', checkItems)
}

const onClear = () => {
    searchValue.value = ''
    onSearch()
}

const init = () => {
    isSelect.value = true
    onSearch()
}

defineExpose({
    init
})
</script>
<style lang="scss" scoped>
.action-sheet-main-box {
    height: 80vh;
    overflow: auto;
}

.action-sheet-button {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    line-height: 40px;

    .action-sheet-button-p {
        width: 100%;
        font-weight: 700;
        font-size: 16px;
        text-align: center;
    }

    .action-sheet-button-c {
        padding: 0 8px;
        color: #aaa;
    }

    .action-sheet-button-s {
        padding: 0 10px;
        position: absolute;
        top: 0;
        right: 12px;
    }
}

.action-sheet-main-box-list {
    height: calc(80vh - 120px);
    padding: 12px;
    box-sizing: border-box;
    overflow: auto;

    .van-checkbox {
        padding: 12px 0;
        font-size: 12px;
    }
}


:deep(.van-cell__title) {
    padding-left: 12px;
}

.action-sheet-bottom {
    .van-checkbox {
        padding: 12px 0;
        font-size: 14px;
    }

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 20px 16px;

    font-size: 16px;
    color: #262626;

    button {
        width: 64px;
    }
}
</style>
