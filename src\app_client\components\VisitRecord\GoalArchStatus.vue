<template>
  <div class="visit_filter_item">
    <el-select v-model="localValue" collapse-tags placeholder="目标达成" popper-class="vf-header" style="width: 150px">
      <el-option v-for="item in options" :key="item.type" :label="item.description" :value="item.type" />
    </el-select>
  </div>
</template>

<script setup>
const props = defineProps({
  value: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["update:value", "reload"]);
const localValue = ref(props.value);
const options = ref([]);

watch(localValue, (newValue) => {
  emit("update:value", toRaw(newValue));
  emit("reload", "");
});

const updateValue = (v) => {
  localValue.value = v;
};

const query = () => {
  options.value = [
    { type: "-1", description: "全部" },
    { type: "达成", description: "达成" },
    { type: "未达成", description: "未达成" },
    { type: "无法判定", description: "无法判定" },
    { type: "结果未评估", description: "结果未评估" },
  ];
}

onMounted(() => {
  query();
})

defineExpose({
  options,
  updateValue,
})
</script>

<style lang="scss">
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
</style>