<template>
    <div class="custom-popover-trigger" ref="triggerRef">
        <slot name="reference"></slot>
    </div>
    <teleport to="body">
        <div v-show="modelValue" class="custom-popover" :style="popoverStyle">
            <slot></slot>
        </div>
    </teleport>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
    modelValue: Boolean,
    width: {
        type: Number,
        default: 300
    },
    placement: {
        type: String,
        default: 'bottom'
    }
})

const emit = defineEmits(['update:modelValue'])

const triggerRef = ref(null)
const popoverStyle = ref({
    position: 'fixed',
    width: `${props.width}px`,
    zIndex: 2000
})

const closeModal = () => {
    emit('update:modelValue', false)
}

const updatePosition = () => {
    if (!triggerRef.value) return

    const triggerRect = triggerRef.value.getBoundingClientRect()

    if (props.placement === 'bottom') {
        popoverStyle.value = {
            ...popoverStyle.value,
            top: `${triggerRect.bottom + 8}px`,
            left: `${triggerRect.left - 220}px`
        }
    }
}


// 监听窗口大小变化，更新位置
const handleResize = () => {
    if (props.modelValue) {
        updatePosition()
    }
}

onMounted(() => {
    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleResize)
    g.emitter.on('close_popover', closeModal)
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', handleResize)
})

// 当显示状态改变时更新位置
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        nextTick(() => {
            updatePosition()
        })
    }
})
</script>

<style lang="scss" scoped>
.custom-popover {
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    padding: 12px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.custom-popover-trigger {
    display: inline-block;
}
</style>