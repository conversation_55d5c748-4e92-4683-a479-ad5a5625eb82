<template>
  <div class="speak_time_wrap" v-if="list && list.length > 0">
    <div class="st_person flex-column" v-for="(row, idx) in list.slice(0, isShowAll ? list.length : 1)" :key="idx">
      <div class="st_header flex-row" @click="onClick(row)">
        <div class="st_name">
          {{ row.name }}
        </div>
        <div class="st_txt">
          {{ row.descrtion || "" }}
        </div>
        <div class="flex_glow"></div>
        <div class="st_txt">{{ row.percent }}%</div>
      </div>
      <div class="st_times">
        <div class="markers markers_tag">
          <div v-for="mark in row.markers" :key="mark.markTime" :class="`mark ${getMarkStyle(row.type)}`"
            :style="getMarkStyleAlways(mark)"></div>
        </div>
      </div>
    </div>
    <div class="showAllBtn" @click="onChangeShow" v-show="list.length > 1">
      {{ isShowAll ? "收起" : `展开全部发言人(${list.length})` }}
    </div>
  </div>
</template>

<script>
import { getSpeakTimeList, getClipSpeakTime } from "./st_tools";

export default {
  components: {},
  data() {
    return {
      isShowAll: false,
      list: [],
    };
  },
  methods: {
    onClick(row) {
      g.emitter.emit("clickTag", [row.name, "username"]);
    },
    onAnalyse() {
      this.$emit("callback", "analyse");
    },
    getMarkStyleAlways(mark) {
      let rate = (100 * mark.markTime) / (this.item.duration / 1000);
      rate = Math.min(Math.max(rate, 0), 100);

      // 计算说话时长的宽度
      let width = rate > 0 ? 1 : 0;
      if (mark.et && mark.bt) {
        const duration = mark.et - mark.bt;
        width = (100 * duration) / this.item.duration;

        width = Math.min(Math.max(width, 0.5), 100); // 最小宽度为0.5%，最大100%
      }
      return {
        left: rate + "%",
        width: width + "%"
      };
    },
    getMarkStyle(type) {
      if (["customer", "internal"].includes(type)) {
        return "d_" + type;
      } else {
        return "d_na";
      }
    },
    onChangeShow() {
      this.isShowAll = !this.isShowAll;
    },
    init(type = "", data) {
      if (type == "clip") {
        const { item, list } = getClipSpeakTime(data);
        this.item = item;
        this.list = list;
      } else {
        this.item = g.postmeetStore.data.playItem;
        this.list = getSpeakTimeList();
      }
      if (!this.list) {
        console.log('no speak time list')
        return
      }
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].detail?.formData?.fieldValues) {
          const fieldValues = this.list[i].detail.formData.fieldValues;
          let descrtion = ''
          for (let j = 0; j < fieldValues.length; j++) {
            const fieldValue = fieldValues[j];
            if (fieldValue.fieldValue && fieldValue.fieldValue !== 'undefined') {
              descrtion += (descrtion ? ' | ' : '') + fieldValue.fieldName + '：' + fieldValue.fieldValue;
            }
          }
          this.list[i].descrtion = descrtion
        } else {
          const position = this.list[i].detail?.position || ''
          this.list[i].descrtion = position ? `岗位：${position}` : ''
        }
      }
    },
  },
};
</script>

<style lang="scss">
.speak_time_wrap {
  .st_person {
    margin: 12px 0;

    .st_header {
      margin: 16px 0;
      cursor: pointer;

      .st_name {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #262626;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      .st_txt {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-left: 12px;
      }

      .flex_glow {
        flex-grow: 1;
      }
    }

    .st_times {
      .markers {
        height: 10.6px;
        display: flex;
        position: relative;
        pointer-events: none;
        margin: 0 1.5%;
        border-radius: 2px;
        height: 4px;
        background-color: #eee;

        .mark {
          height: 4px;
          position: absolute;
          background-color: currentColor;
          border-radius: 2px;
          transition: all 0.3s ease;

          &.d_customer {
            color: #436BFF;
          }

          &.d_internal {
            color: #00B42A;
          }

          &.d_na {
            color: #86909C;
          }
        }
      }
    }
  }

  .ai_icon {
    margin: 4px 6px 0 5px;
  }

  .showAllBtn {
    font-size: 12px;
    color: #436bff;
    cursor: pointer;
  }
}
</style>
