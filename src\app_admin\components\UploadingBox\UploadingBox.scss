.uploading_wrap {
    padding: 16px;
    position: fixed;
    right: 14px;
    bottom: 1px;
    width: 480px;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    border: 1px solid #E9E9E9;
    z-index: 9;


    .win_title {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        height: 20px;
        display: flex;

        .left {
            color: #262626;
        }


        .right {
            .icon {
                width: 20px;
                height: 20px;
                cursor: pointer;
                margin: 0 4px;
            }

            .icon:hover {
                background-color: #F7F9FA;
            }
        }
    }

    .files_box {
        display: flex;
        flex-direction: column;
        margin: 8px 0;
        width: 448px;
        height: 61px;
        border-radius: 4px;
        padding-top: 8px;

        .file_up {
            display: flex;
            flex-direction: row;
            align-items: center;

            .file_icon {
                width: 32px;
                height: 32px;
                margin: 0 7px;

            }

            .up_mid {
                display: flex;
                flex-direction: column;

                .title {
                    color: #262626;
                }

                .size {
                    color: #8C8C8C;
                }
            }

            .up_right {
                padding-right: 12px;
                display: none;
            }
        }

        .file_bottom {
            margin: 2px;
            display: flex;
            align-items: center;

            .process_box {
                height: 4px;
                width: 366px;
                background-color: #bbb;

                .process {
                    background-color: blue;
                    height: 100%;
                }
            }

            .note {
                padding-left: 11px;
            }
        }
    }

    .files_box:hover {
        background: #F7F9FA;

        .file_up {
            .up_right {
                display: block;
                cursor: pointer;
            }
        }


    }
}