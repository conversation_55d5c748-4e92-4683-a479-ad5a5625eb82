<template>
  <div>
    <div class="msg_my msg_line">
      <div class="mbody" v-html="md2html(props.data.message)"></div>
    </div>
  </div>
</template>

<script setup>
import config from "@/js/config.js";
import { md2html } from "@/js/md.js";
import { getOssUrl } from '@/js/utils';

const { photo } = g.appStore.user;
const props = defineProps(["data"]);
const myphoto = photo || getOssUrl('my.jpg')

defineExpose({ props, myphoto, config });
</script>

<style lang="scss">
.msg_my {
  display: flex;
  justify-content: flex-end;

  .mbody {
    border-radius: 12px 0 12px 12px;
    background-color: #bdd2ff;
    max-width: 862px !important;
    width: auto !important;
    padding: 0 12px;
    margin-bottom: 16px;
  }
}
</style>
