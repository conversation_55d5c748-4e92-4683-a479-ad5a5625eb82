<template>
  <div class="dims_comm_wrap">
    <div class="rd_sub_note">销售在访前准备时可查看沟通模板，让每次沟通都精准高效</div>
    <div class="upload_wrap">
      <div class="rf_file" v-if="currData.commonTemplate?.templateName">
        <div class="fname" @click="onPreview">
          {{ currData.commonTemplate?.templateName }}
        </div>
        <div class="fbtn" @click="onUpload">替换</div>
        <div class="fbtn" @click="onDelete">删除</div>
      </div>

      <div class="rf_upload" v-else>
        <div class="upload_btn" @click="onUpload">
          <div class="icon">
            <IconUpload />
          </div>
          <div class="txt">上传模板</div>
        </div>
        <div class="update_note">支持doc、docx、ppt、pptx、pdf、xlsx、xls格式</div>
      </div>
    </div>

    <div class="upload_wrap" v-show="isShowGoodsRelated">
      <div class="rl_head">
        <el-switch
          v-model="currData.salesRelated"
          @change="onRelatedChange"
          active-text="关联商品售卖"
        />
        <div class="hint">（拜访中，可根据售卖商品类型，为销售显示对应沟通模板）</div>
      </div>
      <FileTable ref="refTable" @callback="cbTable"> </FileTable>
    </div>
    <UploadModal ref="refUpload" />
  </div>
</template>

<script setup>
import IconUpload from "@/app_admin/icons/upload.vue";
import FileTable from "./fileTable.vue";
import { setXmTopicRelated, deleteXmFile } from "@/app_admin/tools/api.js";
import UploadModal from "@/app_admin/components/UploadModal.vue";

const emit = defineEmits(["callback"]);
const showtype = ref("no");
const currData = ref({ salesRelated: false, commonTemplate: { templateName: "" } });
const refTable = ref();
const refUpload = ref();
const isShowGoodsRelated = ref(false);

const cbTable = (action, data) => {
  if (action == "upload") {
    g.adminFileStore.get_config("communication").then((config) => {
      config.param = data;
      refUpload.value.show(config, currData.value.id);
    });
  } else if (action == "reload") {
    g.saleStore.getTopDetail().then((resp) => {
      currData.value = toRaw(g.saleStore.topicDetail);
    });
  }
};

const setShowType = () => {
  const { salesRelated, commonTemplate } = currData.value;
  if (salesRelated) {
    showtype.value = "table";
  } else {
    if (!!commonTemplate && commonTemplate.templateName) {
      showtype.value = "one";
    } else {
      showtype.value = "no";
    }
  }
};

const onUpload = () => {
  const data = { categoryId: 0, topicId: currData.value.id };
  cbTable("upload", data);
};

const onDelete = () => {
  const { commonTemplate } = currData.value;
  function _delete() {
    const topicId = currData.value.id;
    const templateId = commonTemplate.templateId;
    deleteXmFile(topicId, 0, templateId).then(() => {
      emit("callback", "reload");
    });
  }
  const delete_hint = `您确定要删除 【${commonTemplate.templateName}】 吗？删除后不可恢复`;
  ElMessageBox.confirm(delete_hint, "删除提示", {
    confirmButtonText: "确认删除",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      _delete();
    })
    .catch(() => {});
};

const onRelatedChange = () => {
  const related = currData.value.salesRelated ? 1 : 0;
  const { id } = currData.value;
  setXmTopicRelated(id, related).then((resp) => {
    if (resp.code == 0) {
      setShowType();
    } else if (resp.code == 1808) {
      ElMessage.error(`开启失败，暂无可售卖商品分类，请前往商品管理维护`);
    } else {
      ElMessage.error(`操作失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
    }
  });
};

const onPreview = () => {
  const { templateDownloadUrl, templateName } = currData.value.commonTemplate;
  const ext = templateName.split(".").pop();
  const url = templateDownloadUrl;
  g.emitter.emit("app_preview_file", { ext, url });
};

const init = () => {
  currData.value = toRaw(g.saleStore.topicDetail);
  setShowType();
  refTable.value.init();
  isShowGoodsRelated.value = g.appStore.getFuncStatus("sales_product_in_strategy");
};

defineExpose({
  init,
  cbTable,
  onRelatedChange,
  onUpload,
  onPreview,
  FileTable,
  isShowGoodsRelated,
  IconUpload,
  showtype,
  UploadModal,
  refTable,
  refUpload,
});
</script>

<style lang="scss">
.dims_comm_wrap {
  padding: 0 24px;
  height: calc(100vh - 130px);
  overflow-y: auto;

  .rd_sub_note {
    height: 26px;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 26px;
    margin-top: 24px;
  }

  .upload_wrap {
    padding: 16px 24px 24px 24px;
    margin: 24px 0;
    background: #fafafa;

    .rl_head {
      display: flex;
      flex-direction: row;
      margin-bottom: 12px;

      .hint {
        font-size: 14px;
        color: #8c8c8c;
        line-height: 32px;
      }
    }

    .rf_upload {
      display: flex;
      flex-direction: column;

      .table_wrap {
        padding: 0;
      }

      .upload_btn {
        background: rgba(255, 255, 255, 0.01);
        border-radius: 4px;
        border: 1px solid #436bff;
        width: 116px;
        height: 32px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .icon {
          padding-top: 2px;
          margin-right: 5px;
        }

        .txt {
          color: #436bff;
        }
      }

      .update_note {
        height: 26px;
        font-size: 14px;
        color: #8c8c8c;
        line-height: 26px;
        margin-top: 5px;
        width: 100%;
      }
    }

    .rf_file {
      display: flex;
      flex-direction: row;
      margin: 10px 0;

      .fname {
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        cursor: pointer;
        margin-right: 16px;
      }

      .fname:hover {
        color: #436bff;
      }

      .fbtn {
        font-size: 14px;
        color: #436bff;
        cursor: pointer;
        line-height: 22px;
        margin: 0 6px;
      }
    }
  }
}
</style>
