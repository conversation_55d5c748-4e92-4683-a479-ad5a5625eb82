<template>
    <el-dialog v-model="visible" :title="title" width="316px" :close-on-click-modal="false" :show-close="false">
        <div>{{ hint }}</div>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleEndMeet" :loading="loading">
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    hint: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)
const loading = ref(false)

watch(() => props.modelValue, (newVal) => {
    visible.value = newVal
})

watch(() => visible.value, (newVal) => {
    emit('update:modelValue', newVal)
})

const handleEndMeet = async () => {
    visible.value = false
}
</script>