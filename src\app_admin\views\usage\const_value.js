export const timeTypes = [
    { value: 'ACCUMULATE', label: '累计' },
    { value: 'WEEK', label: '按周' },
    { value: 'MONTH', label: '按月' },
    { value: 'QUARTER', label: '按季' }
]

// export const apps = ['产业信息挖掘机', '企业信息侦察机', '智能商品推荐师'];

export const getAppOptions = (apps) => {
    return [{ value: '', label: '所有类型' }, ...apps.map(x => { return { value: x, label: x } })]
}

export const timeTypeRangeMap = {
    ACCUMULATE: 'date',
    WEEK: 'week',
    MONTH: 'month',
    QUARTER: 'quarter'
}
export const getTableColumns = (param) => {
    const mapDict = {
        'APPLICATION': ["appName", "time", "deptName", "accessCount", "headerCount"],
        'USER': ["userName", "userAccount", "deptName", "position", "time", "appName", "accessCount"]
    }
    let columns = mapDict[param.reportType]
    if (param.timeType == 'ACCUMULATE') {
        columns = columns.filter(x => x != 'time')
    }
    return columns
}

export const getSpanColumns = (param) => {
    const mapDict = {
        'APPLICATION': ["appName", "time"],
        'USER': ["userName", "time", "userAccount", "deptName", "position"]
    }
    let columns = mapDict[param.reportType]
    if (param.timeType == 'ACCUMULATE') {
        columns = columns.filter(x => x != 'time')
    }
    return columns
}

export const getSpanKeyColumn = (param) => {
    return param.reportType == 'APPLICATION' ? 'appName' : 'userAccount'
}