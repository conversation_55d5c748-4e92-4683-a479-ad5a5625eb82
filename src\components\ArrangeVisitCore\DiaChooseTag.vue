<template>
  <el-dialog v-model="dialogVisible" title="选择商品大类" width="500" :before-close="handleClose">
    <div class="bve_main">
      <SalesRelatedComponent ref="refSalesRelated" v-model="param" @callback="cbSalesRelated" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import SalesRelatedComponent from "@/components/SalesRelated/SalesRelatedComponent.vue"
const emit = defineEmits(["callback"]);
const dialogVisible = ref(false);
const selectedTag = ref(null);
const param = ref({});
const refSalesRelated = ref(null)
let saleGoodsHintList = []

const show = ({ salesRelatedType, salesGoodsCategories, currItem, tagList, selectedTags }) => {
  param.value = { salesRelatedType, salesGoodsCategories, currItem }
  dialogVisible.value = true;
  nextTick(() => {
    refSalesRelated.value.setCurrItem(currItem);
    refSalesRelated.value.setTags(tagList);
    refSalesRelated.value.setModelValue(toRaw(param.value));
    refSalesRelated.value.setSelectedTags(selectedTags);
  })
}

const cbSalesRelated = (action, data) => {
  if (action == 'saleGoodsHintList') {
    saleGoodsHintList = toRaw(data)
  }
}

const handleClose = () => {
  dialogVisible.value = false;
};

const onConfirm = () => {
  let data = { type: param.value.salesRelatedType, list: [] }
  const categories = param.value.salesGoodsCategories.split(',').filter(x => !!x)
  if (data.type == 1) {
    data.list = categories.map(x => ({ id: x, label: x }))
  } else if (saleGoodsHintList.length > 0) {
    const ids = categories
    for (let i = 0; i < saleGoodsHintList.length; i++) {
      data.list.push({ id: ids[i], label: saleGoodsHintList[i] })
    }
  }
  emit('callback', 'choosed', data);
  handleClose();
}

defineExpose({
  handleClose, dialogVisible, onConfirm, show, param, selectedTag, SalesRelatedComponent, refSalesRelated
});
</script>

<style lang="scss">
.bve_main {
  max-height: 400px;
  overflow-y: auto;

  .fixed-text {
    margin-bottom: 20px;
    font-size: 16px;
    color: #333;
  }

  .bve_item {
    padding: 20px;
    align-items: center;
    border-bottom: 1px solid #F0F0F0;

    &:last-child {
      border-bottom: none;
    }

    .bve_icon {
      width: 40px;
      height: 40px;
      margin-right: 16px;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .bve_txt {
      flex: 1;

      .bve_name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
      }

      .bve_desc {
        font-size: 14px;
        color: #999999;
        line-height: 20px;
      }
    }

    .bve_check {
      width: 40px;
    }
  }
}
</style>
