<template>
    <div class="av_item_value tag-picker">
        <el-select placeholder="选择主题标签" v-model="localParam.salesMateTags" @change="handleChange">
            <el-option v-for="item in tagList.data.customerTopics" :key="item.id" :label="item.label"
                :value="item.label" />
        </el-select>
        <div class="av_item_value_goods" @click="openDiaChooseTag"
            v-if="localParam.salesMateTags && currItem.salesRelated">
            关联商品售卖
        </div>
        <DiaChooseTag ref="refDiaChooseTag" @callback="cbDiaChooseTag" />
        <div class="av_item_value_tags" v-if="pageShownTags">
            <el-tag v-for="tag in pageShownTags" :key="tag.id" closable @close="handleClose(tag.id)">
                {{ tag.label || tag._label }}
            </el-tag>
        </div>
    </div>
</template>

<script setup>
import DiaChooseTag from './DiaChooseTag.vue'
import { getGoodsDetails } from '@/js/api'
import { getSalesTagsConverted, mergeColumnsAndData, calcCategoryIdNameMap } from '@/js/utils'

const tagList = ref({ data: { customerTopics: [] } })
const pageShownTags = ref([])
const refDiaChooseTag = ref(null)
//  {customer: true, id: '1839573664631099392', label: '方案沟通', salesRelated: true}
const currItem = ref({ customer: true, salesRelated: false, id: '', label: '' })
const cateIdNameMap = ref({})

const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
        default: {
            salesRelatedType: 1,
            salesGoodsCategories: '',
            salesMateTags: ''
        }
    }
})
const localParam = ref(props.modelValue)

const emit = defineEmits(['update:modelValue', 'change'])

const handleChange = (val) => {
    localParam.value.salesMateTags = val
    currItem.value = tagList.value.data['customerTopics'].find(x => x.label == val) || {};
    updateModelValue()
}

const cbDiaChooseTag = (action, data) => {
    if (action == 'choosed') {
        pageShownTags.value = data.list
        localParam.value.salesRelatedType = data.type
        localParam.value.salesGoodsCategories = data.list.map(x => x.id).join(',')
        updateModelValue()
    }
}

const updateModelValue = () => {
    emit('update:modelValue', localParam.value)
    emit('change', localParam.value)
}

const openDiaChooseTag = () => {
    const { salesRelatedType, salesGoodsCategories } = localParam.value;
    const param = {
        salesRelatedType,
        salesGoodsCategories,
        currItem: toRaw(currItem.value),
        tagList: toRaw(tagList.value),
        selectedTags: toRaw(pageShownTags.value)
    }
    if (g.config.isElectron) {
        g.electronStore.openWin('choose_tag', param)
    } else {
        refDiaChooseTag.value.show(param)
    }
}

const handleClose = (id) => {
    pageShownTags.value = pageShownTags.value.filter(x => x.id != id)
    localParam.value.salesGoodsCategories = pageShownTags.value.map(x => x.id).join(',')
    updateModelValue()
}

// 修改 getCategorys 方法，使用 topicId.value
const _getCategorys = () => {
    return new Promise(async (resolve) => {
        handleChange(localParam.value.salesMateTags)
        const templates = await g.cacheStore.getXMTopicDetail(currItem.value.id)
        if (templates.length > 0) {
            cateIdNameMap.value = calcCategoryIdNameMap(templates)
            resolve(true)
        } else {
            resolve(false)
        }
    })
}

const _initEditSalesCategorys = async (ids) => {
    return new Promise(async (resolve) => {
        const resp = await getGoodsDetails(ids)
        if (resp.code == 0) {
            const { datas, header } = resp.data
            if (datas && datas.length > 0) {
                pageShownTags.value = mergeColumnsAndData(header, toRaw(cateIdNameMap.value), datas)
            } else {
                pageShownTags.value = []
            }
        } else {
        }
        resolve(true)
    })
}

const _initEditSalesGoods = async (categories) => {
    return new Promise(async (resolve) => {
        pageShownTags.value = categories.map(x => ({ id: x, label: x }))
        resolve(true)
    })
}

const init = async (modelValue) => {
    localParam.value = modelValue
    pageShownTags.value = []
    const categories = localParam.value.salesGoodsCategories.split(',').filter(x => !!x)
    tagList.value = getSalesTagsConverted(await g.cacheStore.getSalesConfigure())
    if (tagList.value.data.customerTopics && tagList.value.data.customerTopics.length === 1) {
        localParam.value.salesMateTags = tagList.value.data.customerTopics[0].label
    }
    const status = await _getCategorys()
    if (status) {
        if (localParam.value.salesRelatedType == 2) {
            await _initEditSalesCategorys(categories)
        } else if (localParam.value.salesRelatedType == 1) {
            await _initEditSalesGoods(categories)
        } else {
        }
    }
}


onMounted(() => {
    if (g.config.isElectron) {
        g.ipcRenderer.on('forward_message', (_, { action, data }) => {
            if (action === 'choosed' && data.list) {
                pageShownTags.value = data.list
                localParam.value.salesRelatedType = data.type
                localParam.value.salesGoodsCategories = data.list.map(x => x.id).join(',')
                updateModelValue()
            }
        })
    }
})

defineExpose({
    localParam, refDiaChooseTag, init
})
</script>

<style lang="scss" scoped>
.tag-picker {
    width: 100%;
    align-items: flex-start;

    .av_item_value_goods {
        margin-top: 10px;
        font-size: 12px;
        color: #436BFF;
        cursor: pointer;
    }

    .av_item_value_tags {
        margin-top: 10px;

        .el-tag {
            margin: 0 5px 5px 0;
            max-width: 250px;

            :deep(.el-tag__content) {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: calc(100% - 20px); // 预留关闭按钮的空间
            }
        }
    }
}
</style>
