class ELog {
    constructor() {
        this.logId = '';
    }

    start(id) {
        this.logId = id;
    }

    _str(...args) {
        let str = "";
        for (const arg of args) {
            if (typeof arg === 'object') {
                str += JSON.stringify(arg) + " "
            } else {
                str += arg + " "
            }
        }
        return str + "\r\n"
    }

    log(...args) {
        console.log(this._str(...args))
        this.logId && g.ipcRenderer.send('write-elog', this.logId, this._str(...args))
    }

    error(...args) {
        console.error(this._str(...args))
        this.logId && g.ipcRenderer.send('write-elog', this.logId, 'ERROR', this._str(...args))
    }

    stop() {
        if (!this.logId) return;
        console.log('stop elog', this.logId);
        g.ipcRenderer.send('upload-log', this.logId);
        this.logId = '';
    }
}

export default ELog
