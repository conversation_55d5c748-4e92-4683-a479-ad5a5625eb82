<template>
  <div class="notice-settings">
    <div class="setting-item bline flex-col">
      <el-checkbox v-model="settings.enableDotMessage" label="开启消息红点" @change="onChange" />
      <el-checkbox v-model="settings.enableMessagePush" label="开启消息推送" @change="onChange" />
    </div>
    <div class="setting-item">
      <div class="clear_btn" @click="onClear">清空信息</div>
    </div>
  </div>
  <dialogConfirmClear ref="refDialog" v-model="dialogVisible" />
</template>

<script setup>
const settings = ref(g.electronStore.settings.message);
import dialogConfirmClear from "./dialogConfirmClear.vue";
import { getMsgSetting, updateMsgSetting } from '@/app_electron/tools/api.js';

const dialogVisible = ref(false);
const refDialog = ref(null);

const onClear = () => {
  dialogVisible.value = true;
}

watch(() => settings.value, (newSettings) => {
  g.electronStore.updateSetting('message', newSettings)
}, { deep: true });

const onChange = (status) => {
  const param = toRaw(settings.value);
  updateMsgSetting(param).then((resp) => {
    if (resp.code == 0) {
      ElMessage.success(status ? '开启成功' : '关闭成功')
    } else {
      ElMessage.error('更新失败')
    }
  }).catch(() => {
    ElMessage.error('更新失败')
  })
}

onMounted(() => {
  getMsgSetting().then((resp) => {
    if (resp.code == 0) {
      const { enableDotMessage, enableMessagePush } = resp.data;
      settings.value = { enableDotMessage, enableMessagePush }
    } else {
      settings.value = g.electronStore.settings.message;
    }
  }).catch(() => {
    settings.value = g.electronStore.settings.message;
  })
});

defineExpose({
  settings
})

</script>

<style lang="scss" scoped>
.notice-settings {
  .setting-item {
    display: flex;
    padding: 10px 0;


    .el-checkbox {
      margin-bottom: 10px;
    }
  }

  .bline {
    border-bottom: 1px solid #eee;
  }

  .clickable {
    cursor: pointer;
  }

  .clickable:hover {
    background-color: #f5f5f5;
  }

  .clear_btn {
    font-size: 14px;
    color: #f56c6c;
    cursor: pointer;
    padding: 4px 0;
  }
}
</style>
