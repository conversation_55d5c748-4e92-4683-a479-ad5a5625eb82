<template>
    <div class="dropdown_list_wrap">
        <div @click="toggleDropdown" ref="refToggle">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits(['callback'])
const props = defineProps(['options'])

const refToggle = ref();
const dl_style = ref({})


const toggleDropdown = (e) => {
    const rect = refToggle.value.getBoundingClientRect();
    const style = {
        left: rect.left + 10 + 'px',
        top: rect.top + 40 + 'px'
    }
    setShow(true, style)
}

const selectOption = (option) => {
    emit('callback', option)
    setShow(false)
}

const setShow = (status, style) => {
    g.emitter.emit('ddl_status', { status, data: props.options, style })
}


onMounted(() => {
    g.emitter.on('ddl_select', (item) => {
        emit('callback', toRaw(item))
    })
})

onUnmounted(() => {
    g.emitter.off('ddl_select');
});


defineExpose({
    refToggle,
    dl_style,
    toggleDropdown,
    selectOption
});
</script>

<style lang="scss">
.dropdown_list_wrap {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: fixed;
    left: 200px;
    top: 200px;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    z-index: 99;

    li {
        padding: 5px;
        width: 200px;
        cursor: pointer;
    }

    li:hover {
        background-color: #f2f2f2;
    }

    li:active {
        background-color: #e6e6e6;
    }

    li:first-child {
        border-top: none;
    }

    li:last-child {
        border-bottom: none;
    }
}
</style>