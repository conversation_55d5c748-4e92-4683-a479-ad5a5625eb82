<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
                <stop stop-color="#F96946" offset="0%"></stop>
                <stop stop-color="#FF8F68" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="访前PPT生成" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="模版详情" transform="translate(-744.000000, -307.000000)">
                <g id="编组-6" transform="translate(744.000000, 306.000000)">
                    <g id="1.基础/2.Icon-图标/知识类型/54/PPT" transform="translate(0.000000, 1.000000)">
                        <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                        <path
                            d="M13.3333333,1.66666667 L17.5,5.83333333 L17.5,17.5068333 C17.5,17.9633333 17.12925,18.3333333 16.6721667,18.3333333 L3.32783333,18.3333333 C2.87063333,18.3333333 2.5,17.9539167 2.5,17.5068333 L2.5,2.49316667 C2.5,2.03670833 2.87079167,1.66666667 3.32783333,1.66666667 L13.3333333,1.66666667 Z M6.66666667,6.66666667 L6.66666667,13.3333333 L8.33333333,13.3333333 L8.33333333,11.6666667 L13.3333333,11.6666667 L13.3333333,6.66666667 L6.66666667,6.66666667 Z M8.33333333,8.33333333 L11.6666667,8.33333333 L11.6666667,10 L8.33333333,10 L8.33333333,8.33333333 Z"
                            id="形状" fill="url(#linearGradient-1)"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'PptIcon',
}
</script>

