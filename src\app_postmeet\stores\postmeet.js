import { defineStore } from "pinia";
import { getUser } from "@/js/utils.js";
import {
  convertOv,
  mergeArrays,
  getTotoFromAsrSummary,
  timeAddEt,
  darray2dict,
  countKeyWords,
  countKeyWords_Asr,
  calcUserSpeedTime,
  emptyobj,
  array2ListText,
  convert2TextPolish,
  newPolishToOld,
  generateMarkdown,
} from "@/app_postmeet/tools/tools.js";
import {
  getLaCuEvaluate,
  getLaCompetitorStatus,
  getLaCompetitorMenu,
  getLaTodoList,
  covertToComments,
} from "@/app_postmeet/tools/sale_report.js";

const data = {
  confId: "", //url里的id，有可能是share_id, 或conferenceId
  conferenceId: "", //会议id,report里的id
  user: getUser(),
  recordInfo: {},
  asrRaw: {},
  playIdx: 0,
  playItem: {},
  editmode: false,
  isInterview: false,
  actionLabResult: [], //待办事项
  keySentLResult: [], //重点内容
  meetSummary: [], //摘要
  agendaRich: [], //议程
  topicLabResult: [], //分段主题
  ConversationalSummary: [], //发言总结
  QuestionsAnsweringSummary: [], //问答回顾
  ParagraphSummary: [], //全文概要
  keywords: [], //关键字，
  names: [], //发言人
  subtitles: [], // 字幕
  txtpolishs: [], //优化化的字幕
  txtpolishsHasError: false,
  chartCloudWords: [], // 词云图，
  MindMapMd: "", //markdown格式的脑图
  salesMateSummary: {},
  hasSalesMateSummary: false,
  needAsrMail: false,
  interview: { reports: [] }, //面试相关数据,
  currIVReportID: 0,
  saleTags: [],
  saleReport: {},
  SaleSysDimension: [],
  words_count: 0,
  allowSaleReGen: false,
  saleAnalyseList: {},
  sbComments: [],
  isPlayVideo: true, //false:当前界面显示的是 audio
  saleReportStatus: "",
  saleSpeakPercent: 0,
  isGenReporting: false,
  curr_menu: "summary", //coach,summary,analyse
  min_words_count:
    import.meta.env.VITE_APP_APIENV.indexOf("prod") > -1 ? 1500 : 500,
  recognitionStatus: "",
  kngStudyProcess: 0,
  defaultSalesMethodology: "BANT",
  SalesMethodology: {},
  methodologyCache: {},
  targetSettings: 0,
  enable_edit: true,
  roleList: [],
  summaryMdId: ''
};

function get_curr_on_report() {
  return `re_gen_sale_report_${data.curr_menu}`;
}

function setSaleSysDim(array) {
  var result = {};

  for (var i = 0; i < array.length; i++) {
    var item = array[i];
    result[item.id] = item.name;
  }
  data.SaleSysDimension = result;
}

function setValue(name, value) {
  data[name] = value;
}

function getHasAsr() {
  // 在没有asr 的情况下，也需要显示相似内容
  return 'yes'
  // return data.asrRaw.hasAsr ? "yes" : "no";
}

function isReadonly() {
  // return false;
  const isAdminMode = location.href.indexOf("is_admin_edit_mode") > -1;
  if (isAdminMode) {
    return false;
  }
  data.user = getUser();
  return data.user.id !== data.recordInfo.hostUserId;
}

function getIsShowSetup() {
  // return true;
  const hasReport = JSON.stringify(g.postmeetStore.data.saleReport) != "{}";
  return !isReadonly() && !hasReport;
}

function getRecordViewPermission() {
  // return false;
  return data.asrRaw.recordViewPermission == 1;
}

function getValue(name, defval = undefined) {
  return data[name] || defval;
}

function updateValue() {
  const {
    asrContent,
    asrSummary,
    updatedAsrContent,
    meetingType,
    salesMateSummary,
    recognitionStatus,
  } = data.asrRaw;
  data.recognitionStatus = recognitionStatus;
  let {
    UidInfo,
    KeywordLabResults,
    AsrResultList,
    MindMapSummary,
    TextPolish,
    AsrParagraphsList,
  } = asrContent;
  const st = data.recordInfo.startTime;

  if (AsrParagraphsList) {
    const temp = newPolishToOld(AsrParagraphsList);
    AsrResultList = temp.AsrResultList;
    TextPolish = temp.TextPolish;
    data["asrRaw"]["asrContent"]["AsrResultList"] = AsrResultList;
    data["asrRaw"]["asrContent"]["TextPolish"] = TextPolish;
  }
  data.isInterview = meetingType == 8;
  if (!!salesMateSummary) {
    data.hasSalesMateSummary = !!salesMateSummary;
    if (data.hasSalesMateSummary) {
      data.salesMateSummary = {
        ...{ challenge: [], plan: [], suggestion: [] },
        ...salesMateSummary,
      };
      delete data.asrRaw["salesMateSummary"];
      const { challenge, plan, suggestion } = data.salesMateSummary;
      data.needAsrMail = plan.length + challenge.length + suggestion.length > 0;
    }
  }
  //待办事项
  data.actionLabResult = getTotoFromAsrSummary(asrSummary, st);

  //重点内容
  data.keySentLResult = convertOv(asrContent, "KeySentenceLabResult", st);
  if (updatedAsrContent.hasOwnProperty("KeySentenceLabResult")) {
    data.keySentLResult = mergeArrays(
      data.keySentLResult,
      updatedAsrContent["KeySentenceLabResult"],
      "bt"
    );
  }

  //分段主题
  data.topicLabResult = convertOv(asrContent, "TopicLabResult", st);
  if (updatedAsrContent.hasOwnProperty("TopicLabResult")) {
    data.topicLabResult = mergeArrays(
      data.topicLabResult,
      updatedAsrContent["TopicLabResult"]
    );
    data.topicLabResult = mergeArrays(
      data.topicLabResult,
      updatedAsrContent["TopicLabResult"],
      "id",
      "id",
      "summary"
    );
  }

  //议程
  data["agendaRich"] = data.recordInfo.agendaRich;
  data["CustomerRoles"] = _getDictValue(updatedAsrContent, "CustomerRoles", []);
  data["InternalPartners"] = _getDictValue(
    updatedAsrContent,
    "InternalPartners",
    []
  );

  //改名字

  if (!emptyobj(AsrResultList)) {
    if (updatedAsrContent.hasOwnProperty("ParticipantResult")) {
      const unmap = darray2dict(updatedAsrContent["ParticipantResult"]);
      for (let key of Object.keys(UidInfo)) {
        if (unmap[key]) {
          UidInfo[key].name = unmap[key];
        }
      }
      for (let item of data["InternalPartners"]) {
        if (UidInfo[item["ui"]]) {
          UidInfo[item["ui"]].name = item.name;
        } else {
          console.error("InternalPartners no ui fond", item, UidInfo);
        }
      }
      for (let item of data["CustomerRoles"]) {
        if (UidInfo[item["ui"]]) {
          UidInfo[item["ui"]].name = item.name;
        } else {
          console.error("CustomerRoles no ui fond", item, UidInfo);
        }
      }
      asrContent["UidInfo"] = UidInfo;
    }
    data["asrRaw"]["asrContent"]["UidInfo"] = UidInfo;
  }
  //发言总结
  const ConversationalSummary = Array.isArray(asrContent.ConversationalSummary)
    ? asrContent.ConversationalSummary
    : [];
  const ConversationalSummary2 = [];
  for (let i = 0; i < ConversationalSummary.length; i++) {
    const item = ConversationalSummary[i];
    if (!!item["ui"] && UidInfo.hasOwnProperty(item["ui"])) {
      ConversationalSummary[i]["name"] = UidInfo[item["ui"]].name;
      ConversationalSummary2.push(ConversationalSummary[i]);
    }
  }
  data["ConversationalSummary"] = sortConvSummary(ConversationalSummary2);

  //问答回顾
  data["QuestionsAnsweringSummary"] = Array.isArray(
    asrContent.QuestionsAnsweringSummary
  )
    ? asrContent.QuestionsAnsweringSummary
    : [];

  //全文概要
  data["ParagraphSummary"] = asrContent.ParagraphSummary
    ? asrContent.ParagraphSummary
    : "";

  //摘要
  data["meetSummary"] = asrSummary;

  function transformData(data) {
    if (!!data) {
      let new_data = data.map((item) => ({
        name: item.Title,
        children: item.Topic.map((topicItem) => transformData([topicItem])[0]),
      }));
      if (new_data.length > 1) {
        new_data = [
          {
            name: "摘要",
            children: new_data,
          },
        ];
      }
      return new_data;
    } else {
      return [];
    }
  }
  //脑图
  if (updatedAsrContent.hasOwnProperty("MindMap")) {
    data["MindMapMd"] = updatedAsrContent["MindMap"];
  } else {
    data["MindMapMd"] = generateMarkdown(transformData(MindMapSummary));
  }

  if (!emptyobj(AsrResultList)) {
    //字幕
    data.subtitles = _getSubtitles(AsrResultList);
    if (TextPolish && TextPolish.length > 0) {
      const resp = convert2TextPolish(data.subtitles, TextPolish);
      const status = resp[0];
      const txtpolishs = resp[1];
      setValue("txtpolishsHasError", !status);
      if (status) {
        data.txtpolishs = txtpolishs;
      } else {
        data.txtpolishs = [];
      }
    }

    // 词云图,keywords
    let cloudData = [];
    let keywords = [];
    if (asrSummary.length > 0) {
      cloudData = countKeyWords(asrSummary).slice(0, 300);
      if (cloudData.length) {
        cloudData.sort((a, b) => (a.value - b.value > 0 ? -1 : 1));
        keywords = cloudData.map((x) => x.name).slice(0, 20);
      } else {
        keywords = emptyobj(KeywordLabResults)
          ? []
          : KeywordLabResults.map((x) => x.value);
        cloudData = countKeyWords_Asr(keywords, data.subtitles);
      }
    } else if (KeywordLabResults) {
      keywords = emptyobj(KeywordLabResults)
        ? []
        : KeywordLabResults.map((x) => x.value);
      cloudData = countKeyWords_Asr(keywords, data.subtitles);
    }
    data.chartCloudWords = cloudData;

    // 过滤字幕里没有提到的词语
    // const allSubText = data.subtitles.map(x => x.txt).join('|')
    // keywords = keywords.filter(x => allSubText.indexOf(x) > -1)

    data.words_count = data.subtitles.map((x) => x.txt).join("").length;
    data.keywords = keywords;
  }
}

function sortConvSummary(list) {
  let { AsrResultList, UidInfo } = data["asrRaw"]["asrContent"];
  const userTimesArray = calcUserSpeedTime(AsrResultList, UidInfo, 1);
  const userTimes = {};

  for (let i = 0; i < userTimesArray.length; i++) {
    const row = userTimesArray[i];
    userTimes[row.name] = row.value;
  }

  function _sort(a, b) {
    return userTimes[a.name] > userTimes[b.name] ? -1 : 1;
  }
  let result = list.sort(_sort);

  // 按名字合并，一个人多次进会时需要
  function mergeSummaryByName(input) {
    const roleList = getRoleList();
    const mergedValues = [];
    const mergedNames = [];

    input.forEach((item) => {
      const { name, Summary, ui } = item;
      const person = roleList.find((x) => x.ui == ui);
      const index = mergedNames.indexOf(name);
      if (person.hasSame) {
        if (index === -1) {
          mergedNames.push(name);
          mergedValues.push({ name, Summary, person });
        } else {
          mergedValues[index].Summary += Summary;
        }
      } else {
        mergedNames.push(name);
        mergedValues.push({ name, Summary, person });
      }
    });
    return mergedValues;
  }

  result = mergeSummaryByName(result);
  return result;
}

function getRoleList() {
  const { UidInfo } = data.asrRaw?.asrContent || "";
  if (!UidInfo) {
    return [];
  }
  const users = Object.entries(UidInfo).map(([key, value]) => {
    return { ...value, ui: key };
  });
  let roles = [
    ..._add_id_key(data["CustomerRoles"], "type", "customer"),
    ..._add_id_key(data["InternalPartners"], "type", "internal"),
  ];
  roles = roles.filter((x) => x.ui);
  const merges = [];
  for (let i = 0; i < users.length; i++) {
    let user = users[i];
    const role = roles.find((x) => x.ui == user.ui);
    if (role) {
      user = { ...user, ...role };
    }
    merges.push(user);
  }
  //添加一个个字段hasSame，判断是否有和其它人的信息一样的。 internal 类型的，检查position，name。 customer类似的检查title，name，position,role
  for (let i = 0; i < merges.length; i++) {
    const item = merges[i];
    const { name, position, title, role, type } = item;
    item["hasSame"] = false;
    if (type == "internal") {
      if (
        merges.filter((x) => x.name == name && x.position == position).length >
        1
      ) {
        item["hasSame"] = true;
      }
    } else {
      if (
        merges.filter(
          (x) =>
            x.name == name &&
            x.title == title &&
            x.position == position &&
            x.role == role
        ).length > 1
      ) {
        item["hasSame"] = true;
      }
    }
  }
  data.roleList = merges;
  return merges;
}

function getUserTimeChartDate(et_start, et_end) {
  let { AsrResultList, UidInfo } = data["asrRaw"]["asrContent"];
  AsrResultList = AsrResultList.filter(
    (x) => x.bt - x.pd >= et_start && x.bt - x.pd <= et_end
  );
  return calcUserSpeedTime(AsrResultList, UidInfo);
}

function _add_id_key(list, key, value) {
  let i = 0;
  return list.map((x) => {
    x[key] = value;
    x["id"] = x.hasOwnProperty("id") ? x["id"] : i;
    i += 1;
    return x;
  });
}

function _getDictValue(dict, key, defVal) {
  if (dict.hasOwnProperty(key)) {
    return dict[key];
  } else {
    return defVal;
  }
}

function _getSubtitles(AsrResultList) {
  const { asrRaw, recordInfo } = data;
  const { updatedAsrContent } = asrRaw;
  const UidInfo = getRoleList();
  if (!AsrResultList || emptyobj(AsrResultList)) {
    return [];
  } else {
    const startTime = !asrRaw.needCalRealTime
      ? "2023-01-01 00:00:00"
      : recordInfo.startTime;
    let messages = AsrResultList.map((item) => {
      item.time = timeAddEt(startTime, item.bt);
      item.name = UidInfo.find((x) => x.ui == item.ui).name;
      return item;
    });
    if (updatedAsrContent) {
      if (updatedAsrContent.hasOwnProperty("SubtitleResult")) {
        let ucsb = updatedAsrContent["SubtitleResult"];
        const ucsb1 = ucsb
          .filter((x) => x.value)
          .map((x) => {
            return {
              bt: parseInt(x.id),
              txt: x.value,
            };
          });
        messages = mergeArrays(messages, ucsb1, "bt", "bt", "txt");

        const ucsb2 = ucsb
          .filter((x) => x.ui)
          .map((x) => {
            return {
              bt: parseInt(x.id),
              name: x.ui,
            };
          });
        messages = mergeArrays(messages, ucsb2, "bt", "bt", "name");
      }
    }
    messages.sort((a, b) => (a.bt > b.bt ? 1 : -1));
    return messages;
  }
}

function getCurrRecord() {
  if (
    !data.recordInfo ||
    !data.recordInfo.recordList ||
    data.recordInfo.recordList.length == 0
  ) {
    return {};
  }
  return data.recordInfo?.recordList[data.playIdx] || {};
}

function getCurrIVReport() {
  if (data.interview && data.interview.reports.length > 0) {
    return data.interview.reports[data.currIVReportID];
  } else {
    return {};
  }
}

function setUpdatedContent(updatedAsrContent) {
  data.asrRaw["updatedAsrContent"] = updatedAsrContent;
  updateValue();
}

// 查询还需要转录的用户列表信息
function getCanConverUsers() {
  const { UidInfo } = data.asrRaw.asrContent;
  const convedUids = [];
  for (let key of Object.keys(UidInfo)) {
    if (UidInfo[key].record_id) {
      const uuid = UidInfo[key].uuid.split(",")[0];
      if (!convedUids.includes(uuid)) {
        convedUids.push(uuid);
      }
    }
  }
  const datas = Object.values(UidInfo).filter(
    (x) => !convedUids.includes(x.uuid) && !x.record_id
  );
  return datas;
}

function getMailBody() {
  // 尊敬的{客户姓名}老师，您好！\n非常荣幸收到您的邀请，于今日沟通贵司就项目需求进行交流，非常感谢贵司的热情接待与耐心解答，现将此次沟通的具体内容陈述如下：\n\n沟通时间：{沟通时间:YYYY年MM月dd日，HH:mm}\n参会人：{参会人}\n\n一、{关联客户}的培训诉求：\n{challenge}\n\n二、云学堂的建议和方案：\n{suggestion}\n\n三、待办：\n{plan}\n\n最后感谢{关联客户}对云学堂的信任与支持，希望有机会能与贵司达成长期战略合作！祝您工作顺利！\n\n云学堂：{主持人}\n{年月日:YYYY年MM月dd日}\n
  let mail = data.asrRaw.mailTemplate;

  if (!mail) {
    try {
      const saleReport = data.saleReport.analysisReports.find(
        (x) => x.systemId == 5
      );
      if (saleReport) {
        mail = saleReport.report.mail;
      }
    } catch (e) {
      console.log("error getMailBody", e);
    }
  }

  if (!mail) {
    return "-";
  }

  const { CustomerRoles, recordInfo, salesMateSummary, names, asrRaw } = data;
  const { challenge, suggestion, plan } = salesMateSummary;
  const { customerName } = asrRaw;

  let param = {
    customerUserName: { mb: "客户姓名", value: "" },
    startTime: { mb: "沟通时间:YYYY年MM月dd日，HH:mm", value: "" },
    parList: { mb: "参会人", value: "" },
    challenge: { mb: "challenge", value: "" },

    suggestion: { mb: "suggestion", value: "" },
    plan: { mb: "plan", value: "" },
    customerName: { mb: "关联客户", value: "" },
    hostName: { mb: "主持人", value: "" },

    today: { mb: "年月日:YYYY年MM月dd日", value: "" },
  };

  function setValue(key, value) {
    param[key]["value"] = value;
  }

  function _getRecordTime() {
    const date = new Date(recordInfo.startTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${year}年${month}月${day}日，${hours}:${minutes}`;
  }

  function _today() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}年${month}月${day}日`;
  }

  function _customUserName() {
    if (CustomerRoles.length > 0) {
      return `${CustomerRoles[0].title} ${CustomerRoles[0].name}`;
    } else {
      return "***";
    }
  }

  setValue("customerUserName", _customUserName());
  setValue("startTime", _getRecordTime());
  setValue("parList", names.join(","));
  setValue("challenge", array2ListText(challenge));
  setValue("suggestion", array2ListText(suggestion));
  setValue("plan", array2ListText(plan));
  setValue("customerName", customerName);
  setValue("hostName", getCurrRecord()["hostName"]);

  setValue("today", _today());

  for (let key of Object.keys(param)) {
    const item = param[key];
    const regex = new RegExp("{" + item["mb"] + "}", "g");
    mail = mail.replace(regex, item["value"]);
  }
  return mail;
}

function setSaleReport(data_) {
  const { tabs } = data_;
  const reports = data_["report"] || [];
  if (tabs && tabs.length > 0) {
    data.saleTags = tabs;
  } else {
    data.saleTags = [];
    const analysisReports =
      reports.analysisReports || reports.salesSummaryReports;

    if (analysisReports) {
      // sale report
      const cmap = {
        1: "segmented_summary",
        2: "key_points",
        3: "speecher_summary",
        4: "meet_minutes",
        5: "sale_meet",
        7: "chart_mind",
        8: "normal_meet",
      };
      for (let i = 0; i < analysisReports.length; i++) {
        const report = analysisReports[i];
        if (report.systemPreset && report.systemId != "6") {
          report["label"] = cmap[report.systemId + ""];
        } else {
          report["label"] = report["name"];
        }
        if (report.systemId == 5 && report.systemPreset) {
          data.salesMateSummary = report.report;
          const { challenge, suggestion, plan } = data.salesMateSummary;
          data.needAsrMail =
            plan.length + challenge.length + suggestion.length > 0;
        }
      }
      reports["analysisReports"] = analysisReports;
      data.saleReport = reports;
    } else if (data.estimates) {
      // HR interview
      const confId = data.confId;
      for (let i = 0; i < data.reports.length; i++) {
        const item = data.reports[i];
        if (item.confId == confId) {
          data.reports[i] = data;
          break;
        }
      }
    }

    getLaCuEvaluate();
    getLaCompetitorStatus();
    getLaCompetitorMenu();
    getLaTodoList();
  }
}

function lessData() {
  return data.words_count < data.min_words_count;
}

const ElectronStore = defineStore("postmeet", {
  state: () => ({
    user: getUser(),
    data,
  }),
  getters: {
    isLogin(state) {
      return state.user && state.user.token;
    },
  },
  actions: {
    setValue,
    getHasAsr,
    getValue,
    isReadonly,
    updateValue,
    getRoleList,
    getCurrIVReport,
    getIsShowSetup,
    getRecordViewPermission,
    getCurrRecord,
    setUpdatedContent,
    getCanConverUsers,
    getMailBody,
    getUserTimeChartDate,
    setSaleReport,
    setSaleSysDim,
    covertToComments,
    get_curr_on_report,
    lessData,
  },
});

export default ElectronStore;
