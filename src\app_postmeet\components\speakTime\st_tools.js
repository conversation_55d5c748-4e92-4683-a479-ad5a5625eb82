import { mergeSameNameData, tagsToMarkers, sum } from "@/app_postmeet/tools/tools";
import { round } from '@/js/utils';
export function getSpeakTimeList() {
    const subtitles = g.postmeetStore.data.subtitles;
    const item = g.postmeetStore.data.playItem;
    if (subtitles.length > 0) {
        const rolelist = g.postmeetStore.getRoleList();
        let list = rolelist.map((x) => {
            const sub_titles = subtitles.filter((y) => {
                return y.ui == x.ui;
            });
            const { name, type } = x;
            if (sub_titles.length > 0) {
                const markers = tagsToMarkers(item, sub_titles)
                return {
                    name,
                    type,
                    markers,
                    detail: x,
                    // percent,
                };
            } else {
                return {
                    name,
                    detail: x,
                    type,
                    markers: [],
                    percent: 0,
                };
            }
        });

        let total_time = 0;
        for (let i = 0; i < list.length; i++) {
            total_time += sum(list[i].markers.filter((x) => x.et - x.bt > 0).map((x) => x.et - x.bt));
        }
        for (let i = 0; i < list.length; i++) {
            list[i].percent = 0;
            if (list[i].markers.length > 0) {
                const sum_time = sum(list[i].markers.filter((x) => x.et - x.bt > 0).map((x) => x.et - x.bt))
                if (sum_time > 0) {
                    list[i].percent = round((100 * sum_time) / total_time, 0)
                } else {
                    list[i].percent = -1
                }
            } else {
                list[i].percent = -1
            }
        }

        list = list.filter(x => x.percent >= 0);
        if (list.length > 1) {
            // 处理零值情况
            const zeroPercentItems = list.filter(x => x.percent <= 0);
            if (zeroPercentItems.length > 0) {
                // 找到最大的百分比值
                const maxPercentItem = list.reduce((max, item) =>
                    item.percent > max.percent ? item : max
                );

                // 将零值设置为1，并从最大值中减去相应的值
                zeroPercentItems.forEach(item => {
                    item.percent = 1;
                    maxPercentItem.percent -= 1;
                });
            }

            const sum_percent = sum(list.slice(0, list.length - 1).map((x) => x.percent))
            if (sum_percent < 100) {
                list[list.length - 1].percent = 100 - sum_percent;
            }
        }
        list = mergeSameNameData(list);

        const hostName = g.postmeetStore.data.playItem.hostName;
        const saleList = list.filter((x) => x.name == hostName);
        if (saleList.length > 0) {
            const saleSpeakPercent = sum(saleList.map((x) => x.percent));
            g.postmeetStore.setValue("saleSpeakPercent", saleSpeakPercent);
            g.emitter.emit("update_saleSpeakPercent", saleSpeakPercent);
        }
        return list
    }
}

export function getClipSpeakTime(testData) {
    // 提取 item 信息
    const item = {
        duration: testData.duration
    };

    // 按说话人分组并处理数据
    const speakerMap = {};

    // 计算总说话时长
    let total_time = 0;
    testData.sentences.forEach(sentence => {
        const { startTime, endTime } = sentence;
        // 计算每句话的时长
        const [startHours, startMinutes, startSeconds] = startTime.split(':').map(Number);
        const [endHours, endMinutes, endSeconds] = endTime.split(':').map(Number);
        const start = startHours * 3600 + startMinutes * 60 + startSeconds;
        const end = endHours * 3600 + endMinutes * 60 + endSeconds;
        total_time += (end - start);
    });

    // 记录每个说话人的总说话时长
    const speakerDurations = {};

    testData.sentences.forEach(sentence => {
        const { info, startTime, endTime } = sentence;
        const { customer, displayName, name, position, ui } = info;

        // 计算这句话的时长
        const [startHours, startMinutes, startSeconds] = startTime.split(':').map(Number);
        const [endHours, endMinutes, endSeconds] = endTime.split(':').map(Number);
        const start = startHours * 3600 + startMinutes * 60 + startSeconds;
        const end = endHours * 3600 + endMinutes * 60 + endSeconds;
        const duration = end - start;

        // 累加每个说话人的总时长
        speakerDurations[ui] = (speakerDurations[ui] || 0) + duration;

        // 如果是新的说话人，初始化数据结构
        if (!speakerMap[ui]) {
            speakerMap[ui] = {
                title: displayName,
                name,
                position,
                ui,
                type: customer ? "customer" : "internal",
                markers: []
            };
        }

        // 添加时间标记
        speakerMap[ui].markers.push({
            markTime: start,
            startTime
        });
    });

    // 转换为数组格式并计算百分比
    const list = Object.entries(speakerMap).map(([ui, speaker]) => {
        // 计算说话时长百分比
        const percent = Math.round((speakerDurations[ui] * 100) / total_time);

        // 对每个说话人的标记点按时间排序
        speaker.markers.sort((a, b) => a.markTime - b.markTime);

        return {
            ...speaker,
            percent
        };
    });
    // 处理百分比总和不为100的情况
    if (list.length > 1) {
        // 处理零值情况
        const zeroPercentItems = list.filter(item => item.percent === 0);
        if (zeroPercentItems.length > 0) {
            // 找到最大的百分比值
            const maxPercentItem = list.reduce((max, item) =>
                item.percent > max.percent ? item : max
            );

            // 将零值设置为1，并从最大值中减去相应的值
            zeroPercentItems.forEach(item => {
                item.percent = 1;
                maxPercentItem.percent -= 1;
            });
        }

        const totalPercent = list.reduce((sum, speaker) => sum + speaker.percent, 0);
        if (totalPercent !== 100) {
            // 将差值加到最后一个说话人的百分比上
            list[list.length - 1].percent += (100 - totalPercent);
        }
    }
    return { item, list };
}

