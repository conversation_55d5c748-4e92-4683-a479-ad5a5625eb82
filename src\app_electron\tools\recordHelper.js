let AVR<PERSON>order, AVCanvas, VideoSprite, AudioSprite, fs, ReadableWebToNodeStream;

if (window.nodeRequire) {
    AVRecorder = window.nodeRequire('avrecorder-recorder').AVRecorder;
    AVCanvas = window.nodeRequire('avrecorder-canvas').AVCanvas;
    VideoSprite = window.nodeRequire('avrecorder-canvas').VideoSprite;
    AudioSprite = window.nodeRequire('avrecorder-canvas').AudioSprite;
    fs = window.nodeRequire("fs");
    ReadableWebToNodeStream = window.nodeRequire('web-to-node-readablestream').ReadableWebToNodeStream;
}

class AVRecorderObject {
    constructor(type) {
        this.type = type;
        this.stream = null;
        this.sprite = null;
    }

    clearStream(stream) {
        if (stream) {
            for (const track of stream.getTracks()) {
                track.stop();
            }
        }
    }

    async updateStream(stream, avCanvas) {
        if (this.stream !== null) {
            avCanvas.spriteManager.removeSprite(this.sprite);
            this.sprite.destroy();
            this.sprite = null;
        }

        this.clearStream(this.stream);
        this.stream = null;

        if (stream === null) {
            return;
        }

        this.stream = stream.clone();

        if (this.type === "mic" || this.type === "systemAudio") {
            this.sprite = new AudioSprite(this.type, this.stream, {
                audioCtx: avCanvas.spriteManager.audioCtx
            });
        } else {
            this.sprite = new VideoSprite(this.type, this.stream, {
                audioCtx: avCanvas.spriteManager.audioCtx
            });
            if (this.type === 'camera') {
                this.sprite.zIndex = 10; // 使摄像头视频始终在最上层
            }
        }

        try {
            await avCanvas.spriteManager.addSprite(this.sprite);
        } catch (e) {
            console.log('AVRecorderObject::updateStream::error = ', e);
        }
    }

    isValid() {
        return this.stream !== null && this.sprite !== null;
    }

    async destroy() {
        await this.sprite?.destroy();
        this.sprite = null;
        this.clearStream(this.stream);
        this.stream = null;
    }
}

class AVLocalRecorder {
    constructor(previewDiv, layout = "fullscreen", outputFrameRate = 15, outputResolution = { width: 1920, height: 1080 }) {
        this.layout = layout;
        this.outputFrameRate = outputFrameRate;
        this.outputResolution = outputResolution;
        this.updateBitrate();

        this.avCanvas = new AVCanvas(previewDiv, {
            bgColor: "#E3E3E7",
            resolution: {
                width: 1920,
                height: 1080
            }
        });

        this.avRecorder = new AVRecorder(this.avCanvas.captureStream(), {
            width: this.outputResolution.width,
            height: this.outputResolution.height,
            bitrate: this.outputBitrate,
            expectFPS: this.outputFrameRate,
            audioCodec: 'aac'
        });

        this.avCanvas.updateLayoutType(layout);

        // 初始化录制对象
        this.cameraObject = new AVRecorderObject("camera");
        this.displayObject = new AVRecorderObject("display");
        this.micObject = new AVRecorderObject("mic");
        this.systemAudioObject = new AVRecorderObject("systemAudio");
    }

    updateBitrate() {
        const { width, height, frameRate } = this.outputResolution;
        if (width === 1920 && height === 1080 && frameRate === 25
            || width === 1280 && height === 720 && frameRate === 30) {
            this.outputBitrate = 13_000_000;
        } else if (width === 1920 && height === 1080 && frameRate === 30) {
            this.outputBitrate = 15_000_000;
        } else if (width === 1280 && height === 720 && frameRate === 25) {
            this.outputBitrate = 10_000_000;
        } else {
            this.outputBitrate = 8_000_000;
        }
    }

    async updateStream(stream, type) {
        switch (type) {
            case 'camera':
                if (stream === null && this.layout !== 'fullscreen') {
                    this.updateLayout('fullscreen');
                }
                if (stream !== null && this.layout === 'fullscreen' && this.displayObject?.isValid()) {
                    this.updateLayout('sidebyside');
                }
                await this.cameraObject?.updateStream(stream, this.avCanvas);
                break;
            case 'display':
                if (stream === null && this.layout !== 'fullscreen') {
                    this.updateLayout('fullscreen');
                }
                if (stream !== null && this.layout === 'fullscreen' && this.cameraObject?.isValid()) {
                    this.updateLayout('sidebyside');
                }
                await this.displayObject?.updateStream(stream, this.avCanvas);
                break;
            case 'mic':
                await this.micObject?.updateStream(stream, this.avCanvas);
                break;
            case 'systemAudio':
                await this.systemAudioObject?.updateStream(stream, this.avCanvas);
                break;
            default:
                break;
        }
    }

    updateLayout(layout) {
        if (this.layout !== layout) {
            this.layout = layout;
            this.avCanvas.updateLayoutType(layout);
        }
    }

    updateFrameRate(frameRate) {
        if (this.outputFrameRate !== frameRate) {
            this.outputFrameRate = frameRate;
            this.updateBitrate();
        }
    }

    updateResolution(width, height) {
        if (this.outputResolution.width !== width || this.outputResolution.height !== height) {
            this.outputResolution = { width, height };
            this.updateBitrate();
        }
    }

    async startRecording(filePath) {
        if (this.avRecorder.state === 'inactive') {
            const conf = {
                width: this.outputResolution.width,
                height: this.outputResolution.height,
                bitrate: this.outputBitrate,
                expectFPS: this.outputFrameRate
            };
            this.avRecorder.updateConf(conf);

            try {
                await this.avRecorder.start();

                const nodeWriteStream = fs.createWriteStream(filePath, { flags: 'w' });
                const nodeReadableStream = new ReadableWebToNodeStream(this.avRecorder.outputStream);
                nodeReadableStream.pipe(nodeWriteStream);
            } catch (e) {
                console.log('AVLocalRecorder::startRecording::error = ', e);
            }
        }
    }

    async stopRecording() {
        if (this.avRecorder.state !== 'inactive') {
            try {
                await this.destroy();
            } catch (e) {
                console.log('AVLocalRecorder::stopRecording::error = ', e);
            }
        }
    }

    pauseRecording() {
        if (this.avRecorder.state === 'recording') {
            this.avRecorder.pause();
        }
    }

    resumeRecording() {
        if (this.avRecorder.state === 'paused') {
            this.avRecorder.resume();
        }
    }

    getState() {
        return this.avRecorder.state;
    }

    async destroy() {
        try {
            await this.avRecorder.stop();
            await this.cameraObject?.updateStream(null, this.avCanvas);
            await this.cameraObject?.destroy();
            this.cameraObject = null;
            await this.micObject?.updateStream(null, this.avCanvas);
            await this.micObject?.destroy();
            this.micObject = null;
            await this.displayObject?.updateStream(null, this.avCanvas);
            await this.displayObject?.destroy();
            this.displayObject = null;
            await this.systemAudioObject?.updateStream(null, this.avCanvas);
            await this.systemAudioObject?.destroy();
            this.systemAudioObject = null;
            this.avCanvas.destroy();
            this.avCanvas = null;
        } catch (e) {
            console.log('AVLocalRecorder::destroy::error = ', e);
        }
    }
}

export { AVLocalRecorder }