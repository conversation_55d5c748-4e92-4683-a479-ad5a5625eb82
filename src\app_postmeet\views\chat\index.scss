.chat_wrap {
    .chat_header {
        height: 49px;
        width: 100%;
        font-size: 24px;
        border-bottom: 1px solid #eaeaea;
        display: flex;

        .rh_left {
            display: flex;

            .rh_logo {
                width: 40px;
                height: 40px;
                padding: 0 10px;
                cursor: pointer;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .rh_meet_info {
                display: flex;
                flex-direction: column;
                justify-content: center;

                .diy_box {
                    input:focus {
                        width: 400px;
                    }
                }

                .rh_title {
                    font-size: 14px;
                }

                .rh_sub_title {
                    font-size: 12px;
                    color: #999;
                }
            }
        }


        ._blank {
            flex-grow: 1;
        }

        .rh_right {
            align-items: center;
            display: flex;

            .rh_name {
                display: inline-flex;
                margin: 0 9px;

                .rh_abridge {
                    flex: 0 0 auto;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
    }

    .chat_main {
        margin: 20px 30px;
        overflow: hidden;

        ol,
        li {
            list-style: decimal;
            margin-left: 16px;
        }

        .chat-subtitles-list {
            height: auto;
            display: revert;
            overflow-y: auto;
            padding-bottom: 20px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
                background-color: #f5f5f5;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 4px;
                background: #e0e0e0;
            }

            &::-webkit-scrollbar-track {
                border-radius: 10px;
                background-color: #fff;
            }

            li {
                margin: 8px 0;

                .name_time {
                    font-size: 13px;
                    color: #999;
                    margin-bottom: 0;
                    display: flex;

                    .t_name {
                        margin-right: 12px;
                    }
                }

                img {
                    max-height: 400px;
                }

                .txt {
                    font-size: 15px;
                    color: #333;
                }
            }

            .highli {

                div,
                p {
                    color: #436bff;
                }
            }
        }
    }

    .loading {
        margin-top: 100px;
    }
}

pre {
    box-sizing: border-box;
    /*以下样式是自动换行代码*/
    white-space: pre-wrap;
    /* css-3 */
    white-space: -moz-pre-wrap;
    /* Mozilla, since 1999 */
    white-space: -pre-wrap;
    /* Opera 4-6 */
    white-space: -o-pre-wrap;
    /* Opera 7 */
    word-wrap: break-word;
    /* Internet Explorer 5.5+ */
    /*以上样式是自动换行代码，需要的加上，不需要的删除*/
    overflow: auto;
    font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
    font-size: 13px;
    padding: 9.5px;
    margin-top: 0px;
    margin-bottom: 10px;
    line-height: 1.42857;
    color: #333333;
    word-break: break-all;
    word-wrap: break-word;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    background-color: #F5F5F5;
}