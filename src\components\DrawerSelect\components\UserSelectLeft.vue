<template>
    <div class="us_left">
        <div class="us_left_header">
            <TreeDeptSelector ref="refTreeDeptSelector" @callback="cbDept" :allDept="props.allDept" />
            <el-input v-model="searchName" placeholder="请输入姓名" class="search_name" :prefix-icon="Search" clearable />
        </div>
        <div class="us_left_main">
            <MyTable ref="refTable" :cfg="tableConfig" @callback="cbDatas">
            </MyTable>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { getUserListPager } from "@/js/api"
import MyTable from "@/components/Table.vue"
import TreeDeptSelector from "@/components/TreeDeptSelector.vue"
const emit = defineEmits(['update:selectedUsers']);

const props = defineProps({
    selectedUsers: {
        type: Array,
        default: () => []
    },
    allDept: {
        type: Boolean,
        default: false
    },
    queryManagedDepartment: {
        type: Boolean,
        default: true
    },
    enable_checkbox_1: {
        type: Boolean,
        default: true
    }
})

const searchName = ref('')
const refTable = ref(null)
const refTreeDeptSelector = ref()

const tableConfig = reactive({
    tableid: 'drawer_user_select',
    pk: 'id',
    param: { name: '', deptIds: [], queryManagedDepartment: props.queryManagedDepartment },
    show_search: false,
    need_header: true,
    need_init_load: false,
    enable_checkbox: true,
    enable_checkbox_1: props.enable_checkbox_1,
    show_btn_column: false,
    form: {},
    urlGet: getUserListPager,
    columns: ["fullname", 'username', 'deptName'],
    column_widths: {
        fullname: 80,
    }
})

onMounted(() => {
    g.emitter.on('TreeDeptSelector_loading', (status) => {
        setTimeout(() => {
            if (refTable.value) {
                refTable.value.setLoading(status)
            }
        }, 300)
    })
    nextTick(() => {
        refTreeDeptSelector.value.init()
    })
})

const cbDept = (dept) => {
    tableConfig.param.deptIds = [dept.value]
    refTable.value.search()
}

const cbDatas = (action, _data) => {
    const data = toRaw(_data)
    if (action == "after_search") {
        if (props.selectedUsers.length > 0) {
            const ids = props.selectedUsers.map(x => x.id)
            nextTick(() => {
                nextTick(() => {
                    refTable.value.manualCheck(ids)
                })
            })
        } else {
            console.log('no props.selectedUsers')
        }
        refTable.value.setLoading(false)
    } else if (action === "check_row") {
        let newSelected = [...props.selectedUsers]

        if (data['checked']?.length) {
            // 过滤掉已经存在的用户
            const newUsers = data['checked'].filter(newUser =>
                !newSelected.some(existingUser => existingUser.id === newUser.id)
            )
            // 添加新用户
            newSelected = [...newSelected, ...newUsers]
        }

        if (data['unchecked']?.length) {
            const uncheckedIds = data['unchecked'].map(u => u.id)
            newSelected = newSelected.filter(u => !uncheckedIds.includes(u.id))
        }
        emit('update:selectedUsers', newSelected)
    }
}

watch(searchName, () => {
    tableConfig.param.name = searchName.value
    refTable.value.search()
})

const reset = () => {
    refTable.value.manualCheck([])
}

const setDeptId = (deptId) => {
    refTreeDeptSelector.value.setDeptId(deptId)
}

defineExpose({
    reset,
    refTable,
    refTreeDeptSelector,
    setDeptId
})
</script>

<style lang="scss" scoped>
.us_left {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;

    .us_left_header {
        height: 44px;
        display: flex;
        align-items: center;

        .search_name {
            margin-left: 12px;
            width: 240px;
        }
    }

    .us_left_main {
        height: calc(100vh - 220px);
        overflow-y: auto;
    }
}
</style>