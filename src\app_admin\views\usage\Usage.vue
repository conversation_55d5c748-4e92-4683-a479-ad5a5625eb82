<template>
    <el-tabs v-model="activeName" @tab-change="onTabChange">
        <el-tab-pane label="应用使用情况" name="app">
            <TabMain type="APPLICATION" ref="refApplication" />
        </el-tab-pane>
        <el-tab-pane label="个人使用情况" name="user">
            <TabMain type="USER" ref="refUser" />
        </el-tab-pane>
    </el-tabs>
</template>

<script setup>
import TabMain from './TabMain.vue';
const refApplication = ref()
const refUser = ref()
const activeName = ref('app')

const onTabChange = (tab) => {
    if (tab == 'app') {
        refApplication.value.init()
    } else {
        refUser.value.init()
    }
}

onMounted(() => {
    const fn = async () => {
        onTabChange('app')
    }
    fn()
})


defineExpose({
    TabMain,
    activeName,
    refApplication,
    refUser
})
</script>

<style lang="scss">
.el-tabs__item {
    margin-left: 20px;
    padding: 0 5px;
}
</style>
