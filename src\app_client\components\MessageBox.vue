<template>
    <div class="message_box_wrap" v-show="!!showHint">
        <div class="msg_box">
            <InfoIcon v-if="showIcon" />
            <div class="note">
                {{ showHint }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from "vue"
import InfoIcon from "@/app_client/icons/info.vue"

const showHint = ref('')
const showIcon = ref(false);

onMounted(() => {
    g.emitter.on('show_msg_box', ({ message, time, need_icon }) => {
        showHint.value = message;
        showIcon.value = need_icon;
        setTimeout(() => {
            showHint.value = ''
        }, time * 1000)
    })
})

onUnmounted(() => {
    g.emitter.off('show_msg_box');
});

defineExpose({ InfoIcon })
</script>

<style lang="scss">
.message_box_wrap {
    position: fixed;
    display: flex;
    top: 8px;
    width: 100vw;
    justify-content: center;
    z-index: 2;

    .msg_box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        height: 40px;
        background: #FFFFFF;
        box-shadow: 0px 4px 12px 0px rgba(104, 108, 122, 0.16);
        border-radius: 4px;
        padding: 0 18px;

        svg {
            margin-right: 10px;
        }
    }

}
</style>