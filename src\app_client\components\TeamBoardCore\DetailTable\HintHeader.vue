<template>
  <div class="hint_header flex-row">
    <div>{{ trans(column, '') }}</div>
    <div class="vh_refer">
      <el-tooltip class="box-item" effect="dark" :content="type_column_hints[column]" placement="top">
        <el-icon>
          <Question />
        </el-icon>
      </el-tooltip>
    </div>
  </div>
</template>
<script setup>
import Question from "@/icons/question.vue";
import { type_column_hints } from "../misc.js";
import trans from "@/js/lang.js";

const props = defineProps(['column']);

defineExpose({
});
</script>

<style lang="scss">
.hint_header {
  justify-content: space-between;

  .vh_refer {
    margin-top: 2px;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
