<template>
    <div class="sale_theme_wrap flex-row">
        <div class="left">
            <LeftMenu ref="refMenu" @callback="cbLeft"></LeftMenu>
        </div>
        <div class="right">
            <RightList ref="refRight" :type="props.type"></RightList>
        </div>
    </div>
</template>

<script setup>
import LeftMenu from './LeftMenu.vue';
import RightList from './RightList.vue';

const props = defineProps(['type']);
const currData = ref()
const refRight = ref()
const refMenu = ref()

const emit = defineEmits(['callback']);

const cbLeft = (action, data) => {
    currData.value = data;
    refRight.value.init(data)
}

onMounted(() => {
    const fn = async () => {
        await g.saleStore.fetchSysDims()
    }
    fn()
    g.emitter.on('file_uploaded', () => {
        refRight.value && refRight.value.init(currData.value)
    })
    refMenu.value.init()
})

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    props,
    cbLeft,
    LeftMenu,
    refRight,
    refMenu,
    RightList
})

</script>

<style lang="scss">
.sale_theme_wrap {
    height: 100%;

    .left {
        width: 250px;
        height: 100%;
    }

    .right {
        width: calc(100vw - 416px);

        .el-tabs__nav-wrap {
            padding: 0 20px;
        }
    }
}
</style>