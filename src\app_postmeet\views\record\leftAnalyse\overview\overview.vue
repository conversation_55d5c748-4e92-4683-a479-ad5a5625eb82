<template>
    <div class="left_analyse_wrap">
        <laGoal />
        <laAttiSummary />
        <ovTodo />
        <laCompetitorStatus />
    </div>
</template>
<script>
import laGoal from './laGoal';
import laCompetitorStatus from "./laCompetitorStatus.vue"
import laAttiSummary from "./laAttiSummary"
import ovTodo from './ovTodo.vue';

export default {
    components: {
        laGoal, laAttiSummary, ovTodo, laCompetitorStatus
    },
    data() {
        return {
        }
    },
    methods: {
    }
}

</script>

<style lang="scss">
.left_analyse_wrap {}
</style>