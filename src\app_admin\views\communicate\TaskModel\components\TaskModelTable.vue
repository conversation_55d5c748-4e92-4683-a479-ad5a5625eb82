<template>
  <div class="task-model-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>

      <template #col_modelName="{ row }">
        <div class="model-name">{{ row.modelName }}</div>
      </template>

      <template #col_relatedTasks="{ row }">
        <div class="related-tasks">
          <div v-for="(line, index) in (row.relatedTasks || '').split('\n')" :key="index" class="task-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <TaskModelFormDrawer ref="taskModelFormDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getTaskModelList, deleteTaskModel } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import TaskModelFormDrawer from './TaskModelFormDrawer.vue';

const refTable = ref();
const taskModelFormDrawerRef = ref();

const tableConfig = reactive({
  tableid: 'task_model',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模型名称",
  delete_hint_column: 'modelName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["modelName", "relatedTasks", "creator", "createTime"],
  template: ["modelName", "relatedTasks"],
  urlGet: getTaskModelList,
  urlDelete: deleteTaskModel
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  taskModelFormDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  taskModelFormDrawerRef.value.openDrawer(row);
};

const handleDelete = (row) => {
  confirmDelete(row.modelName, (status) => {
    if (status) {
      deleteTaskModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.task-model-table {
  padding: 24px 0;

  .model-name {
    font-weight: 500;
    color: #333;
  }

  .related-tasks {
    max-width: 400px;

    .task-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>