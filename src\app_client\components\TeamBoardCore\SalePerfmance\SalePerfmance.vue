<template>
    <div v-ai-tip="'center'">
        <Overview />
        <SalePerfAvg />
        <SaleEvaluationRanking />
        <PersonPerformance />
        <SaleVsTable />
    </div>
</template>

<script setup>
import Overview from './Overview.vue';
import SalePerfAvg from './SalePerfAvg.vue';
import SaleEvaluationRanking from './SaleEvaluationRanking/SaleEvaluationRanking.vue';
import PersonPerformance from './PersonPerformance.vue';
import SaleVsTable from './SaleVsTable.vue';
import { getStandardSetting } from '@/app_client/tools/api';


onMounted(() => {
    getStandardSetting(g.appStore.user.orgId).then(res => {
        g.clientBoardStore.standardSetting = res.data || {};
    })
})

</script>
<style lang="scss">
.title-line {
    width: 3px;
    height: 16px;
    background: #436bff;
    margin-right: 12px;
}

.title {
    font-weight: 700;
    font-size: 18px;
    color: #262626;
    line-height: 26px;
}

.title-line-wrap {
    align-items: center;

}

.el-empty__image {
    width: 200px;
    height: 130px;
}
</style>