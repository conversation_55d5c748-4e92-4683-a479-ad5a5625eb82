<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="j323--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="icon交互-搜索-交互" transform="translate(-534.000000, -461.000000)">
                <g id="编组-3" transform="translate(209.000000, 453.000000)">
                    <g id="Icon/16px/arrow_left"
                        transform="translate(333.000000, 16.000000) scale(-1, 1) translate(-333.000000, -16.000000) translate(325.000000, 8.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <g id="编组"
                            transform="translate(8.000000, 8.000000) scale(-1, 1) rotate(90.000000) translate(-8.000000, -8.000000) translate(4.000000, 5.500000)"
                            fill="#595959">
                            <path
                                d="M4.33355108,0.0978978879 L4.39569401,0.155374228 L7.87993448,4.22247085 C8.05348322,4.41446408 8.03693488,4.70936624 7.84297273,4.88115328 C7.67056192,5.03385286 7.41600649,5.03787698 7.23967831,4.90204307 L7.17753537,4.84456673 L4.00009405,1.11669075 L0.822464628,4.84456673 C0.648915882,5.03655995 0.35098943,5.05294031 0.157027273,4.88115328 C-0.0153835335,4.72845369 -0.0476155207,4.47847795 0.0692391753,4.28996999 L0.120065523,4.22247085 L3.60430599,0.155374228 C3.6874279,0.0634182569 3.7990817,0.0117473819 3.91417929,0.00174097689 L4.00075619,0.00206981402 C4.08810103,-0.00613100298 4.17736592,0.0100738064 4.25723417,0.0500379301 L4.33355108,0.0978978879 Z"
                                id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>
