<template>
    <Modal ref="refModal" @callback="cbModal" destroy-on-close>
        <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules">
            <el-form-item label="" prop="label">
                <el-input v-model.trim="formData.label" maxlength="50" show-word-limit placeholder="请输入标签名称" />
            </el-form-item>
        </el-form>
    </Modal>
</template>

<script setup>
import { reactive, ref, toRaw } from "vue";
import { createXmTopic, updateXmTopic } from "@/app_admin/tools/api.js"
import { apiHintWrap } from "@/app_admin/tools/utils.js"
import Modal from '@/components/Modal.vue';
const refModal = ref();
const title = ref("");
const refForm = ref("");
const emit = defineEmits(['callback']);

const defaultForm = {
    label: '',
    customer: true,
};

const formData = ref({ ...defaultForm })

const cfg = {
    width: "480px"
}


const _resetForm = () => {
    formData.value = { ...defaultForm };
}

const show_add = (isCustomer) => {
    defaultForm['customer'] = isCustomer
    _resetForm()
    cfg['title'] = "添加主题标签";
    refModal.value.show(cfg);
    nextTick(() => {
        refForm.value.resetFields()
    })
}

const show_edit = (data) => {
    formData.value = { ...data };
    cfg['title'] = "编辑主题标签";
    refModal.value.show(cfg);
}

const cbModal = (action) => {
    if (action == "confirm") {
        btnOK()
    } else if (action == 'cancel') {
        btnCancel()
    }
}

const btnCancel = () => {
    _resetForm()
    refModal.value.hide()
}

const btnOK = () => {
    if (!refForm.value) return
    refForm.value.validate((valid, fields) => {
        if (valid) {
            const data = toRaw(formData.value);
            if (!data.id) {
                apiHintWrap(createXmTopic(data), cfg['title']).then(({ status, resp }) => {
                    if (status) {
                        emit('callback', 'reload', data.label)
                        btnCancel()
                    }
                })
            } else {
                apiHintWrap(updateXmTopic(data.id, data), cfg['title']).then(({ status }) => {
                    if (status) {
                        emit('callback', 'reload', data.label)
                        btnCancel()
                    }
                })
            }
        }
    })
}

const rules = reactive({
    label: [
        { required: true, message: '请输入标签名称', trigger: 'blur' },
    ]
})

defineExpose({ title, show_add, show_edit, cbModal, formData, rules });


</script>

<style lang="scss">
.el-dialog__body {
    padding: 15px 24px 5px 24px;
}
</style>