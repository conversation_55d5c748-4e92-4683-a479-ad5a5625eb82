<template>
  <div class="pain-point-analysis prepare_wrap" v-if="demandList.length > 0 || painPointList.length > 0"
    v-ai-tip="'bottom-right'">
    <PrepareHeader title="痛点与需求分析" :show_btn="false" />
    <div class="analysis-card">
      <div class="section" v-if="demandList.length > 0">
        <div class="section-title">需求</div>
        <div class="section-content">
          <ul>
            <li v-for="(item, index) in demandList" :key="index">
              <div class="item-title">{{ index + 1 }}. {{ item["需求名称"] }}</div>
              <div class="item-desc">{{ item["需求描述"] }}</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="section" v-if="painPointList.length > 0">
        <div class="section-title">痛点</div>
        <div class="section-content">
          <ul>
            <li v-for="(item, index) in painPointList" :key="index">
              <div class="item-title">{{ index + 1 }}. {{ item["痛点名称"] }}</div>
              <div class="item-desc">{{ item["痛点描述"] }}</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PrepareHeader from "./PrepareHeader.vue";
const demandList = ref([]);
const painPointList = ref([]);

const init = (data) => {
  if (data.customerDemand) {
    demandList.value = data.customerDemand["需求列表"] || [];
    painPointList.value = data.customerDemand["痛点列表"] || [];
  }
};

defineExpose({
  init,
});
</script>

<style lang="scss" scoped>
.pain-point-analysis {
  .analysis-card {
    .section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        display: inline-block;
        padding: 2px 6px;
        background: #e6ebfd;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        color: #436bff;
        line-height: 18px;
        margin-bottom: 12px;
      }

      .section-content {
        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .item-title {
              font-size: 14px;
              color: #1d2129;
              font-weight: 500;
              margin-bottom: 4px;
            }

            .item-desc {
              font-size: 14px;
              color: #262626;
              line-height: 1.6;
              padding-left: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
