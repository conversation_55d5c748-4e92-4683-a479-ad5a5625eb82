<template>
  <div :class="`chat_wrap common_chat_wrap ${isFull ? 'full' : 'nofull'}`">
    <div class="center_box custom-scrollbar">
      <div class="msgs_list">
        <component
          :is="getMsgComponent(msg)"
          :data="msg"
          v-for="msg in msgs"
          @callback="cbMsg"
          :key="msg.id"
        >
        </component>
        <div class="btn_re_answer" v-if="!isAnswering && msgs.length > 1">
          <BtnReAnswer ref="refBtnReAnswer" @callback="onReAnswer" />
        </div>
      </div>
      <ChatInput ref="refInput" @callback="cbInput">
        <template #input_top>
          <slot name="input_top" />
        </template>
        <template #input_inside_top>
          <slot name="input_inside_top" />
        </template>
      </ChatInput>
    </div>
  </div>
</template>

<script setup>
import { askMeet<PERSON><PERSON> } from "@/app_postmeet/tools/meetRequest.js";
import MsgMy from "@/components/chatCoze/msg_my.vue";
import MsgAi from "@/components/chatCoze/msg_ai.vue";
import SendIcon from "@/icons/send.vue";
import ChatInput from "@/components/chatCoze/chatInput/chatInput.vue";
import BtnReAnswer from "@/components/chatCoze/BtnReAnswer.vue";
import { getAiConfHistory } from "@/app_postmeet/tools/api.js";
import { meetApi2history } from "@/app_postmeet/tools/tools.js";
import { useRoute } from "vue-router";

const route = useRoute();
const emit = defineEmits(["callback"]);
const isAnswering = ref(false);
const refBtnReAnswer = ref();
const refInput = ref();
const msgs = ref([]);
const isFull = ref(false);
const conversation = ref({});
const config = ref({});

let new_msgs = [];
let updateing = false;
let chatListDom;
let lastQuestion = "";
let msg_type = ""; // 'generate_answer_finish'
let confId = route.params.confId;

const getMsgComponent = (msg) => {
  if (config.value.getMsgComponent && typeof config.value.getMsgComponent == "function") {
    return config.value.getMsgComponent(msg);
  } else {
    return msg.my ? MsgMy : MsgAi;
  }
};

const addHelloMsg = () => {
  msgs.value = [];
  msgs.value.push({
    my: false,
    type: "hello",
    message: "嗨，有关于本次拜访的问题可以咨询我哦～",
  });
};

const setFull = (status) => {
  isFull.value = status;
};

const cbMsg = (action, data) => {
  if (action == "send_input") {
    cbInput("send", data);
  } else if (action == "update_conv") {
    conversation.value = data;
    emit("callback", "update_conv", data);
  } else {
    emit("callback", action, data);
  }
};

const cbInput = (action, data) => {
  if (action === "stop_answer") {
    const lastMsg = msgs.value[msgs.value.length - 1].message;
    if (lastMsg == "") {
      msgs.value[msgs.value.length - 1].message = "已停止回答";
    }
  } else if (action == "clear") {
  } else if (action == "send") {
    msg_type = "";
    if (config.value.onBeforeSend && typeof config.value.onBeforeSend == "function") {
      const result = config.value.onBeforeSend(data);
      if (result.status) {
        data = result.data;
      } else {
        console.log("onBeforeSend false");
        return;
      }
    }

    msgs.value.push({
      my: true,
      message: data,
    });
    msgs.value.push({
      my: false,
      message: ``,
    });
    const param = { info: data };
    _setIsAnswering(true);

    askMeetApi(confId, param).then(([chatId, resp]) => {});
    autoScroll();
  }
};

const _setIsAnswering = (status) => {
  isAnswering.value = status;
  refInput.value.setIsAnswering(status);
  g.emitter.emit("set_answering", status);
};

const onReAnswer = () => {
  if (lastQuestion == "") {
    lastQuestion = msgs.value[msgs.value.length - 2].message.replace("\n", "");
  }
  refInput.value.setQuestion(lastQuestion);
  refInput.value.onSend();
};

const autoScroll = () => {
  nextTick(() => {
    if (chatListDom) {
      chatListDom.scrollTop = chatListDom.scrollHeight;
    }
  });
};

const keyEnter = (event, ctrl) => {
  onSend();
};

const updateMsg = () => {
  if (updateing) {
    return;
  }
  const animateText = (line) => {
    return new Promise((resove) => {
      if (line.length == 0) {
        resove();
      } else {
        let currentIndex = 0;
        const interval = setInterval(() => {
          const new_item = line.slice(currentIndex, currentIndex + 1)[0];
          let line_obj = {};
          if (new_item && new_item.indexOf('{"') > -1) {
            try {
              line_obj = JSON.parse(new_item);
              if (line_obj.msg_type) {
                msg_type = line_obj.msg_type;
              }
            } catch (e) {}
          }
          if (msg_type == "") {
            msgs.value[msgs.value.length - 1].message += new_item;
          } else {
            console.log("error unkown type", msg_type, new_item);
          }
          currentIndex++;
          if (currentIndex >= line.length) {
            clearInterval(interval);
            resove();
          }
        }, 30);
      }
    });
  };

  const fn = async () => {
    while (new_msgs.length > 0) {
      updateing = true;
      await animateText(new_msgs.shift());
      autoScroll();
      if (new_msgs.length == 0) {
        updateing = false;
        setTimeout(() => {
          updateMsg();
        }, 200);
      }
    }
  };
  fn();
};

const setConversation = (data) => {
  conversation.value = data;
  queryHistory();
};

const addListener = () => {
  g.emitter.on("coze_resp", (data) => {
    new_msgs.push(data);
    updateMsg();
  });
  g.emitter.on("coze_resp_done", () => {
    setTimeout(() => {
      _setIsAnswering(false);
    }, 500);
  });
  chatListDom = document.getElementsByClassName("chat_wrap")[0];
  autoScroll();
};

const addMsg = (msg) => {
  msgs.value.push(msg);
};

const getMsgs = () => {
  return msgs.value;
};

const setMsgs = (msgs) => {
  msgs.value = msgs;
};

const updateConfig = (key, value) => {
  config.value[key] = value;
};

const init = () => {
  refInput.value.init({ show_clear: false, show_stop: true });
  addListener();
  getAiConfHistory(confId).then((resp) => {
    if (resp.code == 0 && resp.data.length > 0) {
      msgs.value = meetApi2history(resp.data);
      autoScroll();
    } else {
      addHelloMsg();
    }
  });
};

defineExpose({
  refBtnReAnswer,
  SendIcon,
  config,
  MsgMy,
  MsgAi,
  cbInput,
  keyEnter,
  onReAnswer,
  BtnReAnswer,
  isFull,
  setFull,
  setConversation,
  updateConfig,
  init,
  config,
  getMsgComponent,
  addMsg,
  getMsgs,
  setMsgs,
});
</script>

<style lang="scss">
.chat_wrap {
  display: flex;
  align-content: center;
  flex-wrap: wrap;
  flex-direction: column;
  overflow-y: auto;

  .center_box {
    height: 100%;
    width: 100%;

    .msgs_list {
      overflow-y: auto;
      margin: 0 auto;
      margin: 16px 0;
      height: 438px;

      .msg_line {
        display: flex;
        flex-direction: row;
        margin: 16px 0;

        .mbody {
          margin: 0 10px;
          padding: 1px 20px;
          z-index: 1;
          word-break: break-all;
          word-wrap: break-word;
          color: #262626;
        }
      }
    }

    .btn_stop_box {
      display: flex;
      bottom: 96px;
      width: 100%;
      max-width: 400px;
      justify-content: center;
      align-items: center;
    }

    .meet_minutes_box {
      left: 0;
      height: 73px;
      position: absolute;
    }
  }
}

.chat_footer_commend {
  padding: 34px 0 24px 0;

  .btn_stop_box {
    height: 14px;
  }
}

table {
  width: 100%;
  table-layout: auto;

  thead {
    height: 50px;
    background: #f9fafc;
    box-shadow: inset 0px -1px 0px 0px #e9e9e9;
    border-radius: 4px 4px 0px 0px;

    tr {
      border: 1px solid #e9e9e9;

      th {
        padding: 0 8px;
        height: 22px;
        font-weight: 500;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        text-align: left;
      }
    }
  }

  tbody {
    tr {
      box-shadow: inset 0px -1px 0px 0px #e9e9e9;

      td {
        padding: 0 8px;
        height: 48px;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        text-align: left;
      }
    }
  }
}

.mbody {
  border-radius: 0px 12px 12px 12px;
  text-align: left;
  background-color: #ffffff;

  .thinking {
    font-size: 32px;
    position: relative;
    top: -4px;
  }

  ol,
  ul {
    margin-left: 22px;

    li {
      font-size: 14px;
      line-height: 24px;
    }
  }

  a {
    color: #0052d9;
  }

  p {
    font-size: 14px;
  }

  h1 {
    font-size: 24px;
  }

  h2 {
    font-size: 22px;
  }

  h3 {
    font-size: 20px;
  }

  h4 {
    font-size: 18px;
  }

  h5 {
    font-size: 16px;
  }

  h6 {
    font-size: 14px;
  }
}

.msg_ai {
  justify-content: flex-start;
  position: relative;

  .mbody_tk {
    width: 150px !important;
  }

  .right_top_btn {
    display: none;
    height: 28px;
    background: #fff;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    position: absolute;
    padding: 8px 14px;
    top: -19px;
    right: 56px;
    z-index: 3;
    cursor: pointer;

    svg:hover {
      color: #436bff;
    }
  }

  .question_box {
    display: flex;
    flex-direction: column;
    padding: 21px 12px;

    .q_hint {
      height: 22px;
      font-size: 14px;
      color: #8c8c8c;
      line-height: 22px;
    }

    .q_list {
      display: flex;
      flex-direction: column;

      .q_answer {
        padding: 7px 16px;
        height: auto;
        width: fit-content;
        background: #ffffff;
        border-radius: 8px;
        font-size: 14px;
        color: #757575;
        line-height: 22px;
        margin: 6px 0;
        cursor: pointer;
      }

      .q_answer:hover {
        color: #436bff;
      }
    }
  }
}

.chat_footer {
  width: 400px;
  background-color: unset !important;
  position: absolute;
  bottom: 0;

  .footer_bottom {
    .input_border {
      .chat_send_btn {
        bottom: 27px !important;
      }
    }
  }
}
</style>
