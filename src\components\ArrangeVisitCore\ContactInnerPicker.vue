<template>
    <div class="contact-picker">
        <el-select v-model="selectedContactNames" placeholder="添加内部参会人" style="width: 100%" multiple remote filterable
            :remote-method="remoteMethod" :loading="loading" @change="handleSelectChange"
            popper-class="cp_inner_selector" :persistent="false" :reserve-keyword="false" @focus="onFocus"
            @blur="onBlur" ref="refContactInnerPicker">
            <el-option v-for="item in contacts" :key="item.id" :label="item.name" :value="item.name">
                <div class="rdn_item flex-row">
                    <div class="rdn_icon">
                        <userIcon :size="30" :src="item.imgUrl" :name="item.name" :showname="false" />
                    </div>
                    <div class="rdn_txt flex-column">
                        <span class="rdn_name">{{ item.name }}</span>
                        <span class="rdn_position">{{ item.positionName.substring(0, 10) }}</span>
                        <span class="rdn_dept">{{ item.deptName }}</span>
                    </div>
                </div>
            </el-option>
            <template #footer>
                <div class="cp_footer flex-row" @click="onAddPerson">
                    <div class="fn_icon flex-center">
                        <el-icon :size="16">
                            <Plus />
                        </el-icon>
                    </div>
                    <div class="fn_txt">
                        添加内部参会人
                    </div>
                </div>
            </template>
        </el-select>
        <SuggestedContacts ref="refSuggestedContacts" :multiple="true" @select="handleSuggestionSelect" />
        <DiaAddInnerPerson ref="refDiaAddInnerPerson" @callback="cbAddPerson" />
    </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import DiaAddInnerPerson from './DiaAddInnerPerson.vue';
import SuggestedContacts from './SuggestedContacts.vue'
import { getUserList } from '@/js/api'

const props = defineProps({
    modelValue: Array
})

const emit = defineEmits(['update:modelValue'])

const refContactInnerPicker = ref(null);
const selectedContacts = ref([])
const selectedContactNames = ref([])
const refDiaAddInnerPerson = ref()
const contacts = ref([])
const loading = ref(false)
const refSuggestedContacts = ref()

const onFocus = async () => {
    refSuggestedContacts.value.show()
}

const onBlur = () => {
    setTimeout(() => {
        refSuggestedContacts.value.hide()
    }, 500)
}

const onAddPerson = () => {
    if (g.config.isElectron) {
        g.electronStore.openWin('add_inner_contact')
    } else {
        refDiaAddInnerPerson.value.show()
        refContactInnerPicker.value.blur()
    }
}

const cbAddPerson = (type, data) => {
    if (type === 'add_person') {
        selectedContactNames.value.push(data.name)
        selectedContacts.value.push(data)
        emit('update:modelValue', toRaw(selectedContacts.value))
    }
}

const remoteMethod = (name) => {
    if (name) {
        refSuggestedContacts.value.hide()
        loading.value = true
        getUserList({ name }).then(res => {
            loading.value = false
            if (res.code === 0) {
                // 这里 res.data数组里的fullname字段，改改name字段
                contacts.value = res.data.map(item => ({ ...item, name: item.name || item.fullname }))
            }
        })
    } else {
        contacts.value = []
    }
}

watch(() => props.modelValue, (newVal) => {
    selectedContactNames.value = newVal.map(item => item.name)
    if (refSuggestedContacts.value) {
        refSuggestedContacts.value.updateSelected(toRaw(selectedContactNames.value))
    }
    selectedContacts.value = newVal;
}, { immediate: true, deep: true })

const handleSelectChange = (names) => {
    // 检查是否试图删除自己
    if (!names.includes(g.appStore.user.name)) {
        ElMessage.warning('不能删除自己')
        names.push(g.appStore.user.name)
        selectedContactNames.value = names
        return
    }
    selectedContactNames.value = names
    const newContacts = contacts.value.filter(item => names.includes(item.name))
    let temp = [...toRaw(selectedContacts.value), ...newContacts]
    temp = temp.filter(x => names.includes(x.name)).filter((item, index, self) => self.findIndex(t => t.name === item.name) === index)
    temp.forEach(item => {
        item.userId = item.userId || item.id || ''
        item.attendeeType = 'PARTNER_ATTENDEE'
    })
    selectedContacts.value = temp
    emit('update:modelValue', temp)
}

const handleSuggestionSelect = (item) => {
    selectedContactNames.value.push(item.name)
    item.userId = item.userId || ''
    item.attendeeType = 'PARTNER_ATTENDEE'
    selectedContacts.value.push(item)
    emit('update:modelValue', toRaw(selectedContacts.value))
}

const getSuggestions = () => {
    refSuggestedContacts.value.getSuggestions()
}

defineExpose({
    contacts,
    refSuggestedContacts,
    refDiaAddInnerPerson,
    selectedContacts,
    Plus,
    remoteMethod,
    handleSelectChange,
    getSuggestions
})

</script>

<style lang="scss">
.contact-picker {
    position: relative;

    .cp_hd {
        margin-bottom: 10px;
    }
}

.cp_inner_selector {
    .el-select-dropdown__item {
        height: 79px !important;
        padding: 10px;

        .rdn_icon {
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;

            .user-icon .ui_class {
                border-radius: 4px !important;
            }
        }

        .rdn_txt {
            display: inline-block;
            vertical-align: middle;

            span {
                display: block;
                line-height: 1.4;
            }

            .rdn_name {
                font-family: PingFangSC, PingFang SC;
                font-size: 14px;
                color: #262626;
                line-height: 22px;
            }

            .rdn_position {
                font-size: 12px;
                color: #8C8C8C;
                line-height: 17px;
            }
        }
    }

    .el-select-dropdown__footer {
        .cp_footer {
            padding: 10px 20px;
            cursor: pointer;

            .fn_icon {
                width: 20px;
                height: 20px;
            }

            .fn_txt {
                width: 174px;
                height: 22px;
                font-size: 14px;
                color: #595959;
                line-height: 22px;
                text-align: left;
            }
        }

        .cp_footer:hover {
            background-color: #F5F7FD;
            color: #436BFF;

            .fn_txt {
                color: #436BFF;
            }
        }
    }
}
</style>
