<template>
  <div class="radar_chart_wrap">
    <div v-if="hasData">
      <div class="score-legend" v-if="showLegend">
        <div class="legend-item" v-for="(item, idx) in allData" :key="item.assessmentName">
          <span class="dot" :style="{ backgroundColor: colors21[idx + 1 % colors21.length] }"></span>
          <span class="legend-text">{{ item.assessmentName }}</span>
        </div>
      </div>
      <div ref="refChart" class="Chart"></div>
    </div>
    <div v-else>
      <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)" />
    </div>

  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import echarts from "@/js/echarts"
import { nextTick, ref } from "vue";
import { colors21 } from "@/app_client/tools/const_value.js";


const hasData = ref(false)
const refChart = ref(null)
const allData = ref([]);
const props = defineProps({
  showLegend: {
    type: Boolean,
    default: true
  }
})

const clear = () => {
  // 清除图表的方法
}

const toSeriers = (datas) => {
  return datas.map((item, idx) => {
    return {
      name: item.assessmentName,
      type: 'line',
      smooth: true,
      smoothMonotone: 'x',
      data: item.score.map(x => {
        return {
          value: x,
          name: item.assessmentName
        }
      }),
      color: colors21[(idx + 1) % colors21.length],
      connectNulls: true, // 连接空值
    }
  })

}


const init = (dataType, param) => {
  const reportTimes = param?.reportTimes || []
  allData.value = param?.datas || [];
  hasData.value = allData.value.length > 0 && reportTimes.length > 0;
  if (!hasData.value) {
    return;
  }

  // 计算所有数据的最大值和最小值
  const allScores = allData.value.flatMap(item => item.score).filter(score => score !== null && score !== undefined);

  if (allScores.length === 0) {
    return;
  }

  // 计算最大值和最小值
  const maxScore = Math.max(...allScores);
  const minScore = Math.min(...allScores);

  // 最大值向上取整十数，不超过100
  const yAxisMax = Math.min(100, Math.ceil(maxScore / 10) * 10);

  // 最小值向下取整十数，不小于0
  const yAxisMin = Math.max(0, Math.floor(minScore / 10) * 10);

  const getShowParam = (params) => {
    const typeStr = dataType === 'ABILITY' ? '能力评估' : '任务达成'
    return `${params.name}<br/>${typeStr}：${params.value ? Number(params.value).toFixed(2) : 0}分`;
  }

  const option = {
    xAxis: {
      type: 'category',
      data: reportTimes || [],
      axisLabel: {
        align: 'center',
        color: '#8C8C8C'
      },
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          color: '#8C8C8C'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E5E5',
        },
      },
      axisLabel: {
        color: '#8C8C8C'
      },
      axisLine: {
        lineStyle: {
          color: '#8C8C8C'
        }
      }
    },
    series: toSeriers(allData.value),
    markLine: {
      symbol: 'none',
      data: [
        {
          xAxis: reportTimes[Math.floor(reportTimes.length / 2)],
        }
      ],
      lineStyle: {
        color: '#ff4d4f',
        type: 'dashed',
        width: 2
      },
      label: {
        show: false
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: getShowParam
    },
    grid: {
      left: '40',  // 距离左侧的距离
      right: '5%', // 距离右侧的距离
      top: '24px',  // 距离顶部的距离
      bottom: '30' // 距离底部的距离
    },
  };

  nextTick(() => {
    const myChart = echarts.init(refChart.value, "light")
    myChart.setOption(option)
  })

}

defineExpose({
  init,
  clear
})
</script>

<style lang="scss" scoped>
.radar_chart_wrap {
  padding: 24px;
  background: #FFFFFF;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  box-sizing: border-box;
  margin-top: 24px;

  .Chart {
    height: 260px;
    width: 100%;
  }

  .score-legend {
    display: flex;
    // justify-content: center;
    gap: 24px;

    .legend-item {
      display: flex;
      align-items: center;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .legend-text {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
      }

      .red {
        background-color: #ff4d4f;
      }

      .orange {
        background-color: #fa8c16;
      }

      .blue {
        background-color: #436bff;
      }

      .green {
        background-color: #52c41a;
      }
    }
  }
}
</style>
