<template>
    <div class="summary_message">
        <div class="message_header">
            <img :src="getAssetUrl('yxt_ai.png')" alt="summary" />
            <span>概要</span>
        </div>
        <p>{{ msg.summary_content }}</p>
    </div>
</template>

<script setup>
import { getAssetUrl } from '@/js/utils'

defineProps({
    msg: {
        type: Object,
        required: true
    }
})
</script>

<style scoped lang="scss">
.summary_message {
    width: 284px;
    margin-left: 6px;
    background: linear-gradient(270deg, #F0F8FF 0%, #FFF6FF 100%);
    border-radius: 8px;
    position: relative;
    padding: 12px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        padding: 1px;
        background: linear-gradient(135deg, rgba(109, 27, 255, 1), rgba(29, 101, 255, 1), rgba(0, 188, 230, 1), rgba(217, 217, 217, 1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask-composite: xor;
        pointer-events: none;
    }

    .message_header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }

        span {
            font-size: 14px;
            color: #262626;
            line-height: 20px;
        }
    }

    p {
        margin: 0;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
    }
}
</style>