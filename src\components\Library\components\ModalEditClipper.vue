<template>
  <Modal ref="modalRef" @callback="cbModal" class="edit-clipper-modal" direction="rtl">
    <template #header>
      <div class="vd_title">
        编辑剪辑片段
      </div>
    </template>
    <el-form :model="form" label-width="80px">
      <el-form-item label="名称">
        <el-input ref="nameRef" v-model="form.title" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item label="文件夹名">
        <el-tree-select v-model="form.libId" :data="treeData" placeholder="请选择文件夹" check-strictly
          :render-after-expand="false" :props="defaultTreeProps" node-key="id" :default-expand-all="true" />
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { getClipLibTree } from "@/js/api";
import { updateClipLib } from "@/app_client/tools/api";
import Modal from "@/components/Modal.vue";

const emit = defineEmits(["confirm"]);
const modalRef = ref(null);
const treeData = ref([])
const nameRef = ref(null);
let clipperInfo = {}
const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

const defaultTreeProps = {
  children: "children",
  label: "name",
};

// 获取树形数据
const loadTreeData = async () => {
  try {
    const res = await getClipLibTree(props.isadmin)
    treeData.value = [res]
  } catch (error) {
    console.error('获取文件夹树失败:', error)
  }
}
const form = ref({
  title: "",
  libId: "",
});

const cbModal = async (action, data) => {
  if (action == "confirm") {
    if (!form.value.title) {
      ElMessage.warning("请输入文件夹名称");
      return;
    }
    try {
      const id = clipperInfo.id;
      const { title, libId } = form.value
      const res = await updateClipLib(id, { title, libId });
      ElMessage.success("编辑成功");
      emit("callback", "refresh");
      modalRef.value.hide();
    } catch (error) {
      console.log(error);
    }
  }
};

const show_edit = (data) => {
  clipperInfo = data
  form.value.title = data.title;
  form.value.libId = data.libId;
  loadTreeData()
  const cfg = {
    title: '编辑剪辑片段',
    width: "400px",
  };
  modalRef.value.show(cfg);
  setTimeout(() => {
    nameRef.value.focus();
  }, 100);
};

// 对外暴露打开方法
defineExpose({
  open,
  form,
  nameRef,
  show_edit,
  cbModal,
  modalRef,
});
</script>

<style lang="scss" scoped>
.edit-clipper-modal {
  .el-form {
    padding: 20px;

    .el-form-item {
      margin: 12x 0;
    }
  }
}
</style>
