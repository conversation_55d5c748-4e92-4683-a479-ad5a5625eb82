<template>
  <div class="team_layout_wrap">
    <el-affix :offset="0">
      <div class="dept_tree_container">
        <div class="dt_left">
          <el-icon class="back-btn" @click="goBack" v-if="reportDateTitle">
            <ArrowLeft />
          </el-icon>
          <TreeDeptSelector ref="refTreeDeptSelector" @callback="onCallback" :disabledTopDept="true" />
          <div class="update_time" v-if="reportDateTitle">
            {{ reportDateTitle }}
          </div>
          <div class="update_time" v-else-if="updateTime">
            数据更新时间：{{ updateTime }}
          </div>
        </div>
        <div class="dt_right" v-show="isShowTimeRange">
          <PeriodSelector v-model="periodType" @change="handleTimeRangeChange" />
        </div>
      </div>
    </el-affix>
    <div class="team_content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft } from "@element-plus/icons-vue";
import TreeDeptSelector from "@/components/TreeDeptSelector.vue";
import { getUrlParam } from "@/js/utils";
import PeriodSelector from '@/app_client/components/PeriodSelector.vue';
import { getDefaultPeriodType } from '@/app_client/tools/utils.js';
const emit = defineEmits(["callback"]);
const refTreeDeptSelector = ref();
const periodType = ref(getDefaultPeriodType());
const updateTime = ref("");

const props = defineProps({
  isShowTimeRange: {
    type: Boolean,
    default: false,
  },
})

const reportDateTitle = computed(() => {
  return g.clientBoardStore.reportDateTitle
})
const handleTimeRangeChange = (value) => {
  g.clientBoardStore.periodType = value;
};
const deptId = getUrlParam('deptId')

const onCallback = (value) => {
  emit("callback", "updateDept", value);
};
const goBack = () => {
  g.clientBoardStore.reportDateTitle = ''
  g.router.push({ path: `/client/team/board` });
};

onMounted(() => {
  refTreeDeptSelector.value.init();
  if (deptId) {
    refTreeDeptSelector.value.setDeptId(deptId)
  }
  g.emitter.on("update_team_report_time", (time) => {
    updateTime.value = time;
  });
});

defineExpose({
  refTreeDeptSelector,
  updateTime,
  onCallback,
});
</script>

<style lang="scss">
.team_layout_wrap {
  width: 100%;

  .dept_tree_container {
    height: 44px;
    background-color: #fff;
    padding: 6px 24px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;

    .dt_left {
      display: flex;
      align-items: center;
      height: 22px;
      font-size: 14px;
      color: #8C8C8C;
      line-height: 22px;

      .back-btn {
        cursor: pointer;
        margin-right: 12px;
      }
    }

    .update_time {
      height: 22px;
      margin-left: 12px;
      font-size: 14px;
      color: #8C8C8C;
      line-height: 22px;
    }
  }

  .team_content {
    width: 97%;
    padding: 24px;
    background: #f7f9fe;
  }
}
</style>
