<template>
    <div class="customer_type_item" v-show="labels.length > 0">
        <el-select v-model="selected" clearable collapse-tags placeholder="客户类型" popper-class="custom_select_label"
            style="width: 144px" @change="onChange">
            <el-option v-for="item in labels" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
    </div>
</template>

<script setup>
import { onMounted } from "vue";
import { getCustomerTypeList } from "@/js/api.js";

const props = defineProps({
    modelValue: {
        type: String,
        required: true
    },
})


const emit = defineEmits(['update:modelValue', 'reload'])
const selected = ref(props.modelValue);
const labels = ref([])

watch(() => props.modelValue, (newValue) => {
    selected.value = newValue;
}, { immediate: true })

const search = () => {
    getCustomerTypeList({ status: 1 }).then(resp => {
        if (resp.code == 0) {
            labels.value = resp.data;
        }
    })
}


const onChange = () => {
    emit('update:modelValue', selected.value);
    emit('reload', '');
}

onMounted(() => {
    search()
})

defineExpose({ selected, labels, search })
</script>

<style lang="scss">
.customer_type_item {
    margin-right: 12px;

    .el-checkbox {
        display: flex;
        height: unset;
    }
}
</style>