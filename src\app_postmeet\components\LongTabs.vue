<template>
    <div :class="`long_tabs_wrap ${need_btns ? 'hasb' : 'nob'}`" ref="refTabWrap">
        <div class="cicon cleft" @click="onLeft" v-if="need_btns">
            <LeftIcon />
        </div>
        <div :class="`com_center ${need_btns ? 'hasbtn' : 'nobtn'}`" ref="refCenter">
            <el-radio-group v-model="choosed" ref="refRadio" @input="onClick">
                <el-radio-button :label="item.label" :value="item.label" v-for="item in tags" :key="item.id" />
            </el-radio-group>
        </div>
        <div class="cicon cright" @click="onRight" v-if="need_btns">
            <RightIcon />
        </div>
    </div>
</template>

<script>
import LeftIcon from "@/app_postmeet/icons/left_array.vue"
import RightIcon from "@/app_postmeet/icons/right_array.vue"


export default {
    components: { LeftIcon, RightIcon },
    data() {
        return {
            choosed: '',
            need_btns: false,
            tags: [],
        }
    },
    methods: {
        init(data) {
            this.tags = data;
            this.$nextTick(() => {
                this.$nextTick(() => {
                    const width = this.$refs.refRadio.$el.scrollWidth;
                    const wrap_width = document.body.clientWidth - 64;
                    // const wrap_width = this.$refs.refTabWrap.scrollWidth;
                    this.need_btns = width > wrap_width - 70;
                })
            })
            if (this.tags.length > 0) {
                const item = this.tags[0]
                this.choosed = item.label
                this.$emit('callback', item);
            }
        },
        onClick() {
            const currItem = this.tags.find(x => x.label === this.choosed);
            this.$emit('callback', currItem);
        },
        onLeft() {
            const target = this.$refs.refCenter.scrollLeft - 100;
            this.$refs.refCenter.scroll(target, 0);
        },
        onRight() {
            const target = this.$refs.refCenter.scrollLeft + 100;
            this.$refs.refCenter.scroll(target, 0);
        }
    }
}
</script>


<style lang='scss'>
.long_tabs_wrap {
    margin: 16px 0;
    width: calc(100vw - 73px);
    display: flex;
    flex-direction: row;
    position: relative;

    .cicon {
        width: 32px;
        height: 32px;
        background: #FFFFFF;

        border: 1px solid #D9D9D9;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        cursor: pointer;
        user-select: none;
        z-index: 2;
    }

    .cleft {
        left: 0;
        border-radius: 4px 0 0 4px;
    }

    .cright {
        right: 0;
        border-radius: 0 4px 4px 0;
    }

    .com_center {
        overflow-x: auto;
        display: flex;
        justify-content: center;

        .el-radio-group {
            display: flex;

            .el-radio-button__inner {
                height: 34px;
                padding: 10px 20px;
                user-select: none;
            }
        }
    }

    .com_center::-webkit-scrollbar {
        display: none;
    }

    .hasbtn {
        width: calc(100% - 35px);

        .el-radio-group {
            width: calc(100% - 35px);
        }

    }

    .nobtn {
        justify-content: left;
    }
}

.hasb {
    justify-content: center;
}

.nob {
    justify-content: left;
}
</style>