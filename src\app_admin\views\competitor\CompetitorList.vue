<template>
    <div class="competitor-list-wrap" v-loading="loading" element-loading-text="正在加载...">
        <div class="content-card">
            <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
                <template #_header_left="{ row }">
                    <div class="action-bar">
                        <el-button type="primary" @click="handleAdd">
                            <el-icon>
                                <Plus />
                            </el-icon>
                            添加竞争对手
                        </el-button>
                        <el-button @click="handleBatchImport">
                            <el-icon>
                                <Upload />
                            </el-icon>
                            批量导入
                        </el-button>
                        <el-button @click="downloadTemplate" type="info" plain>
                            <el-icon>
                                <Download />
                            </el-icon>
                            下载模板
                        </el-button>
                        <input type="file" ref="fileInput" @change="onFileChange" style="display: none;"
                            accept=".xlsx,.xls" />
                    </div>
                </template>

                <template #col_commonName="{ row }">
                    <span class="competitor-name">{{ row.commonName }}</span>
                </template>
                <template #col_companyName="{ row }">
                    <span>{{ row.companyName || '-' }}</span>
                </template>
                <template #col_alternativeName="{ row }">
                    <div class="alternative-names">
                        <template v-if="row.alternativeName">
                            <el-tag v-for="name in row.alternativeName.split(',').filter(x => x.trim())" :key="name"
                                size="small" type="info" class="name-tag">
                                {{ name.trim() }}
                            </el-tag>
                        </template>
                        <span v-else class="no-data">-</span>
                    </div>
                </template>
                <template #col_createdTime="{ row }">
                    <span>{{ formatDate(row.createdTime) }}</span>
                </template>
            </MyTable>
        </div>

        <CompetitorEditDialog ref="refEditDialog" @callback="onDialogCallback" />
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Plus, Upload, Download } from '@element-plus/icons-vue';
import MyTable from '@/components/Table.vue';
import CompetitorEditDialog from './CompetitorEditDialog.vue';
import { competitorApi } from '@/app_admin/api';

const refTable = ref();
const refEditDialog = ref();
const fileInput = ref();
const loading = ref(false);

const tableConfig = reactive({
    tableid: 'competitor_list',
    param: { pageSize: 50 },
    need_init_load: true,
    show_search: true,
    need_header: true,
    show_btn_add: false,
    columns: ['commonName', 'companyName', 'alternativeName', 'createdTime'],
    show_link_column: true,
    show_link_edit: true,
    show_link_delete: true,
    show_link_view: false,
    delete_hint_column: 'commonName',
    urlGet: (params) => {
        return new Promise(async (resolve) => {
            try {
                const resp = await competitorApi.getCompetitor(params);
                resolve({
                    code: 0,
                    data: {
                        datas: resp.data || [],
                        totalNum: resp.data?.length || 0
                    }
                });
            } catch (error) {
                resolve({
                    code: -1,
                    message: error.message || '获取数据失败'
                });
            }
        });
    },
    urlDelete: ({ id }) => {
        return competitorApi.deleteCompetitor(id);
    }
});

// 格式化日期
const formatDate = (dateStr) => {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

// 添加竞争对手
const handleAdd = () => {
    refEditDialog.value.show_add();
};

// 批量导入
const handleBatchImport = () => {
    fileInput.value.click();
};

// 下载模板
const downloadTemplate = async () => {
    try {
        loading.value = true;
        const param = {
            filename: "批量导入竞争对手模板",
        };
        await competitorApi.downloadCompetitorTemplate(param);
    } catch (error) {
        ElMessage.error("模板下载失败，请重试");
        console.error('Download template failed:', error);
    } finally {
        loading.value = false;
    }
};

// 文件上传处理
const onFileChange = () => {
    const file = fileInput.value.files[0];
    if (!file) return;

    loading.value = true;
    const formData = new FormData();
    formData.append('file', file);

    competitorApi.batchUploadCompetitor(formData, '', onUploadSuccess, onUploadFail);
};

const onUploadSuccess = (data) => {
    loading.value = false;
    clearFileInput();
    if (data.code == 0) {
        if (data.data && data.data.length === 0) {
            ElMessage.success("批量导入成功");
            refTable.value.search();
        } else {
            const tip = data.data?.join('<br>') || '导入完成，但有部分数据存在问题';
            ElMessage({
                dangerouslyUseHTMLString: true,
                message: tip,
                type: 'warning',
            });
            refTable.value.search();
        }
    } else {
        ElMessage.error(data.message || '导入失败');
    }
};

const onUploadFail = (error) => {
    loading.value = false;
    clearFileInput();
    ElMessage.error("Excel解析失败，请检查文件格式后重新上传");
    console.error('Upload failed:', error);
};

const clearFileInput = () => {
    if (fileInput.value) {
        fileInput.value.value = '';
    }
};

// 表格回调处理
const handleTableCallback = (action, data) => {
    if (action === 'init_edit') {
        refEditDialog.value.show_edit(data);
    } else if (action === 'delete_success') {
        ElMessage.success('删除成功');
        refTable.value.search();
    }
};

// 对话框回调处理
const onDialogCallback = (action) => {
    if (action === 'reload') {
        refTable.value.search();
    }
};
</script>

<style lang="scss">
.competitor-list-wrap {
    padding: 24px;

    .action-bar {
        margin-bottom: 16px;
        display: flex;
        gap: 12px;

        .el-button {
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }

    .content-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .table_box {
            height: calc(100vh - 180px);
            overflow: auto;
        }
    }

    .competitor-name {
        font-weight: 500;
        color: #303133;
    }

    .alternative-names {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .name-tag {
            margin: 0;
        }

        .no-data {
            color: #c0c4cc;
        }
    }
}
</style>