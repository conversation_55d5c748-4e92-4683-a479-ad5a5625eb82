<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" @select="handleSelect">
    <TopMenu></TopMenu>
    <div class="flex-grow" />
    <el-sub-menu index="2">
      <template #title>
        <userIcon :name="user && user.name" :photo="user && user.photo" :showname="true"></userIcon>
      </template>
      <el-menu-item index="go_client">前往用户端</el-menu-item>
      <el-menu-item index="logout">退出登录</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import userIcon from "@/components/userIcon.vue";
import TopMenu from "@/app_admin/components/TopMenu.vue";
const user = ref({ name: "", photo: "" });
const emit = defineEmits(["callback"]);

const handleSelect = (key, keyPath) => {
  if (key == "logout") {
    g.appStore.logout();
  } else if (key == "go_client") {
    g.router.push({ path: g.cv.defaultClientHomePage });
  }
};

onMounted(() => {
  if (g.appStore.isLogin) {
    user.value = g.appStore.user;
  } else {
    console.log("logout2");
    g.appStore.logout();
  }
});

defineExpose({
  handleSelect,
  user,
  userIcon,
});
</script>

<style scoped lang="scss">
.el-menu-demo {
  padding-right: 36px;
}
</style>
