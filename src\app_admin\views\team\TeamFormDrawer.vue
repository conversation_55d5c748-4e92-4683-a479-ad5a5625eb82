<template>
    <el-drawer v-model="visible" :title="drawerTitle" size="500px" :close-on-click-modal="false"
        class="team_form_drawer">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" label-position="left"
            class="drawer-form">
            <el-form-item label="团队名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入团队名称" />
            </el-form-item>
            <el-form-item label="团队负责人" prop="leader">
                <DrawerSelectMixed ref="refLoader" @callback="onOwnerSelect" type="user" />
            </el-form-item>
            <el-form-item label="管理范围" prop="scope">
                <DrawerSelectMixed ref="refScope" @callback="onScopeSelectDept" type="dept" :mustSubDept="true" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="drawer-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                    确定
                </el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { addTeam, updateTeam } from '@/app_admin/tools/api.js'
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue"
import { nextTick } from 'vue'

const refLoader = ref(null)
const refScope = ref(null)
const drawerTitle = ref('')
const emit = defineEmits(['callback'])

const visible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const _defautlData = {
    id: '',
    name: '',
    leader: [],
    scope: []
}

const formData = ref({ ..._defautlData })

const rules = {
    name: [{ required: true, message: '请输入团队名称', trigger: 'blur' }]
}

const resetForm = () => {
    formData.value = { ..._defautlData }
    formRef.value?.resetFields()
}

const _add = (data) => {
    addTeam(data).then(res => {
        if (res.code === 0) {
            ElMessage.success('添加成功')
            emit('callback', 'reload')
            visible.value = false
        } else {
            ElMessage.error(res.msg || '添加失败')
        }
        submitLoading.value = false
    }).catch(err => {
        ElMessage.error('添加失败')
        console.error(err)
        submitLoading.value = false
    })
}

const _update = (data) => {
    updateTeam(data.id, data).then(res => {
        if (res.code === 0) {
            ElMessage.success('更新成功')
            emit('callback', 'reload')
            visible.value = false
        } else {
            ElMessage.error(res.msg || '更新失败')
        }
        submitLoading.value = false
    }).catch(err => {
        ElMessage.error('更新失败')
        console.error(err)
        submitLoading.value = false
    })
}


const handleSubmit = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            submitLoading.value = true
            const params = {
                name: formData.value.name,
                members: [...formData.value.leader, ...formData.value.scope],
            }
            if (formData.value.id) {
                params.id = formData.value.id
                _update(params)
            } else {
                _add(params)
            }
        }
    })
}

const onScopeSelectDept = (action, data) => {

    formData.value.scope = [
        ...data.users.map(x => ({ targetId: x.id, targetType: 1 })),
        ...data.depts.map(x => ({ targetId: x.value, targetType: 2 }))
    ]
}

const onOwnerSelect = (action, data) => {
    formData.value.leader = [
        ...data.users.map(x => ({ targetId: x.id, targetType: 0 }))
    ]
}

const show_add = () => {
    drawerTitle.value = '新增团队'
    resetForm()
    visible.value = true
    nextTick(() => {
        const defData = {
            users: [],
            depts: []
        }
        refLoader.value?.init(defData)
        refScope.value?.init(defData)
    })
}

const show_edit = (data) => {
    drawerTitle.value = '编辑团队'
    formData.value = { ..._defautlData, ...data }
    const leaders = {
        users: [...data.members.filter(x => x.targetType === 0).map(x => ({ id: x.targetId, fullname: x.targetName }))],
        depts: []
    }
    const scopes = {
        users: [...data.members.filter(x => x.targetType === 1).map(x => ({ id: x.targetId, fullname: x.targetName }))],
        depts: [...data.members.filter(x => x.targetType === 2).map(x => ({ value: x.targetId, label: x.targetName }))]
    }
    visible.value = true
    nextTick(() => {
        onScopeSelectDept(null, scopes)
        onOwnerSelect(null, leaders)
        refLoader.value?.init(leaders)
        refScope.value?.init(scopes)
    })
}

defineExpose({
    show_add,
    show_edit
})

</script>

<style lang="scss">
.team_form_drawer {
    .el-drawer__header {
        margin-bottom: 0;
        padding: 0 12px;

        .el-drawer__title {
            margin-left: 12px;
        }

        .el-drawer__close-btn {
            margin-right: 12px;
        }
    }

    .drawer-form {
        padding: 20px;

        :deep(.el-form-item__label) {
            justify-content: flex-start;
        }
    }

    .drawer-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 0 20px 20px;
    }
}
</style>