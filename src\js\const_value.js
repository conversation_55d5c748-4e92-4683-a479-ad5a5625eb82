export const keyPre = 'yxtlm_';

export const ConstValue = {
    keyPre,
    keySettings: 'settings',
    keyUserInfo: 'userInfo',
    keyMenuPermission: 'menuPermission',
    keyOrgInfo: 'orgInfo',
    keyToken: 'token',
    keySkipVersion: 'skipVersion',
    keyLastUpdateRemindTime: 'lastUpdateRemindTime',
    defaultElectronHomePage: '/electron/home',
    defaultClientHomePage: '/client/router',
    keyLastCheckTokenDate: 'lastCheckTokenDate',
    keyFuncStatusMap: 'funcStatusMap',
    keyNotifyList: 'notifyList',
    keyMeetOptions: 'meetOptions',
    keyYxtUserInfo: 'yxtUserInfo',
    keyClientId: 'clientId',
    keyMainScreenInfo: 'mainScreenInfo',
    keyClinetDefaultOpeneds: 'ClinetDefaultOpeneds',
    keyInmeetCompanyName: 'inmeetCompanyName',
    keyMeetPlan: 'meetPlan',
    keyCardConferenceId: 'cardConferenceId',
    keyProdApihost: 'prodApihost',
    keyMeetingInfo: 'meetingInfo',
    keyMeetTimer: 'meetTimer',
    keyMeetChatList: 'meetChatList',
}

export const FieldTypeList = [
    "String",
    "TextArea",
    "DateTime",
    "Date",
    "Integer",
    "Select",
    "MultiSelect"
]

export const systemIdTypeMap = {
    205: 'salesAbilityAssesses',
    206: 'taskCompleteAssesses'
}