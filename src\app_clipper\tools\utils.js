export const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString()}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

export const getItemStyle = (item, padding) => {
  const duration = g.clipperStore.getPageInfo("duration");
  const containerWidth = g.clipperStore.getPageInfo("containerWidth");
  const startPosition = (item.startSeconds / duration) * containerWidth;
  const width =
    ((item.endSeconds - item.startSeconds) / duration) * containerWidth -
    2 * padding;
  return {
    position: "absolute",
    left: `${Math.max(0, startPosition)}px`, // 防止负数，导致超出左侧startPosition}px`,
    width: `${Math.max(width, 0)}px`,
  };
};

