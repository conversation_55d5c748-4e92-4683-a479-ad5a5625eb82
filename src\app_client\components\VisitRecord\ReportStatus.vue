<template>
  <div class="visit_filter_item">
    <el-select v-model="localValue" multiple collapse-tags placeholder="处理状态" popper-class="vf-header"
      style="width: 182px">
      <el-option v-for="item in options" :key="item.type" :label="item.description" :value="item.type" />
    </el-select>
  </div>
</template>

<script setup>
import { removeURLParams } from "@/js/utils.js"
const props = defineProps({
  value: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["update:value", "reload"]);
const localValue = ref(props.value);
const options = ref([]);

watch(localValue, (newValue) => {
  removeURLParams(["abilityStatus"]);
  emit("update:value", toRaw(newValue));
  emit("reload", "");
});

const updateValue = (v) => {
  localValue.value = v;
};

const query = () => {
  options.value = [
    { type: "RECORD_FAIL", description: "处理失败" },
    { type: "IN_PROCESS", description: "文件处理中" },
    { type: "LITTLE_CONTENT", description: "会话字幕量不足" },
    { type: "NEED_ANNOTATION", description: "参会人身份未标注" },
    { type: "NEED_GENERATE", description: "生成按钮未点击" },
    { type: "FAIL", description: "生成失败" },
    { type: "SUCCESS", description: "生成成功" },
  ];
}

onMounted(() => {
  query();
})

defineExpose({
  options,
  updateValue,
})
</script>

<style lang="scss">
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
</style>