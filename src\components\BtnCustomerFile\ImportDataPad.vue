<template>
    <div class="import-data-pad">
        <div class="import-steps">
            <div class="step">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">点击"下载"按钮下载需要导入的模板文件。</div>
                    <div class="step-desc">请仔细阅读模板中的说明，并按要求将数据填写到对应的单元格中。</div>
                    <el-button type="primary" @click="downloadTemplate">下载</el-button>
                </div>
            </div>
            <div class="step-line"></div>
            <div class="step step2">
                <div class="step-number"></div>
                <div class="step-content">
                    <div class="step-title">完成文件的填写后，点击或将文件拖拽到上传区域进行上传。</div>
                    <div class="step-desc">系统会校验填写数据的正确性，并提示错误的数据；在检验无错误数据的情况下，系统将数据导入到系统中。</div>

                    <el-upload v-if="!uploadStatus" v-loading="isLoading" class="upload-area" drag
                        :http-request="customUpload" :accept="'.xlsx,.xls'" :before-upload="beforeUpload"
                        :on-success="handleUploadSuccess" :on-error="handleUploadError" :show-file-list="false">
                        <div class="upload-content">
                            <el-icon><upload-filled /></el-icon>
                            <div class="upload-text">点击或将文件拖拽到这里上传</div>
                            <div class="upload-tip">支持扩展名：.xlsx .xls</div>
                        </div>
                    </el-upload>

                    <div v-if="uploadStatus" class="upload-progress">
                        <el-steps :active="activeStep" finish-status="success" process-status="process"
                            direction="vertical">
                            <el-step :status="uploadStatus.uploadStepStatus">
                                <template #title>
                                    <div class="step-progress-title">
                                        <span>{{ uploadStatus.fileName || '文件上传' }}</span>
                                        <el-button v-if="uploadStatus.uploadStepStatus !== 'process'" type="primary"
                                            @click="resetUpload" class="resupload-button">重新上传</el-button>
                                    </div>
                                </template>
                                <template #description>
                                    <span v-if="uploadStatus.uploadStepStatus === 'success'" class="success-text">
                                        上传成功</span>
                                    <span v-if="uploadStatus.uploadStepStatus === 'error'">上传失败: {{
                                        uploadStatus.uploadMessage || '未知错误' }}</span>
                                    <span v-if="uploadStatus.uploadStepStatus === 'process'">正在上传...</span>
                                </template>
                            </el-step>

                            <el-step :status="uploadStatus.checkStepStatus"
                                :title="uploadStatus.checkStepStatus !== 'wait' ? '数据检查完成' : '等待数据检查'">
                                <template #description>
                                    <span v-if="uploadStatus.checkStepStatus === 'success'">检查通过</span>
                                    <span v-if="uploadStatus.checkStepStatus === 'error'">检查不通过: {{
                                        uploadStatus.checkMessage || '校验失败' }}</span>
                                    <span v-if="uploadStatus.checkStepStatus === 'process'">正在检查...</span>
                                </template>
                            </el-step>

                            <el-step :status="uploadStatus.importStepStatus">
                                <template #title>
                                    <div class="step_import_title flex-row">
                                        <div v-if="uploadStatus.importStepStatus == 'wait'">等待导入结果</div>
                                        <div v-else class="flex-row">
                                            <span class="success_num">{{ uploadStatus.successCount }}</span>条导入成功，<span
                                                class="fail_num">{{
                                                    uploadStatus.failCount
                                                }}</span>条导入失败
                                        </div>
                                    </div>
                                </template>
                                <template #description>
                                    <span v-if="uploadStatus.importStepStatus === 'success'">导入完成</span>
                                    <span
                                        v-if="uploadStatus.importStepStatus === 'success' && uploadStatus.failCount > 0"
                                        class="download-error-data" @click="downloadErrorData">下载失败数据</span>
                                    <span v-if="uploadStatus.importStepStatus === 'error'">导入失败: 导入过程中发生错误</span>
                                    <span v-if="uploadStatus.importStepStatus === 'process'">正在导入...</span>
                                </template>
                            </el-step>
                        </el-steps>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { createFile, downloadImportTemplate, getImportProcess, importCustomer } from '@/js/api'
import { downloadFile } from '@/js/utils'

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})

const isLoading = ref(false)
let currentQueryCount = 0;
const macQueryCount = 20;
const timer = ref(null)
const uploadUrlInfo = ref({
    fileId: " ",
    presignedUrl: ""
})
// 'wait' | 'process' | 'finish' | 'error' | 'success'
const testStatus = {
    fileName: "TEST.xlsx",
    uploadStepStatus: 'success',
    uploadMessage: '',
    checkStepStatus: 'success',
    checkMessage: '',
    importStepStatus: 'success',
    importMessage: '',
    successCount: 3,
    failCount: 1,
    fileUrl: ''
}

const uploadStatus = ref()
const activeStep = ref(0)

const downloadTemplate = () => {
    const param = {
        filename: "客户导入模板文件",
    }
    downloadImportTemplate(param)
}

const resetUpload = () => {
    uploadStatus.value = null
    activeStep.value = 0
    isLoading.value = false
}

const customUpload = async (options) => {
    try {
        const file = options.file
        const response = await fetch(uploadUrlInfo.value.presignedUrl, {
            method: 'PUT',
            body: file,
            headers: {
                'Content-Type': 'application/octet-stream'
            }
        })
        console.log("customUpload response", JSON.stringify(response))
        if (!response.ok) {
            throw new Error('上传失败')
        }

        // 上传成功后调用 onSuccess
        options.onSuccess({
            code: 0,
            data: {
                checkPassed: true,
            }
        })
    } catch (error) {
        options.onError(error)
    }
}

const downloadErrorData = () => {
    console.log('downloadErrorData')
    downloadFile(uploadStatus.value.fileUrl, '客户导入失败数据')
}

const beforeUpload = async (file) => {
    resetUpload()
    isLoading.value = true

    const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase())
    if (!isExcel) {
        ElMessage.error('只能上传 Excel 文件!')
        isLoading.value = false
        return false
    }
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
        ElMessage.error('文件大小不能超过 5MB!')
        isLoading.value = false
        return false
    }

    uploadStatus.value = {
        fileName: file.name,
        uploadStepStatus: 'process',
        uploadMessage: '',
        checkStepStatus: 'wait',
        checkMessage: '',
        importStepStatus: 'wait',
        importMessage: '',
        successCount: 0,
        failCount: 0,
        fileUrl: ''
    }
    activeStep.value = 0

    const param = {
        "module": "import_customer",
        "fileNameExt": file.name.split('.').pop(),
        "isPrivate": 1,
        "bizId": ""
    }
    const res = await createFile(param)
    console.log('createFile res', JSON.stringify(res))
    if (res.code === 0) {
        uploadUrlInfo.value = res.data
        return true
    } else {
        ElMessage.error(res.message)
        isLoading.value = false
        return false
    }
}

const _beginCheckUploadStatus = () => {
    currentQueryCount = 0;
    const _fn = () => {
        if (currentQueryCount >= macQueryCount) {
            uploadStatus.value.importStepStatus = 'error'
            uploadStatus.value.importMessage = '导入数据处理超时'
            activeStep.value = 3
            !!timer.value && clearTimeout(timer.value)
            return
        }
        currentQueryCount++;
        getImportProcess(uploadUrlInfo.value.fileId).then(res => {
            console.log('getImportProcess res', JSON.stringify(res))
            if (res.code === 0) {
                // 处理状态 0处理中，10检查完成 11 格式校验失败 20完成 30处理失败:
                //   {
                // 	"code":0,
                // 	"data":{
                // 		"failCount":1,
                // 		"fileUrl":"https://",
                // 		"status":20,
                // 		"successCount":0
                // 	},
                // 	"message":"success"
                // }
                // 处理状态 0处理中，10检查完成 11 格式校验失败 20完成 30处理失败:
                const status = res.data?.status || 0
                if (status == 0 || status == 10) {
                    timer.value = setTimeout(() => {
                        _fn()
                    }, 3000)
                } else if (status == 10) {
                    uploadStatus.value.checkStepStatus = 'success'
                    uploadStatus.value.importMessage = ''
                    activeStep.value = 2;
                } else if (status == 11) {
                    uploadStatus.value.checkStepStatus = 'error'
                    uploadStatus.value.importMessage = '格式校验失败'
                    activeStep.value = 2
                } else if (status == 20) {
                    uploadStatus.value.failCount = res.data.failCount || 0
                    uploadStatus.value.successCount = res.data.successCount || 0
                    uploadStatus.value.importStepStatus = 'success'
                    uploadStatus.value.importMessage = `导入成功${res.data.successCount}条，导入失败${res.data.failCount}条`
                    uploadStatus.value.fileUrl = res.data.fileUrl
                    activeStep.value = 3
                } else if (status == 30) {
                    uploadStatus.value.failCount = 0;
                    uploadStatus.value.successCount = 0;
                    uploadStatus.value.importStepStatus = 'error'
                    uploadStatus.value.importMessage = '导入失败'
                    activeStep.value = 3
                }

                if (activeStep.value == 3) {
                    !!timer.value && clearTimeout(timer.value)
                }
            } else {
                ElMessage.error(res.message)
            }
        })
    }
    _fn()
}

const _beginImport = () => {
    return new Promise((resolve, reject) => {
        const param = {
            "fileId": uploadUrlInfo.value.fileId,
            "source": props.isAdmin ? 0 : 1
        }
        importCustomer(param).then(res => {
            console.log('importCustomer res', JSON.stringify(res))
            resolve(res)
        }).catch(err => {
            reject(err)
        })
    })
}

const _onImportError = (errorMessage) => {
    uploadStatus.value.checkStepStatus = 'error'
    uploadStatus.value.checkMessage = errorMessage
    uploadStatus.value.importStepStatus = 'wait'
    ElMessage.error(errorMessage)
}


const handleUploadSuccess = (response, uploadFile) => {
    console.log("handleUploadSuccess", response)
    // {code:0,data:{checkPassed:true,importPassed:true,successCount:0}}
    if (!response) return

    isLoading.value = false

    if (!uploadStatus.value) return

    uploadStatus.value.uploadStepStatus = 'success'
    activeStep.value = 1

    if (response && response.code === 0 && response.data) {
        const data = response.data
        uploadStatus.value.checkStepStatus = data.checkPassed ? 'success' : 'error'
        uploadStatus.value.checkMessage = data.checkPassed ? '' : '校验失败'
        if (data.checkPassed) {
            activeStep.value = 2
            _beginImport().then(res => {
                if (res.code === 0) {
                    _beginCheckUploadStatus()
                } else {
                    _onImportError(response?.message || '导入处理失败')
                }
            }).catch(err => {
                _onImportError(response?.message || '导入处理失败')
            })
        } else {
            uploadStatus.value.importStepStatus = 'wait'
            ElMessage.error(uploadStatus.value.checkMessage || '数据检查失败')
            return
        }
    } else {
        uploadStatus.value.uploadStepStatus = 'error'
        uploadStatus.value.uploadMessage = response?.message || '上传失败'
        activeStep.value = 1
    }
}

const handleUploadError = (error, uploadFile) => {
    isLoading.value = false
    if (!uploadStatus.value) {
        uploadStatus.value = { fileName: uploadFile?.name || '未知文件', uploadStepStatus: 'error', uploadMessage: '上传请求失败', checkStepStatus: 'wait', importStepStatus: 'wait' }
    } else {
        uploadStatus.value.uploadStepStatus = 'error'
        uploadStatus.value.uploadMessage = '网络错误或服务器无响应'
        uploadStatus.value.checkStepStatus = 'wait'
        uploadStatus.value.importStepStatus = 'wait'
    }
    activeStep.value = 0
    ElMessage.error(uploadStatus.value.uploadMessage)
    console.error("Upload Error:", error)
}

const reset = () => {
    uploadStatus.value = null
    activeStep.value = 0
    isLoading.value = false
}

defineExpose({ reset })

</script>

<style lang="scss" scoped>
.import-data-pad {
    padding: 20px;

    .import-steps {
        max-width: 800px;

        .step-line {
            position: relative;
            width: 2px;
            background: #BFBFBF;
            top: -118px;
            height: 125px;
            left: 5.2px;
        }

        .step {
            display: flex;
            margin-bottom: 30px;
            position: relative;

            .step-number {
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
                flex-shrink: 0;
                z-index: 2;
                width: 7px;
                height: 7px;
                background: #FFFFFF;
                border: 2px solid #436BFF;
            }

            .step-content {
                flex: 1;

                .step-title {
                    font-size: 16px;
                    font-weight: 500;
                    margin-bottom: 8px;
                }

                .step-desc {
                    color: #666;
                    font-size: 14px;
                    margin-bottom: 16px;
                }

                .upload-area {
                    width: 100%;

                    :deep(.el-loading-mask) {
                        background-color: rgba(255, 255, 255, 0.7);
                    }

                    .upload-content {
                        padding: 40px;
                        text-align: center;

                        .el-icon {
                            font-size: 60px;
                            color: #c0c4cc;
                            margin-bottom: 16px;
                        }

                        .upload-text {
                            margin: 10px 0;
                            font-size: 16px;
                            color: #606266;
                        }

                        .upload-tip {
                            color: #909399;
                            font-size: 14px;
                        }
                    }

                    :deep(.el-upload-dragger) {
                        width: 100%;
                        height: auto;
                        padding: 20px;
                        border: 1px dashed #d9d9d9;

                        &:hover {
                            border-color: #409eff;
                        }
                    }
                }

                .upload-progress {
                    margin-top: 20px;

                    :deep(.el-step__main) {
                        padding-bottom: 12px;
                    }


                    .step-progress-title {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;

                        .resupload-button {
                            margin-left: 10px;
                            padding: 0 6px;
                        }
                    }

                    .step_import_title {
                        color: #595959;

                        .success_num {
                            color: green;
                        }

                        .fail_num {
                            color: red;
                        }
                    }
                }

                :deep(.el-step__description) {
                    font-size: 13px;
                    color: #666;
                }

                :deep(.el-step.is-vertical .el-step__line) {
                    left: 11px;
                }

                :deep(.el-step.is-vertical .el-step__icon.is-icon) {
                    width: 24px;
                }

                .download-error-data {
                    color: #436BFF;
                    margin-left: 10px;
                    cursor: pointer;
                }
            }
        }
    }

    .step2 {
        top: -118px;
    }
}
</style>
