<template>
  <div class="la_com_menu_wrap flex-row">
    <div
      :class="`lam ${activeId == tag.name ? 'active' : 'normal'}`"
      v-for="tag in tags"
      :key="tag.name"
      @click="onClick(tag)"
    >
      {{ `${tag.name}(${tag.count})` }}
    </div>
    <div class="flex-grow"></div>
    <div
      class="lac_detail"
      @click="onDetail"
      v-if="tags.length > 0 && recordViewPermission"
    >
      对话详情
    </div>
  </div>
</template>

<script>
import { getLaCompetitorMenu } from "@/app_postmeet/tools/sale_report.js";
export default {
  data() {
    return {
      activeId: "",
      tags: [],
      recordViewPermission: false,
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    init() {
      this.tags = getLaCompetitorMenu();
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      if (this.tags.length > 0) {
        this.onClick(this.tags[0]);
      } else {
        this.$emit("callback", "");
      }
    },
    onClick(tag) {
      this.activeId = tag.name;
      this.$emit("callback", tag);
    },
    onDetail() {
      const ar = g.postmeetStore.data.saleAnalyseList["竞争对手"];
      const row = ar[0];
      g.emitter.emit("setAnalyseTag", [row, 1, "竞争对手"]);
    },
  },
};
</script>

<style lang="scss">
.la_com_menu_wrap {
  // justify-content: space-between;

  .lam {
    padding: 8px 12px;
    height: 20px;
    line-height: 20px;
    border-radius: 4px;
    font-size: 14px;
    margin: 12px 12px 0 0;
    cursor: pointer;
  }

  .active {
    color: #436bff;
  }

  .normal {
    color: #262626;
  }

  .lac_detail {
    margin: 20px 15px 0 0;
    color: #436bff;
    cursor: pointer;
  }
}
</style>
