<template>
  <el-drawer v-model="is_show" direction="rtl" class="drawer_gp_add_customer_field" :close-on-click-modal="true">
    <template #header>
      <div class="vd_title">{{ title }}</div>
    </template>
    <template #default>
      <div class="form-container">
        <el-form :model="formData" label-width="70px">
          <!-- 名称 -->
          <el-form-item label="名称" required>
            <el-input v-model="formData.fieldName" placeholder="请输入名称"></el-input>
          </el-form-item>

          <!-- 类型 -->
          <el-form-item label="类型" required>
            <el-radio-group v-model="formData.fieldType" @change="onUpdate('fieldType')">
              <el-radio value="String">字符</el-radio>
              <el-radio value="Select">选项</el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="form-item-container">
            <!-- 字符限制 - 仅在type为字符时显示 -->
            <el-form-item v-if="formData.fieldType === 'String'" label-width="0">
              <div class="char-limit flex-row">
                <div class="limit-item ">
                  <div>最小字符数</div>
                  <el-input-number v-model="formData.minLength" :min="1" :max="formData.maxLength"
                    @change="validateCharLength"></el-input-number>
                </div>
                <div class="limit-item">
                  <div>最大字符数</div>
                  <el-input-number v-model="formData.maxLength" :min="formData.minLength || 1" :max="100"
                    @change="validateCharLength"></el-input-number>
                </div>
              </div>
            </el-form-item>

            <!-- 选项内容 - 仅在type为选项时显示 -->
            <el-form-item v-if="formData.fieldType === 'Select'" label-width="0">
              <div class="options-container">
                <div v-for="(option, index) in formData.fieldOptions" :key="index" class="option-item">
                  <span class="option-index">{{ index + 1 }}</span>
                  <el-input v-model="formData.fieldOptions[index]" placeholder="请输入选项内容" :maxlength="50">
                    <template #append>
                      <span class="char-count">{{ formData.fieldOptions[index] ? formData.fieldOptions[index].length : 0
                      }}/50</span>
                    </template>
                  </el-input>
                  <el-icon @click="removeOption(index)" class="delete-btn">
                    <Delete />
                  </el-icon>
                </div>
                <div class="add-option" v-if="formData?.fieldOptions?.length || 0 < 50">
                  <el-link :underline="false" @click="addOption">
                    + 添加选项 ({{ formData?.fieldOptions?.length || 0 }}/50)
                  </el-link>
                </div>
              </div>
            </el-form-item>
          </div>

          <!-- 开关选项 -->
          <el-form-item label="是否必填">
            <div class="switch-group">
              <el-switch v-model="formData.isRequired" :active-value="1" :inactive-value="0"></el-switch>
            </div>
          </el-form-item>
          <el-form-item label="是否显示">
            <div class="switch-group">
              <el-switch v-model="formData.fieldStatus" :active-value="1" :inactive-value="0"></el-switch>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { Delete } from '@element-plus/icons-vue'
import { addFormField, updateFormField } from '@/app_admin/tools/api'

const is_show = ref(false);
const emit = defineEmits(["callback"]);
const title = ref('')

const props = defineProps({
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const _default = {
  "fieldName": "",
  "fieldStatus": 1,
  "fieldType": "String",
  "fieldOptions": [''],
  "isRequired": 1,
  "maxLength": 50,
  "minLength": 1,
  "placeholder": "",
  "sortOrder": 1
};

const formData = ref({ ..._default });
const formCode = ref('');
const isEdit = ref(false);

const onCancel = () => {
  is_show.value = false;
};


const onConfirm = () => {
  if (props.readonly) {
    is_show.value = false;
    emit("callback", "confirm", formData.value);
  } else {
    if (isEdit.value) {
      updateFormField(formData.value.id, formData.value).then(res => {
        if (res.code == 0) {
          ElMessage({
            grouping: true,
            message: '修改成功',
            type: 'success'
          });
          is_show.value = false;
          emit("callback", "reload", formCode.value);
        } else {
          ElMessage.error(res.message);
        }
      });
    } else {
      addFormField(formCode.value, formData.value).then(res => {
        if (res.code == 0) {
          ElMessage.success("添加成功");
          is_show.value = false;
          emit("callback", "reload", formCode.value);
        } else {
          ElMessage.error(res.message);
        }
      });
    }
  }
};

const onUpdate = (key, field) => {
  if (key == 'fieldType') {
    formData.value.fieldOptions = ['']
  }
}

const showAdd = (code, field) => {
  is_show.value = true;
  isEdit.value = false;
  formData.value = { ..._default, ...field };
  formCode.value = code;
  title.value = '添加属性'
};

const showEdit = (code, field) => {
  isEdit.value = true;
  is_show.value = true;
  const fieldCopy = JSON.parse(JSON.stringify(field));
  fieldCopy.fieldOptions = typeof fieldCopy.fieldOptions === 'string' ?
    JSON.parse(fieldCopy.fieldOptions) : fieldCopy.fieldOptions;
  formData.value = fieldCopy;
  formCode.value = code;
  title.value = code == 'OUTER_PARTNER' ? '客户参会人属性设置' : '内部参会人属性设置'
};

// 添加选项
const addOption = () => {
  if (formData.value.fieldOptions.length < 50) {
    formData.value.fieldOptions.push('');
  }
};

// 删除选项
const removeOption = (index) => {
  if (formData.value.fieldOptions.length <= 1) {
    ElMessage.warning('至少需要保留一个选项');
    return;
  }
  formData.value.fieldOptions.splice(index, 1);
};

// 验证字符长度
const validateCharLength = () => {
  if (formData.value.minLength > formData.value.maxLength) {
    ElMessage.warning('最小字符数不能大于最大字符数');
    formData.value.minLength = formData.value.maxLength;
  }
};

defineExpose({
  title,
  showAdd,
  showEdit,
  onCancel,
  onConfirm,
  is_show,
  formData
});
</script>

<style lang="scss">
.drawer_gp_add_customer_field {
  width: 600px !important;

  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    .ed_main {
      height: calc(100vh - 100px);

      .av_item {
        .av_item_value {
          width: 90%;
        }
      }
    }

    .msg_ai {
      .mbody {
        background: #bdd2ff;
      }
    }

    .msg_my {
      .mbody {
        background: #fff;
      }
    }
  }

  .el-drawer__footer {
    border-top: 1px solid #e9e9e9;
    padding-top: 20px;

    .dialog-footer {
      text-align: right;
    }
  }

  .form-container {
    padding: 20px;

    .form-item-container {
      padding: 20px;
      border-radius: 4px;
      background-color: #fafafa;
    }
  }

  .char-limit {
    display: flex;
    gap: 20px;

    .limit-item {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .switch-group {
    display: flex;
    gap: 20px;
  }



  .options-container {
    .option-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .option-index {
        min-width: 20px;
      }

      .el-input {
        flex: 1;
      }

      .char-count {
        font-size: 12px;
        color: #999;
        margin: 0 8px;
      }

      .delete-btn {
        color: #8C8C8C;
      }
    }
  }

  .add-option {
    margin-top: 10px;
    color: #436BFF;
  }
}
</style>
