<template>
  <div class="video-player flex-center">
    <video ref="videoRef" :src="videoUrl" controls>
    </video>
  </div>
</template>

<script setup>
const videoRef = ref(null);
const videoUrl = ref("");

const init = () => {
  videoUrl.value = g.clipperStore.audioUrl;

  // 添加时间更新事件监听
  g.emitter.on("update_current_time", (currentTime) => {
    if (videoRef.value) {
      videoRef.value.currentTime = currentTime;
    }
  });
};

onUnmounted(() => {
  // 清理事件监听
  g.emitter.off("update_current_time");
});

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.video-player {
  height: 100%;
  background: #000;

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
