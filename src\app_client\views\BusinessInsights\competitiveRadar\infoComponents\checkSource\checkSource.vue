<template>

    <div class="checkSource_wrap">
        <RadioGroup v-model="competitorId" class="fch_radio" :options="options" />
        <div class="content_section flex-col">
            <FilterCondition />
            <div class="content_inner flex-row">
                <CategoryList :competitorId="competitorId" />
                <div class="feedback_list">
                    <FeedbackList :data="competitiveSelectRadarList" v-loading="loading" />
                    <el-pagination :current-page="currentPage" :page-size="pageSize" background
                        layout="total,  prev, pager, next" :total="parseInt(totalNum)"
                        @current-change="handleCurrentChange" @size-change="handleSizeChange"
                        v-show="totalNum > pageSize" class="checkSource_wrap_pagination" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import FilterCondition from './components/FilterCondition.vue';
import CategoryList from './components/CategoryList.vue';
import FeedbackList from './components/FeedbackList.vue';
import RadioGroup from '@/app_client/components/RadioGroup.vue'

const store = g.clientBiStore
const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.alternativeName || i.commonName,
    value: i.competitorId,
})))
const competitorId = ref('')
const loading = ref(false)
const periodType = computed(() => store.periodType || '')
const currentPage = ref(1)
const pageSize = ref(4)
const totalNum = ref(0)
const crFilterCondition = computed(() => store.crFilterCondition || {})
const competitiveSelectRadarList = computed(() => {
    const listP = store.competitiveSelectRadarDataObj;
    totalNum.value = listP.totalNum;
    return listP.datas;
})
const customer = computed(() => g.clientBiStore.filterConditionObj.customer || [])
const attitudeType = computed(() => g.clientBiStore.filterConditionObj.attitudeType || '')

const handleCurrentChange = (val) => {
    currentPage.value = val
    init()
}

const handleSizeChange = (val) => {
    pageSize.value = val
    init()
}

const init = async () => {
    if (!competitorId.value) return;

    loading.value = true
    const param = {
        pageNumber: currentPage.value,
        pageSize: pageSize.value,
        competitorId: competitorId.value,
        periodType: periodType.value,
        parentId: crFilterCondition.value.parentId,
        dimensionId: crFilterCondition.value.dimensionId,
        attitudeType: crFilterCondition.value.attitudeType,
        customerIds: crFilterCondition.value.customerIds,
    }
    await param.parentId && store.getCompetitorRadarData(param)
    loading.value = false
}

// 同时监听多个值
watch(() => [competitorId.value, periodType.value, g.clientBiStore.crSearchVersion],
    async () => {
        currentPage.value = 1
        init()
    },
    { immediate: true, deep: true }
)

watch(() => options.value, (newValue) => {
    if (newValue.length > 0) {
        competitorId.value = newValue[0].value
        g.clientBiStore.setCrFilterCondition({
            parentId: ''
        })
        store.crFbCompetitorId = competitorId.value;

    }
}, { immediate: true, deep: true })

watch(() => [competitorId.value, g.clientBiStore.periodType, g.clientBiStore.crSearchVersion], () => {
    store.getCrDimensionList(competitorId.value, {
        parentId: crFilterCondition.value.parentId,
        dimensionId: crFilterCondition.value.dimensionId,
        attitudeType: crFilterCondition.value.attitudeType,
        customerIds: crFilterCondition.value.customerIds,
    })
}, { immediate: true })

</script>

<style lang="scss" scoped>
.checkSource_wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .fch_radio {
        margin: 24px 0 24px 15px;
    }

    .cvh_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }

    .filter_section {
        margin-bottom: 16px;
    }

    .content_section {
        gap: 16px;
        min-height: 0;
        padding: 24px;
        background: #F9FAFC;
        border-radius: 8px;
        margin: 0 15px !important;

        .content_inner {
            padding: 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .feedback_list {
                width: calc(100% - 240px);
            }
        }
    }

    .checkSource_wrap_pagination {
        display: flex;
        justify-content: flex-end;
        margin: 0;
        // margin: 12px 24px;
    }
}

.bottom_details_filter {
    margin: 24px 0 24px 15px;
}
</style>

<style lang="scss"></style>