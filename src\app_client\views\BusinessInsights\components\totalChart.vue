<template>
    <div class="competitiveRadar_total_chart" v-loading="loading">
        <p class="title">{{ props.setting.title }}</p>
        <div v-if="data.length" ref="chartDom"></div>
        <el-empty v-else style="display: flex;justify-content: flex-start;" :image="getOssUrl('no-data.png', 3)"
            description="暂无数据" />
    </div>
</template>

<script setup lang="js">
import { checkDomReady } from "@/app_client/tools/utils.js"
import { getOssUrl } from "@/js/utils.js";
import echarts from '@/js/echarts'

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    metrics: {
        type: Array,
        default: () => []
    },
    setting: {
        type: Object,
        default: () => ({
        })
    },
    loading: {
        type: Boolean,
        default: false
    }
})
const chart = ref(null)
const chartDom = ref(null)

// 计算合适的x轴最大值
const calculateMaxValue = (data) => {
    if (!data || data.length === 0) return 100

    // 计算所有数据中的最大值
    const maxValue = Math.max(...data.map(item =>
        props.metrics.reduce((sum, metric) => sum + (item[metric.key] || 0), 0)
    ))
    // 取最大值的整10倍数
    return Math.ceil(maxValue / 10) * 10
}

// 初始化图表
const initChart = () => {
    if (chart.value) {
        chart.value.dispose()
        chart.value = null
    }
    if (!chartDom.value || props.data.length === 0 || !checkDomReady(chartDom)) return
    chart.value = markRaw(echarts.init(chartDom.value))
    const categories = props.data.map(item => item.name)
    // 预处理数据，找出每个平台最后一个非零值的索引
    const lastNonZeroIndices = props.data.map(platformData => {
        let lastIndex = -1;
        for (let i = props.metrics.length - 1; i >= 0; i--) {
            if (platformData[props.metrics[i].key] > 0) {
                lastIndex = i;
                break;
            }
        }
        return lastIndex;
    });
    const seriesData = props.metrics.map((metric, idx) => ({
        name: metric.name,
        type: 'bar',
        stack: 'total',
        barWidth: 14,
        itemStyle: {
            borderRadius: [0, 0, 0, 0],
            color: metric.color
        },
        label: { show: false },
        data: props.data.map((item, dataIndex) => ({
            value: item[metric.key],
            itemStyle: {
                borderRadius: lastNonZeroIndices[dataIndex] === idx ? [0, 2, 2, 0] : [0, 0, 0, 0]
            }
        }))
    }))

    const maxValue = calculateMaxValue(props.data)

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#eee',
            borderWidth: 1,
            textStyle: {
                color: '#333',
                fontSize: 14
            },
            padding: [10, 15],
            formatter: function (params) {
                if (!params || params.length === 0) return '';
                const platform = params[0].axisValue;
                let result = `<div style="color: #8C8C8C; font-size: 12px;width: 200px;margin-left: 4px;">
                    ${platform}</div>`;
                params.forEach((param, idx) => {
                    result += `
                        <div style="display: inline-block; width: 48%; min-width: 80px; margin-bottom: 8px;margin-left: 4px; vertical-align: top;">
                            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${param.color}; margin-right: 8px; vertical-align: middle;"></span>
                            <span style="color: #757575; font-size: 12px; vertical-align: middle;">${param.seriesName}</span>
                            <div style="color: #262626; font-size: 14px; font-weight: bold; margin-top: 2px; padding-left: 28px;">${param.value}</div>
                        </div>
                        ${idx % 2 === 1 ? '<br/>' : ''}
                    `;
                });
                return result;
            }
        },
        legend: {
            data: props.metrics.map(item => item.name),
            top: 0,
            left: 'left',
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 24,
            textStyle: {
                fontSize: 12,
                color: '#8A8B8E',
                fontWeight: 400
            },
            selected: props.metrics.reduce((acc, item, index) => {
                acc[item.name] = true;
                return acc;
            }, {})
        },
        grid: {
            left: props.setting.width || 4,
            right: 30,
            top: 50,
            bottom: 30
        },
        xAxis: {
            type: 'value',
            max: maxValue,
            axisLabel: {
                formatter: function (value) {
                    return value === maxValue ? value + '（条）' : value;
                },
                color: '#8A8B8E',
                fontSize: 12
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: '#E9E9E9'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: categories,
            inverse: true,
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
                fontSize: 14,
                overflow: 'break',
                interval: 0,
                width: props.setting.width || 100,
                color: '#595959',
                formatter: function (value) {
                    // 如果文本长度超过8个字符，则换行显示
                    // if (value.length > 8) {
                    //     return value.split('').reduce((prev, curr, index) => {
                    //         if (index % 8 === 0 && index !== 0) {
                    //             return prev + '\n' + curr;
                    //         }
                    //         return prev + curr;
                    //     }, '');
                    // }
                    return value;
                },
            }
        },
        series: seriesData.map(series => ({
            ...series,
        }))
    }
    if (chart.value) {
        chart.value.setOption(option)

        // 添加图例选择变化事件监听
        chart.value.on('legendselectchanged', function (params) {
            // 检查是否只有一个图例被选中
            const selectedCount = Object.values(params.selected).filter(Boolean).length;

            // 如果当前操作是取消选择，并且这是最后一个选中的图例，则阻止操作
            if (selectedCount === 0) {
                // 重新设置该图例为选中状态
                const newSelected = { ...params.selected };
                newSelected[params.name] = true;

                chart.value.setOption({
                    legend: {
                        selected: newSelected
                    }
                });
            }
        });
    }
}

// 监听数据变化并重绘
watch(() => [props.data, g.clientBiStore.selectTabType], () => {
    nextTick(() => {
        initChart()
    })
}, { immediate: true, deep: true })

const resizeHandler = () => {
    if (chart.value) {
        chart.value.resize()
    }
}

onMounted(() => {
    nextTick(() => {
        initChart()
    })
    window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler)
    if (chart.value) {
        chart.value.dispose()
        chart.value = null
    }
})
</script>


<style lang="scss" scoped>
.competitiveRadar_total_chart {
    padding: 24px;
    background: #fff;
    box-sizing: border-box;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    width: 100%;
    height: 100%;

    .title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        margin: 0 0 12px 0;
    }

    >div {
        height: 340px;
        width: 100%;
    }
}
</style>