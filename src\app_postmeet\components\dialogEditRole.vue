<template>
  <el-dialog v-model="is_show" width="480px" append-to-body class="dialog_edit_role_wrap">
    <div @click="onClose">
      <closeIcon class="btnClose"></closeIcon>
    </div>
    <EditRole ref="refEditRole" @callback="onCallback"></EditRole>
  </el-dialog>
</template>

<script setup>
import closeIcon from "@/app_postmeet/icons/close.vue";
import EditRole from "@/components/RenameSpeaker/EditRole.vue";

const emit = defineEmits(["callback"]);
const is_show = ref(false);
const item = ref({});
const refEditRole = ref(null);

const onClose = () => {
  nextTick(() => {
    is_show.value = false;
  });
}

const onCallback = (action) => {
  if (action == "close") {
    emit("callback", "close");
    nextTick(() => {
      is_show.value = false;
    });
  } else if (action == "cancel") {
    is_show.value = false;
  }
}

const show = (item) => {
  is_show.value = true;
  item.value = item;
  nextTick(() => {
    refEditRole.value.show(g.postmeetStore.data.confId, item.value);
  });
}

defineExpose({
  item,
  show
})

</script>

<style lang="scss">
.dialog_edit_role_wrap {
  padding: 0;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .btnClose {
    position: absolute;
    top: 12px;
    right: 12px;
    cursor: pointer;
    z-index: 999;
  }

  .btnClose:hover {
    color: #436bff;
  }
}
</style>
