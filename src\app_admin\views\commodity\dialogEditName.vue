<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close>
    <el-form
      ref="refForm"
      :model="formData"
      label-width="auto"
      label-position="top"
      size="default"
      :rules="rules"
    >
      <el-form-item label="清单名称" prop="listName">
        <el-input
          v-model="formData.listName"
          maxlength="50"
          show-word-limit
          placeholder="请输入清单名称"
        />
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { reactive, ref, toRaw } from "vue";
import { updateGoodsName } from "@/app_admin/tools/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
const refModal = ref();
const title = ref("");
const refForm = ref("");
const emit = defineEmits(["callback"]);
let cid = "";

const defaultForm = {
  listName: "",
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "480px",
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const show_edit = (_cid, data) => {
  cid = _cid;
  formData.value = { ...data };
  cfg["title"] = "编辑清单名称";
  refModal.value.show(cfg);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;
  refForm.value.validate((valid, fields) => {
    if (valid) {
      const data = toRaw(formData.value);
      apiHintWrap(
        updateGoodsName(cid, data.id, { name: data.listName }),
        cfg["title"]
      ).then(({ status }) => {
        if (status) {
          emit("callback", "reload");
          btnCancel();
        }
      });
    }
  });
};

const rules = reactive({
  listName: [{ required: true, message: "请输入清单名称", trigger: "blur" }],
});

defineExpose({ title, show_edit, cbModal, formData, rules });
</script>

<style lang="scss">
.el-dialog__body {
  padding: 15px 24px 5px 24px;
}
</style>
