<template>
    <div class="dtxt">
        {{ props.row.reportStatus }}
        <el-tooltip class="ccol_dept" effect="dark" :content="getShowHint()" placement="top-start" raw-content>
            <el-icon v-show="getShowHint()">
                <QuestionFilled />
            </el-icon>
        </el-tooltip>
    </div>
</template>

<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'
const props = defineProps(['row']);

const getShowHint = () => {
    if (props.row.customizeReportStatus == 7) {
        return props.row.customizeReportFailReason
    }
    return ''
};

defineExpose({ props, getShowHint, QuestionFilled })
</script>

<style lang="scss">
.ccol_dept {
    .dtxt {
        font-size: 14px;
        color: #262626;
    }
}
</style>