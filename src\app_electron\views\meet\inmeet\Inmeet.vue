<template>
    <div class="inmeet-container custom-scrollbar" ref="containerRef">
        <el-alert v-if="errorMsg" :title="errorMsg" type="error" />

        <div class="header flex-row" :style="{ backgroundImage: `url(${getAssetUrl('meet_bg.png')})` }">
            <AudioWave ref="refAudioWave" @callback="updateStatus" />
            <div class="flex-col">
                <div class="txt-name">
                    {{ statusText }}
                </div>
                <div v-show="options.general.showRecordingDuration">
                    <Timer ref="refTimer" @callback="cbTimer" />
                </div>
            </div>
        </div>

        <MessageList ref="messageListRef" @callback="cbMessageList" />
        <div v-loading="true" element-loading-text="正在结束录制..."
            :class="`${meetingStatus === MeetStatus.paused ? 'is-paused' : 'is-recording'} reconnecting-container`"
            v-if="isEnding">
        </div>
        <!-- 底部按钮区 -->
        <div class="footer-btns">
            <StopMeeting ref="refStopMeeting" @callback="updateStatus" />
            <PauseMeeting ref="refPauseMeeting" @callback="updateStatus" />
        </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="'状态通知'" width="316px" :close-on-click-modal="false" :show-close="false">
        <div>
            <div>{{ endReason }}</div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleEndMeet" :loading="loading">
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
    <DiaConfirm1 v-model="noNetworkDialogVisible" title="操作失败" hint="当前网络异常，请检查您的网络连接后重试 " />
</template>

<script setup>
import { throttle, getAssetUrl } from '@/js/utils';
import pauseIcon from '@/app_electron/icons/pause.vue';
import Timer from '@/app_electron/components/Timer.vue';
import PauseMeeting from '@/app_electron/components/PauseMeeting.vue';
import StopMeeting from '@/app_electron/components/StopMeeting.vue';
import AudioWave from '@/app_electron/components/AudioWave.vue';
import MessageList from './MessageList.vue'
import { MeetStatus, reloadSchedule } from '@/app_electron/tools/utils';
import DiaConfirm1 from '@/app_electron/components/DiaConfirm1.vue';

const errorMsg = ref('');
const options = ref({ general: { showRecordingDuration: true } })
const plan = ref({})
const refTimer = ref(null)
const meetingStatus = ref('');
const refPauseMeeting = ref(null);
const messageListRef = ref(null)
const statusText = ref('连接中...')
const refAudioWave = ref(null)
const refStopMeeting = ref(null)
const dialogVisible = ref(false)
let beforeDisconnectStatus;
let needResentOptions = false;
const containerRef = ref(null)
const endReason = ref('')
const emit = defineEmits(['callback'])
const isReconnecting = ref(false)
const isEnding = ref(false)
const noNetworkDialogVisible = ref(false)

watch(() => isReconnecting.value, (new_value) => {
    if (!messageListRef.value) {
        return
    }
    messageListRef.value.setIsReconnecting(new_value && !isEnding.value);
}, { immediate: true })

const updateStatus = async (status) => {
    errorMsg.value = '';
    if (status === MeetStatus.started) {
        refTimer.value.startTimer();
        refAudioWave.value.startAnimation();
        statusText.value = '录制中...'
        refPauseMeeting.value.updateStatus(false);
        messageListRef.value.setStatus(MeetStatus.resumed);
        reloadSchedule()
        isReconnecting.value = false;
    } else if (status === MeetStatus.paused) {
        if (await g.meetStore.pauseMeeting()) {
            refTimer.value.pauseTimer();
            refAudioWave.value.stopAnimation();
            statusText.value = '已暂停'
            refPauseMeeting.value.updateStatus(true)
            messageListRef.value.setStatus(MeetStatus.paused);
            reloadSchedule()
        } else {
            errorMsg.value = '暂停录制失败'
            refPauseMeeting.value.updateStatus(false);
            messageListRef.value.setStatus(MeetStatus.resumed);
            return;
        }
    } else if (status === MeetStatus.resumed) {
        if (await g.meetStore.resumeMeeting()) {
            console.log('resumeMeeting success')
            refTimer.value.startTimer();
            refAudioWave.value.startAnimation();
            refPauseMeeting.value.updateStatus(false);
            messageListRef.value.setStatus(MeetStatus.resumed);
            statusText.value = '录制中...'
            if (needResentOptions) {
                g.meetStore.updateMeetingOptions(toRaw(options.value))
                needResentOptions = false;
            }
            reloadSchedule()
        } else {
            errorMsg.value = '继续录制失败'
            console.error('errorMsg', errorMsg.value)
            return;
        }
    } else if (status === MeetStatus.connecting) {
        if (!isEnding.value) {
            beforeDisconnectStatus = meetingStatus.value;
            errorMsg.value = ''
            isReconnecting.value = true;
        }
    } else if (status === MeetStatus.reconnected) {
        errorMsg.value = ''
        isReconnecting.value = false;
        if (beforeDisconnectStatus === MeetStatus.paused) {
            statusText.value = '已暂停'
            refTimer.value.pauseTimer();
            refAudioWave.value.stopAnimation();
            refPauseMeeting.value.updateStatus(true)
            messageListRef.value.setStatus(MeetStatus.paused);
        } else {
            statusText.value = '录制中...';
            refPauseMeeting.value.updateStatus(false)
            refTimer.value.startTimer();
            refAudioWave.value.startAnimation();
        }
        reloadSchedule()
    } else if (status === MeetStatus.stop) {
        if (isEnding.value) {
            return
        }
        isEnding.value = true;
        if (await g.meetStore.endOrderMeeting()) {
            refStopMeeting.value.setIsEnding(false);
            emit('callback', 'stop')
        } else {
            isEnding.value = false;
            noNetworkDialogVisible.value = true
            refStopMeeting.value.setIsEnding(false);
        }
    } else if (status === MeetStatus.no_network) {
        noNetworkDialogVisible.value = true;
    } else {
        console.log('unknown status', status)
    }
    if (status !== MeetStatus.connecting && status !== MeetStatus.reconnected) {
        meetingStatus.value = status;
        messageListRef.value.setStatus(status);
    }
}

const openSetting = () => {
    g.elog.log('openSetting')
    g.electronStore.openWin('meet_setting')
}

const minimize = () => {
    g.elog.log('minimize')
    g.electronStore.minimizeWin('meet');
}

const startMeeting = () => {
    options.value = g.meetStore.meetingOptions;
    updateStatus(MeetStatus.started);
    if (g.meetStore.fromStore) {
        try {
            const timer = parseInt(g.appStore.getStore(g.cv.keyMeetTimer)) || 0
            console.log(' fromStore timer', timer, typeof (timer))
            refTimer.value.updateTime(timer)
            messageListRef.value.recoverMsgList()
        } catch (e) {
            console.log('fromStore timer error', e)
        }
    } else {
        g.appStore.removeStore(g.cv.keyMeetTimer)
        g.appStore.removeStore(g.cv.keyMeetChatList)
    }
    g.ipcRenderer.send('update_closeable', 'meet', false)
}

// 添加检查是否在底部的方法
const isScrolledToBottom = () => {
    const el = containerRef.value
    if (!el) return false
    return Math.abs(el.scrollHeight - el.scrollTop - el.clientHeight) <= 1
}

// 滚动到底部方法
const scrollToBottom = () => {
    const el = containerRef.value
    if (el) {
        el.scrollTop = el.scrollHeight
    }
}

// 添加消息更新的处理方法
const onMessageUpdate = () => {
    if (isScrolledToBottom()) {
        nextTick(() => {
            scrollToBottom()
        })
    }
}

const cbMessageList = (action) => {
    if (action === 'new_msg') {
        onMessageUpdate()
    }
}

const cbTimer = (data) => {
    const { time, second } = data;
    messageListRef.value.setTime(time)
}

const handleEndMeet = () => {
    if (endReaonCode) {
        emit('callback', 'stop')
    } else {
        dialogVisible.value = false
        updateStatus(MeetStatus.stop)
    }
}

const addListener = () => {
    g.ipcRenderer.on('forward_message', async (_, { action, data }) => {
        console.log('forward_message', action, data)
        if (action === 'updateMeetingOptions') {
            options.value = data;
            if (meetingStatus.value === MeetStatus.started) {
                g.meetStore.updateMeetingOptions(data)
            }
        } else if (action === 'rename-speaker') {
            console.log('rename-speaker', JSON.stringify(data))
            g.meetStore.renameData[data.ui] = data
            const status = await g.meetStore.rename(data)
            g.electronStore.sendMessage('rename_inmeet', 'rename-speaker-status', status)
            console.log('rename-speaker status', status)
        } else if (action === 'resume_recording') {
            updateStatus(MeetStatus.resumed)
        } else if (action === 'update_record_duration') {
            refTimer.value.updateTime(data)
        }
    })
}

const handleDeviceChange = throttle(async () => {
    options.value = g.meetStore.meetingOptions;
    const devices = await g.meetStore.getAudioInputs();
    g.elog.log('handleDeviceChange', options.value.audio.audioInputDevice, devices.length)
    const currentDevice = devices.find(d => d.deviceId === options.value.audio.audioInputDevice);
    if (!currentDevice) {
        g.elog.log('using mic device disconnected')
        ElMessage.error('麦克风设备已断开，请重新连接')
        updateStatus(MeetStatus.paused);
        needResentOptions = true;
        openSetting()
    } else {
        g.elog.log('using mic device still connected')
    }
}, 1000);

let endReaonCode = ''
onMounted(() => {
    g.ipcRenderer.on('window-event', (_, data) => {
        if (data === 'restore') {
            scrollToBottom()
        }
    });
    g.emitter.on('wsStatus', (status) => {
        if (status === 'reconnecting' || status === 'disconnected') {
            updateStatus(MeetStatus.connecting)
        } else if (status === 'reconnected' || status === 'connected') {
            updateStatus(MeetStatus.reconnected)
        }
    })
    g.emitter.on('forceEndMeeting', (reason) => {
        endReaonCode = reason;
        if (reason === 'roomNotExists') {
            endReason.value = '房间已销毁无法再重连。'
        } else if (reason === 'endByServer') {
            endReason.value = '连接断开: 另一端结束本次拜访'
        }
        dialogVisible.value = true
        reloadSchedule()
    })
    // 添加设备变更监听
    navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    addListener()
})

onBeforeUnmount(() => {
    g.emitter.off('wsStatus');
    navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    refStopMeeting.value.stopMeeting();
    g.ipcRenderer.removeAllListeners('forward_message')
    g.emitter.off('forceEndMeeting');
})

defineExpose({
    meetingStatus, updateStatus, pauseIcon, startMeeting, plan, options, refTimer,
    Timer, openSetting, minimize, refAudioWave, MeetStatus, errorMsg, refStopMeeting
})
</script>

<style scoped lang="scss">
.inmeet-container {
    width: 100%;
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);
    position: relative;

    .el-alert {
        margin-bottom: 10px;
    }

    .is-recording {
        height: 400px;
    }

    .header {
        display: flex;
        flex-wrap: wrap;
        position: fixed;
        width: 100%;
        padding: 16px;
        height: 284px;
        background-color: #E9F1FF;
        z-index: 1;

        .txt-name {
            font-size: 14px;
            color: #262626;
            line-height: 22px;
        }

        .timer {
            font-size: 12px;
            color: #757575;
            line-height: 18px;
        }
    }

    .reconnecting-container {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.1);
        opacity: 0.9;
        z-index: 999;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        :deep(.el-loading-text) {
            width: 205px;
            margin: 0 auto;
        }
    }

    // 底部按钮区
    .footer-btns {
        width: 100%;
        display: flex;
        justify-content: space-between;
        position: fixed;
        bottom: 0;
        border-top: 1px solid #e9e9e9;
        padding: 16px 0;
        background-color: #fff;
        z-index: 22;

        .ebtn {
            width: 50%;
            border-radius: 6px;
            height: 40px;
            font-size: 16px;
        }

        .end-btn {
            color: #F5222D;
            border: 1px solid #F5222D;
            background: #fff;
            margin: 0 10px 0 20px;
        }

        .continue-btn {
            color: #fff;
            background: #436BFF;
            margin: 0 20px 0 10px;
        }
    }
}

.mac-content {
    .inmeet-container {
        height: calc(100vh - 38px);

        .header {
            top: 36px;
        }

        .resume-record {
            top: 83px;
        }

        .is-recording {
            top: 83px;
        }

        .is-paused {
            top: 163px;
        }
    }
}

.win-content {
    .inmeet-container {
        height: 100vh;

        .header {
            top: 0;
        }

        .resume-record {
            top: 58px;
        }

        .is-recording {
            top: 58px;
        }

        .is-paused {
            top: 133px;
        }
    }
}
</style>