<template>
    <div class="overview">
        <totalCharts :data="evaluationDistrList" :metrics="props.totalChartsMetrics" :setting="totalChartsSetting"
            class="w50" :loading="loading1" />
        <div class="radar">
            <div class="radar_title">
                <p style="display: flex;align-items: center;">
                    竞品声量雷达图
                    <TooltipCvCalc />
                </p>

                <span v-if="volumeList.length" class="reset-btn" @click="resetRadar">重置</span>
            </div>
            <radarCards ref="radarCardsRef" v-if="volumeList.length" :data="volumeList" :colorObject='props.colorObject'
                :loading="loading2" @callback="radarCallback">
            </radarCards>
            <radarEcharts :data="volumedistributionAll" :config="radarEchartsConfig" :select="volumedistributionSelect"
                :loading="loading2"></radarEcharts>
        </div>
    </div>
</template>

<script setup lang="js">
import totalCharts from '@/app_client/views/BusinessInsights/components/totalChart.vue'
import radarEcharts from '../chartsComponents/radar.vue'
import radarCards from './radarCards.vue'
import { watch } from '@vue/reactivity'
import TooltipCvCalc from '@/app_client/components/TooltipCvCalc.vue'

const props = defineProps(['totalChartsMetrics'])
const volumedistributionSelect = ref([])
const loading1 = ref(false)
const loading2 = ref(false)
const radarCardsRef = ref(null)
const totalChartsSetting = {
    title: '竞品评价分布图',
    width: 82
}
const radarEchartsConfig = {
    isShowLegend: false
}

// 从store获取数据
const store = g.clientBiStore
const volumedistributionAll = ref([])
const volumeList = computed(() =>
    store.volumedistributionAll || []
)
const evaluationDistrList = computed(() => store.competitorsArr || [])
const init = () => {
    const f1 = async () => {
        loading1.value = true;
        await store.getVolumedistributionAll()

        loading1.value = false;
    }
    const f2 = async () => {
        loading2.value = true;
        await store.getEvaluationDistribution()
        loading2.value = false;
    }
    f1()
    f2()
}
watch(() => volumeList.value, () => { volumedistributionAll.value = volumeList.value })
watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

const radarCallback = (item) => {
    volumedistributionSelect.value = [item]

}
const resetRadar = () => {
    radarCardsRef.value?.reset()
    // volumedistributionAll.value = volumeList.value
    volumedistributionSelect.value = []
}

</script>

<style lang="scss">
.overview {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;

    .w50 {
        width: calc(50% - 12px);
        height: 460px;
    }
}

.radar {
    width: calc(50% - 6px);
    padding: 24px;
    background: #FFFFFF;
    box-sizing: border-box;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    height: 460px;

    &_title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        margin: 0 0 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        p {
            margin: 0;
        }


        .reset-btn {
            font-size: 14px;
            color: #436BFF;
            line-height: 22px;
            cursor: pointer;
            font-weight: 400;
        }
    }

    &_icon {
        margin-left: 4px;

        svg {
            background: #999;
            border-radius: 50%;
        }
    }

    .competitiveRadar_radar_chart {
        height: 320px;
    }
}
</style>