<template>
    <el-form :model="formData" ref="formRef" label-position="top">
        <el-form-item label="内部伙伴姓名" prop="name" :rules="[{ required: true, message: '请选择内部伙伴' }]">
            <el-select v-model="formData.name" filterable remote reserve-keyword placeholder="请输入内部伙伴姓名"
                :remote-method="remoteMethod" :loading="loading" popper-class="rd_persons_select"
                @change="onPersonInChange" @focus="onFocus" @blur="onBlur">
                <el-option v-for="item in options" :key="item.id" :value="item.fullname">
                    <div class="rd_person">
                        <div class="icon">
                            <UserIcon :name="item.fullname" :pic="item.imgUrl" :usemy="false"></UserIcon>
                        </div>
                        <div class="rdp_right">
                            <div class="title">{{ item.fullname }}</div>
                            <div class="position">{{ item.positionName }}</div>
                            <div class="position">{{ item.deptName }}</div>
                        </div>
                    </div>
                </el-option>
            </el-select>
            <div class="rd_name flex-row" v-show="selectPerson">
                <div class="rdn_txt">
                    <span v-if="selectPerson.positionName || selectPerson.position"> 岗位：{{ selectPerson.positionName ||
                        selectPerson.position }}</span>
                </div>
            </div>
            <SuggestedContacts ref="refSuggestedContacts" :multiple="false" @select="handleSuggestionSelect" />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { getUserList } from '@/js/api'
import UserIcon from "@/app_postmeet/components/usericon.vue";
import SuggestedContacts from "@/components/ArrangeVisitCore/SuggestedContacts.vue";

const formRef = ref(null);
const loading = ref(false);
const refSuggestedContacts = ref(null);
const options = ref([]);

const formData = reactive({
    name: ''
});

const selectPerson = ref({
    "deptId": "",
    "deptName": "",
    "fullname": "",
    "id": " ",
    "ssoUserId": " ",
    "positionName": "",
    "username": ""
});

const remoteMethod = (name) => {
    if (name !== "") {
        refSuggestedContacts.value && refSuggestedContacts.value.hide();
        loading.value = true;
        getUserList({ name }).then((resp) => {
            if (resp.code == 0) {
                // 数组中元素结构如下
                // "deptId": "be722044-860d-4a46-9ab1-0c6c87b262b0",
                // "fullname": "张宇",
                // "id": "0cf45832-6845-4698-b93e-f765963b4d8d",
                // "imgUrl": "",
                // "positionName": "高级前端开发工程师",
                // "username": "zhangyu20210943"
                options.value = resp.data;
                loading.value = false;
            }
        });
    } else {
        options.value = [];
    }
}

const onPersonInChange = (e) => {
    if (e) {
        refSuggestedContacts.value.hide();
    }
    const person = options.value.find((x) => x.fullname == e);
    selectPerson.value = person;
}

const onFocus = () => {
    refSuggestedContacts.value.show();
}

const onBlur = () => {
    setTimeout(() => {
        refSuggestedContacts.value && refSuggestedContacts.value.hide();
    }, 500);
}

const handleSuggestionSelect = (item) => {
    item.userId = item.ssoUserId || item.userId || "";
    item.attendeeType = "PARTNER_ATTENDEE";
    selectPerson.value = {
        fullname: item.name,
        positionName: item.position,
        deptName: item.deptFullName,
        id: item.ssoUserId,
    };
    formData.name = item.name;
    refSuggestedContacts.value.hide();
}

const get_data = () => {
    console.log('selectPerson.value', selectPerson.value)
    const result = {
        "name": selectPerson.value.fullname,
        "id": selectPerson.value.id,
        "ssoUserId": selectPerson.value.id,
        "position": selectPerson.value.positionName || selectPerson.value.position,
        "fieldValues": []
    }
    return result;
}

const init = () => {

}
defineExpose({
    init,
    get_data
})

</script>


<style lang="scss" scoped>
.rd_persons_select {
    li {
        height: 79px !important;

        .rd_person {
            display: flex;
            flex-direction: row;
            height: 79px !important;

            .icon {
                margin: 20px;
                width: 32px;

                img {
                    width: 32px;
                    height: 32px;
                }
            }

            .rdp_right {
                display: flex;
                flex-direction: column;
                height: 79px;
                padding-top: 5px;

                .title {
                    height: 22px;
                    font-size: 14px;
                    color: #262626;
                    line-height: 22px;
                }

                .position {
                    height: 20px;
                    font-size: 12px;
                    color: #8c8c8c;
                    line-height: 20px;
                }
            }
        }
    }
}
</style>
