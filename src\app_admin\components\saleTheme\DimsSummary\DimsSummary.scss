.dims_comm_wrap {
    padding: 0 24px;
    height: calc(100vh - 130px);
    overflow-y: auto;

    .dc_btns {
        padding: 14px 0 6px 0;
    }

    .dc_main {
        .sys_dom {
            padding-top: 14px;
        }

        .diy_dom {
            padding-top: 14px;
        }

        .example_txt {
            margin: 14px 0 0 14px;
            width: 40px;
            height: 20px;
            line-height: 20px;
            background: #F5F5F5;
            border-radius: 2px;
            text-align: center;
        }

        .rl_box {
            margin: 16px 0;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #E9E9E9;

            .rl_header {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                border-bottom: 1px dashed #e9e9e9;
                padding: 0 20px 14px 20px;

                .rlh_left {
                    display: flex;
                    flex-direction: row;

                    .icon {
                        height: 14px;
                        width: 14px;
                        padding: 3px 10px 3px 0;
                    }

                    .title {
                        height: 22px;
                        font-weight: 500;
                        font-size: 16px;
                        color: #262626;
                        line-height: 30px;
                    }

                    .score_info {
                        height: 22px;
                        font-size: 14px;
                        color: #8C8C8C;
                        line-height: 22px;
                        margin-top: 5px;
                    }

                    .sales_methodology {
                        font-size: 12px;
                        color: #999;
                        margin-left: 10px;
                        background-color: #f0f2f5;
                        padding: 1px 4px;
                        border-radius: 4px;
                    }
                }

                .rlh_right {
                    .btn {
                        height: 22px;
                        line-height: 22px;
                        color: #436BFF;
                        padding: 0 10px;
                        cursor: pointer;
                        font-size: 14px;
                    }

                    .line {
                        border-left: 1px solid #e9e9e9;
                    }
                }
            }

            .rl_body {
                font-size: 16px;
                color: #595959;
                line-height: 26px;
                padding: 12px;

                textarea {
                    width: 100%;
                    // height: 300px;
                    // border: none;
                    // resize: none;
                    // outline: none;
                    // font-size: 16px;
                    // color: #595959;
                    // font-family: PingFangSC, PingFang SC;
                    // box-shadow: none;
                }
            }
        }
    }
}