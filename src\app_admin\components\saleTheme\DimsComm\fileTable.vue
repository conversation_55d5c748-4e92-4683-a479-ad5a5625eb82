<template>
    <div class="filetable_wrap">
        <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
            <template #col_templateName="{ row }">
                <div @click="onPreview(row)" class="fileLink">
                    <div v-if="row.templateName" class="hasfile">
                        {{ row.templateName }}
                    </div>
                    <div v-else class="no_temp">
                        暂未上传模板，将使用AI生成需求探寻问题
                    </div>
                </div>
            </template>
            <template #_link_pre="{ row }">
                <el-button type="primary" text @click="cbDatas('enable', row)" v-if="row.templateName">
                    {{ row.enable ? '禁用' : '启用' }}
                </el-button>
                <el-button type="primary" text @click="cbDatas('upload', row)">
                    {{ row.templateName ? '替换' : '上传' }}
                </el-button>
            </template>
        </MyTable>
    </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue"
import { deleteXmFile } from "@/app_admin/tools/api.js"

const emit = defineEmits(['callback']);
const refTable = ref()
const currData = ref({})

const deleteXmlFileWrap = (row) => {
    const topicId = currData.value.id;
    const { categoryId, templateId } = row;
    deleteXmFile(topicId, categoryId, templateId).then(() => {
        emit('callback', 'reload')
    })
}

const datas = reactive({
    tableid: 'sale_theme_dims_comm',
    param: {},
    need_header: false,
    need_init_load: false,
    form: {},
    columns: ['categoryName', 'templateName'],
    template: ['templateName'],
    search_txt: "查询",
    show_btn_add: false,
    show_search: false,
    show_search_btn: true,
    show_link_column: true,
    show_link_delete: true,
    delete_hint_column: 'templateName',
    urlDelete: deleteXmlFileWrap,
    fnIsRowNeedDelBtn: (row) => {
        return !!row.templateId;
    }
});

const cbDatas = (action, data) => {
    const { categoryId } = data;
    const topicId = currData.value.id;
    if (action === "upload") {
        emit('callback', 'upload', { categoryId, topicId })
    }
}


const onPreview = (row) => {
    const { templateDownloadUrl, templateName } = row;
    if (templateDownloadUrl) {
        const ext = templateName.split('.').pop();
        const url = templateDownloadUrl;
        g.emitter.emit('app_preview_file', { ext, url });
    }
}


const init = () => {
    currData.value = toRaw(g.saleStore.topicDetail);
    const { categoryTemplates } = currData.value;
    refTable.value.update_data(categoryTemplates, categoryTemplates.length)
}

defineExpose({
    init, onPreview, refTable, datas, cbDatas, currData, MyTable
})

</script>

<style lang="scss">
.filetable_wrap {
    .table_wrap {
        .table_box .table_class {
            height: 300px !important;

            .hasfile {
                cursor: pointer;
                color: #436BFF;
            }

            .no_temp {
                color: #bfbfbf;
            }
        }
    }
}
</style>