<template>
  <div class="customer_type_wrap">
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
      <template #col_customer_type_name="scope">
        <span>{{ scope.row.name }}</span>
      </template>

      <template #col_status="scope">
        <el-dropdown trigger="click" @command="(command) => handleStatusChange(command, scope.row)">
          <div class="status_tag_wrap">
            <div class="dot" :class="scope.row.status === 1 ? 'dot_active' : 'dot_inactive'"></div>
            <span class="status_text">{{ scope.row.status === 1 ? '启用' : '禁用' }}</span>
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="1" v-if="scope.row.status === 0">启用</el-dropdown-item>
              <el-dropdown-item :command="0" v-if="scope.row.status === 1">禁用</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template #col_ct_description="scope">
        <span>{{ scope.row.description }}</span>
      </template>
    </MyTable>
    <DrawerCustomer ref="refDrawerCustomer" @callback="cbDatas" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getCustomerTypeSearch, deleteCustomerType, updateCustomerType } from "@/js/api.js"
import DrawerCustomer from "./DrawerCustomer.vue";
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

const refDrawerCustomer = ref()

const apiSearchWrap = (param) => {
  const new_param = {
    ...param,
    keyword: param.searchKey || '',
    pageNumber: param.pageNumber - 1
  }
  delete new_param['searchKey'];
  return getCustomerTypeSearch(new_param)
}

const refTable = ref(null);
const datas = reactive({
  tableid: 'admin_customer_type',
  param: {},
  need_header: true,
  need_init_load: true,
  form: {},
  delete_hint_column: 'name',
  columns: ['customer_type_name', 'status', 'ct_description'],
  template: ['customer_type_name', 'status', 'ct_description'],
  show_btn_add: true,
  show_link_column: true,
  show_link_edit: true,
  show_link_delete: true,
  show_link_view: false,
  urlGet: apiSearchWrap,
  urlDelete: deleteCustomerType,
});

const cbDatas = (action, data) => {
  if (action == 'init_add') {
    refDrawerCustomer.value.show_add()
  } else if (action == 'reload') {
    refTable.value.search()
  } else if (action == 'init_edit') {
    refDrawerCustomer.value.show_edit(data.id, data)
  }
}

const handleStatusChange = async (status, row) => {
  try {
    const res = await updateCustomerType(row.id, { status })
    if (res.code == 0) {
      ElMessage.success('状态更新成功')
      refTable.value.search()
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

defineExpose({
  MyTable,
  refDrawerCustomer,
  refTable,
  cbDatas,
})
</script>

<style lang='scss'>
.customer_type_wrap {
  padding: 24px;

  .table_box .table_class {
    height: calc(100vh - 185px) !important;

    .col_operation_ {
      width: 104px
    }
  }

  .dates_picker {
    margin-right: 10px;
  }
}

.status_tag_wrap {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 8px;
  border-radius: 4px;

  &:hover {
    background-color: #f5f5f5;
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .dot_active {
    background-color: #67C23A;
  }

  .dot_inactive {
    background-color: #909399;
  }

  .status_text {
    margin-right: 4px;
  }
}
</style>