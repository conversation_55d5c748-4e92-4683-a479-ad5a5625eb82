import { defineStore } from "pinia";
import * as apiCustomerVoice from "@/app_client/data/BusinessInsights/customerVoice.js";
import * as apiCompetitiveAnalysis from "@/app_client/data/BusinessInsights/competitiveAnalysis.js";
import * as apiReqInsight from "@/app_client/data/BusinessInsights/requirementInsight.js";
import { dimFilterData, sortNoChildLast } from "@/app_client/tools/utils.js";

const defaultTableData = { datas: [], totalNum: 0 };
export default defineStore("client_bi", {
  state: () => ({
    selectTabType: "CUSTOMER_VOICE", // 选中的tab COMPETITOR_RADAR,CUSTOMER_VOICE,requirementInsight
    crFbCompetitorId: "", // 竞品雷达 客户评级里客户id
    periodType: "lastweek", // 默认月份选择第一个 lastweek,lastmonth,lastquarter
    //竞品雷达 ---------------------------------
    crFilterCondition: {
      levelFirst: "",
      dimensionId: "",
      attitudeType: "",
      customerIds: "",
      parentId: "",
    },
    crSearchVersion: 0, //页面可以监控这个变量，如果有变化，可以触发搜索
    crDrdimensionList: [], // 维度列表,
    competitorsArr: [], // 竞品列表
    competitiveSelectRadarDataObj: {}, // 竞品数据
    volumedistribution: null, // 选中的竞品雷达
    volumedistributionAll: null, // 竞品雷达
    sentimentDistribution: null, // 竞品功能分布
    keywordSentiment: null, // 词云图
    // 颜色配置
    colorObject: {
      positive: "#04CCA4",
      negative: "#FF6B3B",
      neutral: "#BFBFBF",
    },
    selectAIAnalysisAll: "", // 所有竞品的ai分析
    selectAIAnalysis: "", // 选中竞品的ai分析
    evaluationDistrList: null, // 竞品评价分布
    crCustomerList: [], // 竞品雷达 客户列表

    //需求洞察---------------------------------
    reqDimensions: [],
    reqPriorities: [],
    reqTabelData: {},

    //客户之声---------------------------------
    cvDimensionList: [], // 维度列表,
    cvAlAnalyse: "", //AI 分析结果
    cvAlQuadrantData: {
      negative: [],
      positive: [],
    },
    //客户之声 客户反馈 选择条件级度,感情，客户
    filterConditionObj: {
      levelFirst: "",
      levelSelect: "",
      emotion: "",
      customer: "",
    },
    cvSearchVersion: 0, //页面可以监控这个变量，如果有变化，可以触发搜索
    //客户之声 客户反馈 右下主数据
    cvCustomerFeedbackListP: { ...defaultTableData },

    //客户之声 FAQ 上方 每个大类统计列表
    cvFaqDistList: [],
    //客户之声 FAQ 左边 问题数量
    cvFaqQuestionsCount: [],
    //客户之声 FAQ 问答回顾列表
    cvFaqQAListP: { ...defaultTableData },
    //客户之声 客户态度分布图
    cvCustomerAttiDistList: [],
    cvCustomerList: [], // 客户之声 客户列表,
    cvDimensionFbCountList: [], //客户之声 维度反馈数列表
    defCloudWordConfig: {
      shape: "circle",
      sizeRange: [14, 36],
      rotationRange: [-90, 0],
      rotationStep: 90,
      gridSize: 16,
      drawOutOfBound: false,
      layoutAnimation: false,
      tooltip: true,
      textStyle: {
        color: function (params) {
          return params.data.color || "#333";
        },
      },
      title: "",
    },
  }),
  getters: {
    gFilterDays() {
      const map = {
        lastweek: 7,
        lastmonth: 30,
        lastquarter: 90,
      };
      return map[this.periodType];
    },
  },
  actions: {
    // '态度类型 0:积极 1:中性 2:消极',
    getStatusTagObj: (status) => {
      const cmap = {
        0: {
          text: "积极",
          class: "positive",
        },
        2: {
          text: "消极",
          class: "neutral",
        },
        1: {
          text: "中性",
          class: "middle",
        },
        POSITIVE: {
          text: "积极",
          class: "positive",
        },
        NEGATIVE: {
          text: "消极",
          class: "neutral",
        },
        NEUTRAL: {
          text: "中性",
          class: "middle",
        },
      };
      return cmap[status] || {};
    },
    //更新页面主tab
    setSelectTabType(value) {
      this.selectTabType = value;
    },
    updateCvFbVersion() {
      this.cvSearchVersion++;
    },
    updateCrFbVersion() {
      this.crSearchVersion++;
    },
    // 设置选中竞时间区间
    setSelectId(val) {
      this.periodType = val;
    },
    // 竞品雷达 获取竞品公司列表
    async getCvDimensionList() {
      // this.cvDimensionFbCountList =
      //   await apiCustomerVoice.getCustomerAttiAllDist(this.gFilterDays, {});
      let param = {};
      if (this.filterConditionObj.emotion) {
        param["attitudeType"] = this.filterConditionObj.emotion;
      }
      if (this.filterConditionObj.customer.length > 0) {
        param["customerIds"] = this.filterConditionObj.customer;
      }
      let Obj = await apiCompetitiveAnalysis.getDimensionList({
        days: this.gFilterDays,
        data: param,
      });
      // 将 cvDimensionFbCountList 中的数据关联到 dimensionList 中
      // dimensionList = dimFilterData(dimensionList, this.cvDimensionFbCountList);
      // dimensionList = sortNoChildLast(dimensionList);
      this.cvDimensionList = Obj.datas;
    },
    async getCrDimensionList(competitorId, data) {
      this.crDrdimensionList =
        await apiCompetitiveAnalysis.getCrDimensionListApi(
          competitorId || this.competitorId,
          this.periodType,
          data
        );
      return this.crDrdimensionList;
    },
    // 竞品雷达 ------------------------------------------------------------
    // 竞品雷达 获取所有竞品评价分布
    async getEvaluationDistribution() {
      this.competitorsArr = this.evaluationDistrList =
        await apiCompetitiveAnalysis.getEvaluationDistribution({
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取所有竞品声量分布
    async getVolumedistributionAll() {
      this.volumedistributionAll =
        await apiCompetitiveAnalysis.getVolumedistribution({
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取所有竞品AI分析
    async getCompetitorAIAnalysisAll() {
      this.selectAIAnalysisAll = "";
      this.selectAIAnalysisAll =
        await apiCompetitiveAnalysis.getCompetitorAIAnalysis({
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取选中竞品声量分布
    async getVolumedistribution(str) {
      this.volumedistribution =
        await apiCompetitiveAnalysis.getVolumedistribution({
          competitorId: str,
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取选中竞品情感分布
    async getSentimentDistribution(str) {
      this.sentimentDistribution =
        await apiCompetitiveAnalysis.getSentimentDistribution({
          competitorId: str,
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取选中竞品关键词情感分布
    async getKeywordSentiment(str) {
      this.keywordSentiment = await apiCompetitiveAnalysis.getKeywordSentiment({
        competitorId: str,
        periodType: this.periodType,
      });
    },
    // 竞品雷达 获取选中竞品AI分析
    async getCompetitorAIAnalysis(str) {
      this.selectAIAnalysis = "";
      this.selectAIAnalysis =
        await apiCompetitiveAnalysis.getCompetitorAIAnalysis({
          competitorId: str,
          periodType: this.periodType,
        });
    },
    // 竞品雷达 获取选中竞品所有维度相关信息列表
    async getCompetitorRadarData(data) {
      this.competitiveSelectRadarDataObj =
        await apiCompetitiveAnalysis.getCompetitorRadarData(data);

      return this.competitiveSelectRadarDataObj;
    },
    // 竞品雷达 获取选中竞品所有客户列表
    async getCrCustomerList(query = "") {
      if (this.crFbCompetitorId) {
        this.crCustomerList = await apiCompetitiveAnalysis.getCrCustomerListApi(
          {
            competitorId: this.crFbCompetitorId,
            periodType: this.periodType,
            customerName: query,
          }
        );
      }
    },
    // 客户之声 ------------------------------------------------------------
    setCrFilterCondition(val) {
      this.crFilterCondition = { ...this.crFilterCondition, ...val };
    },
    // 客户之声
    setFilterCondition(val) {
      this.filterConditionObj = { ...this.filterConditionObj, ...val };
    },
    //客户之声 客户反馈 右下主数据
    async getCvCustomerFeedbackList(param) {
      if (this.filterConditionObj.levelSelect) {
        param["dimensionIds"] = [this.filterConditionObj.levelSelect];
      }
      if (this.filterConditionObj.emotion) {
        param["attitudeType"] = this.filterConditionObj.emotion;
      }
      if (this.filterConditionObj.customer.length > 0) {
        param["customerIds"] = this.filterConditionObj.customer;
      }
      this.cvCustomerFeedbackListP =
        await apiCustomerVoice.GetCustomerFeedbackApi(this.gFilterDays, param);

      return this.cvCustomerFeedbackListP;
    },
    async getCvCustomerList(query = "") {
      this.cvCustomerList = await apiCustomerVoice.getCvCustomerListApi(
        this.gFilterDays,
        query
      );
    },
    //客户之声 FAQ 分布
    async getCvFaqDistList() {
      this.cvFaqDistList = await apiCustomerVoice.GetFaqDistListApi(
        this.gFilterDays
      );
    },
    //客户之声 FAQ 问题数量
    async getCvFaqQuestionsCount() {
      this.cvFaqQuestionsCount = await apiCustomerVoice.GetFaqQuestionsCountApi(
        this.gFilterDays
      );
    },
    //客户之声 FAQ 问答回顾列表
    async getCvFaqQAList(param) {
      this.cvFaqQAListP = await apiCustomerVoice.GetFaqQAListApi(
        this.gFilterDays,
        param
      );
    },
    //客户之声 客户态度分布图
    async getCvCustomerAttiDistList() {
      let list = await apiCustomerVoice.GetCustomerAttiDistListApi(
        this.gFilterDays
      );
      if (list.length > 10) {
        list = list.slice(0, 10);
      }
      this.cvCustomerAttiDistList = list;
    },
    async getFaqRecomendAnswer(id) {
      return await apiCustomerVoice.getFaqRecomendAnswer(id, this.gFilterDays);
    },
    //客户之声 客户反馈 两极评价
    async getQuadrantData() {
      this.cvAlQuadrantData = await apiCustomerVoice.getQuadrantData(
        this.gFilterDays
      );
    },
    async getVoiceAnalyse() {
      this.cvAlAnalyse = "";
      this.cvAlAnalyse = await apiCustomerVoice.getVoiceAnalyse(
        this.gFilterDays
      );
    },
    getCompetitors() {
      return apiCustomerVoice.getCompetitors(this.periodType);
    },
    //需求洞察 主表数据
    async getReqTabelData(obj) {
      this.reqTabelData = await apiReqInsight.getReqTabelData(
        this.gFilterDays,
        obj
      );
      return this.reqTabelData;
    },
  },
});
