<template>
  <div class="yxtbiz-joinlib_breadcrumbs pr">
    <i v-if="showLeft" class="pa z-19 yxt-icon-arrow-left text-center hand" @click.stop="scroll('left')" />
    <ul ref="wrap" class="pa over-hidden nowrap text-8c">
      <li
        v-for="(item, i) of list"
        :key="item.id"
        class="d-in-block"
        @click="select(item, i)"
      >
        <span v-if="i !== 0" class="d-in-block v-middle">/</span>
        <label class="nowrap ellipsis v-middle" :class="{'hand': i !== list.length - 1}">{{ item.label }}</label>
      </li>
    </ul>
    <i v-if="showRight" class="pa z-19 yxt-icon-arrow-right text-center hand" @click.stop="scroll('right')" />
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    list: {
      type: Array,
      default() { return []; }
    }
  },
  data() {
    return {
      showLeft: false,
      showRight: false,
      wrapWidth: 0
    };
  },
  watch: {
    list: {
      handler(val) {
        this.$nextTick(() => {
          let wrap = this.$refs.wrap;
          let wrapWidth = this.wrapWidth || wrap.clientWidth; // 容器宽度
          let scrollWidth = wrap.scrollWidth; // 内容宽度
          setTimeout(() => {
            wrap.scrollLeft = scrollWidth;
          }, 30);
          this.showRight = scrollWidth > wrapWidth;
          this.showLeft = false;
        });
      },
      immediate: true
    }
  },
  methods: {
    scroll(type) {
      let wrap = this.$refs.wrap;
      let wrapWidth = this.wrapWidth || wrap.clientWidth;
      let scrollLeft;
      // 左移
      if (type === 'left') {
        this.showRight = true;
        scrollLeft = wrap.scrollLeft + 113;
        // 左移长度 + 容器宽度 >= 内容宽度
        if (scrollLeft + wrapWidth >= wrap.scrollWidth) {
          this.showLeft = false;
        }
      } else {
        this.showLeft = true;
        scrollLeft = wrap.scrollLeft - 113;
        this.showRight = scrollLeft > 0;
      }
      wrap.scrollLeft = scrollLeft;
    },
    // scrollToRight() {
    //   let wrap = this.$refs.wrap;
    //   wrap.scrollLeft = wrap.scrollWidth;
    // },
    select(item, i) {
      if (this.list.length - 1 !== i) {
        this.$emit('change', item, i);
      }
    }
  }
};
</script>
