<template>
    <div class="suggested-contacts" v-if="visible && suggestions.length > 0">
        <div class="suggest-title">猜你想找</div>
        <div class="suggest-list">
            <div v-for="item in suggestions" :key="item.id" class="suggest-item flex-row" @click="onSelect(item)">
                <div class="suggest-icon">
                    <userIcon :size="30" :src="item.imgUrl" :name="item.name" :showname="false" />
                </div>
                <div class="suggest-info flex-col">
                    <span class="suggest-name">{{ item.name }}</span>
                    <span class="suggest-position">{{ item.position }}</span>
                    <span class="suggest-dept">{{ item.deptFullName }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getParticipantRecommend } from '@/js/api'

// {
// 		"deptFullName":"测专业版",
// 		"name":"（勿动）zmmadmin",
// 		"position":"",
// 		"ssoUserId":"91d8ef8c-8452-452c-982a-0e44a5ae7d74",
// 		"userId":""
// 	}

const props = defineProps({
    multiple: {
        type: Boolean,
        default: false
    }
})

let allSuggestions = []
const suggestions = ref([])
const hasSelected = ref([])
const visible = ref(false)


const show = () => {
    visible.value = true
}

const hide = () => {
    visible.value = false
}

const emit = defineEmits(['select'])

const onSelect = (item) => {
    if (props.multiple) {
        hasSelected.value.push(item.name)
    } else {
        hasSelected.value = [item.name]
    }
    suggestions.value = allSuggestions.filter(item => !hasSelected.value.includes(item.name)).slice(0, 10)
    emit('select', item)
}

const updateSelected = (names) => {
    hasSelected.value = names
    suggestions.value = allSuggestions.filter(item => !hasSelected.value.includes(item.name)).slice(0, 10)
}

// {
// 		"deptFullName":"测专业版",
// 		"name":"（勿动）zmmadmin",
// 		"position":"",
// 		"ssoUserId":"91d8ef8c-8452-452c-982a-0e44a5ae7d74",
// 		"userId":""
// 	}


const getSuggestions = async () => {
    const { name, id, ssoUserId, position, departmentName } = g.appStore.user;
    const my = {
        name, position, userId: id, deptFullName: departmentName, ssoUserId
    };
    if (!props.multiple) {
        allSuggestions = [my]
    } else {
        allSuggestions = []
    }
    allSuggestions = [...allSuggestions, ... await getParticipantRecommend()]
    suggestions.value = allSuggestions.slice(0, 10)
}

onMounted(() => {
    getSuggestions()
})

defineExpose({
    suggestions,
    getSuggestions,
    hasSelected,
    onSelect,
    visible,
    show,
    hide,
    updateSelected
})

</script>

<style lang="scss" scoped>
.suggested-contacts {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 2000;

    .suggest-title {
        padding: 8px 12px;
        color: #8c8c8c;
        font-size: 12px;
    }

    .suggest-list {
        max-height: 200px;
        overflow-y: auto;
    }

    .suggest-item {
        padding: 8px 12px;
        cursor: pointer;

        &:hover {
            background-color: #F5F7FD;
        }

        .suggest-icon {
            margin-right: 12px;
            padding: 8px 0 0 3px;
        }

        .suggest-info {
            .suggest-name {
                font-size: 14px;
                color: #262626;
            }

            .suggest-position,
            .suggest-dept {
                font-size: 12px;
                color: #8c8c8c;
            }
        }
    }
}
</style>