// 更新简化标签的函数
export const simplifyLabel = (label) => {
    const parts = label.split(' (');
    if (parts.length > 2) {
        // 如果有两个或更多的括号，移除最后一个括号及其内容
        return parts.slice(0, -1).join(' (');
    }
    // 如果只有一个括号，保留括号内的内容
    return label;
};

export const getWinSizeFromRouter = (routers) => {
    const b = routers[0].children.map((item) => {
        const { path, meta } = item;
        return {
            id: path,
            path: '/electron/' + path,
            meta,
        }
    })

    let winSizes = {}
    for (let i = 0; i < b.length; i++) {
        const { id, meta } = b[i];
        winSizes[id] = meta
    }
    return winSizes;
}

export const MeetStatus = {
    na: -1,
    connecting: 0,
    started: 1,
    paused: 2,
    resumed: 3,
    reconnecting: 4,
    reconnected: 5,
    stop: 6,
    low_power_pause: 7,
    low_power_stop: 8,
    no_network: 9
}

export const pingRequest = () => {
    return new Promise((resolve, reject) => {
        const timestamp = new Date().getTime()
        const url = `${import.meta.env.VITE_DOWNLOAD_PATH}ping?t=${timestamp}`

        fetch(url, {
            method: 'GET',
            cache: 'no-store',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        }).then(data => data.json())
            .then(data => {
                resolve(true)
            })
            .catch(() => {
                resolve(false)
            })
    })
}

export const reloadSchedule = (interval = 1000) => {
    g.electronStore.sendMessage('main', 'reload_schedule', interval)
}