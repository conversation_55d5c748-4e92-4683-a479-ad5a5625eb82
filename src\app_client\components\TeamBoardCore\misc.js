import { round } from "@/js/utils.js"

export const column_widths = {
    deptName: 300,
    teamCount: 120,
    planCount: 120,
    planCustomerCount: 120,
    visitCount: 120,
    visitCustomerCount: 120,
    avgVisitTime: 120,
}

export const default_column_width = 160;

export const numal_team_column = [
    "totalVisitPlanCount",      // 沟通计划总数
    "visitPlanCompletionRate",  // 沟通计划完成率
    "visitPlanCustomerCount",   // 沟通计划覆盖客户数
    "averageVisitPlanPerPerson", // 人均沟通计划数
    "totalVisitCount",          // 沟通总次数
    "visitedCustomerCount",     // 沟通覆盖客户数
    "averageVisitsPerPerson",   // 人均沟通次数
    "visitTargetAchievementRate", // 沟通目标达成率
    "salesAbilityPassRate",     // 能力评估达标率
    "taskCompletePassRate", // 任务完成达标率
]

export const numal_member_column = [
    "totalVisitPlanCount",      // 沟通计划总数
    "visitPlanCompletionRate",  // 沟通计划完成率
    "visitPlanCustomerCount",   // 沟通计划覆盖客户数
    "totalVisitCount",          // 沟通总次数
    "visitedCustomerCount",     // 沟通覆盖客户数
    "visitTargetAchievementRate", // 沟通目标达成率 
    "salesAbilityPassRate",     // 能力评估达标率
    "taskCompletePassRate", // 任务达成达标率
]



export const getColumns = (team) => {
    const memberColumns = [
        'userName',
        'userAccount',
        'deptName',
        'managerName',
        ...numal_member_column
    ];

    const teamColumns = [
        'deptName',
        ...numal_team_column
    ];

    return team ? teamColumns : memberColumns;
}

export const type_column_hints = {
    "visitPlanCompletionRate": "基于沟通计划录制的沟通记录数 / 沟通计划总数",
    "totalVisitCount": "仅统计有效沟通次数，沟通记录处理状态为【处理失败、文件处理中、会话字幕量不足】标识为无效数据",
    "visitTargetAchievementRate": "沟通目标评估结果为「达成」的沟通次数 / 沟通总次数",
    "salesAbilityPassRate": " 沟通辅导中能力评估得分 >= 80分的沟通次数 / 沟通总次数",
    "performanceExpectationsRate": "沟通辅导中任务达成率 >= 80 % 的沟通次数 / 沟通总次数"
}


export const type_hints = {
    "visitPlan": "沟通计划完成率：基于沟通计划录制的沟通记录数 / 沟通计划总数",
    "visitRecord": "沟通总次数：仅统计有效沟通次数，沟通记录处理状态为【处理失败、文件处理中、会话字幕量不足】标识为无效数据",
    "visitPerformance": "1. 能力评估达标率：沟通辅导中能力评估得分 >= 80分的沟通次数 / 沟通总次数 <br/>  2. 任务完成达标率：沟通辅导中任务达成率 >= 80 % 的沟通次数 / 沟通总次数"
}

export const categoryInfo = {
    visitPlan: {
        label: '沟通计划',
        url: '/client/team/visit?tab=visitPlan'
    },
    visitRecord: {
        label: '沟通记录',
        url: '/client/team/customer'
    },
    visitPerformance: {
        label: '沟通表现',
        url: '/client/team/visit?tab=visitRecord'
    }
}

//scope: both: 团队和成员，dept: 团队，member: 成员
export const StatisColumnTypes = [
    // 沟通计划总数
    { field: "totalVisitPlanCount", value: "", category: "visitPlan", scope: 'both' },
    { field: "visitPlanCustomerCount", value: "", category: "visitPlan", scope: 'both' },
    { field: "visitPlanCompletionRate", value: "", category: "visitPlan", scope: 'both' },
    { field: "averageVisitPlanPerPerson", value: "", category: "visitPlan", scope: 'dept' },
    { field: "completedVisitPlanCount", value: "", category: "visitPlan", scope: 'dept', hide: true },

    // 沟通记录
    { field: "totalVisitCount", value: "", category: "visitRecord", scope: 'both' },
    { field: "visitedCustomerCount", value: "", category: "visitRecord", scope: 'both' },
    { field: "averageVisitsPerPerson", value: "", category: "visitRecord", scope: 'dept' },

    // 沟通表现
    { field: "salesAbilityPassRate", value: "", category: "visitPerformance", scope: 'both' },
    { field: "taskCompletePassRate", value: "", category: "visitPerformance", scope: 'both' },
    { field: "salesAbilityPassCount", value: "", category: "visitPerformance", scope: 'both', hide: true },
]

export const convertOverviewData = (res) => {
    const hourFields = [
        "averageVisitDurationPerPerson",
        "totalVisitDuration",
    ]
    let columnData = JSON.parse(JSON.stringify(StatisColumnTypes));

    // 后台api不统一，只能这样转下
    if (!res.data.datas) {
        res.data.datas = [res.data];
    }

    if (res.code === 0 && res.data.datas && res.data.datas.length > 0) {
        // Sort data by reportTime in descending order
        const sortedData = res.data.datas.sort((a, b) => b.reportTime - a.reportTime);
        const latestData = sortedData[0];
        const previousData = sortedData[1]; // May be undefined if only one period

        for (let i = 0; i < columnData.length; i++) {
            let item = columnData[i];
            let value = latestData[item.field];

            // Convert hour fields
            if (hourFields.includes(item.field)) {
                value = value / 3600;
                value = value.toFixed(1);
            }

            // Format the value
            columnData[i].value = item.field.toLowerCase().includes("rate")
                ? `${((value || 0) * 100).toFixed(0)}%`
                : (value || 0).toString();

            // Only calculate diff_value if previous period exists
            if (previousData) {
                const currentValue = latestData[item.field] || 0;
                const prevValue = previousData[item.field] || 0;

                // Calculate percentage change
                let diffValue = 0;
                if (prevValue !== 0) {
                    diffValue = ((currentValue - prevValue) / prevValue * 100).toFixed(1);
                } else if (currentValue !== 0) {
                    diffValue = '100.0';
                }

                columnData[i].diff_value = diffValue;
            }
        }
    }
    return columnData;
}

export const isLowerThanAvg = (avg, row, field) => {
    if (!avg || row === avg) {
        return false;
    }
    const currentValue = row[field];
    const overviewValue = avg[field];
    // 如果值为空或未定义，不显示高亮
    if (currentValue === null || currentValue === undefined ||
        overviewValue === null || overviewValue === undefined) {
        return false;
    }
    return currentValue < overviewValue;
};

export const getReportDate = () => {
    const route = useRoute();
    const isReport = location.href.indexOf('team_report') > 0;
    return isReport ? route.params.reportDate : '';
}


export const formatValue = (value, round_count = 0) => {
    if (value === 0 || !value) {
        return 0
    }
    if (typeof value === 'number') {
        return round(value, round_count)
    } else if (typeof value === 'string') {
        if (value.indexOf('.') > -1 && !value.includes('%')) {
            return parseFloat(value).toFixed(round_count)
        }
        return value
    }
    return value
}
