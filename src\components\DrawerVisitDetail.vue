<template>
    <el-drawer v-model="is_show" direction="rtl" class="arrange_visit_wrap" :close-on-click-modal="true">
        <template #header>
            <div class="vd_title">
                计划详情
            </div>
        </template>
        <template #default>
            <VisitDetail ref="refVisitDetail" @callback="onCallback" />
        </template>
    </el-drawer>
</template>

<script setup>
import VisitDetail from '@/components/VisitDetail/VisitDetail.vue'
const refVisitDetail = ref()
const is_show = ref(false)
const emit = defineEmits(['callback'])

const props = defineProps({
    readonly: {
        type: Boolean,
        required: false,
        default: false
    }
})

const onCancel = () => {
    is_show.value = false;
}

const onConfirm = () => {
    refVisitDetail.value.onConfirm();
}

const onCallback = (type, data) => {
    is_show.value = false;
    if (type == 'reload') {
        emit('callback', 'reload');
    } else if (type == 'edit_plan') {
        emit('callback', 'edit_plan', data)
    } else if (type == 'has_error') {
        is_show.value = false;
    }
}

const init = (plan) => {
    is_show.value = true;
    nextTick(() => {
        refVisitDetail.value.init(plan, props.readonly);
    })
}


defineExpose({
    init, onCancel, onConfirm, is_show, VisitDetail, refVisitDetail
})

</script>

<style lang="scss">
.arrange_visit_wrap {
    width: 600px !important;

    .el-drawer__header {
        height: 56px;
        padding: 0;
        border: 1px solid #E9E9E9;
        font-size: 16px;
        color: #262626;
        margin-bottom: 0;

        .vd_title {
            font-weight: 500;
        }
    }

    .el-drawer__body {
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        padding: 10px 24px;

        .ed_main {
            height: calc(100vh - 100px);

            .av_item {
                .av_item_value {
                    width: 90%;
                }
            }
        }
    }
}
</style>
