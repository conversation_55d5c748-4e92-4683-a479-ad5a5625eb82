<template>
    <div class="boxs_wrap">
        <div class="bs_label">
            {{ props.label }}
        </div>
        <div class="bs_value">
            {{ props.value }}
        </div>
    </div>
</template>

<script setup>

const props = defineProps(['label', 'value']);
</script>

<style lang="scss">
.boxs_wrap {
    width: 316px;
    height: 70px;
    border-radius: 4px;
    border: 1px solid #F0F0F0;
    margin-right: 12px;
    padding: 24px;
    display: flex;
    flex-direction: column;

    .bs_label {
        width: 210px;
        height: 22px;
        font-weight: 500;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
    }

    .bs_value {
        height: 38px;
        font-size: 30px;
        color: #262626;
        line-height: 38px;
        margin-top: 16px;
    }
}
</style>