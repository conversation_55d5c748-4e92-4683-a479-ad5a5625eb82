<template>
  <el-drawer v-model="isShow" :with-header="true" size="640" show-close @close="onClose"
    class="drawer_requirement-insight-wrap" direction="rtl">
    <template #header>
      <div class="vd_title flex-row">
        <div class="vd_title_name">查看来源</div>
      </div>
    </template>
    <div v-loading="loading" v-ai-tip="'center'">
      <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)" style="margin: 0 auto;"
        v-if="!fbList.length && !qaList.length">
      </el-empty>
      <div class="detail-content" v-else>
        <div class="feedback-list-wrap-drawer" v-if="fbList.length > 0">
          <FeedbackList :data="fbList" />
        </div>
        <div class="pagination-wrap" v-if="total > 0">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
            layout="total,  prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            class="checkSource_wrap_pagination" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import RightArrowIcon from "@/icons/right_arrow.vue";
import FeedbackList from "./FeedbackList.vue";
import { getOssUrl } from "@/js/utils.js";
import { getReqTabelDetail } from "@/app_client/data/BusinessInsights/requirementInsight.js";
const url = ref("");
const isShow = ref(false);
const refIframe = ref();
const pageInfo = ref({});
const fbList = ref([])
const qaList = ref([])
const loading = ref(false)

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(4)
const total = ref(0)

const show = async (data) => {
  pageInfo.value = {}
  fbList.value = []
  qaList.value = []
  currentPage.value = 1
  total.value = 0
  isShow.value = true;
  pageInfo.value = data;
  await fetchData()
};

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      pageNumber: currentPage.value,
      pageSize: pageSize.value
    }
    const res = await getReqTabelDetail(pageInfo.value.id, params);
    if (res.code == 0) {
      fbList.value = res.data?.datas || []
      total.value = res.data?.totalNum || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
};

const onClose = () => {
  isShow.value = false;
  pageInfo.value = {};
};

// 分页事件处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

defineExpose({ onClose, show, url, isShow, refIframe, RightArrowIcon });
</script>

<style lang="scss">
.drawer_requirement-insight-wrap {
  .el-drawer__header {
    padding: 16px 28px 16px 4px !important;
    height: auto;

    .vd_title {
      .vd_title_name {
        color: #262626;
        font-weight: 700;
      }
    }
  }

  .checkSource_wrap_pagination {
    height: 36px;
    display: flex;
    justify-content: flex-end;

    .el-pager {
      margin: 0 12px;

      li {
        margin: 0 4px;
      }
    }

    // margin: 8px 0 0 8px !important;
    // margin: 12px 24px;
  }

  .el-drawer__footer {
    padding: 12px 24px;
    box-shadow: inset 0px 1px 0px 0px #e9e9e9;

    .el-button--primary {
      margin-left: 16px;
    }
  }

  .el-drawer__body {
    margin-bottom: 0 !important;
    padding: 24px !important;
  }

  .detail-content {
    padding: 24px;
    background: #f9fafc;
    border-radius: 4px;

    .faq-origin-drawer {
      .qa-list {
        margin-top: 20px;
      }
    }

    .wrap-drawer-title {
      font-weight: 700;
      font-size: 12px;
      color: #436BFF;
      line-height: 18px;
      text-align: right;
      background: #E6EBFD;
      border-radius: 2px;
      padding: 1px 4px;
      margin-bottom: 20px;
    }

    .feedback_list_wrap {
      // padding: 0 24px 0 24px;

      .feedback_item {
        background: #fff;
        border-radius: 8px;
      }
    }

    .pagination-wrap {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .cr_title {
    margin: 0;
    font-weight: 700;
    font-size: 16px;
    color: #262626;
    line-height: 26px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 14px;
      background: #436BFF;
      margin-right: 8px;
      border-radius: 2px;
    }
  }
}
</style>
