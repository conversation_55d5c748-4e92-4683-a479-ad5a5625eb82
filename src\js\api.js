import { getHttp } from "@/js/request.js";
import uploadFile from "@/js/upload_file";

const _http = getHttp();

// 获得我所属部门下的结构树
export const getOrgMymgtdprt = () => _http.get(`api/contact/v1/org/mymgtdprt`);

// 获取所有组织架构
export const getOrgDprt = () => _http.get(`api/contact/v1/org/dprt`)

// 获取导航权限
export const getAdminNavPermission = (param) =>
  _http.post(`api/xmate/configs/user/navigate/permission`, param);

// 获得我用户信息
export const getMyUserInfo = (token = "") =>
  _http.get(`api/conference/user/info`, {}, token);

// 获得销售配置
export const getSalesConfigure = () =>
  _http.get(`api/conference/sales/configure`);

// 创建拜访日程
export const createSchedule = (data) =>
  _http.post(`api/conference/schedule`, data);

// 更新拜访日程
export const updateSchedule = (data) =>
  _http.put(`api/conference/schedule/single`, data);

// 删除拜访日程
export const deleteSchedule = (scheduleId) =>
  _http.delete(`api/xmate/user/plans/${scheduleId}`);

// 列出拜访日程,我的拜访计划
export const getScheduleList = (data) =>
  _http.post(`api/xmate/user/plans`, data);

// 列出拜访日程,我的团队拜访计划
export const getTeamScheduleList = (data) =>
  _http.post(`api/xmate/team/plans`, data);

// 取得用户拜访计划详情
export const getScheduleDetail = (scheduleId) =>
  _http.get(`api/xmate/user/plans/${scheduleId}`);

//查询公司内部用户列表
export const getUserList = (data) =>
  _http.post(`api/conference/user/search`, data);

//查询公司内部用户列表
export const getUserListPager = (data) =>
  _http.post(`api/conference/partner/search/pager`, data);

// 获取客户联系人
export const getCompanyContact = (data) =>
  _http.post(`api/xmate/customer/contact`, data);

// 获取拜访客户联系人/已有客户角色
export const getCustomerContact = (confId) => _http.get(`api/conference/${confId}/customer/contact`)

// 上传拜访附件
export const uploadConferenceFile = (
  formData,
  onProgress,
  onSuccess,
  onFail
) => {
  const url = `${g.config.meetApiHost}/rest/api/conference/schedule/file`;
  return uploadFile(url, formData, onProgress, onSuccess, onFail);
};

// save销售助手的外部伙伴
export const saveExternalRole = (customerId, data) =>
  _http.post(`api/xmate/customer/${customerId}/contact`, data);

// 取得岗位设置
export const getPositionConfig = () => _http.get(`api/xmate/position/config`);

// 取得准备状态
export const getPrepare = (scheduleId) =>
  _http.get(`api/xmate/user/plans/${scheduleId}/prepare`);

// 批量取得商品详情
export const getGoodsDetails = (ids) =>
  _http.post(`api/conference/goods/details`, { ids });

// 取得主题标签详情
export const getXMTopicDetail = (topic_id) =>
  _http.get(`api/xmate/meeting/topic/${topic_id}`);

// 取得参会者推荐
export const getParticipantRecommend = (data) =>
  _http.get(`api/participant/recommend`, data);

// 记录飞书扣子返回内容
export const uploadCozeMsg = (chatId, msg) =>
  _http.post(`api/coze/chat/${chatId}/message`, { msg });

// 创建新的智能产品推荐师对话
export const createGoodsChat = (data) => _http.post(`api/goods/chat`, data);

// 获取智能产品推荐对话内容
export const getChatHistory = (chatId) => _http.get(`api/goods/chat/${chatId}`);

// 获取智能产品推荐对话列表
// topicType：
// PRODUCT_RECOMMENDATION(1), //商品推荐
// QUOTATION_ASSISTANT(2), //报价小助手
// LECTURER_RESOURCES //讲师资源一点通
export const getGoodsChatList = (data) =>
  _http.post(`api/goods/chat/list`, data);

// 删除智能产品推荐对话内容
export const deleteGoodsChatList = (chatId) =>
  _http.delete(`api/goods/chat/${chatId}`);

// 更改智能产品推荐对话内容
export const updateChat = (chatId, data) =>
  _http.patch(`api/goods/chat/${chatId}`, data);

// 批量删除智能产品推荐对话内容
export const deleteChat = (ids) => _http.delete(`api/goods/chat`, { ids });

// 通过qcc查询企业信息
export const searchCompanyAPI = (companyName) =>
  _http.post(`api/company/search`, { companyName });

// 通过我的企业信息
export const searchCustomerAPI = (data) =>
  _http.post(`api/customer/search`, data);

// 更新录制文件信息
export const updateRecordInfo = (data) =>
  _http.put(`api/conference/record/info`, data);

// 搜索商品详情 categoryId: 商品类别id, 如果全部类别，就用0
export const getCategoryGoods = (categoryId, data) =>
  _http.post(`api/conference/goods/category/${categoryId}/list`, data);

// 取得剪辑片段的树状结构
export const getClipLibTree = (isadmin) => {
  if (isadmin) {
    return _http.get(`api/clip/lib/manage/tree`);
  } else {
    return _http.get(`api/clip/lib/tree/view/permission`);
  }
}

// 获取会议知识包
export const getConferenceKnowledgePackage = (conferenceId) =>
  _http.get(`api/userkng/conference/${conferenceId}`);

// 提升课程推荐要素和容量检查
export const getCheckFactor = () => _http.get(`api/userkng/check/factor`);

// 获取表单字段列表
export const getFormFields = (formCode) => _http.get(`api/form/definition/fields?formCode=${formCode}`);

// 获取剪辑分类权限详情
export const getClipFolderPermission = (id) => _http.get(`api/clip/permission/${id}`);


export const updateInmeetCompanyName = (confId, customerName) => {
  const { orgId, id } = g.appStore.user;
  return _http.post(`api/org/${orgId}/user/${id}/conference/${confId}`, { customerName });
}


// 搜索客户类型，带分页
export const getCustomerTypeSearch = (data) => _http.post(`api/v1/customer-type/search`, data);

// 客户类型列表
export const getCustomerTypeList = () => _http.get(`api/v1/customer-type/list`);

// 获取客户类型详情
export const getCustomerTypeDetail = (id) => _http.get(`api/v1/customer-type/${id}`);

// 创建客户类型
export const createCustomerType = (data) => _http.post(`api/v1/customer-type`, data);

// 更新客户类型
export const updateCustomerType = (id, data) => _http.put(`api/v1/customer-type/${id}`, data);

// 删除客户类型
export const deleteCustomerType = ({ id }) => _http.delete(`api/v1/customer-type/${id}`);


// 创建文件对象 
export const createFile = (data) => _http.post(`api/xmatefile/createFile`, data);

// 创建客户 
export const createCustomer = (data) => _http.post(`api/customer`, data);

// 更新客户 
export const updateCustomer = (id, data) => _http.put(`api/customer/${id}`, data);

// 删除客户
export const deleteCustomer = (data) => _http.delete(`api/customer`, data);

// 获取客户基本信息
export const getCustomerInfo = (id) => _http.get(`api/customer/${id}`);

// 导入客户 
export const importCustomer = (data) => _http.post(`api/customer/import?source=${data.source}&fileId=${data.fileId}`, {});

// 导出客户 
export const exportCustomer = (data) => _http.download(`api/customer/export`, data);

// 查询导入进度
export const getImportProcess = (fileId) => _http.get(`api/customer/import/process/${fileId}`);

// 下载导入模板
export const downloadImportTemplate = (data) => _http.download(`api/customer/export/template`, data, "GET");

//获取销售能力-任务维度评估系统设置
export const getSalesAllDims = () => _http.get(`api/xmate/configs/topic/dimension/sales_tasks/assessment/settings`);
// 获取进行中的拜访
export const getOngoingConference = () => _http.get(`api/conference/ongoing`);

