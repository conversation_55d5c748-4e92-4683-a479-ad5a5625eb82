<template>
  <div v-if="modelValue.visible" class="selection-area" :style="selectionStyle" @mousedown.stop="startDrag"
    @contextmenu.prevent="showContextMenu">
    <div class="resize-handle left" @mousedown.stop="startDrag($event, 'left')"></div>
    <div class="resize-handle right" @mousedown.stop="startDrag($event, 'right')"></div>

    <!-- 右键菜单 -->
    <div v-show="showMenu" class="context-menu" :style="{
      position: 'fixed',
      left: menuPosition.left + 'px',
      top: menuPosition.top + 'px',
    }">
      <div class="menu-item" @click="removeSelection">取消选择</div>
    </div>
  </div>
</template>

<script setup>
import { getItemStyle } from "@/app_clipper/tools/utils.js";

// 添加防抖函数
const debounce = (fn, delay = 100) => {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(null, args);
    }, delay);
  };
};

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      visible: false,
      left: 0,
      width: 0,
      startTime: 0,
      endTime: 0,
    }),
  },
  containerWidth: {
    type: Number,
    required: true,
  },
  duration: {
    type: Number,
    required: true,
  },
  timelineRef: {
    type: Object,
    required: true,
  },
  allSelections: {
    type: Array,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "selectionChange", "remove"]);

const isDragging = ref(false);
const dragType = ref("move");
const dragStartX = ref(0);
const initialLeft = ref(0);
const initialWidth = ref(0);

// 添加右键菜单相关状态
const showMenu = ref(false);
const menuPosition = ref({
  top: "0px",
  left: "0px",
});

// 计算选择区域的样式
const selectionStyle = computed(() => {
  if (!props.modelValue.visible) return {};

  return getItemStyle({
    startSeconds: props.modelValue.startTime,
    endSeconds: props.modelValue.endTime
  }, 0);
});

// 显示右键菜单
const showContextMenu = (e) => {
  e.preventDefault();
  showMenu.value = true;

  // 使用鼠标事件的客户端坐标
  menuPosition.value = {
    left: e.clientX,
    // 在鼠标点击位置上方显示菜单
    top: e.clientY - 40, // 40是菜单的大致高度
  };

  // 点击其他地方时关闭菜单
  const closeMenu = (e) => {
    if (!e.target.closest(".context-menu")) {
      showMenu.value = false;
      document.removeEventListener("click", closeMenu);
    }
  };

  document.addEventListener("click", closeMenu);
};

// 移除当前选区
const removeSelection = () => {
  showMenu.value = false;
  emit("remove", props.index);
};

// 创建防抖的事件发送函数
const emitSelectionChange = debounce((selection) => {
  emit("selectionChange", selection);
}, 50); // 50ms 的延迟，你可以根据需要调整

// 检查是否与其他区域重叠
const checkOverlap = (left, width) => {
  const right = left + width;
  const duration = g.clipperStore.getPageInfo("duration");
  const containerWidth = g.clipperStore.getPageInfo("containerWidth");

  // 遍历其他所有选择区域
  for (let i = 0; i < props.allSelections.length; i++) {
    if (i === props.index) continue; // 跳过自己

    const other = props.allSelections[i];
    const otherLeft = (other.startTime / duration) * containerWidth;
    const otherWidth = ((other.endTime - other.startTime) / duration) * containerWidth;
    const otherRight = otherLeft + otherWidth;

    // 检查是否重叠
    if (!(right <= otherLeft || left >= otherRight)) {
      return true; // 发生重叠
    }
  }

  return false; // 没有重叠
};

// 开始拖拽选择
const startDrag = (e, type = "move") => {
  isDragging.value = true;
  dragType.value = type;
  dragStartX.value = e.clientX;

  // 使用 g.clipperStore 获取容器宽度和持续时间
  const duration = g.clipperStore.getPageInfo("duration");
  const containerWidth = g.clipperStore.getPageInfo("containerWidth");

  initialLeft.value = (props.modelValue.startTime / duration) * containerWidth;
  initialWidth.value = ((props.modelValue.endTime - props.modelValue.startTime) / duration) * containerWidth;

  const handleMouseMove = (e) => {
    if (!isDragging.value) return;

    const deltaX = e.clientX - dragStartX.value;
    let updatedSelection = { ...props.modelValue };
    let newLeft = initialLeft.value;
    let newWidth = initialWidth.value;

    // 根据拖拽类型计算新的位置和宽度
    if (dragType.value === "move") {
      newLeft = Math.max(0, initialLeft.value + deltaX);
      newWidth = initialWidth.value;
    } else if (dragType.value === "left") {
      newLeft = Math.max(0, initialLeft.value + deltaX);
      newWidth = Math.max(5, initialWidth.value - deltaX);
    } else if (dragType.value === "right") {
      newLeft = initialLeft.value;
      newWidth = Math.max(5, initialWidth.value + deltaX);
    }

    // 更新时间
    updatedSelection.startTime = (newLeft / containerWidth) * duration;
    updatedSelection.endTime = ((newLeft + newWidth) / containerWidth) * duration;

    emit("update:modelValue", updatedSelection);
    emitSelectionChange({
      startTime: Math.min(updatedSelection.startTime, updatedSelection.endTime),
      endTime: Math.max(updatedSelection.startTime, updatedSelection.endTime),
      limited: false,
    });
  };

  const handleMouseUp = () => {
    isDragging.value = false;

    // 计算当前位置
    const duration = g.clipperStore.getPageInfo("duration");
    const containerWidth = g.clipperStore.getPageInfo("containerWidth");
    const currentLeft = (props.modelValue.startTime / duration) * containerWidth;
    const currentWidth = ((props.modelValue.endTime - props.modelValue.startTime) / duration) * containerWidth;

    // 检查是否重叠
    if (checkOverlap(currentLeft, currentWidth)) {
      // 如果重叠，恢复到初始位置
      const updatedSelection = { ...props.modelValue };
      const startTime = (initialLeft.value / containerWidth) * duration;
      const endTime = ((initialLeft.value + initialWidth.value) / containerWidth) * duration;

      updatedSelection.startTime = startTime;
      updatedSelection.endTime = endTime;

      emit("update:modelValue", updatedSelection);
      emit("selectionChange", {
        startTime: Math.min(startTime, endTime),
        endTime: Math.max(startTime, endTime),
        limited: true,
      });
    }

    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};
</script>

<style lang="scss" scoped>
.selection-area {
  position: absolute;
  top: 0;
  height: 99%;
  background-color: rgba(24, 144, 255, 0.2);
  pointer-events: all;
  z-index: 50;
  border: 1px solid rgba(24, 144, 255, 0.4);
  cursor: move;

  .resize-handle {
    position: absolute;
    top: 0;
    width: 6px;
    height: 100%;
    cursor: ew-resize;
    z-index: 2; // 确保手柄在区域上层

    &.left {
      left: -3px;
    }

    &.right {
      right: -3px;
    }

    &:hover {
      background-color: rgba(24, 144, 255, 0.3);
    }

    &:active {
      background-color: rgba(24, 144, 255, 0.4);
    }
  }

  .context-menu {
    position: fixed; // 改为固定定位
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999; // 提高 z-index
    min-width: 120px;
    padding: 4px 0;

    .menu-item {
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.3s;
      user-select: none;
      color: #333;
      font-size: 14px;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
