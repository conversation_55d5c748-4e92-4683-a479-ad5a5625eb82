<template>
    <div class="left_min_wrap" v-if="isShow" @click="onClick">
        <div class="back_down">
            展开
        </div>
        <div class="icon">
            <PackDownIcon />
        </div>

    </div>
</template>

<script setup>

import PackDownIcon from "@/icons/packDown.vue"
const isShow = ref(false)
const emit = defineEmits(['callback'])
const show = () => {
    isShow.value = true;
}

const onClick = () => {
    isShow.value = false;
    emit('callback', 'packdown')
}

defineExpose({
    show,
    PackDownIcon
})

</script>

<style lang="scss">
.left_min_wrap {
    width: 22px;
    height: 20px;
    background: #FFFFFF;
    box-shadow: 0px 4px 24px 0px rgba(41, 49, 54, 0.12);
    border-radius: 0px 100px 100px 0px;
    position: fixed;
    display: flex;
    flex-direction: row;
    bottom: 100px;
    left: 0;
    padding: 16px;
    cursor: pointer;
    z-index: 1;

    .back_down {
        display: none;
        height: 24px;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 18px;
        margin-right: 6px;
    }

    .icon {
        color: #595959;
    }

    &:hover {
        width: 62px;
        height: 20px;
        background: linear-gradient(180deg, #6B90FF 0%, #436BFF 100%);
        box-shadow: 0px 4px 24px 0px rgba(41, 49, 54, 0.12);
        border-radius: 0px 100px 100px 0px;

        .back_down {
            display: block;
        }

        .icon {
            color: #fff;
        }
    }
}
</style>