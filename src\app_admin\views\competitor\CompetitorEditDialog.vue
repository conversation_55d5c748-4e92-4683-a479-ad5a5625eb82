<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close class="dia_edit_competitor_wrap">
    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules">
      <el-form-item label="名称" prop="commonName">
        <el-input v-model.trim="formData.commonName" maxlength="50" show-word-limit placeholder="请输入竞争对手常用称呼" />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-autocomplete v-model="formData.companyName" :fetch-suggestions="querySearchAsync" placeholder="请输入并选择公司名称"
          maxlength="50" :debounce="1000" />
      </el-form-item>
      <el-form-item label="别称" prop="alternativeName">
        <el-input v-model="new_tag" maxlength="50" show-word-limit placeholder="添加竞争对手其他称呼，输入后，按回车创建"
          @keyup.enter="onNewTag" :disabled="tags.length >= 20" />
        <div class="tags-container" v-if="tags.length > 0">
          <el-tag v-for="tag in tags" :key="tag" closable type="info" @close="onRemoveTag(tag)" class="tag-item">
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { nextTick, ref } from "vue";
import { searchCompanyAPI } from "@/js/api.js";
import Modal from "@/components/Modal.vue";
import { competitorApi } from "@/app_admin/api";

const emit = defineEmits(["callback"]);
const refModal = ref();
const refForm = ref();
const new_tag = ref("");
const tags = ref([]);
let lastKeyword = "";
let lastList = [];

const defaultForm = {
  commonName: "", //内部常用名
  companyName: "", //公司官方全称
  alternativeName: "", //代用名列表
};

const formData = ref({ ...defaultForm });

const rules = {
  commonName: [
    { required: true, message: '请输入竞争对手名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

const cfg = {
  width: "700px",
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
  tags.value = [];
  new_tag.value = "";
};

const querySearchAsync = (keyword, cb) => {
  if (keyword) {
    if (keyword == lastKeyword) {
      cb(lastList);
    } else {
      searchCompanyAPI(keyword).then((resp) => {
        if (resp.hasOwnProperty("data") && resp.data.length > 0) {
          lastKeyword = keyword;
          lastList = resp.data.map((x) => {
            return {
              value: x.name,
            };
          });
          cb(lastList);
        } else {
          cb([]);
        }
      });
    }
  } else {
    cb([]);
  }
};

const onNewTag = () => {
  if (new_tag.value.trim() && !tags.value.includes(new_tag.value.trim())) {
    tags.value.push(new_tag.value.trim());
    new_tag.value = "";
  }
};

const onRemoveTag = (tag) => {
  tags.value = tags.value.filter((x) => x != tag);
};

const show_add = () => {
  _resetForm();
  cfg["title"] = "添加竞争对手";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value?.resetFields();
  });
};

const show_edit = (data) => {
  formData.value = { ...data };
  cfg["title"] = "编辑竞争对手";
  tags.value = data["alternativeName"] ? data["alternativeName"].split(",").filter((x) => !!x) : [];
  refModal.value.show(cfg);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  refForm.value.validate((valid) => {
    if (valid) {
      const param = {
        ...formData.value,
        alternativeName: tags.value.join(",")
      };
      
      if (formData.value.id) {
        // 编辑
        competitorApi.updateCompetitor(formData.value.id, param).then(resp => {
          if (resp.code == 0) {
            ElMessage.success("修改成功");
            refModal.value.hide();
            emit("callback", "reload");
          } else {
            ElMessage.error(resp.message || "修改失败");
          }
        });
      } else {
        // 新增
        competitorApi.createCompetitor(param).then(resp => {
          if (resp.code == 0) {
            ElMessage.success("添加成功");
            refModal.value.hide();
            emit("callback", "reload");
          } else {
            ElMessage.error(resp.message || "添加失败");
          }
        });
      }
    }
  });
};

defineExpose({
  show_add,
  show_edit
});
</script>

<style lang="scss">
.dia_edit_competitor_wrap {
  .tags-container {
    margin-top: 8px;
    
    .tag-item {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
}
</style>