<template>
    <div class="no_access_wrap">
        <div class="pic">
            <Pic401 />
        </div>
        <span class="txt">{{ note }}</span>
        <div class="login_btn" @click="onLogin" v-if="!hasLogin">请登录后查看，点击登录</div>
    </div>
</template>
<script>
import Pic401 from "@/app_postmeet/icons/401.vue"

export default {
    components: { Pic401 },
    data() {
        return {
            ntime: 5,
            hasLogin: false,
            note: ''
        }
    },
    mounted() {
        this.hasLogin = !!g.postmeetStore.data.user.token;
    },
    methods: {
        show(note) {
            this.note = note
        },
        onLogin() {
            localStorage.setItem("gourl_postmeet", window.location.href)
            window.location.href = g.config.publicPath
        }
    },
};
</script>

<style lang="scss" scoped>
.no_access_wrap {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .txt {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 28px;
    }

    .login_btn {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #436bff;
        cursor: pointer;
        line-height: 28px;
        margin-top: 20px;
    }

    .login_note {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #bbb;
        line-height: 28px;
    }
}
</style>