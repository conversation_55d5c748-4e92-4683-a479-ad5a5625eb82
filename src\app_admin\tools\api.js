import { getHttp } from "@/js/request.js";
import uploadFile from "@/js/upload_file";

const _http = getHttp();

export const hpost = (url, param) => _http.post(url, param);

export const getLoginUrl = (domain) =>
  _http.get(`learning/login/url?domain=${domain}&client=web`);

//---------------------------------------------------------------------------------
// PPT类别
//---------------------------------------------------------------------------------
// 列出所有ppt类别
export const getPptCategorys = (data) =>
  _http.post(`api/ppt/category/list`, data);

// 增加ppt类别
export const addPptCategory = (data) => _http.post(`api/ppt/category`, data);

// 取得ppt类别的详细信息
export const getPptCategoryId = (categoryId) =>
  _http.get(`api/ppt/category/${categoryId}`);

// 更新ppt类别的详细信息
export const updatePptCategory = (data) =>
  _http.put(`api/ppt/category/${data.categoryId}`, data);

// 删除ppt类别
export const deletePptCategory = ({ id }) =>
  _http.delete(`api/ppt/category/${id}`);

//---------------------------------------------------------------------------------
// PPT文档
//---------------------------------------------------------------------------------
// 列出所有ppt文档
export const getPptDocs = (data) => _http.post(`api/ppt/doc`, data);

// 取得ppt文档的详细信息
export const getPptCateDocInfo = (categoryId, docId) =>
  _http.get(`api/ppt/category/${categoryId}/doc/${docId}`);
export const getPptDocInfo = (docId) =>
  _http.get(`api/ppt/category/doc/${docId}`);

// 上传PPT文档
export const uploadPpt = (
  categoryId,
  formData,
  onProgress,
  onSuccess,
  onFail
) => {
  const url = `${g.config.meetApiHost}/rest/api/ppt/category/${categoryId}/doc/upload`;
  return uploadFile(url, formData, onProgress, onSuccess, onFail);
};

// 删除ppt文档
export const deletePpt = ({ categoryId, id }) =>
  _http.delete(`api/ppt/category/${categoryId}/doc/${id}`);

// 更新ppt文档的内容
export const updatePpt = (categoryId, docId, data) =>
  _http.put(`api/ppt/category/${categoryId}/doc/${docId}/content`, data);

// 更新PPT文档Search内容
export const updatePptSearch = (categoryId, docId, data) =>
  _http.put(`api/ppt/category/${categoryId}/doc/${docId}/content/search`, data);

// 重新解析PPT文档
export const parseDoc = (categoryId, docId) =>
  _http.post(`api/ppt/category/${categoryId}/doc/${docId}/parse`, {});

// 取得上传文档匹配设置
export const getDocMatchType = () => _http.get(`api/ppt/doc/match/type`);

// 更新上传文档匹配设置
export const updateDocMatchType = (data) =>
  _http.post(`api/ppt/doc/match/type`, data);

//---------------------------------------------------------------------------------
//PPT模板-
//---------------------------------------------------------------------------------
// 列出所有ppt模板
export const getPptTemplate = (data) =>
  _http.post(`api/ppt/template/list`, data);

// 取得ppt模板的详细信息
export const getPptTemplateInfo = (templateId) =>
  _http.get(`api/ppt/template/${templateId}`);

// 修改PPT模板
export const updatePptTemplate = (templateId, data) =>
  _http.put(`api/ppt/template/${templateId}`, data);

// 创建PPT模板
export const createPptTemplate = (data) => _http.post(`api/ppt/template`, data);

// 删除PPT模板的设置
export const deletePptTemplate = ({ id }) =>
  _http.delete(`api/ppt/template/${id}`, {});

// 列出ppt模板的设置
export const getPptTemplateSettings = (templateId) =>
  _http.get(`api/ppt/template/${templateId}/setting`);

// 修改PPT模板的设置
export const updatePptTemplateSetting = (templateId, data) =>
  _http.put(`api/ppt/template/${templateId}/setting/configure`, data);

// 创建/更新全部PPT模板设置项名称
export const updatePptTemplateLabel = (templateId, data) =>
  _http.put(`api/ppt/template/${templateId}/setting/label`, data);

// 创建PPT模板的设置
export const createPptTemplateSetting = (templateId, data) =>
  _http.post(`api/ppt/template/${templateId}/setting`, data);

// 取得对应PPT模板生成的文档
export const getPptTemplateDocument = (data) =>
  _http.post(`api/ppt/template/${data.templateId}/doc`, data);

//---------------------------------------------------------------------------------
//Web office token
//---------------------------------------------------------------------------------
// 取得webofficetoken
export const getPptViewToken = (templateId, docId) => {
  if (templateId == 0) {
    return _http.get(`api/ppt/doc/${docId}/view`);
  } else {
    return _http.get(`api/xuser/ppt/template/${templateId}/doc/${docId}/view`);
  }
};

function _addTime(date, _time) {
  if (date.indexOf(_time) > -1) {
    return date;
  }
  return date + _time;
}
// 获所xmate应用
// export const getApplications = () => _http.get(`api/user/xmate/applications`)

// Xmate埋点日志报表
export const getLandingReport = (data) => {
  data["startTime"] = _addTime(data["startTime"], " 00:00:00");
  data["endTime"] = _addTime(data["endTime"], " 23:59:59");
  return _http.post(`api/user/landing/history/report`, data);
};

export const getLandingExport = (filename, data) => {
  data["startTime"] = _addTime(data["startTime"], " 00:00:00");
  data["endTime"] = _addTime(data["endTime"], " 23:59:59");
  data["filename"] = filename;
  return _http.download(`api/user/landing/history/report/export`, data);
};

// 取得全部岗位
export const getPositions = () => _http.get(`api/xmate/configs/positions`);
// 创建岗位
export const createPositions = (data) =>
  _http.post(`api/xmate/configs/positions`, data);
// 修改岗位
export const updatePositions = (id, data) =>
  _http.put(`api/xmate/configs/positions/${id}`, data);
// 删除岗位
export const deletePositions = (id) =>
  _http.delete(`api/xmate/configs/positions/${id}`);

// 取得岗位的全部维度
export const getEvaluations = (pid) =>
  _http.get(`api/xmate/configs/positions/${pid}/evaluations`);
// 创建岗位维度
export const createEvaluations = (pid, data) =>
  _http.post(`api/xmate/configs/positions/${pid}/evaluations`, data);
// 修改岗位维度
export const updateEvaluations = (pid, eid, data) =>
  _http.put(`api/xmate/configs/positions/${pid}/evaluations/${eid}`, data);
// 删除岗位维度
export const deleteEvaluations = (pid, eid) =>
  _http.delete(`api/xmate/configs/positions/${pid}/evaluations/${eid}`);

// ---------------智能产品推荐师
// 取得商品分类：
export const getGoodsCategory = () => _http.get(`api/goods/category`);
// 创建商品分类
export const createGoodsCategory = (data) =>
  _http.post(`api/goods/category`, data);
// 修改商品分类
export const updateGoodsCategory = (pid, data) =>
  _http.put(`api/goods/category/${pid}`, data);
// 删除商品分类
export const deleteGoodsCategory = (pid) =>
  _http.delete(`api/goods/category/${pid}`);

// 取得商品分类下的商品清单
export const getGoodsList = (param) =>
  _http.post(`api/goods/category/${param.cid}/list`, param);

// 上传商品清单
export const uploadGoodsList = (
  cid,
  formData,
  onProgress,
  onSuccess,
  onFail
) => {
  const url = `${g.config.meetApiHost}/rest/api/goods/category/${cid}/list/upload`;
  return uploadFile(url, formData, onProgress, onSuccess, onFail);
};

// 商品清单上下架 status:on|off：上下架
export const updateGoodsShelf = (cid, gid, status) =>
  _http.put(`api/goods/category/${cid}/list/${gid}/shelf/${status}`, {});
// 修改商品清单名称
export const updateGoodsName = (cid, gid, data) =>
  _http.put(`api/goods/category/${cid}/list/${gid}/name`, data);
// 删除商品清单
export const deleteGoods = (cid, data) =>
  _http.post(`api/goods/category/${cid}/list/delete`, data);

// 取得岗位面试题的提示语
export const getPosQuestionPrompt = (pid) =>
  _http.get(`api/xmate/configs/positions/${pid}/evaluations/question_prompt`);

// 修改岗位面试题的提示语
export const updatePosQuestionPrompt = (pid, data) =>
  _http.put(
    `api/xmate/configs/positions/${pid}/evaluations/question_prompt`,
    data
  );

// 需求调研报告助手----------------------------

// 取得全部主题标签
export const getXmTopic = () => _http.get(`api/xmate/meeting/topic`);

// 创建主题标签
export const createXmTopic = (data) =>
  _http.post(`api/xmate/meeting/topic`, data);

// 更新主题标签
export const updateXmTopic = (topic_id, data) =>
  _http.put(`api/xmate/meeting/topic/${topic_id}`, data);

// 删除主题标签
export const deleteXmTopic = (topic_id, data) =>
  _http.delete(`api/xmate/meeting/topic/${topic_id}`, data);

// 上传/替换商品沟通文档 主题标签id, categoryId 商品分类id, 如果是非商品关联，固定为0
export const updateXmFile = (
  topicId,
  categoryId,
  formData,
  onProgress,
  onSuccess,
  onFail
) => {
  const url = `${g.config.meetApiHost}/rest/api/xmate/meeting/topic/${topicId}/category/${categoryId}/template/upload`;
  return uploadFile(url, formData, onProgress, onSuccess, onFail);
};

// 删除商品沟通文档
export const deleteXmFile = (topicId, categoryId, templateId) =>
  _http.delete(
    `api/xmate/meeting/topic/${topicId}/category/${categoryId}/template/${templateId}`
  );

// 设置主题标签商品关联  relaetd 1关联0不关联
export const setXmTopicRelated = (topicId, related) =>
  _http.post(`api/xmate/meeting/topic/${topicId}/related/${related}`, {});

// 设置主题标签排序顺序
export const updateXmTopicOrder = (data) =>
  _http.post(`api/xmate/meeting/topic/order/update`, data);

// 创建主题标签维度
export const createXmTopicDimension = (topicId, dimensionType, data) =>
  _http.post(
    `api/xmate/meeting/topic/${topicId}/type/${dimensionType}/dimension`,
    data
  );

// 修改主题标签维度
export const updateXmTopicDimension = (topicId, dimensionId, data) =>
  _http.put(
    `api/xmate/meeting/topic/${topicId}/dimension/${dimensionId}`,
    data
  );

// 删除主题标签维度
export const deleteXmTopicDimension = (topicId, dimensionId) =>
  _http.delete(`api/xmate/meeting/topic/${topicId}/dimension/${dimensionId}`);

// 查询主题标签维度
export const getXmTopicDimension = (topicId, dimensionId) =>
  _http.get(`api/xmate/meeting/topic/${topicId}/dimension/${dimensionId}`);

// 设置主题标签维度排序顺序  dimensionType: 维度类型, SUMMARY: 总结 ANALYSIS:分析 COUNSELLING:辅导
export const updateXmTopicDimensionOrder = (topicId, dimensionType, data) =>
  _http.post(
    `api/xmate/meeting/topic/${topicId}/type/${dimensionType}/dimension/order/update`,
    data
  );

// 取得主题标签系统维度设置
export const getXMTopicSystemDimension = () =>
  _http.get(`api/xmate/meeting/topic/system/dimension/all`);

//下载文件
export const downloadFileApi = (url, filename) =>
  _http.get_excel(url, filename);

// 查询公司在线搜索的内容模板
export const getSearchConfig = () =>
  _http.get(`api/company/online/search/config`);

// 设置公司在线搜索的内容模板
export const updateSearchConfig = (query) =>
  _http.post(`api/company/online/search/config`, { query });

// 取得Bing搜索的查询内容
export const getSearchResult = (data) =>
  _http.post(`api/company/online/search`, data);

//---------------------------------------------------------------------------------
//template 页面 自定义AI字段
//---------------------------------------------------------------------------------

// 列出所有自定义字段
export const getTemplateField = (templateId) =>
  _http.get(`api/ppt/template/${templateId}/field`);

// 新增自定义字段
export const createTemplateField = (templateId, data) =>
  _http.post(`api/ppt/template/${templateId}/field`, data);

// 修改新增自定义字段  模板id, fieldId:字段ID, 0,1缺省为行业和关注点，不可更改
export const updateTemplateField = (templateId, fieldId, data) =>
  _http.put(`api/ppt/template/${templateId}/field/${fieldId}`, data);

// 删除新增自定义字段
export const deleteTemplateField = (templateId, fieldId) =>
  _http.delete(`api/ppt/template/${templateId}/field/${fieldId}`);

// 获取风险配置项的内容
export const getRiskSetting = () =>
  _http.get(`api/xmate/configs/risks/setting`);

// 修改风险配置项的内容
export const updateRiskSetting = (data) =>
  _http.put(`api/xmate/configs/risks/setting`, data);

// 获取全局配置项的内容
export const getGlobalConfig = (param) => _http.get(`api/golableconfig`, param);

// 修改全局配置项的内容
export const updateGlobalConfig = (data) =>
  _http.post(`api/golableconfig`, data);

// 动态表单管理------------------

// 提交表单数据
export const submitFormData = (formCode, data) =>
  _http.post(`api/form/data/submit?formCode=${formCode}`, data);

// 新增表单字段
export const addFormField = (formCode, data) =>
  _http.post(`api/form/definition/field?formCode=${formCode}`, data);

// 删除表单字段
export const deleteFormField = ({ id }) =>
  _http.delete(`api/form/definition/field/${id}`);

// 更新表单字段
export const updateFormField = (fieldId, data) =>
  _http.put(`api/form/definition/field/${fieldId}`, data);

// 修改主题标签维度,
export const updateTopicDimension = (topicId, dimensionId, data) =>
  _http.put(
    `api/xmate/meeting/topic/${topicId}/dimension/${dimensionId}`,
    data
  );
//获取机构热词
export const getOrgHotWords = () => _http.get(`api/hot-words`);
//修改机构热词
export const updateOrgHotWords = (data) => _http.put(`api/hot-words`, data);


// 客户管理-搜索
export const getCustomerManage = (data) =>
  _http.post(`api/customer/manage`, data);

export const createCustomizeDimension = (dimensionType, data) =>
  _http.post(
    `api/xmate/configs/topic/dimension/${dimensionType}/assessment/settings/customize`,
    data
  );

export const deleteCustomizeDimension = (dimensionType, itemId) =>
  _http.delete(
    `api/xmate/configs/topic/dimension/${dimensionType}/assessment/settings/customize/${itemId}`
  );

export const updateCustomizeDimension = (dimensionType, itemId, data) =>
  _http.put(
    `api/xmate/configs/topic/dimension/${dimensionType}/assessment/settings/customize/${itemId}`,
    data
  );

export const searchTeam = (data) => _http.post(`api/team/search`, data);
export const addTeam = (data) => _http.post(`api/team`, data);
export const deleteTeam = (teamId) => _http.delete(`api/team/${teamId}`);
export const updateTeam = (id, data) => _http.put(`api/team/${id}`, data);
