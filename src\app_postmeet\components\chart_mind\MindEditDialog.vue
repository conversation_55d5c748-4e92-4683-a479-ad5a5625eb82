<template>
    <el-dialog :title="title" v-model="isShow" :fullscreen="true" :append-to-body="true" :modal-append-to-body="false"
        class="mind_edit_wrap">
        <div class="me_main">
            <div class="me_left" v-show="isEditMode">
                <textarea v-model="markdownContent" />
            </div>
            <div class="me_right">
                <MarkmapRender ref="refMarkmap" :content="markdownContent" />
            </div>
        </div>
        <template #footer class="dialog-footer">
            <el-button type="default" @click="onSaveImg"> 保存为图片 </el-button>
            <el-button type="default" @click="onSaveMd"> 保存为Markdown </el-button>
            <el-button type="default" @click="onFit">自动居中</el-button>
            <el-button type="default" @click="onCancel">关闭</el-button>
            <el-button type="primary" @click="onConfirm" v-show="isEditMode">保存</el-button>
        </template>
    </el-dialog>
</template>

<script>
import MarkmapRender from "./MarkmapRender.vue"
import { updateMindmap } from "@/app_postmeet/tools/api.js"
import { downloadTxt } from "@/app_postmeet/tools/tools.js"
import d3ToPng from 'd3-svg-to-png';
import { ElMessage } from "element-plus";

export default {
    components: { MarkmapRender },
    data() {
        return {
            isEditMode: false,
            markdownContent: '',
            isShow: false,
            title: '编辑脑图',
        }
    },
    methods: {
        show(value, eidtMode) {
            this.isEditMode = eidtMode;
            this.title = eidtMode ? '编辑脑图' : '查看脑图';
            this.markdownContent = value;
            this.isShow = true;
        },
        onFit() {
            this.$refs.refMarkmap.getDom();
        },
        onSaveImg() {
            d3ToPng('#myMindSvg', this.getFname('png'), {
                scale: 5,
                format: 'png',
                quality: 1,
                download: true,
                ignore: '.ignored',
                background: 'white'
            }).then(fileData => {
                //do something with the data
            });
        },
        getFname(type) {
            const { recordInfo } = g.postmeetStore.data
            let fname = 'mind_' + recordInfo.subject + '_' + recordInfo.startTime.replace(/:/g, '').replace(/ /g, '_') + '.' + type;
            return fname;
        },
        onSaveMd() {
            downloadTxt(this.getFname('md'), this.markdownContent)
        },
        onCancel() {
            this.isShow = false
        },
        onConfirm() {
            const { confId } = g.postmeetStore.data;
            updateMindmap(confId, this.markdownContent).then(resp => {
                if (resp.code == 0) {
                    ElMessage.success("保存成功!");
                    this.$emit('callback', this.markdownContent);
                } else {
                    ElMessage.error("保存失败，请稍后再试！")
                }
            }).catch(e => {
                ElMessage.error("保存失败，请稍后再试！")
            })
        },
    }
}
</script>


<style lang='scss' scoped>
.mind_edit_wrap {
    .me_main {
        display: flex;
        flex-direction: row;

        .me_left {
            width: 50%;

            textarea {
                outline: none;
                width: 100%;
                height: calc(100vh - 177px);
            }
        }

        .me_right {
            width: 100%;
            height: calc(100vh - 177px);
        }
    }
}
</style>
