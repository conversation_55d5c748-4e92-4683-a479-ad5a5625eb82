<template>
    <div class="add_contact_outer_form_wrap">
        <div class="vbp_box">
            <FieldAdd ref="refCustomerAdd" />
        </div>
        <div class="form-footer">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSubmit">确定</el-button>
        </div>
    </div>
</template>

<script setup>
import { getFormFields } from '@/js/api';
import FieldAdd from "@/components/RenameSpeaker/FieldAdd.vue";
import { saveExternalRole } from "@/js/api.js"

const refCustomerAdd = ref(null);
const customerFields = ref([])

let customerId = '';

const customerList = ref([])
const roleList = ref([])
const emit = defineEmits(['cancel', 'submit'])

const show = (_customerId) => {
    customerId = _customerId
}

const onCancel = () => {
    emit('cancel')
}

const onSubmit = async () => {
    try {
        const data = await refCustomerAdd.value.get_data();
        data['attendeeType'] = 'CUSTOMER_ATTENDEE'
        saveExternalRole(customerId, data).then(resp => {
            if (resp.code == 0) {
                // ElMessage.success("添加成功")
                emit('submit', data)
            } else {
                ElMessage.error(resp.message)
            }
        })
    } catch (e) {
        console.log('onSubmit error', e)
    }
}
const getCustomerFields = async () => {
    const respCustomer = await getFormFields('OUTER_PARTNER');
    if (respCustomer.code == 0) {
        customerFields.value = respCustomer.data.filter(item => item.fieldStatus == 1);
        refCustomerAdd.value.init(customerFields.value)
    }
}

onMounted(() => {
    g.cacheStore.getPositionConfig().then(resp => {
        customerList.value = resp.customerPosition
        roleList.value = resp.customerRole
        getCustomerFields()
    })
})

defineExpose({
    show,
    FieldAdd,
    refCustomerAdd
})
</script>

<style lang="scss">
.add_contact_outer_form_wrap {
    padding: 20px;

    .vbp_box {
        margin-bottom: 12px;

        .vbp_title {
            margin-bottom: 12px;

            span {
                color: #F56C6C;
            }
        }
    }

    .form-footer {
        margin-top: 24px;
        text-align: right;
    }
}
</style>