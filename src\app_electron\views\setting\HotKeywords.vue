<template>
  <div class="about-settings">
    <div class="setting-item clickable">
      <span>热词</span>
      <span>></span>
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.setting-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.clickable {
  cursor: pointer;
}

.clickable:hover {
  background-color: #f5f5f5;
}
</style>