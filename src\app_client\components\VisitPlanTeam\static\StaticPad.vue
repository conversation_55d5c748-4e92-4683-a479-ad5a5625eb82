<template>
    <div class="vline4 flex-row">
        <staticItem label="拜访计划总数" :value="result.totalNum" />
        <staticItem label="拜访计划完成数" :value="result.completedNum" />
        <staticItem label="拜访计划覆盖客户数" :value="result.customerNum" />
    </div>
</template>

<script setup>
import staticItem from "./staticItem.vue";

const result = ref({
    totalNum: 0,
    completedNum: 0,
    customerNum: 0
})

const init = (resp) => {
    const { totalNum, completedNum, customerNum, datas } = resp;
    result.value = {
        totalNum, customerNum,
        completedNum
    }
}

defineExpose({
    staticItem, init
})
</script>

<style lang="scss">
.vline4 {
    margin-top: 16px;
}
</style>
