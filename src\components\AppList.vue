<template>
    <div class="app_list_wrap">
        <div :class="`item_wrap ${data.id}`" @click="btnWork(data)" v-for="data in list" v-if="list.length > 0">
            <div class="img_box flex-center">
                <img :src="getOssUrl(data.id + '.png')" :alt="data.title">
            </div>
            <div class="right_tn">
                <div class="text_9">{{ data.title }}</div>
                <div class="text_10">{{ data.note }}</div>
            </div>
        </div>
        <el-empty v-else description="暂无应用"></el-empty>
    </div>
</template>
<script setup>
import { getOssUrl } from "@/js/utils.js"
const props = defineProps({ data: Object });
const list = ref([])

const init = (_list) => {
    list.value = _list
}

const btnWork = (data) => {
    g.clientStore.openUrl(data)
}
defineExpose({
    data: props.data,
    getOssUrl,
    list,
    init,
    btnWork
})

</script>

<style lang="scss">
.app_list_wrap {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    overflow-y: auto;

    .item_wrap {
        display: flex;
        flex-direction: row;
        width: 296px;
        margin: 0 0 12px 0;
        padding: 0 20px;
        background: #FFFFFF;
        box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);
        border-radius: 8px;
        border: 1px solid #FFFFFF;
        cursor: pointer;
        position: relative;
        align-items: center;

        .img_box {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #E5EEFF;

            img {
                width: 24px;
                height: 24px;
            }
        }

        .right_tn {
            display: flex;
            flex-direction: column;
            padding-left: 16px;
            padding-right: 10px;
            margin-top: 10px;
            width: 90%;

            .text_9 {
                height: 24px;
                font-size: 16px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                color: #262626;
                line-height: 24px;
            }

            .text_10 {
                font-size: 14px;
                color: #8C8C8C;
                line-height: 22px;
                margin: 3px 8px 8px 0;
            }
        }
    }

    .item_wrap:hover {
        bottom: 4px;
        box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.16);
    }
}


@media screen and (max-width: 1280px) {
    .item_wrap {
        min-height: 76px;
    }
}

@media screen and (min-width: 1280px) {
    .item_wrap {
        min-height: 92px;
    }
}
</style>