<template>
    <div class="ele_premeet" v-loading="loading" element-loading-text="正在启动录制...">
        <el-alert v-if="errorMsg" :title="errorMsg" type="error" />
        <div class="ele_premeet_audio">
            <img :src="getAssetUrl('inmeet_8_1.png')" alt="audio" />
        </div>
        <div class="record_type" v-if="hasCard">
            <div class="record_type_title">录制方式</div>
            <el-radio-group v-model="recordType">
                <el-radio value="card">工牌录制</el-radio>
                <el-radio value="computer">电脑录制</el-radio>
            </el-radio-group>
        </div>
        <div class="ele_premeet_options flex-col" v-if="recordType === 'computer'">
            <div class="ele_premeet_options_item flex-row">
                <VolumeIndicator ref="volumeIndicator" :device-id="options.audio.audioInputDevice"
                    @no-volume="onNoVolume" />
                <AudioInputSelector ref="refAudioInputSelector" v-model="options.audio.audioInputDevice" />
            </div>
            <div class="ele_premeet_options_item">
                <el-checkbox v-model="options.audio.recordSystemAudio" label="录制电脑音频"
                    @change="onRecordSystemAudioChange" />
            </div>
        </div>
        <div class="ele_premeet_options flex-col" v-show="hasCard && recordType === 'card'">
            <div class="ele_premeet_options_item flex-row">
                <DeviceCard ref="refDeviceCard" @callback="onDevice" />
            </div>
        </div>
        <div class="mic-alert" v-if="showMicAlert">
            <el-alert title="没有检测到您的声音，请确保麦克风已正确连接并开启" type="warning" :closable="false" />
        </div>
        <div class="ele_btn flex-center" v-if="hasCard && !loadingDevice || !hasCard">
            <el-tooltip class="tp_start" effect="dark" :content="disableStartReason" :show-after="500"
                v-if="disableStartReason && recordType == 'card'">
                <el-button type="info" class="btn_start start_disable" :disabled="true">启动</el-button>
            </el-tooltip>
            <el-button type="primary" :loading="loading" @click="onStart" class="btn_start start_enable"
                v-else>启动</el-button>
        </div>
    </div>
</template>

<script setup>
import VolumeIndicator from '@/app_electron/components/VolumeIndicator.vue';
import AudioInputSelector from '@/app_electron/components/AudioInputSelector.vue';
import { getAssetUrl } from '@/js/utils';
import DeviceCard from '@/app_electron/components/DeviceCard.vue';

const options = ref(g.electronStore.settings)
const refAudioInputSelector = ref(null)
const volumeIndicator = ref(null)
const emit = defineEmits(['callback'])
const errorMsg = ref('');
const loading = ref(false)
const showMicAlert = ref(false)
const hasCard = ref(false)
const recordType = ref('computer')
const cardInfo = ref({})
const disableStartReason = ref('')
const loadingDevice = ref(false)
const refDeviceCard = ref(null)
let audioDeviceCode = ''

const onStart = () => {
    if (recordType.value === 'computer') {
        if (!options.value.audio.audioInputDevice && !options.value.audio.recordSystemAudio) {
            ElMessage.error('请选择麦克风或开启录制电脑音频')
            return
        }
        refAudioInputSelector.value.removeListener()
    }
    loading.value = true
    emit('callback', 'start_' + recordType.value, toRaw(options.value))
}

const _updateDisableStartReason = () => {
    if (!hasCard.value) return;

    const { batteryPer, limitBatteryPer, online } = cardInfo.value;
    if (online !== 1) {
        disableStartReason.value = '工牌设备离线，请检查工牌是否开机（蓝灯亮起）或网络是否稳定，然后重新尝试启动。';
        return;
    }

    if (batteryPer < limitBatteryPer) {
        disableStartReason.value = `工牌电量过低，请先充电再进行录制。`;
        return;
    }
}


const cancel = async () => {
    g.electronStore.closeWin('premeet')
}

const onRecordSystemAudioChange = () => {
    if (!g.config.isMac) return
    if (options.value.audio.recordSystemAudio) {
        g.ipcRenderer.invoke('install-mac-virtual-audio').then((res) => {
            if (!res) {
                options.value.audio.recordSystemAudio = false;
                ElMessage.error('安装虚拟音频失败，无法录制电脑音频')
            }
        })
    } else {
        g.ipcRenderer.send('kill-virtual-audio')
    }
}

const checkRecordingPath = () => {
    g.electronStore.checkRecordingPath().then((res) => {
        if (!res) {
            ElMessage.error('录制路径不存在或没有写入权限，请检查')
        }
    })
}
const checkMicrophonePermission = async () => {
    const hasPermission = await g.ipcRenderer.invoke('ensurePermissions', 'microphone')
    if (!hasPermission) {
        ElMessage.error('请授予麦克风权限');
    }
}

const setError = (msg) => {
    errorMsg.value = msg
}

const setLoading = (status) => {
    loading.value = status
}

const onNoVolume = (noVolume) => {
    showMicAlert.value = noVolume
}

const onDevice = (data) => {
    cardInfo.value = data
    _updateDisableStartReason()
    loadingDevice.value = false
}

const init = async () => {
    options.value = g.electronStore.settings
    checkRecordingPath()
    checkMicrophonePermission()
    onRecordSystemAudioChange()
    g.appStore.setStore(g.cv.keyInmeetCompanyName, '');
    await g.appStore.reflashUserInfo();
    await g.appStore.getAllFuncPoints();
    refDeviceCard.value.getStatus()
    const hasCardAccess = g.appStore.getFuncStatus('bind_badge');
    audioDeviceCode = g.appStore.user.audioDeviceCode;
    hasCard.value = hasCardAccess && !!audioDeviceCode;
    recordType.value = hasCard.value ? 'card' : 'computer'
    loadingDevice.value = true
}

defineExpose({
    options, onStart, getAssetUrl, cancel, refAudioInputSelector, AudioInputSelector,
    setError, setLoading, init
})
</script>

<style lang="scss">
.ele_premeet {
    .el-alert {
        margin-bottom: 20px;
    }

    .record_type {
        padding: 0 20px;
        margin-bottom: 20px;

        .record_type_title {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
        }

        :deep(.el-radio) {
            margin-right: 20px;
        }
    }

    .ele_premeet_audio {
        width: 100%;

        img {
            width: 100%;
        }
    }

    .ele_premeet_options {
        margin: 20px;

        .ele_premeet_options_item {
            margin: 4px 0;

            :deep(.el-select) {
                .el-select__wrapper {
                    height: 40px;
                    line-height: 40px;
                    border-radius: 8px;
                }
            }
        }
    }

    .ele_btn {
        position: fixed;
        bottom: 20px;
        width: 100%;

        .start_disable {
            border: none;

            span {
                color: #BFBFBF;
            }

            background-color: #F5F5F5;
        }

        .btn_start {
            margin-top: 37px;
            width: 90%;
            height: 40px;
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 14px;
        }

        .start_enable {
            cursor: pointer;
            background: linear-gradient(45deg, #691FFE 0%, #00B9E9 100%);
        }
    }

    .mic-alert {
        margin: 0 20px;
    }
}

.tp_start {
    width: 320px !important;
}

.el-tooltip {
    width: 80%;
}
</style>