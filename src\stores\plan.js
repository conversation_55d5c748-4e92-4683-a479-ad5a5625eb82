
import { defineStore } from 'pinia'
import { deleteSchedule } from "@/js/api.js"


const PlanStore = defineStore('plan', {
    actions: {
        cancelPlan(scheduleId) {
            return new Promise((resolve, reject) => {
                ElMessageBox.confirm(
                    '确定要取消拜访吗？',
                    '警告',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                )
                    .then(() => {
                        deleteSchedule(scheduleId).then(res => {
                            if (res.code == 0) {
                                ElMessage.success('删除成功')
                                resolve(true)
                            } else {
                                ElMessage.error(res.message)
                                resolve(false)
                            }
                        })
                    })
                    .catch(() => {
                        resolve(false)
                    })
            })
        },
    }
})

export default PlanStore;