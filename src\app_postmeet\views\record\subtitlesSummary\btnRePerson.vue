<template>
  <div class="btn_share_wrap">
    <el-dialog title="重新识别说话人" v-model="centerDialogVisible" width="400px" :append-to-body="true"
      :modal-append-to-body="false" class="share_modal_wrap">
      <div class="cd_main">
        <div class="cd_box">
          <div class="title"> 需要重新识别哪个说话人的语音？</div>
          <el-select v-model="parUi" collapse-tags placeholder="请选择" size="small">
            <el-option v-for="ui in uis" :key="ui.uuid" :label="ui.name" :value="ui.uuid">
            </el-option>
          </el-select>
        </div>
        <div class="cd_box">
          <div class="title"> 您想把当前说话人的录音识别成几个说话人？</div>
          <el-radio-group v-model="roleNum">
            <el-radio :label="2">2人</el-radio>
            <el-radio :label="0">3人及以上</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取消</el-button><el-button type="primary"
          @click="onConfirm">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog title="重新识别说话人" v-model="isShowNoNeed" width="400px" :append-to-body="true" :modal-append-to-body="false"
      class="share_modal_wrap no_need_modal">
      <div class="cd_main2">
        所有说话人都已经转录过，无需再次转录。
      </div>
      <template #footer class="dialog-footer">
        <el-button type="primary" @click="onClose">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { translateRecord, getTranslateStatus } from '@/app_postmeet/tools/api'
export default {
  name: 'BtnShare',
  props: [],
  data() {
    return {
      centerDialogVisible: false,
      recognitionStatus: -1,
      isShowNoNeed: false,
      parUi: '',
      roleNum: 0,
      uis: []
    }
  },
  methods: {
    show() {
      const { meetingType } = g.postmeetStore.data.asrRaw;
      this.uis = g.postmeetStore.getCanConverUsers()
      if (this.uis.length > 0 && meetingType != 7) {
        this.parUi = this.uis[0].uuid;
        this.queryStatus()
      } else {
        ElMessage.warning("所有说话人都已经转录过，无需再次转录。")
      }
    },
    queryStatus() {
      getTranslateStatus(g.postmeetStore.data.confId).then(resp => {
        if (resp.code == 0) {
          // recognitionStatus: number, //转写 状态， 0，没有转写，1：正在转写， 2：转写成功， 3：转写失败
          const status = resp.data.recognitionStatus
          this.recognitionStatus = status;
          if (status == 2) {
            this.centerDialogVisible = true;
          } else if (status == 1) {
            g.emitter.emit('startConvertStatusTimer', '')
            ElMessage.warning("转录正在进行中")
          } else if (status == 3) {
            ElMessage.warning("转录失败")
          } else if (status == 0) {
            this.centerDialogVisible = true;
          }
        }
      })
    },
    onConfirm() {
      const confId = this.$route.params.confId
      const record = g.postmeetStore.getCurrRecord()
      translateRecord(confId, record.id, this.parUi, this.roleNum).then(resp => {
        if (resp.code == 0) {
          g.emitter.emit('startConvertStatusTimer', '')
          ElMessage.success("已发起识别请求")
          this.centerDialogVisible = false
        } else {
          ElMessage.error("提交请求失败")
        }
      })
    },
    onClose() {
      this.isShowNoNeed = false
    }
  }
}
</script>

<style lang='scss'>
.share_modal_wrap {
  .cd_main {
    padding: 20px 0;

    .title {
      margin: 8px 0;
    }
  }

}

.no_need_modal {
  .cd_main2 {
    margin: 24px 0;
  }
}
</style>
