<template>
    <el-drawer v-model="is_show" direction="rtl" class="view_doc_wrap">
        <template #header>
            <div class="vd_title">
                编辑-{{ info.fileName }}
            </div>
        </template>
        <template #default>
            <div class="view_doc_main">
                <div class="left">
                    <el-timeline>
                        <el-timeline-item v-for="(row, index) in info.slides" :key="index" @click="onStep(row)"
                            :hollow="true" :color="activeStep == row.index ? '#446BFF' : '#fff'">
                            <div>
                                {{ `第${row.index}页` }}
                            </div>
                            <div class="star" v-if="!row.content && !row.ocr && !row.note">*</div>
                        </el-timeline-item>
                    </el-timeline>
                </div>
                <div class="right">
                    <div class="r_title">
                        页面标题
                    </div>
                    <el-input v-model="title" type="text" class="r_input" @change="onChange('title')" />
                    <div class="r_title">
                        页面文字
                    </div>
                    <el-input v-model="textarea" type="textarea" @change="onChange('content')" autosize />
                    <div class="r_title">
                        图片文字
                    </div>
                    <el-input v-model="ocr" type="textarea" @change="onChange('ocr')" autosize />
                    <div class="r_title">
                        备注
                    </div>
                    <el-input v-model="note" type="textarea" @change="onChange('note')" autosize />
                </div>
            </div>
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">保存</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { updatePpt } from '@/app_admin/tools/api';
const is_show = ref(false)
const title = ref('')
const textarea = ref('')
const ocr = ref('')
const note = ref('')
const activeStep = ref(1);
const info = ref({});
const updated = ref([])
const loading = ref(true)

const emit = defineEmits(['callback']);

const onCancel = () => {
    is_show.value = false;
}

const onConfirm = () => {
    const { categoryId, id } = info.value;
    updatePpt(categoryId, id, { slides: toRaw(updated.value) }).then(resp => {
        if (resp.code == 0) {
            ElMessage.success("操作成功");
            emit('callback', 'reload');
            is_show.value = false;
        } else {
            ElMessage.error(
                `操作失败.错误代码 ${resp.code}，错误信息 ${resp.message}`
            );
        }
    })

}

const onChange = (type) => {
    for (let i = 0; i < updated.value.length; i++) {
        const row = updated.value[i];
        if (activeStep.value == row.index) {
            if (type == 'title') {
                updated.value[i].title = title.value;
            } else if (type == 'content') {
                updated.value[i].content = textarea.value;
            } else if (type == 'ocr') {
                updated.value[i].ocr = ocr.value;
            } else if (type == 'note') {
                updated.value[i].note = note.value;
            }
        }
    }
}

const onStep = (row) => {
    title.value = row.title || ''
    textarea.value = row.content.trimStart();
    ocr.value = row.ocr || '';
    note.value = row.note || '';
    activeStep.value = row.index;
}

const init = (e) => {
    info.value = e;
    if (e.slides.length > 0) {
        onStep(e.slides[0]);
        is_show.value = true;
        activeStep.value = 1;
    }
    updated.value = e.slides;
}

defineExpose({ init, onCancel, onConfirm, is_show, info, title, textarea, ocr, note })

</script>

<style lang="scss">
@import url('./ViewDoc.scss');
</style>