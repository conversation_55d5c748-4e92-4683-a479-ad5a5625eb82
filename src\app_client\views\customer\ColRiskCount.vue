<template>
  <div class="ccol_risk_count flex-center" @click="onClick">
    <div class="ccol_risk_count_body flex-row" v-if="props.row.riskCount > 0">
      <div class="cbtn flex-center">
        <riskCountIcon />
      </div>
      <div class="txt">
        {{ formatCustNum(props.row.riskCount) }}
      </div>
    </div>
    <div v-else>-</div>
  </div>
</template>

<script setup>
import { formatCustNum } from "@/app_client/tools/utils.js";
import riskCountIcon from "@/app_client/icons/riskCount.vue";
const props = defineProps(["row"]);
const emit = defineEmits(["callback"]);

const onClick = () => {
  emit("callback", props.row, "riskIdentification");
};

defineExpose({ riskCountIcon, props, onClick });
</script>

<style lang="scss">
.ccol_risk_count {
  height: 44px;
  cursor: pointer;

  .ccol_risk_count_body {
    height: 24px;
    width: 48px;
    background: #fdf5cd;
    border-radius: 12px;
    .cbtn {
      margin: 0 6px;
    }

    .txt {
      font-size: 14px;
      color: #262626;
    }
  }
}
</style>
