
export const saleThemeColors = ['#5B8FF9', '#25D954', '#F6BD16', '#FF75AD', '#8D7AF2'];

export function calcSaleColors(tags) {
    const tagColors = {}
    tags.forEach((item, index) => {
        tagColors[item] = saleThemeColors[index % saleThemeColors.length];
    })

    return tagColors;
}

function _addDateColumns(data) {
    // 计算每个日期的出现次数
    const dateCountMap = data.reduce((acc, item) => {
        acc[item.date] = (acc[item.date] || 0) + 1;
        return acc;
    }, {});

    // 添加新列并返回新的数组
    return data.map((item, index) => {
        const isFirst = dateCountMap[item.date] > 1 &&
            index === data.findIndex(obj => obj.date === item.date);

        return {
            ...item,
            date_count: dateCountMap[item.date],
            date_isfirst: isFirst || dateCountMap[item.date] === 1
        };
    });
}


export function calcProcessToData(data, meetSummarys) {
    if (data.length == 0) {
        return { data: [], min_dt: '', max_dt: '' }
    }
    const min_dt = meetSummarys.min_dt || '';
    const max_dt = meetSummarys.max_dt || '';
    const dt_length = max_dt - min_dt;
    // 3. 计算每个元素的 process 值
    data.forEach((item) => {
        const dt = new Date(item.startTime);
        const curr_dt = dt.getTime();
        item.date = item.startTime.substring(0, 10);
        item.process = 100 * (curr_dt - min_dt) / dt_length;
        item.color = meetSummarys.colors[item.tag];
    });

    data = _addDateColumns(data)
    return { data, min_dt, max_dt };
}

export const getColumns = (team) => {
    const col1 = ["companyName", "riskCount", "VisitNews", "meetCount"]
    const col_team = ['hostNames', 'hostDeptNames']
    const col2 = ["meddic", "tag", "todosCount", "attendeeCount", "competitorNames"];
    const columns = team ? [...col1, ...col_team, ...col2] : [...col1, ...col2];
    return columns;
}

export const column_widths = {
    companyName: 320,
    VisitNews: 450,
    tag: 140,
    riskCount: 120,
    meetCount: 150,
    todosCount: 120,
    attendeeCount: 150,
    competitorNames: 140,
    hostNames: 150,
    hostDeptNames: 150,
    meddic: 200,
}

export const meddic_metrics = [
    {
        name: 'Metrics',
        cn_name: '衡量标准',
        desc: '客户在购买决策中所关注的具体量化指标，如投资回报率（ROI）、成本节约、生产效率提升等。',
    },
    {
        name: 'Economic Buyer',
        cn_name: '经济买家',
        desc: '在购买过程中具有最终决策权的人物，他们通常关注投资回报和预算等问题。',
    },
    {
        name: 'Decision Criteria',
        cn_name: '决策标准',
        desc: '客户在评估和选择供应商时所依据的标准，如产品质量、价格、售后服务等。',
    },
    {
        name: 'Decision Process',
        cn_name: '决策流程',
        desc: '客户在做出购买决策时所经历的步骤和流程，包括内部审批、评估等环节。',
    },
    {
        name: 'Identify Pain',
        cn_name: '确定痛点',
        desc: '客户在运营过程中面临的挑战和问题，如生产效率低下、成本过高等。',
    },
    {
        name: 'Champion',
        cn_name: '教练',
        desc: '在客户组织内部支持销售人员的关键人物，他们通常对产品或服务持积极态度，并愿意在内部推动购买决策。',
    },
]

// 参考用：Iframe里公司详情tab页数据
// {
//     name: "公司信息",
//     type: "companyInfo",
//   },
//   {
//     name: "风险识别",
//     type: "riskIdentification",
//   },
//   {
//     name: "沟通记录",
//     type: "visitRecord",
//   },
//   {
//     name: "客户参会人",
//     type: "customerAttend",
//   },
//   {
//     name: "待办",
//     type: "todo",
//   },
//   {
//     name: "竞争",
//     type: "competition",
//   },
// meddic