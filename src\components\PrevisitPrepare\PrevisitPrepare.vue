<template>
  <div class="pre-visit-preparation" v-loading="loading">
    <div v-if="error" class="error">{{ error }}</div>
    <div v-else>
      <VisitGoal ref="refVisitGoal" />
      <div v-if="isShowCompanyDetail">
        <CustomerInfo ref="refCustomerInfo" @callback="onCallback" />
      </div>
      <LastVisitSummary ref="refLastVisitSummary" />
      <PainPointAnalysis ref="refPainPointAnalysis" @callback="onCallback" />
      <CommunicationGuide ref="refCommunicationGuide" @callback="onCallback" />
      <Participants ref="refParticipants" @callback="onCallback" />
    </div>
    <drawerCustomer ref="refDrawer" />
  </div>
</template>

<script setup>
import LastVisitSummary from "./LastVisitSummary.vue";
import PainPointAnalysis from "./PainPointAnalysis.vue";
import VisitGoal from "./VisitGoal.vue";
import CustomerInfo from "./CustomerInfo.vue";
import CommunicationGuide from "./CommunicationGuide.vue";
import Participants from "./Participants.vue";
import { getPrepare } from "@/js/api";
import { getDateRangeStr, now } from "@/js/utils.js";
import drawerCustomer from "@/components/drawerCustomer.vue";

const refDrawer = ref();
const loading = ref(false);
const customerInfo = ref({});
const refCustomerInfo = ref(null);
const refPainPointAnalysis = ref(null);
const refLastVisitSummary = ref(null);
const refCommunicationGuide = ref(null);
const refVisitGoal = ref(null);
const refParticipants = ref(null);
const emit = defineEmits(["callback"]);
const error = ref("");
const isShowCompanyDetail = ref(false)
const onConfirm = () => {
  emit("callback", "confirmed");
};

const onCallback = (action, page) => {
  if (action == "show_customer") {
    onShowCustomer(page);
  }
};

const onShowCustomer = (page) => {
  const company = {
    companyId: customerInfo.value.companyId,
    id: customerInfo.value.customerId,
    name: customerInfo.value.customerName,
    token: g.appStore.user.token,
  };
  const dr = getDateRangeStr("month");
  const queryParam = {
    categoryIds: [],
    startTime: dr.startDt, //开始时间，注间时间格式 YYYY-mm-dd HH24:mi:ss
    endTime: dr.endDt, //结束时间
    tags: [], //标签
    dptIds: [],
    orderBy: "meetCount",
    asc: false,
  };
  refDrawer.value.show(company, page, queryParam, false);
};

const init = (data) => {
  loading.value = true;
  getPrepare(data.scheduleId)
    .then((res) => {
      if (res.code == 0) {
        customerInfo.value = res.data;
        isShowCompanyDetail.value = res.data.showCustomerInfo;
        if (isShowCompanyDetail.value) {
          nextTick(() => {
            refCustomerInfo.value.init(res.data);
          })
        }
        refPainPointAnalysis.value.init(res.data);
        refLastVisitSummary.value.init(res.data);
        refCommunicationGuide.value.init(res.data);
        refVisitGoal.value.init(res.data);
        refParticipants.value.init(res.data);
      } else {
        error.value = res.message;
      }
      loading.value = false;
    })
    .catch((e) => {
      loading.value = false;
      error.value = "获取数据失败";
    });
};

defineExpose({
  init,
  onConfirm,
  refVisitGoal,
  refCommunicationGuide,
  refCustomerInfo,
  refPainPointAnalysis,
  refLastVisitSummary,
  refParticipants,
});
</script>

<style scoped lang="scss">
.pre-visit-preparation {
  padding: 16px;
  max-width: 1000px;
  margin: 0 auto;

  .prepare_wrap {
    background: #f9fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
  }

  .error {
    color: red;
  }
}
</style>
