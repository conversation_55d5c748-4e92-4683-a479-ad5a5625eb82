<template>
    <el-dialog v-model="dialogVisible" :fullscreen="true" width="60%" :before-close="handleClose" class="edit_outline_wrap">
        <template #header>
            <div class="eo_header flex-row">
                <div class="eh1">
                    编辑PPT大纲
                </div>
                <div class="eh2">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="onConfirm">
                        确定
                    </el-button>
                </div>
            </div>
        </template>
        <div v-if="initialValue">
            <editor :initialValue="initialValue" :renderElement="renderElement" :height="600" :enabled="true"
                cursorDefaultColor="red" ref="refEditor">
            </editor>
        </div>
    </el-dialog>
</template>
  
<script  setup>

import {
    Editor,
    createRootENode,
    OutlineTree,
    isOutline,
    createOutlineENode,
} from "@petrel/editor";
import { updatePptTemplateLabel } from "@/app_admin/tools/api.js"
import { renameProperties, convertData, apiHintWrap } from "@/app_admin/tools/utils.js"

const emit = defineEmits(['callback']);
const refEditor = ref();
const initialValue = ref();
let templateId = ''
const dialogVisible = ref(false);
const handleClose = (done) => {
    dialogVisible.value = false;
    emit('callback', 'close')
}

const renderElement = (node) => {
    if (isOutline(node)) {
        return h(OutlineTree, {
            node,
        });
    }
};
const show = (_templateId, row_data) => {
    templateId = _templateId
    dialogVisible.value = true;
    const data = renameProperties(toRaw(row_data));
    nextTick(() => {
        initialValue.value = createRootENode([
            createOutlineENode(3, data),
        ]);
    })
}

const onConfirm = () => {
    const data = toRaw(refEditor.value.getExpose().getOutlines());
    apiHintWrap(updatePptTemplateLabel(templateId, { items: convertData(data) }), '更新').then(({ status }) => {
        if (status) {
            emit('callback', 'confirm')
            dialogVisible.value = false;
        }
    })
}

defineExpose({
    show, dialogVisible, handleClose, renderElement, initialValue, onConfirm
})

</script>
<style lang="scss">
.edit_outline_wrap {
    .el-dialog__header {
        border-bottom: 1px solid #E9E9E9;
        margin-right: 0;

        .eo_header {
            margin-right: 38px;
            justify-content: space-between;

            button:first-child {
                margin-right: 10px;
            }
        }
    }

}
</style>
  