<template>
  <el-button type="default" @click="onBtnSet" class="btn_set">
    设置文件匹配范围
    <el-tooltip
      content="调整此设置，让用户在输入关注点后，能够匹配更精确的内容"
      placement="top"
    >
      <QuestionIcon />
    </el-tooltip>
  </el-button>
  <input type="text" ref="refInput" class="hidden" />
  <Modal ref="refModal" @callback="cbModal" class="bs_modal" destroy-on-close>
    <div class="bs_header flex-row">
      <div class="bs_icon">
        <InfoIcon />
      </div>
      <div class="bs_note">
        用户在输入关注点后，基于设置的匹配范围规则，匹配相关的PPT内容
      </div>
    </div>
    <table class="stable">
      <tbody>
        <tr class="st_header">
          <td class="t1">PPT内容</td>
          <td class="t2">操作</td>
        </tr>
        <tr>
          <td class="t1">页面标题</td>
          <td class="t2">
            <el-switch v-model="formData.enableTitle" />
          </td>
        </tr>
        <tr>
          <td class="t1">页面文字</td>
          <td class="t2">
            <el-switch v-model="formData.enableContent" />
          </td>
        </tr>
        <tr>
          <td class="t1">页面图片</td>
          <td class="t2">
            <el-switch v-model="formData.enableOcr" />
          </td>
        </tr>
        <tr>
          <td class="t1">备注</td>
          <td class="t2">
            <el-switch v-model="formData.enableMemo" />
          </td>
        </tr>
      </tbody>
    </table>
  </Modal>
</template>

<script setup>
import { ref, toRaw, onMounted } from "vue";
import { getDocMatchType, updateDocMatchType } from "@/app_admin/tools/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
import InfoIcon from "@/app_admin/icons/info.vue";
import QuestionIcon from "@/app_admin/icons/question.vue";
const refModal = ref();
const title = ref("");
const refInput = ref();

const defaultForm = {
  enableTitle: true,
  enableContent: true,
  enableMemo: true,
  enableOcr: true,
};

const formData = ref(defaultForm);

const cfg = {
  width: "480px",
};

const query = () => {
  getDocMatchType().then((resp) => {
    if (resp.code == 0) {
      formData.value = resp.data;
    } else {
      ElMessage.error(`获取数据失败`);
    }
  });
};

const onBtnSet = () => {
  cfg["title"] = "设置文件匹配范围";
  refModal.value.show(cfg);
  refInput.value.focus();
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  refModal.value.hide();
};

const btnOK = () => {
  const data = toRaw(formData.value);
  const { enableTitle, enableContent, enableMemo, enableOcr } = data;
  if (enableTitle || enableContent || enableMemo || enableOcr) {
    apiHintWrap(updateDocMatchType(data), cfg["title"]).then(({ status }) => {
      if (status) {
        refModal.value.hide();
      }
    });
  } else {
    ElMessage.warning("设置项至少开启一个");
  }
};

onMounted(() => {
  query();
});

defineExpose({
  title,
  refInput,
  onBtnSet,
  cbModal,
  formData,
  InfoIcon,
  QuestionIcon,
});
</script>

<style lang="scss">
.btn_set {
  svg {
    margin-left: 5px;
  }
}

.hidden {
  width: 1px;
  height: 1px;
  z-index: -1;
}

.bs_modal {
  .el-dialog__body {
    padding: 18px 0;
  }

  .bs_header {
    background: #f0f6ff;
    margin-bottom: 18px;
    border-radius: 4px;

    .bs_icon {
      margin: 3px 3px 0 12px;
    }

    .bs_note {
      width: 371px;
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #595959;
    }
  }

  .stable {
    border-collapse: collapse;
    width: 432px;

    .st_header {
      width: 432px;
      height: 50px;
      background: #f9fafc;
      border-radius: 4px 4px 0px 0px;
      border-top: 1px solid #e9e9e9;
    }

    tr {
      height: 50px;
      border-bottom: 1px solid #e9e9e9;

      .t1 {
        width: 300px;
        padding-left: 12px;
      }

      .t2 {
        width: 108px;
        text-align: right;
        padding-right: 14px;
      }
    }
  }
}
</style>
