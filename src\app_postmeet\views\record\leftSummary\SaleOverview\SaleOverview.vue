<template>
  <div class="sale_overview">
    <template v-if="challenge.length > 0">
      <div class="header">
        <div class="title">【培训诉求】</div>
        <div class="blank"></div>
        <folding v-if="challenge.length > 6" sname="ch_style" @callback="cbShow"></folding>
      </div>
      <ul :style="ch_style">
        <li v-for="(item, i) in challenge" :key="item">
          {{ i + 1 + '. ' }} {{ item }}
        </li>
      </ul>
    </template>
    <template v-if="suggestion.length > 0">
      <div class="header">
        <div class="title">【建议和方案】</div>
        <div class="blank"></div>
        <folding v-if="suggestion.length > 6" sname="su_style" @callback="cbShow"></folding>
      </div>
      <ul :style="su_style">
        <li v-for="(item, i) in suggestion" :key="item">
          {{ i + 1 + '. ' }} {{ item }}
        </li>
      </ul>
    </template>

    <template v-if="plan.length > 0">
      <div class="header">
        <div class="title">【待办】</div>
        <div class="blank"></div>
        <folding v-if="plan.length > 6" sname="pl_style" @callback="cbShow"></folding>
      </div>
      <ul :style="pl_style">
        <li v-for="(item, i) in plan" :key="item">
          {{ i + 1 + '. ' }} {{ item }}
        </li>
      </ul>
    </template>
    <nodata v-show="!hasAnyResult"></nodata>
  </div>
</template>
<script>
import folding from '@/app_postmeet/components/folding.vue'
import DivEdit from "@/app_postmeet/components/div_edit.vue"
import nodata from "@/app_postmeet/components/nodata.vue"
import { getTopicTime, isShouldShowTime } from '@/app_postmeet/tools/tools'
const defaultHeight = '150px'

export default {
  components: { folding, DivEdit, nodata },
  name: 'Overview',
  data() {
    return {
      playItem: {},
      plan: [],
      challenge: [],
      suggestion: [],
      ch_style: { overflow: 'hidden' },
      su_style: { overflow: 'hidden' },
      pl_style: { overflow: 'hidden' },
      hasAnyResult: false,
      roomInfo: {},
    }
  },
  methods() {
    g.emitter.on('setPlayItem', (item) => {
      this.playItem = item;
      this.updateShow()
    })
    g.emitter.on('update_sale_overview', () => {
      this.setUI()
    })
  },
  methods: {
    topicTime(bt) {
      return getTopicTime(this.startTime, bt, this.playItem.startTime)
    },
    shouldShowTime(item) {
      return isShouldShowTime(this.startTime, item.bt, this.playItem.duration, this.playItem.startTime)
    },
    init() {
      this.setUI()
    },
    setUI() {
      this.playItem = g.postmeetStore.getCurrRecord()
      const { challenge, plan, suggestion } = g.postmeetStore.data.salesMateSummary
      this.roomInfo = g.postmeetStore.data.recordInfo;
      this.startTime = this.roomInfo.startTime

      this.challenge = challenge || []
      if (this.challenge && this.challenge.length > 6) {
        this.ch_style['height'] = defaultHeight
      }

      this.plan = plan || []
      if (this.plan && this.plan.length > 6) {
        this.pl_style['height'] = defaultHeight
      }

      this.suggestion = suggestion || []
      if (this.suggestion && this.suggestion.length > 6) {
        this.su_style['height'] = defaultHeight
      }
      this.updateShow()
    },
    updateShow() {
      this.hasAnyResult = g.postmeetStore.data.needAsrMail
    },
    cbShow(sname, status) {
      if (!status) {
        this[sname] = { overflow: 'hidden', height: defaultHeight }
      } else {
        this[sname] = {}
      }
    },
  }
}
</script>

<style lang='scss'>
@use './style.scss';
;
</style>