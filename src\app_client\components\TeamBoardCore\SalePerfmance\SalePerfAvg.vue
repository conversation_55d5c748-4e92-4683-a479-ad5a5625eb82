<template>
  <div class="sale_per_avg flex-col">
    <div class="overview-header flex-row">
      <div class="flex-row title-line-wrap">
        <div class="title-line"></div>
        <div class="title">销售表现均值</div>
      </div>
      <div class="flex-row">
        <RadioGroup v-model="perfType" class="fch_radio" :options="options" />
      </div>
    </div>

    <div class="spa_body" v-loading="loading" element-loading-text="数据加载中... 请稍等">
      <template v-if="perfType == 'ability'">
        <RadarChart ref="refRadarChart" class="radar_chart" :config="{ isShowLegend: true }" :data="initRadarList" />
      </template>
      <template v-if="perfType == 'taskCompletion'">
        <AvgBarChart ref="refAvgBarChart" class="avg_bar_chart" mark="%" :standardScore="standardScore"
          :innerWidth="innerWidth" key="deptSalesDimensionsScore" />
      </template>
    </div>
  </div>
</template>

<script setup>
import RadarChart from '../components/RadarChart.vue'
import AvgBarChart from '@/app_client/components/AvgBarChart.vue'
import { getDimensionScore, getDeptSalesDimensionsScore } from '@/app_client/tools/api.js';
import RadioGroup from '@/app_client/components/RadioGroup.vue'
const orgId = computed(() => g.appStore.user.orgId);
const deptId = computed(() => g.clientBoardStore.overviewDept.value);


const props = defineProps({
  isReport: {
    type: Boolean,
    default: false
  },

});
const options = g.clientBoardStore.perfTypeOptions;
const refAvgBarChart = ref(null);
const innerWidth = ref(0)
const loading = ref(true);
const perfType = ref();
const initRadarList = ref([])

const overviewDept = computed(() => {
  return g.clientBoardStore.overviewDept;
});
const periodType = computed(() => {
  return g.clientBoardStore.periodType;
})
const standardScore = computed(() => {
  return g.clientBoardStore.standardSetting.taskRate4Pass;
})
const initRadar = (list) => {
  const data = list && list.length > 0 ? [

    {
      color: '#426BFF',
      value: list.map((x) => x.score ? Number(x.score).toFixed(2) : 0),
      radarIndicator: list.map((x) => ({
        name: x.assessmentName,
        max: 100,
        min: 0,
        value: x.score ? Number(x.score).toFixed(2) : 0
      })),
    }
  ] : []
  initRadarList.value = data || []
}

const initBar = (list = []) => {
  const data = list.map((item = {}) => {
    return {
      label: item.assessmentName,
      value: item.score ? Number(item.score).toFixed(2) : 0,
      data: item
    }
  });
  innerWidth.value = document.querySelector(".spa_body").offsetWidth
  refAvgBarChart.value.init(data);
}
const handleTypeChange = () => {
  init()
};

const init = () => {
  if (!orgId.value || !deptId.value) return;
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const parm = {
    "pageSize": 10,
    "pageNumber": 1,
    "orderBy": "",
    "asc": false,
    startTime, endTime,
    "reportType": g.clientBoardStore.periodType
  }
  loading.value = true;
  if (perfType.value == 'ability') {
    getDeptSalesDimensionsScore(orgId.value, deptId.value, 'ABILITY', parm).then(res => {
      if (res.code == 0) {
        const list = res.data.datas || [];
        initRadar(list);
      }
    }).finally(() => {
      loading.value = false;
    })
  } else {
    getDeptSalesDimensionsScore(orgId.value, deptId.value, 'COMPLETION', parm).then(res => {
      if (res.code == 0) {
        const list = res.data.datas || [];
        initBar(list);
      }
    }).finally(() => {
      loading.value = false;
    })
  }

}

watch(() => g.clientBoardStore.perfType, () => {
  perfType.value = g.clientBoardStore.perfType;
}, { immediate: true })

watch(() => [perfType.value, periodType.value, overviewDept.value], () => {
  init()
}, { immediate: true });

onMounted(() => {
  init()
})

defineExpose({
  RadarChart,
  AvgBarChart,
  perfType,
  init
});



</script>

<style lang="scss">
.sale_per_avg {
  .overview-header {
    justify-content: space-between;
    margin: 40px 0 24px 0;
  }

  .spa_body {
    display: flex;
    // height: 360px;
    // width: calc(0.5 * (100vw - 220px));
    width: 100%;
    box-sizing: border-box;

    .radar_chart {
      background: #FFFFFF;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
      border-radius: 8px;
      padding: 24px;
    }

  }

  .avg_bar_chart {
    width: 100% !important;
    min-height: 348px !important;
  }
}
</style>
