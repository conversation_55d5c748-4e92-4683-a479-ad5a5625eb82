import App from "./App.vue";
import config from "@/js/config";
import router from "@/js/router";
import { createPinia } from "pinia";
import "element-plus/theme-chalk/el-message-box.css";
import mitt from "mitt";
import VueSortable from "vue3-sortablejs";
import createStore from "@/stores";
import { ConstValue } from "@/js/const_value";
import ELog from "@/app_electron/tools/elog";
import { createApp } from 'vue'
import "@/assets/reset.scss";
import aiTip from './directives/aiTip'
// 导入salesmate-ui组件库
import SalesMateUI from 'salesmate-ui'
import 'salesmate-ui/dist/style.css'
// 把星期一设为每周第一天
import { dayjs } from "element-plus";
dayjs.en.weekStart = 1;

const emitter = mitt();
const app = createApp(App);

app.directive('ai-tip', aiTip);
app.use(createPinia());
app.use(router);
app.use(VueSortable);
// 使用salesmate-ui组件库
app.use(SalesMateUI);
app.mount("#app");

const store = createStore();
window.g = { config, router, emitter, ...store, cv: ConstValue };
if (window.nodeRequire != undefined) {
  g.ipcRenderer = window.nodeRequire("electron").ipcRenderer;
  g.elog = new ELog();
}
