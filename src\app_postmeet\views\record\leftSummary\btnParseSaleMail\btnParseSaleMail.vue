<template>
  <div class="btn_sale_mail_wrap">
    <el-button type="default" :disabled="need_regen" @click="btnMail" class="btn_regen">生成纪要邮件</el-button>
  </div>
</template>
<script>
import editIcon from "@/icons/edit.vue"

export default {
  name: 'BtnRegen',
  components: { editIcon },
  data() {
    return {
      need_regen: false,
    }
  },
  methods: {
    btnMail() {
      g.emitter.emit('gen_sale_mail', '')
    },
  }
}
</script>

<style lang='scss'>
.btn_sale_mail_wrap {
  margin: 12px;

  .btn_regen {
    height: 32px;
    border: 1px solid #D9D9D9;
    line-height: 0px;
  }
}
</style>
