import { defineStore } from 'pinia'

// upload_files:{ "file": {}, "fileIcon": { "name": "ClearIcon", "__hmrId": "55d25697", "__file": "C:/projects/xmate-web-client/src/views/visit/BtnUpload/videoIcon.vue" }, "startTime": "1724143853278_0", "subject": "202403201256.mp4", "size": "423.72 MB", "status": "waiting" }
export default defineStore('client_file', {
    state: () => ({
        fileName: '',
        upload_files: [],
    }),
    actions: {
        setFileName(name) {
            this.fileName = name;
        },
        add_file(param) {
            // status:waiting,uploading,uploaded,delete,error
            this.upload_files.push(param)
        },
        update_file_status(startTime, new_status) {
            for (let i = 0; i < this.upload_files.length; i++) {
                if (startTime == this.upload_files[i].startTime) {
                    this.upload_files[i]['status'] = new_status;
                }
            }
        },
        get_files() {
            return this.upload_files
                .filter((x) => ['waiting', 'uploading'].includes(x.status));
        },
    }
})

