<template>
    <CustomPopover v-model="visible" :width="300">
        <template #reference>
            <el-button class="btn_pop_more" type="default" @click="openPopover">
                <span>
                    更多筛选
                </span>
                <el-icon class="el-icon--right">
                    <ArrowDown />
                </el-icon>
            </el-button>
        </template>
        <div class="pop-more-content">
            <div class="filter-items">
                <div class="filter-item">
                    <div class="label">目标达成</div>
                    <GoalArchStatus ref="refGoalArch" v-model:value="filters.targetStatus" />
                </div>
                <div class="filter-item">
                    <div class="label">能力评估</div>
                    <AbilityStatus ref="refAbility" v-model:value="filters.abilityStatus" />
                </div>
                <div class="filter-item">
                    <div class="label">任务达成</div>
                    <TaskStatus ref="refTask" v-model:value="filters.taskStatus" />
                </div>
            </div>
            <div class="actions">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">确定</el-button>
            </div>
        </div>
    </CustomPopover>
</template>

<script setup>
import CustomPopover from '@/components/CustomPopover.vue'
import GoalArchStatus from './GoalArchStatus.vue'
import AbilityStatus from './AbilityStatus.vue'
import TaskStatus from './TaskStatus.vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { getUrlParam, removeURLParams } from "@/js/utils.js";

const emit = defineEmits(['change'])
const visible = ref(false)

let _targetStatus = getUrlParam('targetStatus', '-1')
let _abilityStatus = getUrlParam('abilityStatus', -1, 'int')
let _taskStatus = getUrlParam('taskStatus', -1, 'int')

const filters = reactive({
    targetStatus: _targetStatus,
    abilityStatus: _abilityStatus,
    taskStatus: _taskStatus
})

const openPopover = () => {
    visible.value = true
}

const onCancel = () => {
    visible.value = false
    // 重置所有过滤条件
    // filters.targetStatus = "-1"
    // filters.abilityStatus = -1
    // filters.taskStatus = -1
}

const onConfirm = () => {
    removeURLParams(['targetStatus', 'abilityStatus', 'taskStatus'])
    visible.value = false
    emit('change', { ...filters })
}

defineExpose({
    filters
})
</script>

<style lang="scss" scoped>
.btn_pop_more {
    margin-right: 12px;

    span {
        color: var(--el-text-color-placeholder);
    }
}

.pop-more-content {
    padding: 16px;

    .filter-items {
        .filter-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .label {
                width: 80px;
                margin-right: 12px;
                font-size: 14px;
                color: #606266;
            }

            .visit_filter_item {
                margin-right: 0;
            }
        }
    }

    .actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
        gap: 12px;
    }
}
</style>