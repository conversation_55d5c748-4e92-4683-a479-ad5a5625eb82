<template>
    <div class="et_top">
        <div class="et_header">
            <div class="title">
                {{ templateInfo.name }}
            </div>
            <div class="et_edit" @click="onEdit">
                编辑
            </div>
        </div>

        <div class="e_line flex-row">
            <div class="et">
                应用场景
            </div>
            <div class="en">
                {{ templateInfo.scenario }}
            </div>
        </div>
        <div class="e_line flex-row">
            <div class="et">
                场景说明
            </div>
            <div class="en">
                {{ templateInfo.description }}
            </div>
        </div>
        <div class="et_space"> </div>
    </div>
    <dialogTemplate ref="refDialog" @callback="cbModal"></dialogTemplate>
</template>

<script setup>
import dialogTemplate from "./dialogTemplate.vue";
import { getPptTemplateInfo } from "@/app_admin/tools/api.js"
const templateInfo = ref({});
const refDialog = ref(null);
let templateId = ''
const init = (id) => {
    templateId = id;
    getPptTemplateInfo(templateId).then(resp => {
        if (resp.code == 0) {
            templateInfo.value = resp.data;
        } else {
            ElMessage.error(`获取数据失败`);
        }
    })
}

const onEdit = () => {
    const { name, scenario, description } = templateInfo.value;
    refDialog.value.show_edit(templateId, { name, scenario, description });
}

const cbModal = (action, data) => {
    if (action == "reload") {
        const { name, scenario, description } = data;
        templateInfo.value = { name, scenario, description }
    }
}

defineExpose({ init, refDialog, onEdit, templateInfo, dialogTemplate })

</script>

<style lang="scss">
.et_top {

    .et_header {
        display: flex;
        justify-content: space-between;

        .title {
            height: 24px;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #262626;
            line-height: 24px;
            padding: 16px;
        }

        .et_edit {
            font-size: 14px;
            margin: 10px 40px 10px 10px;
            color: var(--el-color-primary);
            cursor: pointer;
            padding: 8px;
        }
    }


    .e_line {
        padding: 0 16px 16px 16px;

        .et {
            width: 92px;
            height: 22px;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #8C8C8C;
            line-height: 22px;
        }

        .en {
            width: calc(100% - 100px);
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #262626;
            line-height: 22px;
            margin-left: 16px;
        }

    }

    .et_space {
        background-color: #f5f5f5;
        width: 100%;
        height: 12px;
        margin-top: 15px;
    }
}
</style>