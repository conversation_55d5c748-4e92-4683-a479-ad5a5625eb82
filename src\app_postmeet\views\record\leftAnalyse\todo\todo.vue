<template>
  <div class="todo_wrap">
    <div v-if="list.length > 0">
      <div class="td_header flex-row">
        <div class="td_left">共{{ list.length }}项待办</div>
      </div>
      <div class="td_items">
        <div class="td_item" v-for="item in list">
          <div class="td1 flex-row">
            <input type="checkbox" v-model="item.done" @change="onChange(item)" />
            <div :class="`td_title ${item.done ? 'delline' : ''}`">
              {{ item["待办内容"] }}
            </div>
            <div class="flex-grow"></div>
            <div :class="`la_detail ${recordViewPermission ? 'la_detail_enable' : ''}`" @click="onReview(item)"
              v-if="recordViewPermission">
              回顾
            </div>
          </div>
          <div class="td2 flex-row">
            <div class="td2_txt t2_time">截止时间： {{ fdt(item["截止时间"]) }}</div>
            <div class="td_line"></div>
            <div class="td2_txt t2_name">执行人： {{ item["执行人"] }}</div>
            <div class="flex-grow"></div>
            <div class="td_btn" @click="onEdit(item)" v-if="isHost">
              <EditIcon />
            </div>
            <div class="td_btn" @click="onDelete(item)" v-if="isHost">
              <DeleteIcon />
            </div>
          </div>
        </div>
      </div>
      <todoEditDialog ref="refEditDialog" @callback="cbEditDialog" />
    </div>
    <el-empty description="暂无待办" v-else> </el-empty>
  </div>
</template>
<script>
import EditIcon from "@/icons/edit.vue";
import DeleteIcon from "@/app_postmeet/icons/delete.vue";
import todoEditDialog from "./todoEditDialog.vue";
import { deleteTodo, updateTodo } from "@/app_postmeet/tools/api";
import {
  mergeTodoList,
  getLaTodoList,
  setTodoList,
} from "@/app_postmeet/tools/sale_report.js";
import { formatDate } from "@/js/utils.js";

export default {
  components: { todoEditDialog, EditIcon, DeleteIcon },
  data() {
    return {
      checked: false,
      list: [],
      recordViewPermission: false,
      isHost: false,
      activeId: "overview",
    };
  },
  mounted() {
    this.init();
    g.emitter.on("after_update_sale", () => {
      this.init();
    });
  },
  methods: {
    fdt(ds) {
      if (ds) {
        return formatDate(new Date(ds), "yyyy-MM-dd hh:mm");
      } else {
        return "-";
      }
    },
    init() {
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
      this.isHost = !g.postmeetStore.isReadonly();
      this.list = getLaTodoList();
    },
    onChange(item) {
      const data = this._toDbObj(item);
      updateTodo(data.id, data).then((resp) => {
        console.log("updateTodo resp", resp);
      });
    },
    handleClick() {
      this.$emit("callback", this.activeName);
    },
    onAdd() {
      this.$refs.refEditDialog.showAdd();
    },
    _toDbObj(row) {
      const data = {
        id: row.id,
        todoContent: row["待办内容"],
        timestamp: row["时间戳"],
        originalWords: row["原话"],
        executor: row["执行人"],
        deadline: row["截止时间"],
        done: row["done"] || false,
      };
      return data;
    },
    onEdit(row) {
      const data = this._toDbObj(row);
      this.$refs.refEditDialog.showEdit(data);
    },
    onDelete(row) {
      const that = this;
      ElMessageBox.confirm("此操作将永久删除该待办, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteTodo(row.id).then((resp) => {
            if (resp.code == 0) {
              that.list = that.list.filter((x) => x.id != row.id);
              that._updateTodoList();
              ElMessage.success("删除成功");
            } else {
              ElMessage.error("删除失败");
            }
          });
        })
        .catch((e) => {
          if (e !== "cancel") {
            ElMessage.info("删除时出错" + e);
          }
        });
    },
    _updateTodoList() {
      setTodoList(this.list);
      g.emitter.emit("after_analyse_dia", "");
      this.$forceUpdate();
    },
    cbEditDialog(action, data) {
      if (action == "add") {
        this.list = mergeTodoList(this.list, [data]);
      } else if (action == "edit") {
        this.list = mergeTodoList(this.list, [data]);
      }
      this._updateTodoList();
    },
    onReview(item) {
      const row = g.postmeetStore.data.saleAnalyseList["待办"][0];
      const index = row.value.map((x) => x["id"]).indexOf(item["id"]) + 1;
      g.emitter.emit("setAnalyseTag", [row, index, "待办"]);
    },
  },
};
</script>

<style lang="scss">
.todo_wrap {
  .td_header {
    justify-content: space-between;
    margin: 16px 0 24px 0;

    .td_right {
      color: #436bff;
      cursor: pointer;
    }
  }

  .td_items {
    .td_item {
      padding: 20px;
      margin: 10px 0;
      border-radius: 8px;
      background: #f9fafc;

      .td1 {
        margin: 0 0 10px 0;

        .td_title {
          font-weight: 400;
          color: #262626;
          line-height: 24px;
          margin: 0 0 0 12px;
        }

        .la_detail {
          display: none;
        }

        .delline {
          // width: 93% !important;
          text-decoration: line-through;
        }
      }

      .td2 {
        .td2_txt {
          height: 24px;
          color: #8c8c8c;
          line-height: 24px;
        }

        .td_line {
          width: 1px;
          height: 14px;
          background: #bfbfbf;
          margin: 4px 12px;
        }

        .td_btn {
          display: none;
        }
      }
    }

    .td_item:hover {
      background: #f1f3fb;

      .td1 {
        .la_detail {
          display: block;
          height: 22px;
          font-size: 14px;
          color: #436bff;
          line-height: 22px;
        }

        .la_detail_enable {
          cursor: pointer;
        }
      }

      .td2 {
        .td_btn {
          display: block;
          margin: 0 8px;
          color: #8c8c8c;
          cursor: pointer;
        }

        .td_btn:hover {
          color: #436bff;
        }
      }
    }
  }
}
</style>
