<template>
    <div class="opportunity_wrap">
        <el-card class="opportunity_card card_no_border" shadow="never">
            <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
                <template #_header_left>
                    <el-select v-model="datas.param.status" placeholder="进行中" clearable @change="onSearch"
                        style="width: 120px; margin-right: 16px;">
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>

                <template #col_riskCount="{ row }">
                    <ColRiskCount :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_meddic="{ row }">
                    <Meddic :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_communicationCount="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'communication')">
                        {{ formatCustNum(row.communicationCount) || '-' }}
                    </div>
                </template>

                <template #col_todosCount="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'todo')">
                        {{ formatCustNum(row.todosCount) }}
                    </div>
                </template>
            </MyTable>
        </el-card>
    </div>
</template>

<script setup>
import { apiOpportunity } from "@/app_client/api";
import { getColumns, column_widths } from "./misc.js";
import { formatCustNum } from "@/app_client/tools/utils.js";
import MyTable from "@/components/Table.vue";
import ColRiskCount from "@/app_client/views/customer/ColRiskCount.vue";
import Meddic from "@/app_client/views/customer/Meddic.vue";

const refTable = ref();
const statusOptions = ref([]);

const columns = getColumns();

const datas = reactive({
    tableid: "opportunity_client",
    param: {
        status: "进行中",
        searchKey: "",
        pageNumber: 1,
        pageSize: 20
    },
    need_init_load: true,
    show_search: true,
    need_header: true,
    form: {},
    fixed_column: "name",
    sortable: "custom",
    modal_type: "link",
    search_ph: "请输入商机名称搜索",
    delete_hint_column: "name",
    show_link_column: false,
    columns: columns,
    always_show_columns: ["name"],
    sortables: ["riskCount", "communicationCount", "todosCount"],
    template: ["riskCount",
        "meddic",
        "communicationCount",
        "todosCount"
    ],
    template_header: [],
    column_widths,
    urlGet: apiOpportunity.getOpportunityList,
});

const cbDatas = (action, data) => {
    if (action === "after_search") {
        // 处理搜索后的数据
    }
};

const onSearch = () => {
    refTable.value.search();
};

const onShowOpportunity = (row, page = "") => {
    // 处理商机详情查看
    console.log('查看商机:', row, page);
};

// 获取状态选项
const loadStatusOptions = async () => {
    try {
        const response = await apiOpportunity.getOpportunityStatusList();
        if (response.code === 0) {
            statusOptions.value = response.data;
        }
    } catch (error) {
        console.error('加载状态选项失败:', error);
    }
};

onMounted(() => {
    loadStatusOptions();
});

</script>

<style lang="scss" scoped>
.opportunity_wrap {
    background: #f7f9fe;
    padding: 24px;

    .table_wrap {
        margin: 4px;
    }

    :deep(.search_box) {
        .search_input {
            width: 215px !important;
        }
    }

    :deep(.table_class) {
        height: calc(100vh - 232px);

        .el-scrollbar {
            overflow: auto;
        }

        table {
            border-collapse: collapse;

            :deep(td:first-child) {
                border-right: 2px solid black;
            }

            .link {
                cursor: pointer;
            }
        }
    }

    :deep(table) {
        min-width: 1200px !important;
    }
}
</style>