<template>
    <el-drawer v-model="is_show" direction="rtl" class="arrange_visit_wrap" :close-on-click-modal="false">
        <template #header>
            <div class="vd_title">
                安排沟通
            </div>
        </template>
        <template #default>
            <ArrangeVisitCore ref="refArrangeVisitCore" @callback="onCallback" />
        </template>
        <template #footer>
            <div style="flex: auto">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm" :disabled="uploading">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
const refArrangeVisitCore = ref()
const is_show = ref(false)
const emit = defineEmits(['callback'])
const uploading = ref(false)

const onCancel = () => {
    is_show.value = false;
}

const onConfirm = () => {
    refArrangeVisitCore.value.onConfirm();
}

const onCallback = (action, data) => {
    if (action == 'reload') {
        emit('callback', 'reload');
        is_show.value = false;
    } else if (action == 'uploading') {
        uploading.value = data
    } else if (action == 'has_error') {
        is_show.value = false;
    }
}

const showEdit = (plan) => {
    is_show.value = true;
    nextTick(() => {
        refArrangeVisitCore.value.showEdit(plan);
    })
}

const init = (init_data = {}) => {
    is_show.value = true;
    nextTick(() => {
        refArrangeVisitCore.value.init(init_data);
    })
}

defineExpose({
    init, onCancel, onConfirm, is_show, showEdit, refArrangeVisitCore, uploading
})

</script>

<style lang="scss">
.arrange_visit_wrap {
    width: 600px !important;

    .el-drawer__body {
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        padding: 10px 24px;

        .ed_main {
            height: calc(100vh - 100px);

            .av_item {
                .av_item_value {
                    width: 90%;
                }
            }
        }
    }
}
</style>
