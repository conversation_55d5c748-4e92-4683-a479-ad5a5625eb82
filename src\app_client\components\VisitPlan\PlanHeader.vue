<template>
    <div class="plan-header">
        <div class="plan-header-left">
            <el-button class="today-btn" @click="handleTodayClick">今天</el-button>

            <div class="date-range">
                <el-icon class="arrow-icon" @click="handleDateRangeClick('prev-week')">
                    <ArrowLeft />
                </el-icon>
                <span>{{ formatDateRange }}</span>
                <el-icon class="arrow-icon" @click="handleDateRangeClick('next-week')">
                    <ArrowRight />
                </el-icon>
            </div>
        </div>
        <div class="plan-header-right">
            <el-select v-model="visitType" placeholder="沟通类型" @change="handleVisitTypeChange" class="visit-type-select"
                clearable>
                <el-option v-for="option in visitTypeOptions" :key="option.value" :label="option.label"
                    :value="option.value">
                    <div class="visit-type-item">{{ option.label }}</div>
                </el-option>
            </el-select>
            <LxSelect v-model="selectStatus" placeholder="沟通状态" @change="handleStatusChange" class="sel_status"
                clearable>
                <template #label="{ label }">
                    <div class="sel_status_item">
                        <div class="status-dot" :class="`status_${getStatusCss(label)}`"></div>
                        <span>{{ label }}</span>
                    </div>
                </template>
                <LxOption v-for="option in optionsStatus" :key="option.value" :label="option.label"
                    :value="option.value">
                    <div class="sel_status_item">
                        <div class="status-dot" :class="`status_${option.css}`"></div>
                        <span>{{ option.label }}</span>
                    </div>
                </LxOption>
            </LxSelect>
            <el-select v-model="selectedOption" placeholder="请选择" @change="handleOptionChange" class="sel_type">
                <el-option v-for="option in optionsType" :key="option.value" :label="option.label"
                    :value="option.value" />
            </el-select>
        </div>

    </div>
</template>

<script setup>
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { formatDate, addDays } from '@/js/utils';
import LxSelect from '@/components/LxSelect/LxSelect.vue';
import LxOption from '@/components/LxSelect/LxOption.vue';

const formatDateRange = ref('');
const dateRange = ref([]);
const selectStatus = ref('');
const selectedOption = ref('list');
const emit = defineEmits(['callback'])
const localFormatDateRange = ref({})

const optionsStatus = ref([
    // { value: 'all', label: '沟通状态', css: 'all' },
    { value: 'false', label: '未完成', css: 'undo' },
    { value: 'true', label: '已完成', css: 'done' },
]);

const optionsType = ref([
    { value: 'list', label: '列表' },
    { value: 'calendar', label: '日历' },
]);

const visitType = ref('')
const visitTypeOptions = ref([
    // { value: 'all', label: '沟通类型' },
    { value: 'my', label: '我创建的沟通' },
    { value: 'collaborated', label: '我协同的沟通' }
])

const handleTodayClick = () => {
    const today = new Date();
    const startDate = new Date(localFormatDateRange.value.startTime);
    const endDate = new Date(localFormatDateRange.value.endTime);

    // 如果今天的日期在范围内，则不触发更新
    if (today >= startDate && today <= endDate) {
        return;
    }

    emit('callback', 'update_calendar', today);
};

const handleStatusChange = (val) => {
    emit('callback', 'update_status', val)
};

const handleOptionChange = (val) => {
    emit('callback', 'change_view', val)
};

const handleVisitTypeChange = (val) => {
    if (!val) {
        emit('callback', 'update_visit_type', 'all')
    } else {
        emit('callback', 'update_visit_type', val)
    }
}

const handleDateRangeClick = (action) => {
    const startDate = localFormatDateRange.value.startTime
    const date = action == 'prev-week' ? addDays(startDate, -7) : addDays(startDate, 7)
    emit('callback', 'update_calendar', date)
}

const setDateRange = (dateParam) => {
    localFormatDateRange.value = dateParam
    const startDate = new Date(dateParam.startTime);
    const endDate = new Date(dateParam.endTime);
    const startDateStr = formatDate(startDate, 'yyyy年MM月dd日');
    const endDateStr = formatDate(endDate, 'yyyy年MM月dd日');
    formatDateRange.value = `${startDateStr} - ${endDateStr}`;
}

const getStatusCss = (label) => {
    const option = optionsStatus.value.find(opt => opt.label === label)
    return option ? option.css : 'all'
}

defineExpose({
    dateRange,
    selectedOption,
    handleStatusChange,
    handleOptionChange,
    optionsType,
    optionsStatus,
    formatDateRange,
    handleTodayClick,
    setDateRange,
    visitType,
    handleVisitTypeChange
})

</script>

<style lang="scss" scoped>
.plan-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding: 10px 0;
    justify-content: space-between;
    flex-wrap: nowrap;
    gap: 16px;

    .plan-header-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;

        .today-btn {
            margin-right: 0;
        }

        .visit-type-item {
            font-size: 14px;
            color: #262626;
        }

        .date-range {
            display: flex;
            align-items: center;
            user-select: none;
            white-space: nowrap;
        }

        .arrow-icon {
            margin: 0 10px;
            cursor: pointer;
            user-select: none;
        }
    }

    .plan-header-right {
        display: flex;
        flex-wrap: nowrap;
        gap: 10px;
        align-items: center;
        min-width: fit-content;
    }

    :deep(.el-select),
    :deep(.lx-select) {
        width: 120px;

        &.visit-type-select {
            width: 140px;
        }

        // .sel_status_item {
        //     display: inline-flex;
        // }

        .sel_status_item {
            display: flex;
            align-items: center;
            gap: 8px;

            span {
                font-size: 14px;
                color: #606266;
            }

            .status-dot {
                display: inline-block;
                width: 4px;
                height: 14px;
                flex-shrink: 0;

                &.status_all {
                    background-color: #fff;
                }

                &.status_undo {
                    background-color: #FF7300;
                }

                &.status_done {
                    background-color: #52C41A;
                }
            }
        }
    }
}
</style>
