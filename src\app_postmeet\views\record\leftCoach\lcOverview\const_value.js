
export const s_units = {
    '销售说话时长占比': '%',
    '销售说话速率': '字/分钟',
    '最长销售说辞': '分钟',
    '销售耐心值': '秒',
}

export const s_hints = {
    '销售说话时长占比': '销售人员说话总时长 / 拜访所有发言人说话总时长，建议值【30%，65%】',
    '销售说话速率': '每分钟说话字数，建议值【120，300】字/分钟',
    '最长销售说辞': '销售人员单次连续发言时间最长的一段，建议值【3，10】分钟',
    '销售耐心值': '销售人员在轮到他们发言前的平均等待时间 = 销售人员在轮到他们发言前的等待时间之和 / 发言次数，建议值【10，30】秒'
}


export const s_levels = {
    '销售说话时长占比': [0.30, 0.65],
    '销售说话速率': [120, 300],
    '最长销售说辞': [3, 10],
    '销售耐心值': [10, 30],
}

export const s_notes = {
    '销售说话时长占比': {
        "low": "销售说话时长占比较低，建议在适当时机增加发言时间，充分传达信息。",
        "high": "销售说话时长占比较高，建议减少单方面的发言时间，多倾听客户的需求和意见。",
        "normal": "销售说话时长占比在建议范围内，继续保持。",
    },
    '销售说话速率': {
        "low": "销售说话速率略慢，建议稍微加快语速，以保持客户的兴趣和互动的节奏。",
        "high": "销售说话速率略快，建议稍微放慢语速，以确保客户能清楚理解销售观点。",
        "normal": "销售说话速率控制在建议范围内，继续保持。",
    },
    '最长销售说辞': {
        "low": "最长销售说辞时间较短，可能无法充分传达信息，建议在关键问题上适当延长发言时间。",
        "high": "最长销售说辞时间较长，长时间的单方面发言可能会导致客户的注意力下降，建议适时与客户互动。",
        "normal": "最长销售说辞时间在建议范围内，继续保持。",
    },
    '销售耐心值': {
        "low": "销售耐心值略低，可能会打断客户的发言，建议在客户发言时稍作等待。",
        "high": "销售耐心值略高，可能会导致沟通节奏过慢，建议在适当等待后积极回应客户。",
        "normal": "销售耐心值在建议范围内，继续保持。",
    }
}