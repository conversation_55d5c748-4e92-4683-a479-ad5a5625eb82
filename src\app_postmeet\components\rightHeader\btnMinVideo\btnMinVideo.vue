<template>
  <div :class="`hicon ${isPlayVideo ? '' : 'isaudio'}`">
    <el-popover placement="bottom" trigger="manual" v-model="visible">
      <template #reference>
        <div @click="onClick">
          <el-tooltip class="item" effect="dark" content="隐藏/显示视频" placement="top">
            <HideIcon v-if="isPlayVideo" />
            <ShowIcon v-else />
          </el-tooltip>
        </div>
      </template>
      <div class="min-video-tip">
        <div class="light-icon">
          <LightIcon />
        </div>
        <div class="tip-content">
          <div class="tip-text">点击这里，可收起视频播放区域，全屏查看字幕哦～</div>
          <div class="know-btn-div">
            <el-button type="primary" size="small" class="know-btn" @click="onKnow"
              >我知道了</el-button
            >
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import HideIcon from "@/app_postmeet/icons/right_header/hide_video.vue";
import ShowIcon from "@/app_postmeet/icons/right_header/show_video.vue";
import LightIcon from "@/app_postmeet/icons/light.vue";

export default {
  components: { HideIcon, ShowIcon, LightIcon },
  data() {
    return {
      visible: false,
      isPlayVideo: localStorage.getItem("isPlayVideo") == "true",
    };
  },
  mounted() {
    setTimeout(() => {
      const hasShow = localStorage.getItem("hasShowMinVideoTip") || false;
      if (!hasShow) {
        localStorage.setItem("hasShowMinVideoTip", 1);
        this.visible = true;
      }
    }, 1000);
    g.emitter.on("showVideoPlayer", (status) => {
      this.updateStatus(status);
    });
    this.isPlayVideo = localStorage.getItem("isPlayVideo") == "true";
    this.updateStatus(this.isPlayVideo);
  },
  methods: {
    onClick() {
      this.updateStatus(!this.isPlayVideo);
    },
    updateStatus(status) {
      this.isPlayVideo = status;
      localStorage.setItem("isPlayVideo", status);
      g.postmeetStore.setValue("isPlayVideo", this.isPlayVideo);
      g.emitter.emit("playing_video", this.isPlayVideo);
      this.$emit("callback", "playing_video", this.isPlayVideo);
    },
    onKnow() {
      this.visible = false;
    },
  },
};
</script>
<style lang="scss">
.isaudio {
  color: #436bff !important;
}

.min-video-tip {
  width: 252px;
  height: 84px;
  background: linear-gradient(90deg, #e7f1ff 0%, #dce0ff 100%);
  padding: 14px;
  display: flex;
  flex-direction: row;

  .tip-content {
    display: flex;
    flex-direction: column;

    .know-btn-div {
      width: 100%;
      display: flex;
      justify-content: flex-end;

      .know-btn {
        margin-top: 14px;
      }
    }
  }
}
</style>
