export default class SoundMeter {
	constructor(context) {
		this.context = context;
		this.instant = 0.0;
		this.script = null;
		this.mic = null;
	}

	connectToSource(stream, callback) {
		try {
			this.mic = this.context.createMediaStreamSource(stream);
			this.script = this.context.createScriptProcessor(2048, 1, 1);
			
			this.script.onaudioprocess = (event) => {
				const input = event.inputBuffer.getChannelData(0);
				let sum = 0.0;
				for (let i = 0; i < input.length; ++i) {
					sum += input[i] * input[i];
				}
				this.instant = Math.sqrt(sum / input.length);
			};

			this.mic.connect(this.script);
			this.script.connect(this.context.destination);
			
			if (typeof callback !== 'undefined') {
				callback(null);
			}
		} catch (error) {
			if (typeof callback !== 'undefined') {
				callback(error);
			}
		}
	}

	stop() {
		if (this.mic) {
			this.mic.disconnect();
			this.mic = null;
		}
		if (this.script) {
			this.script.disconnect();
			this.script = null;
		}
	}
}