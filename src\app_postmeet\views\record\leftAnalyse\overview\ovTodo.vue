<template>
    <div class="la_customer_wrap flex-column">
        <div class="la_header flex-row">
            <div class="la_left flex-row">
                <div class="la_title">
                    待办
                </div>
            </div>
            <div v-if="items.length > 0" class="la_detail" @click="onDetail">
                详情
            </div>
        </div>
        <div class="la_md ovtodo_list">
            <ul v-if="items.length > 0">
                <li v-for="item in items">
                    {{ item['待办内容'] }}
                </li>
            </ul>
            <div v-else>
                暂无待办
            </div>
        </div>
    </div>
</template>

<script>
import { getLaTodoList } from "@/app_postmeet/tools/sale_report.js"
export default {
    data() {
        return {
            items: [],
            activeName: '概览',
            tags: ['概览', '客户态度', '风险项']
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
    },
    methods: {
        init() {
            this.items = getLaTodoList();
        },
        onDetail() {
            g.emitter.emit('update_analyse_menu', 'todo')
        }
    }
}

</script>

<style lang="scss">
.la_customer_wrap {
    position: relative;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 20px;
    margin: 12px 0;

    .la_header {
        justify-content: space-between;

        .la_left {
            width: 300px;

            .la_title {
                width: 96px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #262626;
                line-height: 26px;
                text-align: left;
                font-style: normal;
            }
        }

        .icon {
            margin: 4px 0 0 12px;
        }

        .la_detail {
            height: 22px;
            font-size: 14px;
            color: #436BFF;
            line-height: 22px;
            cursor: pointer;
        }
    }

    .ovtodo_list {
        ul {
            list-style: circle;
            padding-left: 35px;

            li {
                list-style: decimal !important;
            }
        }
    }
}
</style>