<template>
  <div class="ability-model-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <!-- <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template> -->

      <template #col_modelName="{ row }">
        <div class="model-name">{{ row.modelName }}</div>
      </template>

      <template #col_relatedAbilities="{ row }">
        <div class="related-abilities">
          <div v-for="(line, index) in row.relatedAbilities.split('\n')" :key="index" class="ability-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_testScore="{ row }">
        <div class="test-score">{{ row.testScore }}</div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
      </template>
    </MyTable>

    <AbilityModelFormDrawer ref="formDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import AbilityModelFormDrawer from "./AbilityModelFormDrawer.vue";
import { getAbilityModelList, deleteAbilityModel } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';

const refTable = ref();
const formDrawerRef = ref();

const tableConfig = reactive({
  tableid: 'ability_model',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模型名称",
  delete_hint_column: 'modelName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["modelName", "relatedAbilities", "testScore", "creator", "createTime"],
  template: ["modelName", "relatedAbilities"],
  urlGet: getAbilityModelList,
  urlDelete: deleteAbilityModel
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  formDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  formDrawerRef.value.openDrawer(row);
};

const handleFormSuccess = (result) => {
  const { mode, data } = result;
  if (mode === 'add') {
    ElMessage.success("创建成功");
  } else {
    ElMessage.success("更新成功");
  }
  // 刷新表格数据
  refTable.value.search();
};

const handleDelete = (row) => {
  confirmDelete(row.modelName, (status) => {
    if (status) {
      deleteAbilityModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.ability-model-table {
  padding: 24px 0;

  .model-name {
    font-weight: 500;
    color: #333;
  }

  .related-abilities {
    max-width: 400px;

    .ability-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .test-score {
    color: #333;
    font-weight: 500;
  }
}
</style>