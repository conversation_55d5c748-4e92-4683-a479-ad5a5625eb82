<template>
  <div class="admin_layout">
    <div :class="isCollapse ? 'sliderCollapse slider' : 'slider'">
      <div class="logo_box" @click="go_client">
        <img :src="getOssUrl('logo_admin2.png')" class="logo" />
      </div>
      <LeftSliderLx :isCollapse="isCollapse"></LeftSliderLx>
    </div>
    <div class="right">
      <div class="header">
        <Header @callback="cbHeader"></Header>
      </div>
      <div class="main">
        <div class="router_wrap">
          <router-view v-slot="{ Component }">
            <component :is="Component" />
          </router-view>
        </div>
      </div>
    </div>
    <UploadingBox />
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import Header from "./Header.vue";
import LeftSliderLx from "./LeftSliderLx.vue";
import LeftBottom from "./LeftBottom.vue";
import UploadingBox from "./UploadingBox";
import { getOssUrl, jsOpenNewWindow } from "@/js/utils.js";

import { Base64 } from "js-base64";
import config from "@/js/config";

const route = useRoute();
const isManage = ref(true);

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    if (!newPath.startsWith("/manage") && !newPath.startsWith("/admin")) {
      return;
    }
    isManage.value = newPath.startsWith("/manage");
    document.title = g.appStore.getTitle(newPath);
  },
  { immediate: true }
);

const go_client = () => {
  g.router.push(g.cv.defaultClientHomePage);
};

const cbHeader = (e) => {
  isManage.value = e;
};

const preview_file = (e) => {
  let go_url = "";
  const { url, ext } = e;
  if (ext == "pdf") {
    go_url = url;
  } else {
    go_url = `${config.publicPath}/#/ve/${Base64.encode(url)}`;
  }
  jsOpenNewWindow(go_url);
};

onMounted(() => {
  g.appStore.reloadUserInfo().then((status) => {
    if (status) {
      g.emitter.on("app_preview_file", preview_file);
    } else {
      g.appStore.logout();
    }
  });
});

onUnmounted(() => {
  g.emitter.off("app_preview_file");
});

const isCollapse = ref(false);
const handleModifySlider = () => {
  isCollapse.value = !isCollapse.value;
};
defineExpose({
  Header,
  go_client,
  getOssUrl,
  isManage,
  LeftSliderLx,
  LeftBottom,
  isCollapse,
  UploadingBox,
  handleModifySlider,
});
</script>

<style scoped lang="scss">
.admin_layout {
  width: 100%;
  height: 100%;
  display: flex;

  .slider {
    background-color: #fff;
    height: 100%;
    position: relative;

    .logo_box {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo {
        width: 130px;
        height: 28px;
      }
    }

    .el-menu {
      width: 183px;
      // 移动这些样式到 ::-webkit-scrollbar 选择器之前
      -ms-overflow-style: none;
      /* IE and Edge */
      scrollbar-width: none;
      /* Firefox */

      // 隐藏滚动条
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .sliderCollapse {
    width: 70px;
  }

  .right {
    flex: 1;
    display: flex;
    flex-direction: column;

    .header {
      height: 50px;

      .el-menu--horizontal {
        height: 50px;
      }
    }

    .main {
      flex: 1;
      box-sizing: border-box;
      padding: 10px 10px 0 10px;
      background-color: #f5f5f5;
      width: calc(100vw - 183px);
      height: calc(100vw - 300px);
      overflow-y: hidden;

      .router_wrap {
        width: 100%;
        height: 100%;
        background-color: #fff;
      }
    }
  }
}
</style>
