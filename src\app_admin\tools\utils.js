import { formatDate, addDays } from "@/js/utils.js"

// JS判断字符串长度（英文占1个字符，中文汉字占2个字符）
export const calcLetterlen = (str) => {
    if (str == null) return 0;
    if (typeof str != "string") {
        str += "";
    }
    return str.replace(/[^\x00-\xff]/g, "01").length;
}


export function getNDaysAgo(N) {
    var today = new Date();
    today.setDate(today.getDate() - N);
    var year = today.getFullYear();
    var month = (today.getMonth() + 1).toString().padStart(2, '0');
    var day = today.getDate().toString().padStart(2, '0');

    return year + '-' + month + '-' + day;
}


export function getCurrentWeekDay(date, week, format = 'YYYY-MM-dd') {
    if (typeof date == "string") {
        date = new Date(date)
    }
    // 输入一个日期，返回这个日期所在周某天的日期，week1 返回当周星期一的日期，week 7 返回当周星期天的日期，
    return formatDate(addDays(date, week - date.getDay()), format);
}

export function nowFormat(n = 0, format = 'YYYY-MM-dd') {
    return formatDate(addDays(new Date(), n), format);
}

export const apiHintWrap = (api, opname, ok_msg = '', fail_msg = '') => {
    return new Promise((resolve) => {
        api.then(resp => {
            if (resp.code == 0) {
                ElMessage.success(ok_msg ? ok_msg : `${opname}成功`);
                resolve({ status: true, resp: resp })
            } else {
                ElMessage.error(fail_msg ? fail_msg : `${opname}失败`);
                resolve({ status: false, resp: resp })
            }
        }).catch(e => {
            ElMessage.error(fail_msg ? fail_msg : `${opname}失败`);
            resolve({ status: false, resp: e })
        })
    })
}

export function transTemplateSaveObject(obj) {
    const result = [];
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const item = obj[key];
            const { selectedFields, topK } = item;
            const transformedItem = {
                id: key,
                label: item.label,
                assemblyMethod: item.assemblyMethod,
                docIds: item.docs.map(x => x.id),
                categoryIds: item.categories.map(x => x.id),
                selectedFields, topK
            };
            result.push(transformedItem);
        }
    }

    return result;
}

export const getDocColor = (status) => {
    if (status.indexOf('FAIL') > -1) {
        //red
        return '#F5222D'
    } else if (status.indexOf('SUCCESS') > -1) {
        // green
        return '#52C41A'
    } else {
        // blue
        return '#436BFF'
    }
}




export function renameProperties(data) {
    if (typeof data !== 'object' || data === null) {
        return data;
    }

    if (Array.isArray(data)) {
        return data.map(item => renameProperties(item));
    }

    const renamedData = {};
    for (let key in data) {
        if (key === 'child') {
            renamedData['children'] = renameProperties(data[key]);
        } else if (key === 'label') {
            renamedData['content'] = renameProperties(data[key]);
        } else if (key == 'data') {
            continue
        } else {
            if (key == 'docs' || key == 'categories') {
                renamedData['data'][key] = data[key];
            } else {
                if (!renamedData.hasOwnProperty('data')) {
                    renamedData['data'] = {}
                }
                renamedData['data'][key] = renameProperties(data[key]);
            }
        }
    }
    return renamedData;
}

export function convertData(jsonData) {
    let result = [];

    function traverse(node) {
        let item = {
            id: node.data?.id || '',
            label: node.content,
            items: []
        };

        for (let child of node.children) {
            item.items.push(traverse(child));
        }

        return item;
    }

    for (let node of jsonData) {
        result.push(traverse(node));
    }

    return result;
}

export function checkHasSpecChar(input) {
    // 使用正则表达式匹配特殊字符
    const specialChars = /[!@#$%^&！*()_+\-=\[\]{};`':"\\|,.<>\/?]+/;
    return specialChars.test(input)
}

export function removeSpaces(str) {
    // 使用正则表达式替换空格
    return str.replace(/\s/g, "");
}

export const loadScript = (src) => {
    return new window.Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.async = true
        script.defer = true
        script.src = src
        script.onload = () => resolve()
        script.onerror = () => reject()
        document.head.appendChild(script)
    })
}


export function anyhas(arr, key) {
    let isHas = false;
    for (let a of arr) {
        if (!isHas && a && a.indexOf(key) > -1) {
            isHas = true
            break
        }
    }
    return isHas
}

export function convertDataFormat(datas) {
    var result = [];

    // 获取列名
    var columns = datas[0];

    // 遍历数据行
    for (var i = 1; i < datas.length; i++) {
        var row = datas[i];
        var obj = {};

        // 遍历每一列，并将对应的值添加到对象中
        for (var j = 0; j < columns.length; j++) {
            var column = columns[j];
            obj[column] = row[j];
        }

        result.push(obj);
    }

    return result;
}

export function extractIds(data) {
    let ids = [];

    function traverse(node) {
        ids.push(node.value);

        if (node.children && node.children.length > 0) {
            for (let child of node.children) {
                traverse(child);
            }
        }
    }
    traverse(data);
    return ids;
}

export function array_remove(a, b) {
    return a.filter(x => b.indexOf(x) == -1)
}

export function array_merge(a, b) {
    return [...a, ...array_remove(b, a)]
}


export function calcDays(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000; // 1天的毫秒数

    // 将输入的日期字符串转换为Date对象
    const d1 = new Date(date1);
    const d2 = new Date(date2);

    // 计算两个日期之间的天数差
    const diffDays = Math.round(Math.abs((d1 - d2) / oneDay));

    return diffDays;
}

export function getSameLevelIds(treeData, parentId) {
    let sameLevelIds = [];
    function findSameLevelIds(node, parentId) {
        if (parentId === null) {
            return;
        }
        if (node.pid === parentId) {
            sameLevelIds.push(node.value);
        }
        if (node.children && node.children.length > 0) {
            for (let i = 0; i < node.children.length; i++) {
                findSameLevelIds(node.children[i], parentId);
            }
        }
    }

    findSameLevelIds(treeData, parentId);
    return sameLevelIds;
}

export function getLastDayOfQuarter(dateStr) {
    const date = new Date(dateStr);
    const quarter = Math.floor((date.getMonth() / 3)); // 获取季度，0表示第一季度，1表示第二季度，以此类推
    const year = date.getFullYear();

    const lastMonthOfQuarter = (quarter + 1) * 3; // 当前季度的最后一个月
    const lastDayOfMonth = new Date(year, lastMonthOfQuarter, 0).getDate(); // 当前季度最后一个月的总天数

    const lastDate = new Date(year, lastMonthOfQuarter - 1, lastDayOfMonth);
    return formatDate(lastDate)
}

export function getLastDayOfMonth(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月份从0开始，所以需要加1

    // 获取下一个月的第0天，即当前月的最后一天
    const lastDayOfMonth = new Date(year, month, 0).getDate();

    const lastDate = new Date(year, month - 1, lastDayOfMonth);
    return formatDate(lastDate)
}

function getFirstDayOfMonth(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth();

    return formatDate(new Date(year, month, 1))
}

function getFirstDayOfQuarter(dateStr) {
    const date = new Date(dateStr);
    const quarter = Math.floor(date.getMonth() / 3); // 获取季度，0表示第一季度，1表示第二季度，以此类推
    const year = date.getFullYear();

    const firstMonthOfQuarter = quarter * 3; // 当前季度的第一个月
    const firstDayOfMonth = new Date(year, firstMonthOfQuarter, 1); // 当前季度第一个月的第一天

    return formatDate(firstDayOfMonth)
}

export function autoDate(type, param) {
    let start = param.startTime
    let end = param.endTime;
    if (type == "month") {
        start = getFirstDayOfMonth(start)
        end = getLastDayOfMonth(end)
    } else if (type == "week") {
        start = getCurrentWeekDay(start, 1)
        end = getCurrentWeekDay(end, 7)
    } else if (type == "quarter") {
        start = getFirstDayOfQuarter(start)
        end = getLastDayOfQuarter(end)
    }
    return [start, end]
}

export function downloadFile(url, fileName) {
    // 创建一个隐藏的 <a> 元素
    var a = document.createElement('a');
    a.style.display = 'none';
    document.body.appendChild(a);
    // 设置文件的 URL 和名称
    a.href = url;
    a.download = fileName;
    // 模拟点击下载链接
    a.click();
    // 清理并移除 <a> 元素
    document.body.removeChild(a);
}

export function a2o(arr) {
    return arr.reduce((obj, item) => {
        obj[item.value] = item.label;
        return obj;
    }, {});
}

export function renameKeys(obj, renameMap) {
    return Object.fromEntries(
        Object.entries(obj).map(([key, value]) => {
            const newKey = renameMap[key] || key;
            return [newKey, value];
        })
    );
}

export const try2Array = (arr) => {
    if (!arr) {
        return []
    } else if (Array.isArray(arr)) {
        return arr;
    } else if (typeof arr == "string") {
        return arr.split(",")
    } else {
        return []
    }
}

