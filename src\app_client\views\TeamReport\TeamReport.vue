<template>
    <div class="team_report_container">
        <el-card class="team_report_card card_no_border" shadow="never">
            <slot name="header"></slot>
            <div class="v_header">
                <ReportType ref="refReportType" v-model:value="datas.param.reportType" @reload="onSearch" />
                <BtnDate v-model:start="datas.param.startTime" v-model:end="datas.param.endTime" @reload="onSearch"
                    defaultType="last_week" />
            </div>
            <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
                <template #col_report_name="{ row }">
                    <div class="report_name">
                        每{{ reportTypeMap[row.reportType] }}团队效能分析报告
                    </div>
                </template>
                <template #col_created_periods="{ row }">
                    <div class="report_periods">
                        <span>{{ row.startDate }}</span> ~
                        <span>{{ row.endDate }}</span>
                    </div>
                </template>
            </MyTable>
        </el-card>
    </div>
</template>

<script setup>
import { getTeamReport } from "@/app_client/tools/api.js";
import { getDateRangeStr, jsOpenNewWindow } from "@/js/utils.js";
import { reportTypeMap } from "@/app_client/tools/utils.js"
import MyTable from "@/components/Table.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import ReportType from "./ReportType.vue";
const isShowCrmStatus = ref(false);

const props = defineProps({
    team: {
        type: Boolean,
        required: false,
        default: false,
    },
});

const emit = defineEmits(["callback"]);
const refTable = ref();
const _getTeemReport = (p) => {
    const param = { ...p };
    if (typeof param.dptIds == "string") {
        param["dptIds"] = [param["dptIds"]];
    }
    return new Promise((resolve, reject) => {
        const orgId = g.appStore.user.orgId;
        const userId = g.appStore.user.id;
        if (p.reportType == "ALL") {
            delete p.reportType;
        }
        getTeamReport(orgId, userId, p).then((resp) => {
            if (resp.code == 0) {
                const { totalNum } = resp.data;
                resolve({
                    code: 0,
                    data: {
                        datas: resp.data.datas || [],
                        totalNum,
                    },
                });
            } else {
                reject(resp);
            }
        });
    });
};


const dr = getDateRangeStr("last_year");
const columns = ["report_name", "created_periods"];
const datas = reactive({
    tableid: 'team_report',
    param: {
        startTime: dr.startDt,
        endTime: dr.endDt,
        reportType: "ALL"
    },
    need_init_load: false,
    need_header: false,
    form: {},
    view_txt: "查看",
    modal_type: "link",
    show_link_column: true,
    show_link_view: true,
    columns,
    template: columns,
    urlGet: _getTeemReport,
});

const onSearch = () => {
    refTable.value.search();
};

const cbDatas = (action, data) => {
    if (action == "init_view") {
        const url = `${g.config.publicPath}/#/team_report/${data.reportType}/${data.reportTime}?startDate=${data.startDate}&endDate=${data.endDate}`;
        jsOpenNewWindow(url);
    }
};

const setDeptIds = (ids) => {
    datas.param.dptIds = ids;
    onSearch();
};

const onCallback = (action, data) => {
    if (action === "updateDept") {
    }
};

onMounted(() => {
    if (!props.team) {
        refTable.value.init();
    }

    g.emitter.on("file_uploaded", (page) => {
        if (page == "visit") {
            onSearch();
        }
    });

    isShowCrmStatus.value = g.appStore.getFuncStatus("sales_crm_update_filter");
});

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    MyTable,
    cbDatas,
    refTable,
    BtnDate,
    setDeptIds,
    props,
});

defineOptions({
    name: "VisitRecord",
});
</script>

<style lang="scss" scoped>
.team_report_container {
    background: #f7f9fe;
    padding: 20px;
    height: 100%;
}

.team_report_card {
    overflow: auto;
    background: #fff;

    .v_header {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .table_wrap {
        padding: 24px 0;
    }


    .vline2 {
        margin-top: 24px;
    }

    .vline3 {
        height: 20px;
        font-size: 12px;
        color: #8c8c8c;
        font-style: normal;
        margin: 24px 0;

        .link {
            color: var(--el-color-primary);
            cursor: pointer;
        }
    }

    .col_operation_ {
        .el-button {
            padding: 0;
        }
    }
}
</style>
