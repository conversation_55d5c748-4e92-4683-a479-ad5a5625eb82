<template>
    <div class="menu_wrap flex-row">
        <el-tabs v-model="activeName" @tab-change="handleClick">
            <el-tab-pane label="总结" name="summary"></el-tab-pane>
            <el-tab-pane label="分析" name="analyse"></el-tab-pane>
            <el-tab-pane label="辅导" name="coach"></el-tab-pane>
        </el-tabs>
        <!-- {{ isHost }},{{ !lessData }},{{ !loading }},{{ recordViewPermission }},{{ !isShowSetup }} -->
        <div v-show="isHost && !lessData && !loading && recordViewPermission && !isShowSetup">
            <div v-if="enableButton || isAdminMode">
                <img class="mwr_btn" src="./regen.png" @click="onRegen" />
            </div>
            <el-popover placement="top" width="230" trigger="hover">
                <template #default>
                    <div class="pop_mu_main">
                        <div class="phint">
                            仅当发言人信息变更时才可重新生成，请先在右侧发言记录中修改发言人信息
                        </div>
                        <img class="bg1" src="./bg3.png" />
                    </div>
                </template>
                <template #reference>
                    <div>
                        <img class="mwr_btn" src="./regen.png" v-if="!(enableButton || isAdminMode)" />
                    </div>
                </template>
            </el-popover>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUrlParam, removeURLParams } from '@/js/utils'

const emit = defineEmits(['callback'])

const isHost = ref(false)
const enableButton = ref(false)
const allowSaleReGen = ref(false)
const activeName = ref('summary')
const isShowSetup = ref(false)
const lessData = ref(false)
const loading = ref(false)
const recordViewPermission = ref(false)
const isAdminMode = ref(location.href.indexOf("is_admin_edit_mode") > -1)

const init = () => {
    isHost.value = !g.postmeetStore.isReadonly()
    allowSaleReGen.value = g.postmeetStore.data.allowSaleReGen
    isShowSetup.value = g.postmeetStore.getIsShowSetup()
    recordViewPermission.value = g.postmeetStore.getRecordViewPermission();
    enableButton.value = allowSaleReGen.value && !isShowSetup.value
    lessData.value = g.postmeetStore.lessData()
    loading.value = false
}

const handleClick = (value) => {
    g.postmeetStore.setValue('curr_menu', activeName.value)
    g.emitter.emit('postmeet_click_left_menu', activeName.value)
    emit('callback', activeName.value)
}

const onRegen = () => {
    enableButton.value = false;
    loading.value = true
    g.emitter.emit(g.postmeetStore.get_curr_on_report(), '')
}

onMounted(() => {
    init()
    g.emitter.on('updatedAsrContent', () => {
        init()
    })
    g.emitter.on('after_update_sale', () => {
        init()
    })
    g.emitter.on('monitor_sale_report', () => {
        loading.value = true
    })

    // 检查URL中是否有menu1参数，如果有则自动切换到对应tab
    const menuParam = getUrlParam('menu1')
    console.log('menuParam', window.location.search, menuParam)
    if (menuParam && ['summary', 'analyse', 'coach'].includes(menuParam)) {
        activeName.value = menuParam
        handleClick(menuParam)
        removeURLParams('menu1')
    }
})
defineExpose({
    init, activeName, handleClick, onRegen, isAdminMode
})
</script>

<style lang="scss">
.menu_wrap {
    justify-content: space-between;

    .mwr_btn {
        margin-top: 9px;
        width: 74px;
        height: 14px;
        cursor: pointer;
    }
}

.pop_mu_main {
    padding: 6px;
    color: #595959;

    .bg1 {
        width: 182px;
        height: 67px;
        margin-top: 10px;
    }
}
</style>