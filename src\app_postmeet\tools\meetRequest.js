export default function fetchWithTimeout(url, options, timeout = 4 * 60 * 1000) {
    return Promise.race([
        fetch(url, options),
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), timeout),
        ),
    ]);
}

let controllerCoze;
let signalCoze;

export function abortCozeRequest() {
    try {
        !!controllerCoze && controllerCoze.abort("user cancel");
    } catch (e) {
        console.log('abortRequest error', e)
    }
}


export function askMeetApi(confId, data) {
    return new Promise(async (resolve) => {
        if (!data.info) {
            console.error("no info")
            resolve(['', ''])
        }

        let response;
        let token = g.appStore.user.token || '';

        const url = `${g.config.meetApiHost}/rest/api/aichat/conference/${confId}/answer`;
        controllerCoze = new AbortController();
        signalCoze = controllerCoze.signalCoze;
        response = await fetchWithTimeout(url, {
            method: 'post',
            signalCoze,
            headers: {
                'Content-type': 'application/json',
                "Accept": "text/event-stream",
                stream: true,
                token
            },
            body: JSON.stringify(data)
        });
        const chatId = response.headers.get('Chat-id');
        const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();

        let answer = ''
        let lastIdx = 0;
        while (true) {
            const { value, done } = await reader.read();
            if (done) {
                reader.releaseLock()
                g.emitter.emit("coze_resp_done", '');
                break
            };
            answer += value;
            const new_resp = answer.slice(lastIdx);
            if (new_resp) {
                // console.log('新的答案：', new_resp)
                g.emitter.emit("coze_resp", new_resp);
            }
            lastIdx += new_resp.length;
        }
        resolve([chatId, answer]);
    })
}


export function askMeetApi2(confId, data) {
    const resps = [
        ['【', '商品', '信息', '查询', '】', '烧', '番茄', '蛋', '汤', '，', '先', '准备', '好', '番茄', '、', '鸡蛋', '、'],
        ['葱', '、', '姜', '、', '盐', '、', '糖', '、', '食用油', '等', '食材', '。', '将', '番茄', '洗净', '切块', '，', '鸡蛋', '打散', '备用'],
        ['。', '锅中', '倒油', '烧热', '，', '放入', '葱姜', '爆', '香', '，', '加入', '番茄', '块', '翻炒', '出', '汁', '，', '再', '加入', '适量'],
        ['清水', '煮开', '。', '接着', '慢慢', '倒入', '鸡蛋', '液', '，', '边', '倒', '边', '搅拌', '，', '形成', '蛋', '花', '。', '最后', '加入'],
        ['盐', '、', '糖', '等', '调味', '，', '撒', '上', '葱花', '就', '可以', '啦', '。']
    ]
    return new Promise((resolve) => {
        if (!data.info) {
            console.error("no info")
            resolve(['', ''])
        }

        let lastIdx = 0;
        const timer = setInterval(() => {
            if (lastIdx == resps.length) {
                timer && clearInterval(timer)
                g.emitter.emit("coze_resp_done", '');
                return resolve(['chatid', resps])
            }
            const new_resp = resps[lastIdx];
            // console.log('新的答案：', resps[lastIdx])
            g.emitter.emit("coze_resp", new_resp);
            lastIdx += 1;
        }, 1 * 100);
    })
}