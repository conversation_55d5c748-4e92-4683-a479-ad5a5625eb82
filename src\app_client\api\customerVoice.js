import {
  getCustomerFeedback<PERSON><PERSON>,
  getCustomerFaqApi,
  getCustomerFaqDistApi,
  getCustomerAttiDistApi,
  getFaqQuestionsCountApi,
  getCVCustomerNameListApi,
  getTop6FeedbacksApi,
  getCustomerFeedbacksAnalysisApi,
  getCustomerAttiAllDistApi,
  getFaqRecomendAnswerApi
} from "@/app_client/tools/api.js";

export const GetCustomerFeedbackApi = (days, param) => {
  return new Promise(async (resolve, reject) => {
    const mock_data = {
      datas: [],
      totalNum: 0
    }
    try {
      const res = await getCustomerFeedbackApi(days, param);
      if (res.code == 0 && res.data.totalNum > 0) {
        resolve(res.data);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("GetCustomerFeedbackApi error", days, param, e);
      resolve(mock_data);
    }
  });
};

export const GetFaqDistListApi = (obj) => {
  return new Promise(async (resolve, reject) => {
    const demo_data = [
      // {
      //   dimensionId: 0,
      //   dimensionName: "核心Mock",
      //   qaCount: 1250,
      //   qaRatio: 0.5,
      // },
      // {
      //   dimensionId: 1,
      //   dimensionName: "用户体验",
      //   qaCount: 987,
      //   qaRatio: 0.4,
      // },
    ];
    try {
      const res = (await getCustomerFaqDistApi(obj)) || { code: -1 };
      if (res.code == 0) {
        let list = res.data.datas;
        list = list.filter((x) => x.qaCount > 0);
        list = list.sort((a, b) => b.qaRatio - a.qaRatio);
        resolve(list || demo_data);
      } else {
        resolve(demo_data);
      }
    } catch (e) {
      console.log("GetFaqDistListApi error", obj, e);
      resolve(demo_data);
    }
  });
};

export const GetFaqQuestionsCountApi = (obj) => {
  return new Promise(async (resolve, reject) => {
    const mock_data = [
      // {
      //   dimensionName: "核心功能",
      //   question: "mock应用启动时网络连接不通",
      //   qaCount: "1250次",
      // },
      // {
      //   dimensionName: "用户体验",
      //   question: "商品详情页加载速度过慢",
      //   qaCount: "987次",
      // },
      // {
      //   dimensionName: "价格体系",
      //   question: "会员续费价格高于首次开通",
      //   qaCount: "845次",
      // }
    ];
    try {
      const res = (await getFaqQuestionsCountApi(obj)) || {};
      if (res.code == 0) {
        let list = res.data.datas || mock_data;
        list.sort((a, b) => b.qaCount - a.qaCount);
        resolve(list);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("GetFaqQuestionsCountApi error", obj, e);
      resolve(mock_data);
    }
  });
};

export const GetFaqQAListApi = (days, param) => {
  return new Promise(async (resolve, reject) => {
    const mock_data = {
      datas: [
        //   {
        //   "answer": "我们将在下周的月会上通过PPT材料展示本月的工作成果，确保最终效果的呈现。",
        //   "conferenceId": "1933453220164714496",
        //   "contactIds": "user2",
        //   "contactNames": "",
        //   "createdTime": "2025-06-13 17:15:45",
        //   "createdUser": "时东东",
        //   "customerId": "1933437517076414464",
        //   "customerName": "新疆中泰（集团）有限责任公司",
        //   "dimensionId": "1934495891444543489",
        //   "question": "在搭建过程中，我们还没有看到最终效果，是否有具体的呈现计划？"
        // }
      ],
      "totalNum": 0
    };
    try {
      const res = await getCustomerFaqApi(days, param);
      if (res.code == 0) {
        resolve(res.data);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("GetFaqQAListApi error", days, param, e);
      resolve(mock_data);
    }
  });
};

export const GetCustomerAttiDistListApi = (obj) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = (await getCustomerAttiDistApi(obj)) || {};
      // {
      // 	"negativeCount":24,
      // 	"positiveCount":58
      // } -->
      // {
      //     name: '核心功能',
      //     positive: 4,
      //     negative: 0,
      // },
      if (res.code == 0) {
        const list =
          res.data.datas.map((item) => ({
            id: item.dimensionId,
            name: item.dimensionName,
            positive: item.positiveCount,
            negative: item.negativeCount,
          })) || [];
        resolve(list || []);
      } else {
        resolve([]);
      }
    } catch (e) {
      console.log("GetCustomerAttiDistListApi error", obj, e);
      resolve([]);
    }
  });
};

export const getQuadrantData = (param) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getTop6FeedbacksApi(param);
      if (res.code == 0) {
        const list = res.data || [];
        resolve(list);
      } else {
        resolve([]);
      }
    } catch (e) {
      console.log("getTop6FeedbacksApi error", param, e);
      resolve([]);
    }
  });
};

export const getVoiceAnalyse = async (days) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getCustomerFeedbacksAnalysisApi(days)
      if (res.code == 0) {
        resolve(res.message);
      } else {
        resolve('');
      }
    } catch (e) {
      console.log("getVoiceAnalyse error", days, e);
      resolve('');
    }
  });
};

export const getCompetitors = async (param1) => {
  const mock_data = [
    // { label: "北森mock", value: "1" },
    // { label: "乐享", value: "2" },
    // { label: "酷学院", value: "3" },
    // { label: "学知网", value: "4" },
  ];

  return new Promise(async (resolve, reject) => {
    try {
      const res = { code: -1 };
      if (res.code == 0) {
        const list = res.data.datas;
        resolve(list);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("getCompetitors error", obj, e);
      resolve(mock_data);
    }
  });
};

//getCvCustomerListApi

export const getCvCustomerListApi = async (days, query = '') => {
  return new Promise(async (resolve, reject) => {
    const mock_data = [
      // {
      //   id: "1839194302903750656",
      //   name: "江苏云学堂网络科技有限公司",
      // },
      // {
      //   id: "1849012073741160448",
      //   name: "四川海底捞餐饮股份有限公司",
      // }
    ];
    try {
      const res = await getCVCustomerNameListApi(days, query, 10);
      if (res.code == 0) {
        const list = res.data.datas || mock_data;
        resolve(list);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("getCompetitors error", days, e);
      resolve(mock_data);
    }
  });
};


export const getCustomerAttiAllDist = async (days, param) => {
  return new Promise(async (resolve, reject) => {
    const mock_data = [
      //   {
      //   "dimensionId": "1930885248975900672",
      //   "dimensionName": "整体情感体验",
      //   "negativeCount": 5,
      //   "positiveCount": 8
      // }
    ]
    try {
      const res = await getCustomerAttiAllDistApi(days, param);
      if (res.code == 0) {
        const list = res.data.datas;
        resolve(list);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("getCompetitors error", days, e);
      resolve(mock_data);
    }
  });
};

export const getFaqRecomendAnswer = async (days, param) => {
  return new Promise(async (resolve, reject) => {
    const mock_data = "mock data";
    try {
      const res = await getFaqRecomendAnswerApi(days, param);
      if (res.code == 0) {
        resolve(res.data.recommend_answer || '');
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("getFaqRecomendAnswer error", days, e);
      resolve(mock_data);
    }
  });
};
