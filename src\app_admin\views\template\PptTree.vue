<template>
    <div v-show="treeData.length > 0">
        <el-tree :data="treeData" :props="defaultProps" :default-expand-all="true" :highlight-current="true"
            @node-click="handleNodeClick" class="ppt_tree_wrap" node-key="id" :current-node-key="currNode.id">
            <template #default="{ node, data }">
                <span :class="`custom-tree-node`">
                    <span>{{ node.label }}</span>

                    <el-tag v-if="data.assemblyMethod == 'FIX_MODE'">固定</el-tag>
                    <el-tag type="warning"
                        v-else-if="data.assemblyMethod == 'AI_MODE' || data.assemblyMethod == 'FILE_MODE'">AI</el-tag>
                </span>
            </template>
        </el-tree>
    </div>
</template>

<script setup>
import { nextTick } from "vue"
const emit = defineEmits(['callback']);
const defaultProps = {
    children: 'child',
    label: 'label',
};
const currNode = ref({ id: '' })

const treeData = ref([])

const handleNodeClick = (data) => {
    nextTick(() => {
        currNode.value = data;
    })

    emit('callback', 'click', toRaw(data) || [])
}

const init = (data) => {
    treeData.value = data;
    handleNodeClick(data[0])
}

defineExpose({
    init,
    treeData,
    currNode,
    handleNodeClick
})
</script>

<style lang="scss">
.ppt_tree_wrap {
    background: #FAFAFA;
    height: calc(100vh - 183px);
    overflow: auto;
    padding-top: 16px;

    .curr_node {
        background-color: aliceblue;
    }

    .el-tag {
        margin-left: 10px;
        height: 17px;
        padding: 3px;
    }
}
</style>