<template>
  <div class="my_vistor_wrap">
    <div class="visit-tabs">
      <el-tabs v-model="activeName" @tab-change="onTabChange">
        <el-tab-pane label="拜访计划" name="visit_plan"> </el-tab-pane>
        <el-tab-pane label="拜访记录" name="visit_record"> </el-tab-pane>
      </el-tabs>
      <div class="visit-btn" v-if="!isElectron">
        <el-button type="primary" @click="onPlanVisit" class="plan_btn">安排拜访</el-button>
      </div>
    </div>
    <div class="visit-content">
      <el-card class="card_no_border" shadow="never">
        <VisitPlan ref="refVisitPlan" v-if="activeName === 'visit_plan'" :team="props.team" @callback="onCallback" />
        <VisitRecord ref="refVisitRecord" v-else :team="props.team" @callback="onCallback" />
      </el-card>
    </div>
    <DrawerArrangeVisit ref="refArrangeVisit" @callback="onCallback" />
    <DrawerVisitDetail ref="refVisitDetail" @callback="onCallback" />
  </div>
</template>

<script setup>
import VisitRecord from "@/app_client/components/VisitRecord/VisitRecord.vue";
import VisitPlan from "@/app_client/components/VisitPlan/VisitPlan.vue";
import DrawerArrangeVisit from "@/app_client/components/DrawerArrangeVisit.vue";
import DrawerVisitDetail from "@/components/DrawerVisitDetail.vue";
import { jsOpenNewWindow } from "@/js/utils";
import { useRoute, useRouter } from 'vue-router';

const refVisitPlan = ref();
const refVisitRecord = ref();
const refArrangeVisit = ref();
const route = useRoute();
const defaultPeriodType = route.query.tab || 'visit_plan';
const activeName = ref(defaultPeriodType);
const refVisitDetail = ref();
const deptIds = ref([]);

const router = useRouter();

const onPlanVisit = () => {
  refArrangeVisit.value.init();
};
const isElectron = ref(g.config.isElectron);

const onTabChange = () => {
  nextTick(() => {
    if (activeName.value == "visit_record") {
      refVisitRecord.value.setDeptIds(deptIds.value);
    } else {
      refVisitPlan.value.setDeptIds(deptIds.value);
    }
  });
};

const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const onCallback = (action, data) => {
  if (action == "reload") {
    if (activeName.value == "visit_plan") {
      refVisitPlan.value.reload(true);
    } else {
      refVisitRecord.value.onSearch();
    }
  } else if (action == "edit_plan") {
    refArrangeVisit.value.showEdit(data);
  } else if (action == "visit_detail") {
    refVisitDetail.value.init(data);
  } else if (action == "prepare") {
    jsOpenNewWindow(`/#/prepare/${data.scheduleId}`, "_blank");
  }
};

const setDeptIds = (ids) => {
  deptIds.value = ids;
  onTabChange();
};

const handleUrlParams = () => {
  const action = route.query.action;
  const salesMateCustomerName = route.query.company;
  const customerId = route.query.id;

  if (action === 'create_plan' && salesMateCustomerName) {
    router.replace({
      path: route.path,
      hash: route.hash.split('?')[0]
    });

    nextTick(() => {
      refArrangeVisit.value.init({ customerId, salesMateCustomerName });
    });
  }
};

onMounted(() => {
  handleUrlParams();
});

defineExpose({
  VisitRecord,
  VisitPlan,
  refArrangeVisit,
  refVisitDetail,
  DrawerVisitDetail,
  isElectron,
  refVisitPlan,
  refVisitRecord,
  DrawerArrangeVisit,
  setDeptIds,
});
</script>

<style lang="scss" scoped>
.my_vistor_wrap {
  background: #e9e9e9;
  height: 100%;

  .visit-tabs {
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background-color: #e9e9e9;
    }

    .el-tabs {
      --el-tabs-header-height: 59px;

      .el-tabs__item {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .plan_btn {
      background: linear-gradient(45deg, #691ffe 0%, #03b8e7 100%);
      color: #fff;
      min-width: 60px;
    }

    .el-tabs__header {
      padding: 0;
      margin: 0;
    }

    .visit-btn {
      margin-top: 10px;
    }
  }

  .visit-content {
    background: #f7f9fe;
    padding: 24px;
    height: calc(100vh - 107px);

    .el-card {
      min-height: calc(100vh - 177px);
      overflow-y: auto;
    }
  }
}
</style>
