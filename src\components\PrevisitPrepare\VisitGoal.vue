<template>
  <div class="visit-goal prepare_wrap" v-ai-tip="'bottom-right'">
    <div class="header">
      <div class="title">
        <img :src="getOssUrl('visit.png')" alt="visit_goal" />
        {{ info.subject }}
      </div>
      <div class="detail" @click="onShowDetail">详情</div>
    </div>
    <div class="date">{{ formatShowTime }}</div>
    <div class="guide-card">
      <div v-html="renderedContent"></div>
    </div>
    <DrawerVisitDetail ref="refVisitDetail" :readonly="true" @callback="onCallback" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { md2html } from "@/js/md.js"
import DrawerVisitDetail from "@/components/DrawerVisitDetail.vue";
import { useRoute } from "vue-router";
import { getOssUrl } from "@/js/utils";

const refVisitDetail = ref();
const info = ref({});
const renderedContent = ref("");
const formatShowTime = ref("");
const route = useRoute();

const onShowDetail = () => {
  const data = {
    scheduleId: route.params.id,
  };
  refVisitDetail.value.init(data);
};

const onCallback = (type, data) => {
  if (type == "reload") {
    emit("callback", "reload");
  }
};

const _formatTime = (timeStr) => {
  if (!timeStr) return "";

  const date = new Date(timeStr);
  const months = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ];
  const weeks = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];

  const month = months[date.getMonth()];
  const day = date.getDate();
  const week = weeks[date.getDay()];
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");

  // 计算结束时间 (假设持续1小时)
  const endDate = new Date(date);
  endDate.setHours(endDate.getHours() + 1);
  const endHours = endDate.getHours().toString().padStart(2, "0");
  const endMinutes = endDate.getMinutes().toString().padStart(2, "0");

  return `${month}${day}日 ${week} ${hours}:${minutes}-${endHours}:${endMinutes}(GMT+8)`;
};

const init = (data) => {
  info.value = data;
  formatShowTime.value = _formatTime(data.startTime);
  if (data.visitStrategy) {
    renderedContent.value = md2html(data.visitStrategy);
  }
};

defineExpose({
  init,
  refVisitDetail,
  DrawerVisitDetail,
});
</script>

<style lang="scss" scoped>
.visit-goal {
  background: #eef1fd !important;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;

  .header {
    display: flex;
    justify-content: space-between;

    .title {
      height: 28px;
      font-weight: 500;
      font-size: 20px;
      color: #262626;
      line-height: 28px;
    }

    .detail {
      height: 24px;
      font-size: 14px;
      color: #436bff;
      line-height: 24px;
      cursor: pointer;
    }
  }

  .date {
    height: 24px;
    font-weight: 400;
    font-size: 14px;
    color: #262626;
    line-height: 24px;
    margin: 10px 0;
  }

  p {
    margin-top: 10px;
    line-height: 1.5;
  }

  .guide-card {
    :deep(h1, h2, h3, h4, h5, h6) {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 500;
      color: #262626;
    }

    :deep(h1) {
      font-size: 18px;
    }

    :deep(h2) {
      font-size: 16px;
    }

    :deep(h3) {
      font-size: 14px;
    }

    :deep(p) {
      margin: 8px 0;
      font-size: 14px;
      color: #262626;
      line-height: 1.6;
    }

    :deep(ul, ol) {
      padding-left: 20px;
      margin: 8px 0;

      li {
        font-size: 14px;
        color: #262626;
        line-height: 1.6;
        margin-bottom: 4px;
      }
    }

    :deep(code) {
      background: #f2f3f5;
      padding: 2px 4px;
      border-radius: 2px;
      font-family: monospace;
    }

    :deep(pre) {
      background: #f2f3f5;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;

      code {
        background: none;
        padding: 0;
      }
    }

    :deep(blockquote) {
      margin: 8px 0;
      padding-left: 12px;
      border-left: 4px solid #e5e6eb;
      color: #86909c;
    }
  }
}
</style>
