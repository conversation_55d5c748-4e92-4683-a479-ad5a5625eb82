<template>
    <div class="sale_left_wrap">
        <EditMenu ref="refMenu" @callback="cbMenu" />
        <dialogType ref="refDialog" @callback="cbModal" />
    </div>
</template>

<script setup>
import dialogType from "./dialogType.vue";
import EditMenu from "@/app_admin/components/EditMenu.vue";
import { getXmTopic, deleteXmTopic, updateXmTopicOrder } from "@/app_admin/tools/api.js"

const rdValue = ref('customer')
const refMenu = ref(null);
const refDialog = ref(null);
const labelDatas = ref({})
const emit = defineEmits(['callback']);

const cbMenu = (action, data) => {
    if (action == 'dialog_add') {
        refDialog.value.show_add(rdValue.value == 'customer')
    } else if (action == 'dialog_edit') {
        refDialog.value.show_edit(data)
    } else if (action == 'click') {
        emit('callback', action, toRaw(data))
    } else if (action == 'sort') {
        const ids = data.map(x => x.id)
        updateXmTopicOrder({ ids }).then(resp => {
            refMenu.value.update_data(data)
        })
    }
}

const query = (force) => {
    return new Promise((resolve) => {
        function fn() {
            const { customerTopics, internalTopics } = labelDatas.value;
            const list = rdValue.value == 'customer' ? customerTopics : internalTopics;
            resolve(list)
        }
        if (!force && labelDatas.value.hasOwnProperty('customerTopics')) {
            fn()
        } else {
            getXmTopic().then(resp => {
                if (resp.code == 0) {
                    labelDatas.value = resp.data;
                    fn()
                } else {
                    resolve([])
                }
            }).catch(() => {
                resolve([])
            })
        }
    })
}

const setRdValue = (value) => {
    rdValue.value = value;
    refMenu.value.query(false)
}

const cbModal = (action, data) => {
    if (action == "reload") {
        refMenu.value.query(true, data)
    }
}

const init = () => {
    const cfg = {
        name: '主题标签',
        methodGet: query,
        methodDelete: deleteXmTopic,
        label: "label",
        canDeleteAll: false,
        maxRows: 20,
        sortable: true,
    }
    refMenu.value.init(cfg)
}

defineExpose({
    init,
    rdValue,
    setRdValue,
    EditMenu,
    deleteXmTopic,
    dialogType,
    cbModal,
    refDialog
})

</script>

<style lang="scss">
.sale_left_wrap {
    .rdType {
        margin: 24px 24px 0 24px;

        .el-radio-button__inner {
            width: 100px;
        }

        .el-radio-button__original-radio:checked+.el-radio-button__inner {
            color: #436BFF;
            background-color: #fff;
        }
    }

    .left_list {
        height: calc(100vh - 194px);
        overflow-y: auto;
    }
}
</style>