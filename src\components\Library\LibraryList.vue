<template>
  <div class="library-list">
    <div class="ll_header">
      <div class="total-count">{{ clipList.length }} 个剪辑片段</div>
      <div>
        <el-dropdown trigger="click" placement="bottom-end" @command="(command) => handleCommand(command, data)">
          <el-icon class="orderby_img">
            <img :src="getOssUrl('orderby.png')" />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="playCount">按播放次数</el-dropdown-item>
              <el-dropdown-item command="createdTime" divided>按更新时间</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="list-item" v-for="item in clipList" :key="item.id">
      <div class="item-left">
        <div class="item-icon">
          <IconMic />
        </div>
        <div class="item-duration">{{ getSecondsShowTime(item.duration / 1000) }}</div>
      </div>

      <div class="item-content">
        <div class="item-title" @click="handleCommand('play', item)">
          {{ item.title }}
        </div>
        <div class="item-info">
          <span>拜访主题: {{ item.title }}</span>
          <span>客户名称: {{ item.salesMateCustomerName }}</span>
          <span>拜访创建人: {{ item.hostName }}</span>
        </div>
        <div class="item-stats">
          <img :src="getOssUrl('view_count.png')" />
          <span>{{ item.playCount }} 次播放</span>
          <img :src="getOssUrl('view_time.png')" />
          <span>创建时间: {{ item.createdTime }}</span>
        </div>
      </div>

      <div class="item-actions" v-if="enableEdit">
        <button class="edit-btn" v-if="getOpAccess('edit')" @click="handleEdit(item)">
          编辑
        </button>
        <el-dropdown trigger="click" v-if="getOpAccess('delete')" @command="(command) => handleCommand(command, item)">
          <el-icon class="more-actions">
            <MoreFilled />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="delete">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
  <ModalEditClipper ref="refModalEditClipper" @callback="cbModal" :isadmin="props.isadmin" />
</template>

<script setup>
import IconMic from "@/app_client/icons/mic.vue";
import MoreIcon from "@/icons/more.vue";
import { MoreFilled } from "@element-plus/icons-vue";
import { searchClipLib, deleteClipLib } from "@/app_client/tools/api";
import { getOssUrl, getSecondsShowTime } from "@/js/utils";
import ModalEditClipper from "./components/ModalEditClipper.vue";

const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

const refModalEditClipper = ref(null);
const emit = defineEmits(["callback"]);
const clipList = ref([]);
const nodeData = ref({ id: "" });
const enableEdit = ref(false);
const hasAdminPermission = ref(false);
const searchParams = ref({
  libId: "",
  keyword: "",
  hostUserId: "",
  pageSize: 100,
  pageNum: 0,
  sortField: "createdTime",
  sortDirection: "DESC",
});

const search = () => {
  searchClipLib(props.isadmin, toRaw(searchParams.value)).then((res) => {
    clipList.value = res.content;
    enableEdit.value =
      props.isadmin && (
        (res.content.length > 0 && searchParams.value.hostUserId) ||
        hasAdminPermission.value);
  });
};

const setSearchKeyword = (keyword) => {
  searchParams.value.keyword = keyword;
  search();
};

const setHostUserId = (userId) => {
  searchParams.value.hostUserId = userId;
  if (userId) {
    searchParams.value.libId = "";
  }
  search();
};

const handleEdit = (item) => {
  refModalEditClipper.value.show_edit(item);
};

const cbModal = (command, data) => {
  if (command == "refresh") {
    search();
  }
};
const handleCommand = (command, data) => {
  switch (command) {
    case "playCount":
    case "createdTime":
      searchParams.value.sortField = command;
      search();
      break;
    case "delete":
      ElMessageBox.confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteClipLib(data.id)
            .then((res) => {
              search();
            })
            .catch(() => {
              ElMessage.error("删除失败");
            });
        })
        .catch((e) => {
        });
      break;
    case "play":
      const url = `${g.config.publicPath}/#/postmeet/clipplayer/${data.id}`;
      window.open(url, "_blank");
      break;
  }
};

const init = (permission, data) => {
  hasAdminPermission.value = permission;
  nodeData.value = data;
  searchParams.value.hostUserId = "";
  searchParams.value.libId = data.id;
  enableEdit.value = props.isadmin && (getAccess('case_manage_opr') || searchParams.value.hostUserId);
  search();
};

const getOpAccess = (type) => {
  const hasCaseManage = getAccess('case_manage_opr');

  if (type === 'edit' || type === 'delete') {
    return hasCaseManage;
  }
  return false;
}

const getAccess = (code) => {
  return g.cacheStore.checkPointActionByCode("case_library", code);
}

defineExpose({
  init,
  setSearchKeyword,
  setHostUserId,
  handleCommand,
  IconMic,
  MoreIcon,
  getSecondsShowTime,
  getOssUrl,
  refModalEditClipper,
  search,
});
</script>
<style lang="scss" scoped>
.library-list {
  padding: 6px 0;
  border-radius: 8px;

  .ll_header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  .total-count {
    font-size: 14px;
    color: #666;
    margin: 0 0 16px 0;
  }

  .list-item {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    .item-left {
      margin-right: 16px;
      text-align: center;

      .item-duration {
        color: #436bff;
        font-size: 12px;
      }
    }

    .item-content {
      flex: 1;

      .item-title {
        font-weight: 500;
        font-size: 14px;
        color: #262626;
        margin-bottom: 8px;
        cursor: pointer;
        width: fit-content;
      }

      .item-info {
        span {
          margin-right: 16px;
          color: #8c8c8c;
          font-size: 12px;
        }
      }

      .item-stats {
        color: #8c8c8c;
        font-size: 12px;
        margin-top: 8px;

        span {
          margin-right: 16px;
        }

        img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          margin-top: 1px;
        }
      }
    }

    .item-actions {
      display: flex;
      align-items: center;

      .edit-btn {
        width: 60px;
        height: 32px;
        background: linear-gradient(45deg, #691ffe 0%, #03b8e7 100%);
        border-radius: 4px;
        color: white;
        border: none;
        padding: 4px 12px;
        border-radius: 4px;
        margin-right: 8px;
        cursor: pointer;
      }

      .more-actions {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        margin-left: 8px;
        border: 1px solid #d9d9d9;
        cursor: pointer;
      }
    }

    &:hover {
      color: #436bff;
    }
  }

  .list-item:hover {
    box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);

    .item-title {
      color: #436bff;
    }
  }
}
</style>
