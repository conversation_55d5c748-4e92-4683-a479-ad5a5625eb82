<template>
    <div class="filter_condition_wrap flex-col">
        <div class="fch_filter flex-row">
            <div class="filter_item">
                <el-select v-model="emotion" placeholder="全部态度" class="filter_select" @change="updateEmotion" clearable>
                    <el-option v-for="item in emotionList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </div>
            <div class="filter_item">
                <el-select v-model="customer" placeholder="全部客户" class="filter_customer" filterable remote
                    @change="updateCustomer" collapse-tags multiple clearable :remote-method="remoteMethod"
                    :reserve-keyword="false">
                    <el-option v-for="item in customerList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </div>
            <div class="filter_item">
                <el-button type @click="handleSearch" class="filter_btn">确定</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
const emotionList = [
    {
        value: '',
        label: '全部态度'
    }, {
        value: '0',
        label: '积极态度'
    }, {
        value: '2',
        label: '消极态度'
    }
]
const emotion = ref('');
const customer = ref('');
const store = g.clientBiStore

const customerList = computed(() => store.cvCustomerList || [])

const remoteMethod = async (query) => {
    await g.clientBiStore.getCvCustomerList(query)
}

const handleSearch = async () => {
    g.clientBiStore.updateCvFbVersion()
}
const updateEmotion = async () => {
    g.clientBiStore.setFilterCondition({
        emotion: emotion.value,
    })
}
const updateCustomer = async () => {
    g.clientBiStore.setFilterCondition({
        customer: customer.value,
    })
}

onMounted(async () => {
    await g.clientBiStore.getCvCustomerList()
})

</script>
