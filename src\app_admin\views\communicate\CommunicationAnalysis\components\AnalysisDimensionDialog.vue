<template>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" :before-close="handleClose"
        @open="openDrawer">
        <el-form :model="form" :rules="rules" ref="form" label-width="100px">
            <el-form-item label="维度名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入维度名称"></el-input>
            </el-form-item>
            <el-form-item label="标签" prop="tag">
                <el-input v-model="form.tag" placeholder="请输入标签"></el-input>
            </el-form-item>
            <el-form-item label="标签类型" prop="tagType">
                <el-select v-model="form.tagType" placeholder="请选择标签类型">
                    <el-option label="默认" value=""></el-option>
                    <el-option label="主要" value="primary"></el-option>
                    <el-option label="成功" value="success"></el-option>
                    <el-option label="警告" value="warning"></el-option>
                    <el-option label="危险" value="danger"></el-option>
                    <el-option label="信息" value="info"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
const emit = defineEmits(['submit']);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = ref({
    name: '',
    tag: '',
    tagType: ''
});

const rules = {
    name: [
        { required: true, message: '请输入维度名称', trigger: 'blur' }
    ]
};

const handleClose = () => {
    dialogVisible.value = false
};
const openDrawer = () => {
    dialogVisible.value = true;
};

const handleSubmit = () => {
    emit('submit', form.value);
    handleClose();
};

defineExpose({
    openDrawer
});
</script>

<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>