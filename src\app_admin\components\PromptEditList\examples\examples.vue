<template>
    <component :is="getComp()" v-model:choosed="choosed" :enableChoose="props.enableChoose" />
</template>
<script setup>
import segmentedSummary1 from './segmentedSummary1.vue';
import QAReview2 from './QAReview2.vue';
import SpokespersonSummary3 from './SpokespersonSummary3.vue';
import MeetingSummary4 from "./MeetingSummary4.vue"
import MeetingSummary8 from "./MeetingSummary8.vue"
import CustomerSummary5 from "./CustomerSummary5.vue"
import DemandResearchReport6 from "./DemandResearchReport6.vue"
import MindMap7 from './MindMap7.vue';

const choosed = ref('');
const props = defineProps(['item', 'choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])
const comps = [MeetingSummary4, MeetingSummary8, segmentedSummary1, QAReview2, SpokespersonSummary3, CustomerSummary5, Demand<PERSON><PERSON>archReport6, MindMap7];

watch(choosed, (val) => {
    emit('update:choosed', val)
})

watch(() => props.choosed, (val) => {
    choosed.value = val;
})

const getComp = () => {
    const { item } = props;
    const id = item.systemPreset ? item.systemId : parseInt(item.id);
    return comps[id - 1]
}

defineExpose({
    choosed, getComp
})
</script>

<style lang="scss">
@import url("./style.scss");
</style>