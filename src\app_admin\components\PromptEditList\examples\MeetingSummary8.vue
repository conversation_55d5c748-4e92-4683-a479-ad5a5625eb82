<template>
  <div :class="`group_10 flex-col ${props.choosed == 8 ? 'active' : ''}`" @click="onClick(8)">
    <div class="header-row flex-row justify-between align-center">
      <span class="text_33">商机识别</span>
      <el-select v-model="defaultSalesMethodology" class="framework-select" @change="changeFramework">
        <el-option v-for="item in analysisTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <dashline />
    <div class="flex-col marginl7">
      <page :id="8" :img="img_url" />
      <div class="image-wrapper_1 flex-row" v-if="props.choosed">
        <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
      </div>
    </div>
  </div>
</template>

<script setup>
import dashline from "./dashline.vue";
import page from "../pages/page.vue";

const analysisTypes = ref(g.cacheStore.salesMethodology);
const defaultSalesMethodology = ref('BANT');
const props = defineProps(["choosed", "enableChoose"]);
const emit = defineEmits(["update:choosed"]);
import { r_png } from "./image.js";
const img_url = ref('salesmethod_bant');

const onClick = (value) => {
  if (props.enableChoose) {
    emit("update:choosed", value);
  }
};

const changeFramework = (value) => {
  g.emitter.emit('update_defaultSalesMethodology', value);
  img_url.value = 'salesmethod_' + value.toLowerCase()
};

onMounted(() => {
  g.emitter.emit('update_defaultSalesMethodology', defaultSalesMethodology.value);;
})

defineExpose({ onClick, page, dashline });


</script>

<style lang="scss" scoped>
.header-row {
  padding: 0 10px;
}

.framework-select {
  width: 120px;
  margin-top: 10px;
}
</style>
