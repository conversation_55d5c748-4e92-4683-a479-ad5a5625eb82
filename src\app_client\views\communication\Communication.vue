<template>
    <div class="communication-page">
        <div class="page-header">
            <h1>沟通</h1>
            <p class="page-description">管理客户沟通记录和沟通策略</p>
        </div>

        <div class="page-content">
            <div class="content-placeholder">
                <div class="placeholder-icon">
                    <el-icon size="48">
                        <ChatDotRound />
                    </el-icon>
                </div>
                <h3>沟通功能开发中</h3>
                <p>该功能正在开发中，敬请期待...</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ChatDotRound } from '@element-plus/icons-vue'

defineOptions({
    name: 'Communication'
})
</script>

<style lang="scss" scoped>
.communication-page {
    padding: 24px;
    height: 100%;

    .page-header {
        margin-bottom: 32px;

        h1 {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 8px 0;
        }

        .page-description {
            color: #909399;
            font-size: 14px;
            margin: 0;
        }
    }

    .page-content {
        height: calc(100% - 80px);
        display: flex;
        align-items: center;
        justify-content: center;

        .content-placeholder {
            text-align: center;
            color: #909399;

            .placeholder-icon {
                margin-bottom: 16px;
                color: #c0c4cc;
            }

            h3 {
                font-size: 18px;
                font-weight: 500;
                margin: 0 0 8px 0;
                color: #606266;
            }

            p {
                font-size: 14px;
                margin: 0;
            }
        }
    }
}
</style>