<template>
  <div class="pie-chart-section">
    <div class="title_line flex-row">
      <div class="title">
        {{ title }}
      </div>
      <div v-if="subTitle" class="sub-title">
        {{ subTitle }}
      </div>
    </div>
    <div class="chart-container" ref="chartRef" v-if="chartData2.length > 0"></div>
    <el-empty v-else description="暂无数据" :image="getOssUrl('no-data.png', 3)" />
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import echarts from '@/js/echarts'
import { jsOpenNewWindow } from '@/js/utils.js'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subTitle: {
    type: String,
    default: ''
  },
  chartData: {
    type: Array,
    required: true,
    // 期望的数据格式：[{value: number, name: string, color: string}]
  }
})

// const chartDataSample = [
//   { value: 30, name: '能力评估达标数' },
//   { value: 15, name: '能力评估未达标数' }
// ]
const chartRef = ref(null)
let chart = null

const getChartTitleColor = (title) => {
  if (title == '结果未评估') {
    return '#D9D9D9'
  } else if (title == '无法判定') {
    return '#FFC125'
  } else if (title.includes('未达成')) {
    return '#FF6B3B'
  } else {
    return '#04CCA4'
  }
}

const chartData2 = computed(() => {
  return props.chartData.filter(item => item.value > 0)
})


const initChart = () => {
  if (!chartRef.value) return

  // 如果已存在图表实例，先销毁它
  if (chart) {
    chart.dispose()
    chart = null
  }

  // 如果数据为空，不初始化图表
  if (chartData2.value.length === 0) return

  chart = echarts.init(chartRef.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['55%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: (params) => {
            return `${params.name}\n${params.value}次 ${Math.round(params.percent)}%`
          },
          fontSize: 12,
          color: '#595959',
          padding: [10, 0, 10, 0],
          lineHeight: 12
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 15,
          smooth: true
        },
        data: props.chartData.filter(item => item.value > 0).map(item => ({
          value: item.value,
          name: item.name,
          type: item.type,
          itemStyle: {
            color: getChartTitleColor(item.name)
          }
        }))
      },
      {
        type: 'pie',
        radius: ['0%', '40%'],
        center: ['50%', '50%'],
        label: {
          show: true,
          position: 'center',
          formatter: () => {
            const totalCount = props.chartData.reduce((sum, item) => sum + item.value, 0);
            return `总次数\n${totalCount}`;
          },
          fontSize: 14,
          color: '#595959',
          lineHeight: 20
        },
        data: [{
          value: 1,
          name: '中心',
          itemStyle: {
            color: 'transparent'
          }
        }]
      }
    ]
  }

  chart.setOption(option)

  // 添加点击事件监听
  chart.on('click', (params) => {
    if (params.seriesIndex === 0) { // 只处理外圈饼图的点击
      const { type } = params.data;
      handleViewDetail(type)
    }
  })
}

const handleViewDetail = (type) => {
  const pageUrl = '/client/team/visit?tab=visitRecord';
  const splitor = pageUrl.includes('?') ? '&' : '?';
  const deptId = g.clientBoardStore.overviewDept.value;
  const user = g.clientBoardStore.regionData.data;

  let ssoUserIdStr = '';
  if (user.ssoUserId) {
    ssoUserIdStr = `&ssoUser=${user.ssoUserId},${user.userName}`;
  }
  const dimension = g.clientBoardStore.dimension;
  const periodType = g.clientBoardStore.periodType.replace('ly', '');
  let url = `${g.config.publicPath}/#${pageUrl}${splitor}periodType=${periodType}&deptId=${deptId}${ssoUserIdStr}`;

  if (dimension == 'visitTargetAchievementRate') {
    //目标达成
    const typeCodes = {
      achievement: '达成',
      unAchievement: '未达成',
      unDetermine: '无法判定',
      unResult: '结果未评估'
    }
    url += '&targetStatus=' + typeCodes[type]
  } else if (dimension == 'salesAbilityPassRate') {
    //能力评估
    const typeCodes = {
      achievement: 1,
      unAchievement: 0,
      unDetermine: 2
    }
    url += '&abilityStatus=' + typeCodes[type]
  } else if (dimension == 'taskCompletePassRate') {
    //任务达成
    const typeCodes = {
      achievement: 1,
      unAchievement: 0,
      unDetermine: 2
    }
    url += '&taskStatus=' + typeCodes[type]
  }

  jsOpenNewWindow(url)
}

onMounted(() => {
  initChart()

  window.addEventListener('resize', () => {
    chart?.resize()
  })
})

// 监听数据变化重新渲染图表
watch(() => props.chartData, (newVal) => {
  if (newVal.length === 0) {
    // 如果数据为空，销毁图表实例
    if (chart) {
      chart.dispose()
      chart = null
    }
  } else {
    // 有数据时重新初始化图表
    nextTick(() => {
      initChart()
    })
  }
}, { deep: true, immediate: true })

onUnmounted(() => {
  window.removeEventListener('resize', () => {
    chart?.resize()
  })
  if (chart) {
    chart.dispose()
    chart = null
  }
})

</script>

<style lang="scss" scoped>
.pie-chart-section {
  .title_line {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #1D2129;
    }

    .sub-title {
      font-size: 12px;
      margin-left: 12px;
      color: #8C8C8C;
    }
  }

  .chart-container {
    height: 220px;
    width: 100%;
    margin-top: 12px;
  }
}
</style>