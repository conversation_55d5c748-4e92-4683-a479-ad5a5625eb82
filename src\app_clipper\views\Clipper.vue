<template>
  <div class="clipper-wrap flex-col" v-loading="loading">
    <HeaderBar ref="refHeaderBar" class="header" />
    <div class="main flex-row">
      <SubtitlePanel ref="refSubtitlePanel" class="subtitle" />
    </div>
    <MediaEditor ref="refMediaEditor" />
  </div>
</template>

<script setup>
import HeaderBar from "./HeaderBar.vue";
import SubtitlePanel from "./SubtitlePanel.vue";
import MediaEditor from "./MediaEditor.vue";
import { useRoute } from "vue-router";

const route = useRoute();
const refSubtitlePanel = ref(null);
const refMediaEditor = ref(null);
const refHeaderBar = ref(null);
const loading = ref(true);

onMounted(async () => {
  const confId = route.params.confId || "";
  const playIdx = route.params.playIdx || 0;
  try {
    loading.value = false;
    if (confId && playIdx >= 0) {
      await g.clipperStore.fetchClipFileInfo(confId, playIdx);
      refMediaEditor.value.init();
      refSubtitlePanel.value.init();
      refHeaderBar.value.init();
    } else {
      ElMessage.error("参数错误");
    }
  } catch (error) {
    console.log(error);
    loading.value = false;
    ElMessage.error("获取视频信息失败");
  }
});

onUnmounted(() => {
  refMediaEditor.value.destroy();
});
</script>

<style lang="scss" scoped>
.clipper-wrap {
  width: 100%;

  .header {
    height: 55px;
  }

  .main {
    flex: 1;
    display: flex;

    .subtitle {
      width: 100%;
      height: calc(100vh - 378px);
      overflow-y: auto;
      border-right: 1px solid #eee;
    }

    .video {
      width: 100%;
      height: calc(100vh - 353px);
    }
  }
}
</style>
