<template>
  <div class="dims_comm_wrap">
    <div class="dc_btns">
      <el-button type="primary" @click="onAdd">添加维度</el-button>
      <el-button type="default" @click="onSortDim" class="sort_btn">维度排序</el-button>
    </div>
    <div class="dc_main" v-if="systemDimensions.length > 0" v-loading="loading">
      <PromptEditList ref="refPromptList" @callback="cbPrompt" title="name" prompt="content" :dtype="props.dtype">
        <template #default="scope">
          <page102 v-if="scope.row.systemId == 102" />
          <!-- 4 会议纪要 8 商机识别 -->
          <page4 v-else-if="[4, 8].includes(+scope.row.systemId)" :img="scope.row.defaultSalesMethodology"
            :systemId="scope.row.systemId" />
          <!-- 206任务 205能力   -->
          <SalesCapability ref="refSalesCapability205" v-else-if="scope.row.systemId == 205" @callback="cbFunc"
            :data="scope.row" />
          <SalesCapability ref="refSalesCapability206" v-else-if="scope.row.systemId == 206" @callback="cbFunc"
            :data="scope.row" />
          <pageFormula v-else-if="[201, 202, 203, 204].includes(scope.row.systemId)"
            :note="notes[scope.row.systemId]" />
          <Pages :id="scope.row.systemId" :key="scope.row.id" v-else />
        </template>
      </PromptEditList>
    </div>
    <el-empty description="暂未添加维度信息" v-else />
  </div>
  <dialogDimension ref="refDialog" @callback="cbFunc" />
  <DialogEdit8 ref="refDialogEdit8" @callback="cbFunc" />
  <DrawerEditMyDim v-if="drawerEditMyDimShow" ref="refDrawerEditMyDim" @callback="cbFunc"
    @onCancel="drawerEditMyDimShow = false" />
</template>

<script setup>
import dialogDimension from "./dialogDimension.vue";
import { deleteXmTopicDimension } from "@/app_admin/tools/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";
import PromptEditList from "@/app_admin/components/PromptEditList";
import DrawerEditMyDim from "@/app_admin/components/saleTheme/MyDim/DrawerEditMyDim.vue";
import Pages from "@/app_admin/components/PromptEditList/pages";
import page102 from "./page102.vue";
import page4 from "./page4.vue";
import pageFormula from "./pageFormula.vue";
import SalesCapability from "./SalesCapability.vue";
import DialogEdit8 from "./dialogEdit8.vue";
const choosed = ref(0);
const emit = defineEmits(["callback"]);
const props = defineProps(["dtype"]);
const refDialog = ref(null);
const systemDimensions = ref([]);
const refPromptList = ref();
const refSalesCapability205 = ref(null);
const refSalesCapability206 = ref(null);
const refSalesTask = ref(null);
const refDialogEdit8 = ref(null);
const refDrawerEditMyDim = ref(null);
const drawerEditMyDimShow = ref(false);
const loading = ref(false);

let topicId = "";

const notes = ref({
  201: "所有销售人员说话总时长 / 拜访所有发言人说话总时长",
  202: "拜访中所有销售人员说话总字数 / 总说话时长（分钟）",
  203: "拜访中所有销售人员说话最长的时间（分钟）",
  204: "销售人员在轮到他们发言前的平均等待时间 = 销售人员在轮到他们发言前的等待时间之和(秒)/ 发言次数",
});

const cbFunc = (action) => {
  if (action == "reload") {
    query();
  }
};

const onSortDim = () => {
  emit("callback", "sort", toRaw(systemDimensions.value));
};

const cbPrompt = (action, item) => {
  if (action == "edit") {
    if (item.systemId == 205) {
      refSalesCapability205.value.showEditor({ topicId, ...item });
    } else if (item.systemId == 206) {
      refSalesCapability206.value.showEditor({ topicId, ...item });
    } else if (item.systemId == 8) {
      refDialogEdit8.value.show_edit(topicId, item);
    } else {
      refDialog.value.show_edit(topicId, item);
    }
  } else if (action == "delete") {
    apiHintWrap(deleteXmTopicDimension(topicId, item.id), "删除维度").then(
      ({ status, resp }) => {
        if (status) {
          query();
        }
      }
    );
  } else if (action == "save") {
    refSalesTask.value.save();
  } else if (action == "cancel") {
    if (item.systemId == 205) {
      refSalesCapability205.value.cancel();
    } else if (item.systemId == 206) {
      refSalesCapability206.value.cancel();
    }
  } else if (action == "edit_standard") {
    emit("callback", "edit_standard", item);
  } else if (action == "select_ability") {
    if (item.systemId == 205) {
      refSalesCapability205.value.showEditor({ topicId, ...item }, 6);
    } else if (item.systemId == 206) {
      refSalesCapability206.value.showEditor({ topicId, ...item }, 10);
    }
  } else if (action == "edit_ability") {
    drawerEditMyDimShow.value = true;
    nextTick(() => {
      refDrawerEditMyDim.value.show({ topicId, ...item });
    });
  }
};

const onAdd = () => {
  const maxCount = 10;
  if (systemDimensions.value.length < maxCount) {
    refDialog.value.show_add(topicId);
  } else {
    ElMessage.warning(`最多允许添加${maxCount}个维度`);
  }
};

const query = () => {
  loading.value = true;
  g.saleStore.getTopDetail().then(() => {
    init();
  }).finally(() => {
    loading.value = false;
  });
};

const init = () => {
  topicId = g.saleStore.topicDetail.id;
  systemDimensions.value = g.saleStore.getCurrDims() || [];
  if (systemDimensions.value.length > 0) {
    nextTick().then(() => {
      const alwaysShowEdit = props.dtype == "SUMMARY";
      const list = toRaw(systemDimensions.value);
      list.forEach((item) => {
        if (item.systemId == 205) {
          item.showEdit = true;
          item.showDelete = false;
        } else if (item.systemId == 206) {
          item.needSave = true;
          item.showDelete = false;
        } else {
          const isUserDim = item.systemId == '0';
          item.showDelete = isUserDim;
        }
        if (props.dtype == "SUMMARY") {
          item.showEdit = true;
          item.showDelete = true;
        } else if (props.dtype == "COUNSELLING ") {
          item.needSave = true;
        }
      });

      refPromptList.value.init(alwaysShowEdit, list);
    });
  }
};

defineExpose({
  init, cbFunc, cbPrompt, onAdd, choosed, refPromptList, Pages, refDialog, PromptEditList, dialogDimension,
  pageFormula, notes, SalesCapability, page4, page102,
});
</script>

<style lang="scss">
@import url("./DimsSummary.scss");
</style>
