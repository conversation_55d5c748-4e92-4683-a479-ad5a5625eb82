<template>
  <TeamLayout @callback="onCallback">
    <el-card class="team_visit_card card_no_border" shadow="never">
      <VisitTeam ref="refVisit" :team="true" />
    </el-card>
  </TeamLayout>
</template>

<script setup>
import TeamLayout from "@/app_client/components/TeamLayout.vue";
import VisitTeam from "@/app_client/components/VisitTeam.vue";

const refVisit = ref();

const onCallback = (action, data) => {
  if (action === "updateDept") {
    refVisit.value.setDeptIds([data.value]);
  }
};

defineExpose({
  VisitTeam,
  refVisit,
});
</script>

<style lang="scss">
.team_visit_card {
  padding: 0;
  min-height: calc(100vh - 100px);
  overflow-y: auto;

  .el-card__body {
    padding: 4px 20px 20px 20px;
  }
}
</style>
