<template>
    <div>
    </div>
</template>

<script setup>

onMounted(() => {
    const user = g.appStore.user;
    g.cacheStore.getUserMenu("client").then((list) => {
        if (!user.manager) {
            list = list.filter((x) => x.code != "my_team");
        }
        const menuList = list;
        if (menuList.length > 0) {
            let firstRouter = ''
            const first = menuList[0];
            if (first.children.length > 0) {
                firstRouter = first.children[0].index;
            } else {
                firstRouter = first.index;
            }
            g.router.push({ path: firstRouter });
        }

    });
})

</script>
