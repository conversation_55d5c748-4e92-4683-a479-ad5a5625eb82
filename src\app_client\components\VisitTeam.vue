<template>
  <div class="vistor_wrap">
    <div class="visit-tabs">
      <el-tabs v-model="activeName" @tab-change="onTabChange">
        <el-tab-pane label="拜访记录" name="visitRecord"> </el-tab-pane>
        <el-tab-pane label="拜访计划" name="visitPlan"> </el-tab-pane>
      </el-tabs>
    </div>
    <div class="visit-content">
      <VisitRecord v-if="activeName === 'visitRecord'" :team="props.team" ref="refVisitRecord" @callback="onCallback" />
      <VisitPlanTeam v-else :team="props.team" ref="refVisitPlan" @callback="onCallback" />
    </div>
    <DrawerArrangeVisit ref="refArrangeVisit" @callback="onCallback" />
    <DrawerVisitDetail ref="refVisitDetail" @callback="onCallback" :readonly="true" />
  </div>
</template>

<script setup>
import VisitRecord from "@/app_client/components/VisitRecord/VisitRecord.vue";
import VisitPlanTeam from "@/app_client/components/VisitPlanTeam/VisitPlanTeam.vue";
import DrawerArrangeVisit from "@/app_client/components/DrawerArrangeVisit.vue";
import DrawerVisitDetail from "@/components/DrawerVisitDetail.vue";
import { jsOpenNewWindow } from "@/js/utils";
import { useRoute } from 'vue-router';

const route = useRoute();
const refVisitPlan = ref();
const refVisitRecord = ref();
const refArrangeVisit = ref();
const activeName = ref(route.query.tab == 'visitPlan' ? 'visitPlan' : 'visitRecord');
const refVisitDetail = ref();
const isElectron = ref(g.config.isElectron);
const deptIds = ref([]);

const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const onTabChange = () => {
  nextTick(() => {
    if (activeName.value == "visitRecord") {
      refVisitRecord.value.setDeptIds(deptIds.value);
    } else {
      refVisitPlan.value.setDeptIds(deptIds.value);
    }
  });
};

const onCallback = (action, data) => {
  if (action == "reload") {
    if (activeName.value == "visitPlan") {
      refVisitPlan.value.reload(true);
    } else {
      refVisitRecord.value.onSearch();
    }
  } else if (action == "edit_plan") {
    refArrangeVisit.value.showEdit(data);
  } else if (action == "visit_detail") {
    refVisitDetail.value.init(data);
  } else if (action == "prepare") {
    jsOpenNewWindow(`/#/prepare/${data.scheduleId}`, "_blank");
  }
};

const setDeptIds = (ids) => {
  deptIds.value = ids;
  onTabChange();
};

defineExpose({
  VisitRecord,
  VisitPlanTeam,
  refArrangeVisit,
  refVisitDetail,
  DrawerVisitDetail,
  isElectron,
  refVisitPlan,
  refVisitRecord,
  DrawerArrangeVisit,
  setDeptIds,
});
</script>

<style lang="scss">
.vistor_wrap {
  background: #e9e9e9;
  height: 100%;
  margin: 4px;

  .visit-tabs {
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;

    .el-tabs__header {
      padding: 0;
    }

    .visit-btn {
      margin-top: 10px;
    }
  }

  .visit-content {
    padding: 0;
    background-color: #fff;

    .v_header {
      margin-top: 24px;
    }
  }
}
</style>
