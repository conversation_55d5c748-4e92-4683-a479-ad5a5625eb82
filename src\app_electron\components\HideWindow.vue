<template>
    <div>
    </div>
</template>

<script setup>
import { getScheduleList } from "@/js/api.js";
import { checkUpcomingPlans } from "@/js/utils.js";
import { reloadSchedule } from '@/app_electron/tools/utils';
import { now, nDate } from "@/js/utils.js";

const updatePlans = () => {
    const param = {
        startTime: now("yyyy-MM-dd"),
        endTime: nDate(2),
        showAssistMeeting: true,
        completed: null,
    };
    getScheduleList(param).then(res => {
        if (g.electronStore.settings.general.scheduleNotification) {
            checkUpcomingPlans(res.data.datas)
        }
    })
}

onMounted(() => {
    g.emitter.on('show_visit_notification', (plan) => {
        // g.electronStore.closeWin('visit_notification')
        // g.electronStore.openWin('visit_notification', plan)
    })

    g.emitter.on('XMATE_CONF_SUMMARY_MSG', (data) => {
        g.electronStore.openWin('meeting_summary_notification', data)
    })

    g.emitter.on('xmate_schedule_update', () => {
        updatePlans()
        reloadSchedule(1)
    });

    g.emitter.on('ws_connected', status => {
        g.electronStore.sendMessage('home', 'ws_connected', status)
    })

    g.appStore.createWsAgent()
    g.ipcRenderer.on('forward_message', (_, { action, data }) => {
        console.log('forward_message', action, data)
        if (action === 'reconnect_ws') {
            g.appStore.reconnectWsAgent()
        } else if (action == "card_record_end") {
            g.electronStore.closeWin('dummy_cardmeet')
            reloadSchedule(1)
        }
    })
})

onUnmounted(() => {
    g.ipcRenderer.removeAllListeners('forward_message');
    g.emitter.off('show_visit_notification');
    g.emitter.off('xmate_schedule_update');
})
</script>
