<template>
    <el-tag v-for="tag in selectDocs" type="info" :key="tag.id" closable :disable-transitions="false"
        @close="handleClose(tag, 'doc')" class="tag_file_cate">
        <PptIcon />
        <div class="tname" @click="onView(tag)">
            {{ tag.name }}
        </div>
    </el-tag>
    <el-tag v-for="tag in selectCates" type="info" :key="tag.id" closable :disable-transitions="false"
        @close="handleClose(tag, 'cate')" class="tag_file_cate">
        <FolderIcon />
        <div class="tname">
            {{ tag.name }}
        </div>
    </el-tag>
</template>

<script setup>
import PptIcon from "@/app_admin/icons/ppt.vue"
import FolderIcon from "@/app_admin/icons/folder.vue"
import { getPptDocInfo } from "@/app_admin/tools/api.js";
import config from '@/js/config';

const selectDocs = ref([]);
const selectCates = ref([]);
const emit = defineEmits(['callback']);

const updateDoc = (docs) => {
    selectDocs.value = docs.sort((a, b) => {
        a.id > b.id ? 1 : -1;
    });
}

const updateCate = (cates) => {
    selectCates.value = cates;
}

const handleClose = (tag, type) => {
    emit('callback', `close_${type}`, tag)
}

const handleClear = () => {
    selectCates.value = [];
    selectDocs.value = [];
}

const onView = (tag) => {
    getPptDocInfo(tag.id).then(resp => {
        if (resp.code == 0) {
            const row = resp.data;
            const url = config.publicPath + row.downloadPath;
            const ext = row.fileName.split('.').pop()
            g.emitter.emit('app_preview_file', { url, ext });
        }

    })
}

defineExpose({
    selectCates, selectDocs, FolderIcon, PptIcon, updateDoc, updateCate, handleClose, handleClear
})

</script>

<style lang="scss">
.tag_file_cate {
    margin: 7px 7px 7px 0;

    .el-tag__content {
        display: flex;
        flex-direction: row;
        margin: 7px 7px 7px 0;

        .tname {
            margin-top: 4px;
            margin-left: 4px;
            cursor: pointer;
        }
    }
}
</style>