<template>
    <div class="audio_icon">
        <div class="mic-wrapper">
            <MicIcon />
            <i class="volume-wave" :style="{ top: getVolumePosition + 'px' }" v-if="isChecking"></i>
        </div>
    </div>
</template>

<script setup>
import MicIcon from '@/app_electron/icons/mic.vue';
import SoundMeter from '@/app_electron/tools/SoundMeter';

const props = defineProps({
    deviceId: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['volume-change', 'no-volume'])

const isChecking = ref(false)
const currentLevel = ref(0)
let audioContext = null
let audioStream = null
let checkTimer = null
let soundMeter = null
let noVolumeTimer = null
let hasDetectedSound = false

const getVolumePosition = computed(() => {
    const cur = 40 - currentLevel.value * 30
    if (cur > 30) return 30
    if (cur < 10) return 10
    return cur
})

const stopChecking = async () => {
    if (noVolumeTimer) {
        clearTimeout(noVolumeTimer)
        noVolumeTimer = null
    }

    if (checkTimer) {
        clearInterval(checkTimer)
        checkTimer = null
    }

    if (audioStream) {
        const tracks = audioStream.getTracks()
        tracks.forEach(track => {
            track.enabled = false
            track.stop()
        })
        audioStream = null
    }

    if (soundMeter) {
        await soundMeter.stop()
        soundMeter = null
    }

    if (audioContext) {
        if (audioContext.state !== 'closed') {
            await audioContext.close()
        }
        audioContext = null
    }

    isChecking.value = false
    currentLevel.value = 0
}

const startAnalyseAudio = async () => {
    try {
        audioStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                deviceId: props.deviceId ? { exact: props.deviceId } : undefined,
            },
            video: false
        })
        audioContext = new (window.AudioContext || window.webkitAudioContext)()
        soundMeter = new SoundMeter(audioContext)

        soundMeter.connectToSource(audioStream, (error) => {
            if (error) {
                console.error('Error connecting to audio source:', error)
                stopChecking()
                return
            }
            console.log('startAnalyseAudio')
            hasDetectedSound = false
            if (noVolumeTimer) {
                clearTimeout(noVolumeTimer)
            }

            noVolumeTimer = setTimeout(() => {
                if (!hasDetectedSound) {
                    clearTimeout(noVolumeTimer)
                    emit('no-volume', true)
                }
            }, 3000)

            checkTimer = setInterval(() => {
                if (!soundMeter) {
                    stopChecking()
                    return
                }
                const instant = soundMeter.instant
                currentLevel.value = Math.min(1, instant * 1000)
                if (currentLevel.value > 0.01) {
                    emit('volume-change', currentLevel.value)
                    if (!hasDetectedSound) {
                        hasDetectedSound = true
                        noVolumeTimer && clearTimeout(noVolumeTimer)
                        emit('no-volume', false)
                    }
                }
            }, 100)
        })

        isChecking.value = true
    } catch (error) {
        console.error('Failed to start audio checking:', error)
        currentLevel.value = 0
        if (checkTimer) {
            clearInterval(checkTimer)
        }
        isChecking.value = false
    }
}

watch(() => props.deviceId, () => {
    stopChecking()
    startAnalyseAudio()
})

onMounted(() => {
    startAnalyseAudio()
})

onUnmounted(async () => {
    await stopChecking()
})

defineExpose({
    startAnalyseAudio,
    stopChecking,
    isChecking
})
</script>

<style lang="scss" scoped>
.audio_icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    display: flex;
    align-items: center;

    .mic-wrapper {
        position: relative;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        background: #f5f7fa;
        border-radius: 4px;

        :deep(svg) {
            width: 24px;
            height: 24px;
            z-index: 1;
            color: #409EFF;
        }

        .volume-wave {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(64, 158, 255, 0.2);
            top: 30px;
            border-radius: 1000px;
            transition: top 0.2s ease-out;
        }
    }
}
</style>