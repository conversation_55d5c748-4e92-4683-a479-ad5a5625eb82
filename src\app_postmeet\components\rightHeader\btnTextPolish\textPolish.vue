<template>
  <el-popover
    placement="bottom"
    width="265"
    trigger="click"
    popper-class="pop_textpolish_wrap"
    @show="onShow"
  >
    <template #reference>
      <div class="hicon">
        <el-tooltip class="item" effect="dark" content="AI字幕改写" placement="top">
          <textPolishIcon />
        </el-tooltip>
      </div>
    </template>
    <div class="sib flex-row" @click="onChange">
      <div>AI字幕改写</div>
      <el-switch v-model="isEnable" active-color="#436bff"> </el-switch>
    </div>
    <div class="tp_note">在保留原文的要点内容基础上，将原文内容进行精简和改写</div>
    <div class="display-options" v-if="isEnable">
      <div class="display-title">显示内容</div>
      <div class="radio-group">
        <el-radio v-model="displayType" label="textpolish">改写结果</el-radio>
        <el-radio v-model="displayType" label="both">原文和改写结果</el-radio>
      </div>
    </div>
  </el-popover>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import textPolishIcon from "@/app_postmeet/icons/right_header/textPolish.vue";

const isEnable = ref(localStorage.getItem("postmeet_enableTextPolish") == "true");
const displayType = ref(
  localStorage.getItem("postmeet_textPolishDisplay") || "textpolish"
);

const emitStatus = () => {
  const new_status = isEnable.value ? displayType.value : "hidden";
  g.emitter.emit("update_textPolish_status", new_status);
};

const onShow = () => {
  isEnable.value = localStorage.getItem("postmeet_enableTextPolish") == "true";
  displayType.value = localStorage.getItem("postmeet_textPolishDisplay") || "textpolish";
};

const onChange = () => {
  localStorage.setItem("postmeet_enableTextPolish", isEnable.value ? "true" : "false");
  emitStatus();
};

onMounted(() => {
  isEnable.value = localStorage.getItem("postmeet_enableTextPolish") == "true";
  displayType.value = localStorage.getItem("postmeet_textPolishDisplay") || "textpolish";
  setTimeout(() => {
    emitStatus();
  }, 500);
});

watch(displayType, (newVal) => {
  localStorage.setItem("postmeet_textPolishDisplay", newVal);
  emitStatus();
});
</script>

<style lang="scss">
.pop_textpolish_wrap {
  padding: 16px !important;

  .sib {
    justify-content: space-between;
  }

  .display-options {
    margin-top: 16px;

    .display-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }

    .radio-group {
      display: flex;
      flex-direction: row;
    }
  }

  .tp_note {
    width: 250px;
    height: 40px;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 20px;
    margin-top: 10px;
  }
}
</style>
