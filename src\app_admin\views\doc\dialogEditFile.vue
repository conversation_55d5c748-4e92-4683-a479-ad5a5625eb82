<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close>
    <el-form
      ref="refForm"
      :model="formData"
      label-width="auto"
      label-position="top"
      size="default"
      :rules="rules"
    >
      <el-form-item label="文件名称" prop="fileName" required>
        <el-input v-model="formData.fileName" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item label="内容总结" prop="summary" required>
        <el-input
          type="textarea"
          v-model="formData.summary"
          maxlength="2000"
          :rows="8"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="文件标签" prop="tags">
        <el-input
          v-model="new_tag"
          maxlength="20"
          show-word-limit
          placeholder="输入并回车后生成标签"
          @keyup.enter.native="onNewTag"
          :disabled="formData.tags.length >= 10"
        />
        <el-tag
          v-for="tag in formData.tags"
          :key="tag"
          closable
          type="info"
          @close="onRemoveTag(tag)"
        >
          {{ tag }}
        </el-tag>
      </el-form-item>
    </el-form>
  </Modal>
</template>
<!-- "tags": [<string], //标签数组
"filename": <string>, //文件名
"summary": <string>, //文件内容总结 -->

<script setup>
import { reactive, ref, toRaw } from "vue";
import { updatePptSearch } from "@/app_admin/tools/api.js";
import { apiHintWrap, try2Array } from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
const refModal = ref();
const title = ref("");
const refForm = ref();
const new_tag = ref("");
const emit = defineEmits(["callback"]);

const defaultForm = {
  fileName: "",
  summary: "",
  tags: [],
};

const formData = ref(defaultForm);

const cfg = {
  width: "50vw",
};

const show_edit = (data) => {
  data.tags = try2Array(data.tags);
  formData.value = { ...defaultForm, ...data };
  cfg["title"] = "编辑文件";
  refModal.value.show(cfg);
};

const onNewTag = () => {
  formData.value.tags.push(new_tag.value);
  new_tag.value = "";
};

const onRemoveTag = (tag) => {
  formData.value.tags = formData.value.tags.filter((x) => x != tag);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;
  refForm.value.validate((valid, fields) => {
    if (valid) {
      const data = toRaw(formData.value);
      const { categoryId, id, fileName, summary, tags } = data;
      const param = { fileName, summary, tags };
      apiHintWrap(updatePptSearch(categoryId, id, param), cfg["title"]).then(
        ({ status }) => {
          if (status) {
            emit("callback", "reload", formData.value);
            formData.value = defaultForm;
            refModal.value.hide();
          }
        }
      );
    }
  });
};

const rules = reactive({
  fileName: [{ required: true, message: "请输入文件名", trigger: "blur" }],
  summary: [{ required: true, message: "请输入文件内容总结", trigger: "blur" }],
});

defineExpose({
  title,
  new_tag,
  show_edit,
  cbModal,
  formData,
  rules,
  onNewTag,
});
</script>

<style lang="scss">
.el-dialog__body {
  padding: 15px 24px 5px 24px;
}
</style>
