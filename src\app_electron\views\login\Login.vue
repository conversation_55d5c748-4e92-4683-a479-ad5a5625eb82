<template>
    <div v-if="isShow">
        <LoginH5></LoginH5>
    </div>
</template>
<script setup>
import LoginH5 from '@/views/Login/LoginH5.vue'

const isShow = ref(false)

const init = () => {
    const currUserAgent = navigator.userAgent
    const isMac = /(Macintosh)/i.test(currUserAgent)
    const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/********* Electron/28.0.0 ' + (isMac ? 'Macintosh' : 'Windows')
    g.ipcRenderer.send('update_user_agent', 'login', userAgent);
    isShow.value = true
}

onMounted(async () => {
    const allWindowNames = await g.ipcRenderer.invoke('get-all-window-names');
    if (allWindowNames.includes('main')) {
        g.electronStore.updateWindowSize()
        g.ipcRenderer.send('close_all_windows');
        g.electronStore.openWin('login')
    } else {
        init();
    }
})
</script>
