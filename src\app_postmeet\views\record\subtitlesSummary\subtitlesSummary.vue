<template>
  <div ref="refWrap" :class="`subtitles-wrap ${isShowVideo ? 'subtitles-video' : 'subtitles-audio'}`"
    @scroll="onScroll">
    <div class="subtitles" v-ai-tip="{ type: 'center', text: '以上字幕由AI生成，仅供参考' }">
      <div :class="['speak-time', { 'has-analyse': isShowAnalyse }]">
        <speakTime ref="refSpeakTime" />
      </div>
      <div :class="['title', { 'has-analyse': isShowAnalyse }]">
        <span>字幕</span>
        <span class="word_count"> {{ words_count }}字</span>
      </div>
      <ul ref="refSubtitle" class="subtitles-list" v-if="messages.length > 0">
        <li v-for="msg in messages" :key="msg.bt + msg.si" :id="'i_' + msg.bt">
          <div :class="['subtitles-list-left', currVideoBt === msg.bt ? 'highli' : '']">
            <div class="name_time">
              <div class="flex-row">
                <div class="name_box edit_name" @click="showRename(msg)" v-if="isHost">
                  <roleTag :role-type="getUserRole(msg)" />
                  <div class="t_name" v-html="msg.name"></div>
                  <div class="editicon">
                    <editIcon></editIcon>
                  </div>
                </div>

                <div :class="`name_box ${isHost && !readonly ? 'edit_name' : ''}`" @click="showRename(msg)" v-else>
                  <roleTag :role-type="getUserRole(msg)" />
                  <div class="t_name" v-html="msg.name"></div>
                </div>
                <div class="sb_time">
                  {{ msg.time }}
                </div>
                <div class="tl_label question_label"
                  v-if="hasIntersection(msg.osi_list, question_ids) && !hasIntersection(msg.osi_list, answer_ids)">
                  问
                </div>
                <div class="tl_label answer_label"
                  v-if="hasIntersection(msg.osi_list, answer_ids) && !hasIntersection(msg.osi_list, question_ids)">
                  答
                </div>
                <div class="tl_label qa_label"
                  v-if="hasIntersection(msg.osi_list, answer_ids) && hasIntersection(msg.osi_list, question_ids)">
                  问答
                </div>
              </div>
              <div class="sb_name_right" v-if="isHost">
                <el-icon @click="onAddTodo(msg)" v-if="hasReport">
                  <DocumentChecked />
                </el-icon>
                <el-tooltip effect="dark" content="本次拜访报告未生成，无法添加待办。" v-else>
                  <el-icon>
                    <DocumentChecked />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
            <div class="msg_box" @click="onClickTxt(msg)">
              <div :class="`txt ${hasIntersection(msg.osi_list, question_ids) || hasIntersection(msg.osi_list, answer_ids)
                ? 'qa_bk'
                : ''
                } ${textPolishStatus == 'hidden' ? 'light_txt' : 'normal_txt'}`" v-html="msg.txt"
                @mouseup="(e) => onSbMouseUp(e, msg)"
                v-show="textPolishStatus == 'hidden' || textPolishStatus == 'both'"></div>
              <div class="split_line" v-show="textPolishStatus == 'both'"></div>
              <div class="txt normal_txt" v-show="textPolishStatus == 'textpolish' || textPolishStatus == 'both'"
                v-html="msg.polish"></div>
              <div class="tp_box flex-row" v-show="textPolishStatus == 'textpolish' || textPolishStatus == 'both'">
                <div class="textpolish_icon">
                  <TextpolishIcon v-if="currVideoBt !== msg.bt" />
                  <TextpolishLightIcon v-else />
                </div>
                <div class="tp_txt">AI改写结果</div>
              </div>
            </div>
          </div>
          <div v-if="isShowAnalyse">
            <adRemark ref="refRemark" :item="msg" :key="msg.bt" :filter="commFilter" />
          </div>
        </li>
      </ul>
      <div v-else class="empty_box">
        <el-empty :description="emptyDesc"></el-empty>
      </div>
      <div class="topbox" @click="toTop">
        <topIcon />
      </div>
      <aiBtn />
    </div>
    <dialogRename ref="refDiaRename"></dialogRename>
    <dialogSaleRename ref="refDiaSaleRename"></dialogSaleRename>
  </div>
</template>
<script>
import {
  isHas,
  isEq,
  toReplace,
  findMinimumValue,
  replaceWithChinesePunctuation,
} from "@/app_postmeet/tools/tools";
import { throttle, deepCopy } from "@/js/utils";
import folding from "@/app_postmeet/components/folding.vue";
import dialogRename from "./dialogRename.vue";
import dialogSaleRename from "@/app_postmeet/components/dialogSaleRename.vue";
import DivEdit from "@/app_postmeet/components/div_edit.vue";
import editIcon from "@/icons/edit.vue";
import { updateSubTitle } from "@/app_postmeet/tools/api";
import FoldShowIcon from "@/icons/fold_show.vue";
import speakTime from "@/app_postmeet/components/speakTime/speakTime.vue";
import TextpolishIcon from "@/app_postmeet/icons/ai_textpolish.vue";
import TextpolishLightIcon from "@/app_postmeet/icons/ai_textpolish_light.vue";
import topIcon from "@/app_postmeet/icons/totop.vue";
import roleTag from "./roleTag.vue";
import aiBtn from "./aiBtn/aiBtn.vue";
import adRemark from "@/app_postmeet/views/record/AnalyseDialog/adRemark.vue";
import {
  getCommentTimeText,
  mergeSbCommentsToMessages,
} from "@/app_postmeet/tools/sale_report.js";
import { DocumentChecked } from '@element-plus/icons-vue';
const tagDefaultHeight = { tags_ul_style: "35px", names_ul_style: "38px" };
export default {
  name: "SubtitleSummary",
  components: {
    editIcon,
    DivEdit,
    folding,
    dialogRename,
    dialogSaleRename,
    aiBtn,
    TextpolishIcon,
    FoldShowIcon,
    speakTime,
    DocumentChecked,
    roleTag,
    TextpolishLightIcon,
    topIcon,
    adRemark,
  },
  data() {
    return {
      roomInfo: {},
      item: null,
      currVideoBt: 0,
      messages_: [], //当前录制文件字幕的数据
      startTime: "",
      select: "",
      tagType: "",
      names: [],
      tags: [],
      messages: [],
      messages_all: [], //当前录制文件字幕的数据,过滤说话前的
      videoTags: [],
      tags_ul_style: {},
      names_ul_style: {},
      mouseOverTimer: null,
      words_count: 0,
      showChartName: "",
      iconClass: "yxtf-icon-arrow-down yxtf-icon--right",
      count: 0,
      et_start: 0,
      et_end: 0,
      readonly: true,
      isHost: false,
      index: 1,
      question_ids: [],
      answer_ids: [],
      textPolishStatus:
        localStorage.getItem("postmeet_textPolishDisplay") || "textpolish",
      hasTextPolish: localStorage.getItem("postmeet_enableTextPolish") == "true",
      speakerFilter: [],
      isShowVideo: false,
      isShowAnalyse: false,
      userTable: [],
      userList: [],
      timer: null,
      emptyDesc: "",
      messages_original: [], // 添加用于保存原始消息数据
      commFilter: { type: "" },
      hasReport: false
    };
  },
  mounted() {
    this.ListenerVideoTimeUpdate();
    this.ListenerSetPlayItem();
    this.ListenerClicktag();
    this.ListenerEditmode();
    this.ListenerQASentence();
    this.ListenerShowSaleRename();
    // 监听过滤类型变化
    g.emitter.on("filterAnalyseType", (filter) => {
      this.commFilter = filter;
    });
  },
  watch: {
    index(newVal) {
      if (!this.select) {
        const all = document.querySelectorAll(
          ".subtitles-list div span:not(.search-highlight)"
        );
        Array.from(all).forEach((item, index) => {
          if (index === newVal - 1) {
            item.style.background = "#436bff";
            item.style.color = "#fff";
            this.$nextTick(() => {
              this.myscroll(item);
            });
          } else {
            item.style.background = "#FFDEA4";
            item.style.color = "#262626";
          }
        });
      }
    },
  },

  methods: {
    // 添加辅助方法检查数组交集
    hasIntersection(arr1, arr2) {
      return arr1.some(item => arr2.includes(item));
    },
    setIsShowAnalyse(value) {
      this.isShowAnalyse = value;
    },
    setEmptyDesc() {
      // DO_NOTHING(0), IN_PROCESS(1), SUCCESS(2), FAIL(3), NO_CONTENT(4)
      const recognitionStatus = g.postmeetStore.data.recognitionStatus;
      if (recognitionStatus == "IN_PROCESS" || recognitionStatus == "DO_NOTHING") {
        this.emptyDesc = "录制文件解析处理中，请耐心等待";
      } else if (recognitionStatus == "SUCCESS" || recognitionStatus == "NO_CONTENT") {
        this.emptyDesc = "暂无字幕";
      } else if (recognitionStatus == "FAIL") {
        this.emptyDesc = "转写失败，请检查录音文件";
      } else {
        this.emptyDesc = "暂无字幕";
      }
    },
    myscroll(element) {
      if (element && this.sbWrapDom) {
        const containerHeight = this.sbWrapDom.clientHeight;
        const elementHeight = element.clientHeight;
        const scrollTop = element.offsetTop - (containerHeight - elementHeight) / 2;

        this.sbWrapDom.scroll({
          top: scrollTop,
          behavior: "smooth",
        });
      }
    },
    init() {
      this.isHost = !g.postmeetStore.isReadonly();
      this.setUI();
      this.sbWrapDom = document.querySelector(".subtitles-wrap");
    },
    onScroll: throttle(function () {
      const that = this;
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        that._getTimeHeights();
      }, 70);
      this._getTimeHeights();
    }, 50),
    getUserRole(msg) {
      const { ui } = msg;
      if (this.userList.includes(ui)) {
        const role = this.userTable[ui];
        return role.type;
      } else {
        return "na";
      }
    },
    onSbMouseUp(e, msg) {
      const enable = false;
      if (enable && !this.readonly && g.postmeetStore.data.hasSalesMateSummary) {
        const text = window.getSelection().toString();
        if (text) {
          g.emitter.emit("showSbPop", [e, text, msg]);
        }
      }
    },
    ListenerQASentence() {
      g.emitter.on("HighLightSentenceIdsOfAnswer", ([qids, aids]) => {
        console.log('qids', qids, aids);
        this.question_ids = qids;
        this.answer_ids = aids;
        if (qids.length > 0) {
          let min_id = findMinimumValue([...qids, ...aids]);
          const msg = this.messages.find((x) => x.osi_list.includes(min_id));
          this.setCurrBt(msg);
          g.emitter.emit("setVideoTag", msg);
          this.$nextTick(() => {
            const cur = document.querySelector(".tl_label");
            this.myscroll(cur);
          });
        }
      });
    },
    ListenerEditmode() {
      g.emitter.on("update_textPolish_status", (status) => {
        this.textPolishStatus = status;
        this.updateMessage();
      });
      g.emitter.on("update_sb_text", ([item, txt]) => {
        this.onCbText(item, txt);
      });
      g.emitter.on("update_editmode", (isedit) => {
        if (isedit) {
          this._onClear();
          this.onSetTag("", "keyword");
          this.onSetTag("", "username");
          this.$nextTick(() => {
            this.readonly = false;
          });
        } else {
          this.readonly = true;
        }
      });
      g.emitter.on("click_sb_tag", (tag, tagType) => {
        this.onClickTag(tag, tagType);
      });
    },
    onAddTodo(msg) {
      let oraMsg = this.messages_all.find((x) => x.time == msg.time);
      if (!oraMsg) {
        oraMsg.txt = msg.polish;
      }
      g.emitter.emit("show_add_todo_dialog", oraMsg);
    },
    setIsShowVideo(value) {
      this.isShowVideo = value;
    },
    _onClear() {
      this.select = "";
      this.index = 0;
      this.count = 0;
      this._setMessage(deepCopy(this.messages_original));
      this.videoTags = [];
      localStorage.removeItem("clickTag");
      g.emitter.emit("clearTag", "");
      this.sendVideoTags();
    },
    _onInput(value) {
      this.onSetTag(value, "keyword");
    },
    _onDown(data) {
      this.index = data;
      if (this.count) {
        this._scrollToHighlight(this.index);
      }
    },
    _onUp(data) {
      this.index = data;
      if (this.count) {
        this._scrollToHighlight(this.index);
      }
    },
    _scrollToHighlight(index) {
      // 获取所有可见的高亮元素
      const highlights = Array.from(
        document.querySelectorAll(".subtitles-list .search-highlight")
      )
      // .filter((el) => {
      //   const txtDiv = el.closest(".txt");
      //   return window.getComputedStyle(txtDiv).display !== "none";
      // });

      // 先重置所有高亮元素的样式
      highlights.forEach((el) => {
        el.style.background = "#FFDEA4";
        el.style.color = "#262626";
      });

      // 直接使用 index-1 作为数组索引获取目标元素
      if (index > 0 && index <= highlights.length) {
        const targetElement = highlights[index - 1];
        if (targetElement) {
          targetElement.style.background = "#436bff";
          targetElement.style.color = "#fff";

          const subtitleItem = targetElement.closest("li");
          if (subtitleItem) {
            this.myscroll(subtitleItem);
          }
        }
      }
    },
    ListenerClicktag() {
      g.emitter.on("clickTag", ([tag, type]) => {
        this.onSetTag(tag, type);
        g.emitter.emit("onSearchKeyword", tag);
      });
      g.emitter.on("updatedAsrContent", () => {
        this.updateMessage();
      });
      const funMap = {
        clear: this._onClear,
        keyword: this._onInput,
        down: this._onDown,
        up: this._onUp,
      };
      g.emitter.on("right_header_search", ([action, data]) => {
        // console.log("right_header_search", action, data);
        funMap[action](data);
      });
    },
    ListenerShowSaleRename() {
      g.emitter.on("showSaleRename", (data) => {
        this.showRename(data);
      });
      g.emitter.on("playing_video", () => {
        this.$nextTick(() => {
          this._getTimeHeights();
        });
      });
    },
    showRename(item) {
      if (!this.isHost) return;
      const recordInfo = g.postmeetStore.getCurrRecord();
      if (recordInfo.customerName) {
        this.$refs.refDiaSaleRename.show(item);
      } else {
        g.emitter.emit("show_edit_customer_name", "");
      }
    },
    sendVideoTags() {
      g.emitter.emit("videoTags", [this.select, this.videoTags, true]);
    },
    onCbText(item, e) {
      const data = {
        id: item.bt,
        value: e,
      };
      updateSubTitle(data).then((resp) => {
        if (resp.code == 0) {
          const subtitles = g.postmeetStore.data.subtitles;
          for (const msg of subtitles) {
            if (item.bt == msg.bt) {
              msg["txt"] = e;
            }
          }
          g.postmeetStore.setValue("subtitles", subtitles);
          for (const msg of this.messages_) {
            if (item.bt == msg.bt) {
              msg["txt"] = e;
            }
          }
          for (const msg of this.messages) {
            if (item.bt == msg.bt) {
              msg["txt"] = e;
            }
          }
          this.words_count = this.messages_.map((x) => x.txt).join("").length;
        } else {
          console.log("update sb error", item, e);
        }
      });
    },
    toTop() {
      const cur = document.querySelector(".subtitles-wrap");
      cur?.scrollTo({ left: 0, top: 0 });
    },
    onClickTag(tag, tagType) {
      g.emitter.emit("clickTag", [tag, tagType]);
      localStorage.setItem("clickTag", JSON.stringify({ tag, tagType }));
    },
    onSetTag(tag, tagType) {
      // console.log("onSetTag", tag, tagType);
      if (!this.readonly) {
        g.emitter.emit("editmode_false", "");
      }
      this.tagType = tagType;
      this.select = tag;
      this.index = 0;
      this.count = 0;

      // 重置消息内容为原始数据
      this._setMessage(deepCopy(this.messages_original));
      let allTxt = ''

      if (tag) {
        const str = `<span class="search-highlight" style="color: #262626;background: #FFDEA4">${tag}</span>`;

        const filterFunc = (x) => {
          let shouldSearchOriginal =
            this.textPolishStatus === "hidden" || this.textPolishStatus === "both";
          let shouldSearchPolish =
            this.textPolishStatus === "textpolish" || this.textPolishStatus === "both";

          let matchOriginal =
            shouldSearchOriginal && (isHas(x.txt, tag) || isEq(x.name, tag));
          let matchPolish = shouldSearchPolish && (isHas(x.polish, tag) || isEq(x.name, tag));
          if (matchOriginal) {
            allTxt += x.txt + " ";
          }
          if (matchPolish) {
            allTxt += x.polish + " ";
          }
          if (isEq(x.name, tag)) {
            allTxt += x.name + " ";
          }
          return matchOriginal || matchPolish;
        };

        this.videoTags = this.messages_.filter(filterFunc);
        // console.log("videoTags", this.videoTags);
        this.sendVideoTags();
        let shouldSearchOriginal =
          this.textPolishStatus === "hidden" || this.textPolishStatus === "both";
        let shouldSearchPolish =
          this.textPolishStatus === "textpolish" || this.textPolishStatus === "both";
        // console.log("videoTags", this.textPolishStatus, shouldSearchOriginal, shouldSearchPolish);
        // 处理高亮显示
        for (const msg of this.messages) {
          let matchOriginal =
            shouldSearchOriginal && (isHas(msg.txt, tag) || isEq(msg.name, tag));
          let matchPolish = shouldSearchPolish && (isHas(msg.polish, tag) || isEq(msg.name, tag));
          // console.log("videoTags2", this.textPolishStatus, msg.name, msg.txt, matchOriginal, matchPolish);

          if (matchOriginal || matchPolish) {
            msg.name_ora = msg.name;
            if (isEq(msg.name, tag)) {
              msg.name = toReplace(msg.name, tag, str);
            }
            msg.txt2 = msg.txt;
            if (tagType == "keyword") {
              if (shouldSearchOriginal && matchOriginal) {
                msg.txt = toReplace(msg.txt, tag, str);
              }
              if (shouldSearchPolish && matchPolish) {
                msg.polish = toReplace(msg.polish, tag, str);
              }
            }
          }
        }

        const txtMatches = allTxt.match(new RegExp(tag, 'g'));
        this.count = txtMatches && txtMatches.length || 0;

        // 计算关键词在文本中出现的总次数
        if (this.count > 0) {
          this.index = 1;
          this._scrollToHighlight(1);
        }
        g.emitter.emit("set_search_input", { index: this.index, count: this.count });
      } else {
        this.videoTags = [];
        this.sendVideoTags();
      }
    },
    setUI() {
      const { recordInfo } = g.postmeetStore.data;
      this.startTime = recordInfo.startTime;
      this.hasTextPolish =
        g.postmeetStore.data.txtpolishs.length > 0 &&
        !g.postmeetStore.data.txtpolishsHasError;
      this.$nextTick(() => {
        const temp = JSON.parse(localStorage.getItem("clickTag") || "{}");
        if (JSON.stringify(temp) != "{}") {
          const { tag, tagType } = temp;
          this.onSetTag(tag, tagType);
        }
        this.setEmptyDesc();
      });
    },
    updateMessage() {
      const { asrRaw } = g.postmeetStore.data;
      const { asrContent } = asrRaw;
      if (!asrRaw || !asrContent || Object.keys(asrContent).length == 0) {
        console.log("no AsrResultList", asrRaw);
        return;
      }
      this.tags = g.postmeetStore.data.keywords;

      this.hasTextPolish =
        g.postmeetStore.data.txtpolishs.length > 0 &&
        !g.postmeetStore.data.txtpolishsHasError;
      let selectSubtitles;
      if (this.hasTextPolish) {
        selectSubtitles = this.textPolishStatus
          ? g.postmeetStore.data.txtpolishs
          : g.postmeetStore.data.subtitles;
        g.emitter.emit("update_text_trace", false);
      } else {
        selectSubtitles = g.postmeetStore.data.subtitles;
        g.emitter.emit("update_text_trace", true);
      }
      this.messages_all = selectSubtitles.filter(
        (x) => x.bt - (x.pd || 0) >= this.et_start && x.bt - (x.pd || 0) <= this.et_end
      );

      const subtitles_all = g.postmeetStore.data.subtitles.filter(
        (x) => x.bt - (x.pd || 0) >= this.et_start && x.bt - (x.pd || 0) <= this.et_end
      );
      this.names = Array.from(new Set(this.messages_all.map((x) => x.name)));

      g.postmeetStore.setValue("names", this.names);
      g.emitter.emit("setSubTitleAll", subtitles_all);
      this.speakerFilter = this.names;
      this.updateMessage2();
    },
    updateMessage2() {
      const rlist = g.postmeetStore.getRoleList();
      const userTable = {};
      for (let i = 0; i < rlist.length; i++) {
        userTable[rlist[i]["ui"]] = rlist[i];
      }
      this.userList = Object.keys(userTable);
      this.userTable = userTable;

      const messages_ = this.messages_all.filter((x) =>
        this.speakerFilter.includes(x.name)
      );

      // 在这里添加合并sbComments的逻辑
      const mergedMessages = mergeSbCommentsToMessages(messages_);
      // console.log("mergedMessages", mergedMessages);

      // 当前录制文件字幕的数据
      this.messages_ = deepCopy(mergedMessages);
      // 保存原始数据
      this.messages_original = deepCopy(mergedMessages);
      this.words_count = mergedMessages.map((x) => x.txt).join("").length;
      // 用于显示文件字幕的数据,可能会有高亮的内容
      this._setMessage(deepCopy(mergedMessages));
      this.$refs.refSpeakTime.init();
    },
    _setMessage(list) {
      this.messages = list;
      this.$nextTick(() => {
        this._getTimeHeights();
      });
    },
    _getTimeHeights() {
      const subtitlesWrap = document.querySelector(".subtitles-list");
      if (subtitlesWrap) {
        const time_doms = document.querySelectorAll(
          ".subtitles-list .name_time .sb_time"
        );
        const sbTimeHeights = [];
        for (let dom of time_doms) {
          sbTimeHeights.push({
            time: dom.innerText,
            height: dom.getBoundingClientRect().top,
          });
        }
        g.postmeetStore.setValue(
          "subtitlesWrapTop",
          subtitlesWrap.getBoundingClientRect().top
        );
        g.postmeetStore.setValue("sbTimeHeights", sbTimeHeights);
        g.postmeetStore.covertToComments(sbTimeHeights);
        this._setCommentTxtHighLine();
        g.emitter.emit("update_sbTimeHeights", "");
      } else {
        g.postmeetStore.setValue("subtitlesWrapTop", 0);
        g.postmeetStore.setValue("sbTimeHeights", []);
        g.postmeetStore.covertToComments([]);
      }
    },
    _setCommentTxtHighLine() {
      const commentTxts = getCommentTimeText();
      if (commentTxts.length == 0) return;
      const messages = deepCopy(this.messages);
      const times = messages.map((x) => x.time);
      for (let row of commentTxts) {
        const { time, txt } = row;
        const str = `<p style='text-decoration: underline;'>${txt}</p>`;
        const temp = times.filter((x) => x <= time);
        if (temp.length > 0) {
          const oratxt = replaceWithChinesePunctuation(messages[temp.length - 1].txt);
          const txt2 = replaceWithChinesePunctuation(txt);
          messages[temp.length - 1].txt = toReplace(oratxt, txt2, str);
        }
      }
      this.messages = messages;
      this.$forceUpdate();
    },
    setCurrBt(msg) {
      if (msg) {
        this.currVideoBt = msg.bt;
        const cur = document.querySelector("#i_" + msg.bt);
        this.myscroll(cur);
      } else {
        console.log("error setCurrBt ", msg);
      }
    },
    onClickTxt(msg) {
      if (this.readonly) {
        this.setCurrBt(msg);
        console.log('msg222', msg);
        g.emitter.emit("setVideoTag", msg);
      }
    },

    setRoomInfo(data) {
      this.roomInfo = data;
    },
    cbShow(sname, status) {
      if (!status) {
        this[sname] = { overflow: "hidden", height: tagDefaultHeight[sname] };
      } else {
        this[sname] = {};
        const otherRef =
          sname == "names_ul_style"
            ? this.$refs.refFoldingTags
            : this.$refs.refFoldingName;
        otherRef && otherRef.onClick(false);
      }
    },
    ListenerSetPlayItem() {
      g.emitter.on("setPlayItem", (item) => {
        // console.log("setPlayItem", item);
        this.item = item;
        this.et_start = item.startInSecord * 1000;
        this.et_end = this.et_start + item.duration;
        const lessData = g.postmeetStore.data.words_count < g.postmeetStore.data.min_words_count;
        this.hasReport = !lessData && g.postmeetStore.data.saleReportStatus == "SUCCESS";
        this.updateMessage();
        this.sendVideoTags();
        this.toTop();
      });
    },
    ListenerVideoTimeUpdate() {
      g.emitter.on("videoTimeUpdate", (bt) => {
        const msgs = this.messages_.filter((x) => x.bt - (x.pd || 0) <= bt && bt <= x.et + 9000);
        if (msgs.length > 0) {
          const msg = msgs.pop();
          // 最多显示10秒，之后即使没有新字幕也不显示上一条字幕了
          if (bt - msg.et > 10 * 1000) {
            this.setCurrBt({ name: "", txt: "", bt: 0 });
          } else {
            this.setCurrBt(msg);
          }
        } else {
          this.setCurrBt({ name: "", txt: "", bt: 0 });
        }
      });
      g.emitter.on("setSpeakerFilter", (filters) => {
        this.speakerFilter = filters;
        this.updateMessage2();
      });
    },
  },
};
</script>

<style lang="scss">
@use './style.scss';

// 添加搜索高亮样式
.search-highlight {
  display: inline-block;
  padding: 0 2px;
  border-radius: 2px;

  &.current {
    background: #436bff;
    color: #fff !important;
  }
}
</style>
