<template>
    <div class="hicon" @click="onClick">
        <el-tooltip class="item" effect="dark" content="重新识别说话人" placement="top">
            <ReregIcon />
        </el-tooltip>
    </div>
</template>

<script>
import ReregIcon from "@/app_postmeet/icons/right_header/rereg.vue"
export default {
    components: { ReregIcon },
    methods: {
        onClick() {
            g.emitter.emit("open_dia_reperson", '');
        }
    }
}

</script>
