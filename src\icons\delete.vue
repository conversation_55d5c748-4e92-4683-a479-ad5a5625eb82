<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="智能产品推荐师" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="智能产品推荐师-对话中" transform="translate(-242.000000, -610.000000)">
                <g id="编组-6备份-5" transform="translate(12.000000, 574.000000)">
                    <g id="编组-2" transform="translate(230.000000, 36.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path
                            d="M13.5,3.5 C13.7761424,3.5 14,3.72385763 14,4 C14,4.27614237 13.7761424,4.5 13.5,4.5 L13,4.5 L13,12.5 C13,13.3284271 12.3284271,14 11.5,14 L4.5,14 C3.67157288,14 3,13.3284271 3,12.5 L3,4.5 L2.5,4.5 C2.22385763,4.5 2,4.27614237 2,4 C2,3.72385763 2.22385763,3.5 2.5,3.5 L5.5,3.5 L5.5,3 C5.5,2.44771525 5.94771525,2 6.5,2 L9.5,2 C10.0522847,2 10.5,2.44771525 10.5,3 L10.5,3.5 L13.5,3.5 Z M12,4.5 L4,4.5 L4,12.5 C4,12.7454599 4.17687516,12.9496084 4.41012437,12.9919443 L4.5,13 L11.5,13 C11.7761424,13 12,12.7761424 12,12.5 L12,4.5 Z M6.5,6.5 C6.77614237,6.5 7,6.72385763 7,7 L7,11 C7,11.2761424 6.77614237,11.5 6.5,11.5 C6.22385763,11.5 6,11.2761424 6,11 L6,7 C6,6.72385763 6.22385763,6.5 6.5,6.5 Z M9.5,6.50000023 C9.77614237,6.50000023 10,6.72385785 10,7.00000023 L10,11.0000002 C10,11.2761426 9.77614237,11.5000002 9.5,11.5000002 C9.22385763,11.5000002 9,11.2761426 9,11.0000002 L9,7.00000023 C9,6.72385785 9.22385763,6.50000023 9.5,6.50000023 Z M9.5,3 L6.5,3 L6.5,3.5 L9.5,3.5 L9.5,3 Z"
                            id="形状结合" fill="currentColor"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'ClearIcon',
}
</script>
