<template>
  <div class="yxtbiz-joinlib_search yxtbiz-joinlib_flex yxtbiz-joinlib_flexcenter mb8">
    <el-input
      v-model.trim="searchText"
      :readonly="readonly"
      maxlength="50"
      :placeholder="请输入搜索内容"
      clearable
      size="medium"
      class="yxtbiz-joinlib_flex1"
      @keyup.enter.prevent
    />
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    readonly: Boolean
  },
  data() {
    return {
      searchText: ''
    };
  },
  watch: {
    searchText(val) {
      if (this.timeId) {
        window.clearTimeout(this.timeId);
        this.timeId = null;
      }
      this.timeId = window.setTimeout(() => {
        this.$emit('search', val);
      }, 500);
    }
  }
};
</script>
