<template>
    <div class="title-menu-h5">
        <div class="warp-title">
            <div class="title-text">
                <slot name="title"></slot>
            </div>
            <van-dropdown-menu>
                <van-dropdown-item v-model="localModal" :options="options" @change="handleChange" />
            </van-dropdown-menu>
        </div>
    </div>
</template>

<script setup>
import ArrowDown from '@/icons/arrow_down.vue'

const options = [
    { text: '上周', value: 'lastweek' },
    { text: '上月', value: 'lastmonth' },
    { text: '上季度', value: 'lastquarter' },
];
const localModal = ref(g.clientBiStore.periodType);

const handleChange = (val) => {
    g.clientBiStore.setSelectId(val)
}

watch(() => g.clientBiStore.periodType, () => {
    localModal.value = g.clientBiStore.periodType
}, { immediate: true })

</script>
<style lang="scss" scoped>
.title-menu-h5 {

    .warp-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .title-text {
            font-weight: 600;
            font-size: 18px;
            color: #262626;
            line-height: 26px;
            padding-left: 8px;
            position: relative;

            &::before {
                content: '';
                display: inline-block;
                position: absolute;
                width: 5px;
                height: 20px;
                background: #436BFF;
                border-radius: 3px;
                left: -11px;
                top: 2px;
            }
        }

        .van-dropdown-menu {
            width: 130px;
        }

        :deep(.van-dropdown-menu__bar) {
            height: 26px;
            background: transparent;
            box-shadow: unset;

            .van-dropdown-menu__item {
                justify-content: flex-end;
            }
        }

        // 自定义下拉菜单箭头样式
        :deep(.van-dropdown-menu__title) {
            font-size: 14px;

            &::after {
                content: '';
                display: inline-block;
                width: 14px;
                height: 14px;
                background-image: url('https://stc.yxt.com/ufd/c5a3e9/down.png');
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                margin-left: 4px;
                transition: transform 0.3s ease;
                border: 0;
                right: -8px;
                margin-top: -8px;
                transform: rotate(0);
            }

            &.van-dropdown-menu__title--active::after {
                transform: rotate(180deg);
            }
        }

        // 隐藏默认的箭头
        :deep(.van-dropdown-menu__title) {
            .van-dropdown-menu__title__icon {
                display: none;
            }
        }
    }
}
</style>