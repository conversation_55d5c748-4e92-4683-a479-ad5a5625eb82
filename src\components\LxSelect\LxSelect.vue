<template>
    <div class="lx-select" v-click-outside="closeDropdown">
        <div class="lx-select-input" @click="toggleDropdown">
            <template v-if="modelValue && selectedLabel">
                <slot name="label" :label="selectedLabel">
                    {{ selectedLabel }}
                </slot>
            </template>
            <template v-else>
                <span class="placeholder">{{ placeholder }}</span>
            </template>
            <div class="suffix-icon" v-if="clearable && modelValue" @click.stop="handleClear">
                <el-icon>
                    <CircleClose />
                </el-icon>
            </div>
            <div class="suffix-icon" v-else>
                <el-icon>
                    <ArrowUp v-if="isOpen" />
                    <ArrowDown v-else />
                </el-icon>
            </div>
        </div>
        <div v-show="isOpen" class="lx-select-dropdown">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import { vClickOutside } from '@/components/directives/clickOutside'
import { ArrowDown, ArrowUp, CircleClose } from '@element-plus/icons-vue'

const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    clearable: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const isOpen = ref(false)
const selectedLabel = ref('')

const toggleDropdown = () => {
    isOpen.value = !isOpen.value
}

const closeDropdown = () => {
    isOpen.value = false
}

const handleClear = () => {
    emit('update:modelValue', '')
    emit('change', '')
    selectedLabel.value = ''
    closeDropdown()
}

const handleSelect = (value, label) => {
    selectedLabel.value = label
    emit('update:modelValue', value)
    emit('change', value)
    closeDropdown()
}

// 提供给子组件的数据和方法
provide('lxSelect', {
    selectedValue: props.modelValue,
    handleSelect
})

defineExpose({
    close: closeDropdown
})
</script>

<style lang="scss" scoped>
.lx-select {
    position: relative;
    width: 120px;
    user-select: none;

    &-input {
        height: 30px;
        line-height: 30px;
        padding: 0 30px 0 12px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: #fff;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;

        &:hover {
            border-color: #c0c4cc;

            .suffix-icon {
                .el-icon {
                    display: flex;
                }
            }
        }

        .placeholder {
            font-size: 14px;
            color: #a8abb2;
        }

        .suffix-icon {
            position: absolute;
            right: 8px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #c0c4cc;
            font-size: 12px;

            .el-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 14px;
                height: 14px;
                cursor: pointer;

                &:hover {
                    color: #909399;
                }
            }
        }
    }

    &-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 274px;
        margin: 5px 0;
        background-color: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 1000;
        overflow-y: auto;
    }
}
</style>