<template>
    <div class="customer-voice-overview-h5">
        <titleMenu>
            <template #title>
                总览
            </template>
        </titleMenu>
        <aiAnalysis :data="cvAlAnalyse" :loading="loadingAi" />
        <totalCharts :data="isExpanded ? totalChartsData : totalChartsData.slice(0, 4)" :metrics="totalChartsMetrics"
            :setting="totalChartsSetting"
            :class="['total-charts-style', isExpanded ? 'total-charts-style-expanded' : 'total-charts-style-collapsed']"
            :loading="loadingAd">
            <template #bottom>
                <el-button link class="total-charts-bottom" @click.stop="toggleContent">
                    <span style="margin-right: 4px;">{{ isExpanded ? '收起' : '展开' }}</span>
                    <el-icon>
                        <ArrowUpBold v-if="isExpanded" />
                        <ArrowDownBold v-else />
                    </el-icon>
                </el-button>
            </template>
        </totalCharts>
        <!-- <quadrantChart /> -->
    </div>
</template>

<script setup>
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue'
import aiAnalysis from "../../components/aiAnalysis.vue";
import totalCharts from "../../components/totalChart.vue";
import titleMenu from "../../components/titleMenu.vue";

const store = g.clientBiStore
const props = defineProps(['colorObject'])

const cvAlAnalyse = computed(() => store.cvAlAnalyse);
const loadingAi = ref(true)

const totalChartsData = computed(() => store.cvCustomerAttiDistList);
const loadingAd = ref(true)

// 图表配置
const totalChartsMetrics = [{
    key: 'positive',
    name: '积极',
    color: props.colorObject['positive']
}, {
    key: 'negative',
    name: '消极',
    color: props.colorObject['negative']
}]
const totalChartsSetting = {
    title: '客户态度分布图',
    width: 82
}

// 是否展开内容
const isExpanded = ref(false)

// 切换内容显示状态
const toggleContent = () => {
    isExpanded.value = !isExpanded.value
}

const init = async () => {
    const f1 = async () => {
        loadingAi.value = true
        await g.clientBiStore.getVoiceAnalyse()
        loadingAi.value = false;
    }
    const f2 = async () => {
        loadingAd.value = true
        await g.clientBiStore.getCvCustomerAttiDistList()
        loadingAd.value = false;
    }
    f1()
    f2()
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

onMounted(() => {
    init()
})

</script>
<style lang="scss" scoped>
.customer-voice-overview-h5 {
    padding: 20px 0;

    .customer-voice-warp-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .title-text {
            font-weight: 600;
            font-size: 18px;
            color: #262626;
            line-height: 26px;
            padding-left: 8px;
            position: relative;

            &::before {
                content: '';
                display: inline-block;
                position: absolute;
                width: 5px;
                height: 20px;
                background: #436BFF;
                border-radius: 3px;
                left: -11px;
                top: 2px;
            }
        }

        .van-dropdown-menu {
            width: 130px;
        }


        :deep(.van-dropdown-menu__bar) {
            height: 26px;
            background: transparent;
            box-shadow: unset;
        }

    }

    :deep(.analysis) {
        margin-top: 16px;
    }

    .total-charts-style {
        width: 100%;
        padding: 4px 16px 16px 16px;
        box-sizing: border-box;
        margin-top: 12px;

        :deep(.chart-container) {
            height: calc(100% - 70px);
        }
    }

    .total-charts-style-expanded {
        height: 420px;
    }

    .total-charts-style-collapsed {
        height: 320px;
    }

    .total-charts-bottom {
        width: 100%;
        font-size: 14px;
        color: #436BFF;
        line-height: 22px;
    }

    .quadrant-chart {
        margin-top: 12px;
    }
}
</style>