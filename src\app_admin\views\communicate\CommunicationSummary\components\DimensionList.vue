<template>
  <div class="dimension-container">
    <div class="dimension-grid">
      <div class="dimension-card" v-for="dimension in dimensions" :key="dimension.id">
        <div class="card-content">
          <div class="card-placeholder">
            <span>示例图</span>
          </div>
          <div class="card-footer">
            <div class="card-title">
              {{ dimension.name }}
              <el-tag v-if="dimension.tag" :type="dimension.tagType" size="small">{{ dimension.tag }}</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getDimensionListData } from "@/app_admin/api/communicate.js";

const dimensions = ref([]);

// 获取维度数据
const loadDimensions = async () => {
  try {
    const response = await getDimensionListData();
    if (response.code === 0) {
      dimensions.value = response.data.datas;
    }
  } catch (error) {
    console.error('获取维度数据失败:', error);
    ElMessage.error('获取维度数据失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDimensions();
});
</script>

<style lang="scss" scoped>
.dimension-container {
  padding: 24px 0;
}

.dimension-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.dimension-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #409eff;
  }
}

.card-content {
  .card-placeholder {
    height: 120px;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;
    border-bottom: 1px solid #e4e7ed;
  }

  .card-footer {
    padding: 16px;

    .card-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-tag {
        margin-left: 8px;
      }
    }
  }
}
</style>