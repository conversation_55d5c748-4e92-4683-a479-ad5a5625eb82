const clientRouter = [
  {
    path: "/client",
    component: () => import("@/app_client/components/Layout.vue"),
    redirect: "/client/router",
    children: [
      {
        path: "router",
        name: "router",
        component: () => import("@/app_client/views/Router.vue"),
      },
      {
        path: "customer",
        name: "customer",
        component: () => import("@/app_client/views/customer/Customer.vue"),
      },
      {
        path: "visit",
        name: "visit",
        component: () => import("@/app_client/views/Visit.vue"),
      },
      {
        path: "team/customer",
        name: "teamcustomer",
        component: () => import("@/app_client/views/TeamCustomer.vue"),
      },
      {
        path: "team/visit",
        name: "teamvisit",
        component: () => import("@/app_client/views/TeamVisit.vue"),
      },
      {
        path: "team/report",
        name: "teamreport",
        component: () => import("@/app_client/views/TeamReport/TeamReport.vue"),
      },
      {
        path: "team/board",
        name: "TeamBoard",
        component: () => import("@/app_client/views/TeamBoard.vue"),
      },
      {
        path: "library",
        name: "library",
        component: () => import("@/app_client/views/LibraryClient.vue"),
      },
      {
        path: "team/demand",
        name: "business_insights",
        component: () => import("@/app_client/views/BusinessInsights/BusinessInsights.vue"),
      },
      {
        path: "communication",
        name: "communication",
        component: () => import("@/app_client/views/communication/Communication.vue"),
      },
      {
        path: "opportunity",
        name: "opportunity",
        component: () => import("@/app_client/views/opportunity/Opportunity.vue"),
      },
    ],
  },
  {
    path: "/team_report/:periodType/:reportDate",
    name: "teamreportPage",
    component: () => import("@/app_client/views/TeamReport/ReportPage.vue"),
  },
  {
    path: "/prepare/:id",
    name: "prepare",
    component: () => import("@/app_client/views/Prepare.vue"),
  },
  {
    path: "/recommend2",
    name: "recommend2",
    component: () => import("@/app_client/views/recommend2"),
  },
  {
    path: "/download",
    name: "download",
    component: () => import("@/app_client/views/download/Download.vue"),
  },
  {
    path: "/check_meet",
    name: "check_meet",
    component: () => import("@/app_client/views/meet/Meet.vue"),
  },
  {
    path: "/player/:id",
    name: "player",
    component: () => import("@/app_client/views/CoursePlayer/CoursePlayer.vue"),
  },
  {
    path: "/team_demand_h5",
    name: "business_insights_h5",
    component: () => import("@/app_client/views/BusinessInsightsH5/BusinessInsightsH5.vue"),
  },
];

export default clientRouter;
