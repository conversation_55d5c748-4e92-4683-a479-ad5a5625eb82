<template>
  <el-dialog title="添加待办" v-model="isShow" width="600px" :append-to-body="true" :modal-append-to-body="false"
    class="todo_add_wrap">
    <div class="cd_main">
      <div class="be_note">
        <div class="title">时间点 </div>
        <div class="bnote">{{ form.timestamp }}</div>
      </div>
      <div class="be_note">
        <div class="title">原话 </div>
        <div class="bnote">{{ form.originalWords }}</div>
      </div>
      <div class="be_row">
        <div class="title">内容 <span></span></div>
        <el-input type="textarea" v-model.trim="form.todoContent" :rows="4" show-word-limit placeholder="请输入"
          @keyup.enter.prevent required maxlength="500" />
      </div>
      <div class="be_row">
        <div class="title">截止时间</div>
        <el-date-picker v-model="form.deadline" type="datetime" placeholder="选择日期" format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate"
          :default-time="new Date(2000, 0, 1, 16, 0, 0)" />
      </div>
      <div class="be_row">
        <div class="title">执行人</div>
        <el-input v-model.trim="form.executor" :maxlength="50" show-word-limit placeholder="请输入" @keyup.enter.prevent />
      </div>
    </div>
    <template #footer class="dialog-footer">
      <el-button type="default" @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addTodo, updateTodo } from "@/app_postmeet/tools/api";
import { formatDate } from "@/js/utils.js";
import { getAsrStoreUpdated } from "@/app_postmeet/tools/api";
import { ElMessage } from "element-plus";
const _default_todo_form = {
  todoContent: "",
  timestamp: "",
  originalWords: "",
  executor: "",
  deadline: "",
};
export default {
  data() {
    return {
      form: { ..._default_todo_form },
      mode: "add",
      title: "待办",
      isShow: false,
    };
  },
  mounted() {
    g.emitter.on("show_add_todo_dialog", (item) => {
      // let lessData =
      //   g.postmeetStore.data.words_count < g.postmeetStore.data.min_words_count;
      // if (lessData) {
      //   ElMessage.warning("内容不足，无法添加待办");
      //   return;
      // }
      this.form = { ..._default_todo_form };
      this.form.originalWords = item.txt;
      this.form.timestamp = item.time;
      this.isShow = true;
    });
  },
  unmounted() {
    g.emitter.off("show_add_todo_dialog");
  },
  methods: {
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7; //当天之后的时间可选
    },
    onCancel() {
      this.isShow = false;
    },
    onConfirm() {
      const { todoContent } = this.form;
      if (!todoContent) {
        ElMessage.warning("内容不可以为空!");
        return;
      }

      if (this.mode == "add") {
        this._add();
      } else {
        this._edit();
      }
    },
    _getForm() {
      const param = { ...this.form };
      param["deadline"] = this._getDL(param["deadline"]);
      delete param["id"];
      return param;
    },
    _getDL(dts) {
      let dt = "";
      if (dts) {
        let deadline = formatDate(dts, "yyyy-MM-dd hh:mm");
        dt = deadline + ":00";
      }
      return dt;
    },
    _add() {
      const that = this;
      addTodo(this._getForm()).then((resp) => {
        if (resp.code == 0) {
          getAsrStoreUpdated().then((resp2) => {
            if (resp2.code == 0) {
              if (resp2.data.hasUpdatedContent) {
                g.postmeetStore.setUpdatedContent(resp2.data.asrUpdatedContent);
                g.emitter.emit("updatedAsrContent", "");
                g.emitter.emit("after_update_sale", "");
              }
              ElMessage.success("添加成功");
              that.form["id"] = resp.data;
              that.form["deadline"] = this._getDL(that.form["deadline"]);
              that.onCancel();
            }
          });
        } else {
          ElMessage.error("添加失败");
        }
      });
    },
  },
};
</script>
<style lang="scss">
.todo_add_wrap {
  .cd_main {
    margin-bottom: 10px;

    .be_note {
      display: flex;
      margin: 10px 0;
      flex-direction: row;

      .title {
        width: 50px;
      }

      .bnote {
        margin-left: 12px;
        width: 480px;
        max-height: 200px;
        overflow-y: auto;
      }
    }

    .be_row {
      .title {
        margin: 10px 0;

        span {
          display: inline-block;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background-color: red;
        }
      }

      .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 359px;
      }
    }
  }
}
</style>
