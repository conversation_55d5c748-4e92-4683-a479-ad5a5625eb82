<template>
    <div class="electron-layout">
        <!-- Mac 平台的标题栏 -->
        <div v-if="isMac" class="mac-titlebar">
            <div class="title-text">{{ title }}</div>
        </div>
        <div :class="isMac ? 'mac-content' : 'win-content'">
            <router-view />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            isMac: process.platform === 'darwin',
            title: window.document.title // 你的应用标题
        }
    }
}
</script>

<style lang="scss" scoped>
.electron-layout {
    width: 100%;

    /* Mac 标题栏样式 */
    .mac-titlebar {
        -webkit-app-region: drag;
        height: 38px;
        display: flex;
        font-weight: 500;
        justify-content: center;
        align-items: center;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 10;
    }

    .title-text {
        color: #000000;
        /* 黑色文字 */
        font-size: 13px;
        user-select: none;
        /* 防止文字被选中 */
    }

    .mac-content {
        height: calc(100vh - 38px);
        width: 100%;
        margin-top: 38px;
    }
}
</style>