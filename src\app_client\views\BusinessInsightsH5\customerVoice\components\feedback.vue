<template>
    <div class="customer-voice-feedback-h5">
        <div class="title-text">
            客户反馈
        </div>
        <businessCard v-if="selectCategoriesObj.id" />
    </div>
</template>
<script setup>
import businessCard from "./businessCard/businessCard.vue";
const selectCategoriesObj = computed(() => g.clientBiStore.cvDimensionList[0] || {})
const customer = computed(() => g.clientBiStore.filterConditionObj.customer || {})
const emotion = computed(() => g.clientBiStore.filterConditionObj.emotion || '')

onMounted(() => {
    g.clientBiStore.getCvDimensionList()

})
watch(() => selectCategoriesObj.value, (newVal) => {
    g.clientBiStore.setFilterCondition({
        levelSelect: newVal.id,
    })
})

watch(() => [g.clientBiStore.periodType, customer.value, emotion.value], () => {
    // g.clientBiStore.getCvDimensionList()
}, { immediate: true })

</script>
<style lang="scss" scoped>
.business-card {
    margin-top: 16px;
}
</style>