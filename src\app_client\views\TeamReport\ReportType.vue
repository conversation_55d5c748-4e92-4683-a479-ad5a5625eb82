<template>
  <div class="visit_filter_item">
    <el-select v-model="localValue" collapse-tags popper-class="vf-header" style="width: 150px">
      <el-option v-for="item in options" :key="item.type" :label="item.description" :value="item.type" />
    </el-select>
  </div>
</template>

<script setup>
const props = defineProps({
  value: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["update:value", "reload"]);
const localValue = ref('ALL');
const options = ref([]);

watch(localValue, (newValue) => {
  emit("update:value", toRaw(newValue));
  emit("reload", "");
});

const updateValue = (v) => {
  localValue.value = v;
};

// yearly,weekly,quarterly,monthly
const query = () => {
  options.value = [
    { type: "ALL", description: "全部报告" },
    { type: "weekly", description: "周报" },
    { type: "monthly", description: "月报" },
    { type: "quarterly", description: "季报" },
    { type: "yearly", description: "年报" },
  ];
}

onMounted(() => {
  query();
})

defineExpose({
  options,
  updateValue,
})
</script>

<style lang="scss">
.custom-header {
  .el-checkbox {
    display: flex;
    height: unset;
  }
}
</style>