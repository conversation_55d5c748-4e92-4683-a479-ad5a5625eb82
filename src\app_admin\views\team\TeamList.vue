<template>
  <div class="team-list-wrap">
    <div class="header-wrap">
      <div class="left">
        <el-button type="primary" @click="handleAdd">
          新增团队
        </el-button>
      </div>
      <div class="right">
        <el-input v-model="searchKey" placeholder="请输入团队名称" class="search-input">
          <template #suffix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData" style="width: 100%" v-loading="loading" border>
      <el-table-column prop="name" label="团队名称" />
      <el-table-column prop="leader" label="团队负责人">
        <template #default="scope">
          {{scope.row.members.filter(e => e.targetType === 0).map(item => item.targetName).join(', ')}}
        </template>
      </el-table-column>
      <el-table-column prop="scope" label="管理范围">
        <template #default="scope">
          {{scope.row.members.filter(e => e.targetType > 0).map(item => item.targetName).join(', ')}}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-wrap">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50]"
        :total="total" layout="total,  prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>

  <TeamFormDrawer ref="refDrawer" @callback="onDrawerCallback" />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue';
import { searchTeam, deleteTeam } from '@/app_admin/tools/api.js'
import { ElMessage } from 'element-plus'
import TeamFormDrawer from './TeamFormDrawer.vue';
const searchKey = ref('')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableData = ref([])
const refDrawer = ref({})

// 抽屉相关数据
const handleAdd = () => {
  refDrawer.value.show_add()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const handleEdit = (row) => {
  refDrawer.value.show_edit(row)
}

const handleDelete = (row) => {
  deleteTeam(row.id).then(res => {
    if (res.code === 0) {
      ElMessage.success('删除成功')
      fetchData()
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  }).catch(err => {
    ElMessage.error('删除失败')
    console.error(err)
  })
}

const onDrawerCallback = (action) => {
  if (action === 'reload') {
    fetchData()
  }
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

const fetchData = () => {
  loading.value = true
  searchTeam({
    page: currentPage.value - 1,
    size: pageSize.value,
    keyword: searchKey.value
  }).then(res => {
    if (res.code === 0) {
      tableData.value = res.data.content
      total.value = res.data.total;
    } else {
      ElMessage.error(res.msg || '获取团队列表失败')
    }
  }).catch(err => {
    ElMessage.error('获取团队列表失败')
    console.error(err)
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  fetchData()
})

</script>

<style lang="scss">
.team-list-wrap {
  padding: 20px;

  .header-wrap {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .right {
      display: flex;
      gap: 10px;

      .search-input {
        width: 200px;
      }
    }
  }

  .pagination-wrap {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>