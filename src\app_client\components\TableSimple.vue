<template>
    <table>
        <thead>
            <tr>
                <th v-for="column in columns" :key="column">{{ column }}</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="row in data" :key="row.id">
                <td v-for="column in columns" :key="column">{{ row[column] }}</td>
            </tr>
        </tbody>
    </table>
</template>
  
<script setup>
const columns = ref([])
const data = ref([])

const init = (config) => {
    columns.value = config.columns;
    data.value = config.data;
}

defineExpose({ init })

</script>