<template>
  <div class="btn_search_wrap">
    <div class="hicon" @click="onSearch" v-if="isShow">
      <el-tooltip class="item" effect="dark" content="搜索" placement="top">
        <SearchIcon />
      </el-tooltip>
    </div>
    <div class="search" v-else>
      <div class="search_icon">
        <SearchIcon />
      </div>
      <input type="text" :value="value" placeholder="搜索" @input="onInput" @blur="onBlur" @focus="onInput" />
      <div class="s_middle">
        <div class="clear_icon" v-if="value" @click="onClear">
          <ClearIcon />
        </div>
        <div class="flex-row">
          <div class="line" v-if="value"></div>
          <div class="clear_icon" v-if="value" @click="onUp">
            <PrevIcon />
          </div>
          <div class="num" v-if="value">{{ num }}</div>
          <div class="clear_icon" v-if="value" @click="onDown">
            <NextIcon />
          </div>
        </div>
      </div>
      <div class="line"></div>
      <div class="btn_return" @click="onReturn">取消</div>
    </div>
  </div>
</template>

<script>
import SearchIcon from "./icons/search.vue";
import PrevIcon from "./icons/prev.vue";
import NextIcon from "./icons/next.vue";
import ClearIcon from "./icons/clear.vue";

export default {
  components: { SearchIcon, SearchIcon, ClearIcon, PrevIcon, NextIcon },
  data() {
    return {
      value: "",
      isShow: true,
      count: 0,
      index: 0,
      num: "0/0",
    };
  },
  mounted() {
    g.emitter.on("set_search_input", ({ count, index }) => {
      this.count = count;
      this.index = index;
      this.updateNum();
    });
    g.emitter.on("onSearchKeyword", (keyword) => {
      this.value = keyword;
      this.isShow = false;
      g.emitter.emit("right_header_search", ["keyword", this.value]);
      this.$emit("callback", "search", this.isShow);
    });
  },
  methods: {
    init(count) {
      this.count = count;
      this.updateNum();
      if (this.value) {
        g.emitter.emit("right_header_search", ["keyword", this.value]);
      }
    },
    onInput(e) {
      this.value = e.target.value.trim();
      g.emitter.emit("right_header_search", ["keyword", this.value]);
    },
    onClear() {
      this.value = "";
      this.count = 0;
      this.index = 0;
      this.num = "0/0";
      this.$emit("onClear");
      g.emitter.emit("right_header_search", ["clear", ""]);
    },
    onSearch() {
      this.isShow = !this.isShow;
      this.$emit("callback", "search", this.isShow);
      if (this.value) {
        g.emitter.emit("right_header_search", ["keyword", this.value]);
      }
    },
    onDown() {
      if (this.count) {
        this.index = this.index === this.count ? 1 : this.index + 1;
      }
      this.updateNum();
      g.emitter.emit("right_header_search", ["down", this.index]);
    },
    onUp() {
      if (this.count) {
        this.index = this.index === 1 ? this.count : this.index - 1;
      }
      this.updateNum();
      g.emitter.emit("right_header_search", ["up", this.index]);
    },
    onReturn() {
      this.currKeyword = "";
      g.emitter.emit("onSearchKeyword", "");
      this.isShow = true;
      this.$emit("callback", "search", this.isShow);
      g.emitter.emit("right_header_search", ["clear", ""]);
    },
    updateNum() {
      this.num = `${this.index}/${this.count}`;
    },
  },
};
</script>

<style lang="scss">
.btn_search_wrap {
  .search {
    width: 400px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #eaeaea;
    padding: 0 10px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;

    .search_icon {
      margin-top: 7px;
      color: #bfbfbf;
    }

    .hicon {
      font-size: 16px;

      .item {
        color: #262626;
      }
    }

    input {
      width: 187px;
      height: 22px;
      font-size: 14px;
      color: #595959;
      line-height: 22px;
      border: none;
      margin-left: 5px;
    }

    input:focus {
      border: none;
      outline: none;
    }

    .s_middle {
      width: 132px;
      display: flex;
      flex-direction: row;
      align-items: center;
      user-select: none;

      .clear_icon {
        margin-top: 6px;
        width: 32px;
        text-align: center;
        cursor: pointer;
      }

      .num {
        margin-top: 4px;
      }
    }

    .line {
      width: 2px;
      height: 21px;
      background: #eee;
      margin: 0 10px;
    }

    .btn_return {
      width: 28px;
      height: 20px;
      font-size: 14px;
      color: #8c8c8c;
      cursor: pointer;
    }

    .btn_return:hover {
      color: #436bff;
    }
  }
}
</style>
