<template>
    <el-dialog v-model="showModal" width="800" title="选择商品" class="dialog_choose_salegoods" append-to-body>
        <ChooseSaleGoodsContent ref="contentRef" @callback="onContentCallback" />
    </el-dialog>
</template>

<script setup>
import ChooseSaleGoodsContent from './ChooseSaleGoodsContent.vue'

const emit = defineEmits(['callback'])
const showModal = ref(false)
const contentRef = ref(null)

const show = (param) => {
    showModal.value = true
    nextTick(() => {
        contentRef.value.init(param)
    })
}

const setChecked = (data) => {
    contentRef.value.setChecked(data)
}

const onContentCallback = (action, data) => {
    if (action === 'cancel') {
        showModal.value = false
    } else if (action === 'confirm') {
        emit('callback', 'confirm', data)
        showModal.value = false
    }
}

defineExpose({
    show,
    setChecked
})
</script>

<style lang="scss">
.dialog_choose_salegoods {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .el-dialog__body {
        width: 749px;
    }
}

.sg_preweb {
    .sg_box {
        width: 100%;
    }
}

.sg_preclient {
    .sg_box {
        width: 93%;
    }
}
</style>
