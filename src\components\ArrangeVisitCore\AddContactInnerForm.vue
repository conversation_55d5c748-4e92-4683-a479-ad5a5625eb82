<template>
  <div class="add_contact_inner_form_wrap">
    <FieldAdd ref="refCustomerAdd" />

    <div class="form-footer">
      <el-button @click="onCancel">关闭</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { getFormFields } from '@/js/api';
import FieldAdd from "@/components/RenameSpeaker/FieldAdd.vue";

const refCustomerAdd = ref(null);
const innerFields = ref([]);
const emit = defineEmits(['cancel', 'submit'])

const setFormData = async (data) => {
  console.log('setFormData', data)
  const resp = await getFormFields('INNER_PARTNER');
  if (resp.code == 0) {
    innerFields.value = resp.data.filter(item => item.fieldStatus == 1);
    refCustomerAdd.value.setFormData({ username: '', fieldValues: {} }, innerFields.value);
  }
}

const onCancel = () => {
  emit('cancel')
}

const onSubmit = async () => {
  const data = await refCustomerAdd.value.get_data();
  data['attendeeType'] = 'PARTNER_ATTENDEE'
  console.log('data', data)
  // ElMessage.success('添加成功')
  emit('submit', data)
}

defineExpose({
  setFormData,
  FieldAdd,
  refCustomerAdd
})
</script>

<style lang="scss">
.add_contact_inner_form_wrap {
  padding: 20px;

  .vbp_box {
    margin-bottom: 12px;

    .vbp_title {
      margin-bottom: 12px;

      span {
        color: #F56C6C;
      }
    }
  }

  .form-footer {
    margin-top: 24px;
    text-align: right;
  }
}
</style>