<template>
  <div class="vnews_head flex-row">
    <div>沟通动态</div>
    <el-popover placement="top-start" :width="200" trigger="click" popper-class="vnews_popper">
      <template #reference>
        <div class="vh_refer">
          <CustomVisitIcon />
        </div>
      </template>
      <div class="vnh_pop_main">
        <div class="vp_line flex-row" v-for="theme in Object.keys(props.data)" :key="theme"
          v-if="Object.keys(props.data).length > 0">
          <div class="vp1" :style="{ background: props.data[theme] }"></div>
          <div class="vp2">
            {{ theme }}
          </div>
        </div>
        <div v-else>
          暂无数据
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import CustomVisitIcon from "@/app_client/icons/custom_visit.vue";


const props = defineProps(['data']);

defineExpose({
  CustomVisitIcon,
});
</script>

<style lang="scss">
.vnews_head {
  justify-content: space-between;

  .vh_refer {
    margin-top: 2px;
    margin-left: 28px;
    cursor: pointer;
  }
}

.vnews_popper {
  .vnh_pop_main {
    .vp_line {
      padding: 4px 0;

      .vp1 {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 4px 6px;
      }
    }
  }
}
</style>
