<template>
    <div class="vbp_box">
        <div class="vbp_title">
            区分发言人
        </div>
        <div class="vbp_value">
            <el-radio-group v-model="localValue">
                <el-radio :value="0">自动识别</el-radio>
                <el-radio :value="1">1人</el-radio>
                <el-radio :value="2">2人</el-radio>
            </el-radio-group>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: Number,
        required: true
    }
})

const emit = defineEmits(['update:value'])

const localValue = ref(props.value)

watch(localValue, (newValue) => {
    emit('update:value', newValue)
})
</script>
