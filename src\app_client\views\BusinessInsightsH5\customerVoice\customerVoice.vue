<template>
    <div class="competitive-radar-warp-h5">
        <div style="position: relative;z-index: 1;">
            <Overview :colorObject="colorObject" ref="refOverview" />
            <Feedback ref="refFeedback"></Feedback>
            <FAQ ref="refFAQ"></FAQ>
        </div>

    </div>
</template>

<script setup>
import Overview from './components/overview.vue';
import Feedback from './components/feedback.vue';
import FAQ from './components/FAQ.vue';

const showPopover = ref(false);
const refOverview = ref(null)
const refFeedback = ref(null)
const refFAQ = ref(null)
const actions = [
    { text: '总览', key: 'refOverview' },
    { text: '客户反馈', key: 'refFeedback' },
    { text: '常见问题', key: 'refFAQ' },
];

// 颜色配置
const colorObject = reactive({
    positive: '#04CCA4',
    negative: '#FF6B3B',
    neutral: '#BFBFBF'
})

const onClickToTable = (value) => {
    showPopover.value = !showPopover.value
    if (value === 'refOverview') {
        refOverview.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
    if (value === 'refFeedback') {
        refFeedback.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
    if (value === 'refFAQ') {
        refFAQ.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 暴露方法给父组件
defineExpose({
    onClickToTable
});

</script>
<style lang="scss" scoped>
.competitive-radar-warp-h5 {
    padding: 0 12px;
    background: #F5F5F5;
    min-height: 100%;
    position: relative;

    &::after {
        content: '';
        display: block;
        width: 100%;
        height: 200px;
        background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
        position: absolute;
        top: 0;
        z-index: 0;
        left: 0;
    }


    :deep(.title-text) {
        font-weight: 600;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        padding-left: 8px;
        position: relative;

        &::before {
            content: '';
            display: inline-block;
            position: absolute;
            width: 5px;
            height: 20px;
            background: #436BFF;
            border-radius: 3px;
            left: -11px;
            top: 2px;
        }
    }
}
</style>