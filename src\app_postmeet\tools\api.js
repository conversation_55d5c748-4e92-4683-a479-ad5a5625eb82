import { getHttp } from "@/js/request.js";

const _http = getHttp();

export const getAsrStoreUpdated = () =>
  _http.get(`api/conference/${g.postmeetStore.data.confId}/asr/updated`);

export const updateSubject = (confId, data) =>
  _http.put(`api/conference/subject/${confId}`, data);

export const updateAsrSummary = (confId, data) =>
  _http.put(`api/conference/${confId}/asrsummary`, data);

export const updateKeySent = (confId, data) =>
  _http.put(`api/conference/${confId}/keysentencelab`, data);

export const learnLogin = (data) => _http.post(`learning/login`, data);

export const getMeetParList = (confId) =>
  _http.get(`api/conference/summary/participant/invite/list/${confId}`);

// 提交分享拜访纪要
export const saveShareConfig = (confId, data) =>
  _http.post(`api/conference/${confId}/share`, data);

// 获得拜访纪要的分享设置
export const getShareConfig = (confId) =>
  _http.get(`api/conference/${confId}/share`);

export const updateAsrTopic = (confId, data) =>
  _http.put(`api/conference/${confId}/asrtopiclab`, data);

export const translateRecord = (confId, recordId, uid, roleNum) =>
  _http.post(
    `api/conference/${confId}/record/${recordId}/participant/${uid}/role/${roleNum}`,
    {}
  );

// 取得录制转写状态
export const getTranslateStatus = (confId) =>
  _http.get(`api/conference/${confId}/record/status`);
// recognitionStatus: number, //转写 状态， 0，没有转写，1：正在转写， 2：转写成功， 3：转写失败

export const updateParName = (confId, data) =>
  _http.put(`api/conference/${confId}/participant`, data);

// 获取录制文件的分享ID及验证码
export const shareGetId = (confId) =>
  _http.get(`api/conference/${confId}/record/share`);

// 设置分享录制文件密码，返回分享id
export const shareUpdatePwd = (confId, code) =>
  _http.post(`api/conference/${confId}/record/share`, { code });

const _share = () => location.href.indexOf("/postmeet/share") > -1;

// 获取分享录制拜访的聊天内容
export const getChat = (confId) => {
  const url = _share()
    ? `conference/record/share/${confId}/chat`
    : `api/conference/chat/${confId}`;
  return _http.get(url);
};

// 获取分享录制拜访的录制信息
export const getRecordList = (confId) => {
  const url = _share()
    ? `conference/record/share/${confId}`
    : `api/conference/record/list/${confId}`;
  return _http.get(url);
};

// 获取分享录制拜访的ASR信息
export const getAsrStore = (confId) => {
  if (_share()) {
    return _http.get(`conference/record/share/${confId}/asr/store`);
  } else {
    return _http.post(`conference/${confId}/asr/store`);
  }
};

// 获取销售报表
export const getSaleReports = (confId) => {
  if (_share()) {
    return _http.get(`conference/record/share/${confId}/report`);
  } else {
    return _http.get(`conference/${confId}/report`);
  }
};

// 更新字幕
// PUT /rest/api/conference/{conference id}/subtitle // conference id: 拜访ID
// request: {
// id: string, //字幕ID
// value: string, //字幕, 如果value为空表示value不变
// ui: string, //字幕对应的说话人的ui， 如果ui为空，表示说话人的ui不变
// }
export const updateSubTitle = (data) =>
  _http.put(`api/conference/${g.postmeetStore.data.confId}/subtitle`, data);

// save销售助手的客户角色
export const saveCustomRole = (confId, data) => {
  if (typeof data.id == "string") {
    return _http.put(
      `api/conference/${confId}/salesmate/customerrole/${data.id}`,
      data
    );
  } else {
    return _http.post(`api/conference/${confId}/salesmate/customerrole`, data);
  }
};

// save销售助手的内部伙伴
export const saveInternalRole = (confId, data) =>
  _http.put(`api/conference/${confId}/salesmate/internalpartner/0`, data);

// 增加或修改销售助手对客纪要内容
export const updateSaleSummary = (confId, summaryType, data) =>
  _http.put(`api/conference/${confId}/salesmate/summary/${summaryType}`, data);

// 验证分享的录制文件并获得token
export const shareCheckPwd = (shareId, data) =>
  _http.post(`conference/record/share/${shareId}`, data);

// 取得面试者的简历
//这个只有在拜访正在开着的时候，才能调用到结果
export const getMeetResume = (confId) =>
  _http.get(`api/conference/${confId}/interview/resume`);

export const getMeetReports = (confId, isHistory) => {
  if (isHistory) {
    return _http.get(`api/conference/${confId}/interview/archive/reports`);
  } else {
    return _http.get(`api/conference/${confId}/interview/reports`);
  }
};

// 查询单个面试结果
export const getMeetReport = (confId) => {
  return _http.get(`api/conference/${confId}/interview/report`);
};

//  /api/conference/{conference_id}/interview/questions/renew

//取得面试者的简历问题，这个只有在拜访正在开着的时候，才能调用到结果
export const getIvQuestions = (confId) =>
  _http.get(`api/conference/${confId}/interview/questions/renew`);

// `http://${publicPath}/rest/conference/ongoing/file/${confId}/${participantId}`

// 根据面试id，获取历史消息
export const getInterviewHistory = (interviewId) =>
  _http.get(`api/conference/interview/${interviewId}`);

// 取得销售助手拜访的沟通模板
export const getSaleTemplate = (confId) =>
  _http.get(`api/conference/${confId}/sales/template`);

// 生成拜访报告内容
export const generateSaleReport = (confId, data) =>
  _http.post(`api/conference/${confId}/report/generate`, data);

// 取得主题标签系统维度设置
export const getSaleSysDimension = () =>
  _http.get(`api/conference/${g.postmeetStore.data.confId}/analysis/dimension`);

// 取得新的商品问题
export const getGoodsQuestions = (goodsId, data) =>
  _http.post(`api/conference/goods/${goodsId}/questions`, data);

// 修改脑图内容
export const updateMindmap = (confId, markdown) =>
  _http.put(`api/conference/${confId}/mind/map`, { markdown });

// 待办：
// 新增待办
export const addTodo = (data) =>
  _http.post(`api/conference/${g.postmeetStore.data.confId}/todo`, data);
// 修改待办
export const updateTodo = (todoId, data) =>
  _http.put(
    `api/conference/${g.postmeetStore.data.confId}/todo/${todoId}`,
    data
  );
// 删除待办
export const deleteTodo = (todoId) =>
  _http.delete(`api/conference/${g.postmeetStore.data.confId}/todo/${todoId}`);

// 对拜访纪要AI问答历史
export const getAiConfHistory = (confId) =>
  _http.get(`api/aichat/conference/${confId}/history`);

// 获取剪辑片段详情
export const getClipInfo = (id) => _http.get(`api/v1/clip/${id}`);

// 增加播放次数
export const addClipPlayCount = (id) => _http.post(`api/v1/clip/${id}/play`);

// /xmate/rest/api/conference/report/{conferenceId}/summary
export const getConferenceReportSummary = (conferenceId, methodology) => {
  // 会议销售方法论 0 通用 1 BANT 2 MEDDIC 3 SPICED 4 NEAT
  const method = {
    bant: 1,
    meddic: 2,
    spiced: 3,
    neat: 4,
  };
  let urlShare = _share() ? "conference/report/share" : "api/conference/report";
  let url = `${urlShare}/${conferenceId}/summary?methodology=${
    method[methodology.toLowerCase()]
  }`;
  return _http.get(url);
  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     resolve({ code: 0, data: { content: 'test:' + methodology, methodology } })
  //   }, 5000)
  // })
};

// 获取剪辑片段权限
export const getClipPermission = (id) =>
  _http.get(`api/v1/clip/conference/${id}/clip/permssion`);

// 生成销售会议纪要内容-强制生成业务报告
export const generateSaleReportForce = (confId, data) =>
  _http.post(`api/conference/${confId}/sales/report/generate`, data);

// 判断指定用户是否为团队负责人
export const getIsUserLeader = (userId) =>
  _http.get(`api/xmateuser/team/member?userId=${userId}`);

// 变更纪要MEDDIC内容
export const putMethodologyUpdate = (confId, type, data) =>
  _http.put(
    `api/conference/${confId}/report/content/methodology/${type}/update`,
    data
  );

export const putTypeUpdate = (confId, type, data) =>
  _http.put(`api/conference/${confId}/report/content/${type}/update`, data);

// 获取分享的会议纪要
export const getShareConfld = (shareld) =>
  _http.get(`conference/conferenceId/${shareld}`);

export const updateSummary = (confId, methodology, data) => {
  return _http.put(
    `api/conference/${confId}/report/content/${methodology}/updatsummary`,
    data
  );
};
