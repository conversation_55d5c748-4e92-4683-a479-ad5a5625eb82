<template>
  <el-drawer v-model="visible" :size="400" :destroy-on-close="true" class="add-to-library-wrap" @closed="onCancel">
    <template #header>
      <div class="vd_title">加入案例库</div>
    </template>
    <template #default>
      <div class="add-to-library-container">
        <el-form ref="refForm" :model="form" label-position="top" :rules="rules">
          <el-form-item label="名称" required prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>

          <el-form-item label="存储位置" required>
            <el-radio-group v-model="storageType">
              <el-radio :value="1">加入文件夹</el-radio>
              <el-radio :value="2">仅加入我的剪辑</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="storageType === 1" label="文件夹" required prop="folderId">
            <el-tree-select v-model="form.folderId" :data="treeData" placeholder="请选择文件夹" check-strictly filterable
              :render-after-expand="false" :props="defaultProps" node-key="id" :default-expand-all="true">
            </el-tree-select>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :disabled="uploading">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { getClipLibTree } from "@/js/api";
import { createClipLib } from "@/app_client/tools/api";
import { Search } from '@element-plus/icons-vue'

const uploading = ref(false);
const visible = ref(false);
const treeData = ref([]);
const storageType = ref(1); //默认选择加入文件夹
const form = ref({
  name: "",
  folderId: "",
});
const refForm = ref();

const rules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  folderId: [
    {
      required: true,
      message: '请选择文件夹',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (storageType.value === 1 && !value) {
          callback(new Error('请选择文件夹'));
        } else {
          callback();
        }
      }
    }
  ]
};

const defaultProps = {
  children: "children",
  label: "name",
};

// 获取树形数据
const loadTreeData = async () => {
  try {
    const res = await getClipLibTree(true);
    treeData.value = [res];
  } catch (error) {
    console.error("获取文件夹树失败:", error);
  }
};

// 取消
const onCancel = () => {
  g.emitter.emit("clipper_show_library", false);
  visible.value = false;
};

// 打开抽屉
const open = () => {
  visible.value = true;
  g.emitter.emit("clipper_show_library", true);
  loadTreeData();
};
//把秒转化毫秒，取整
const timeToMs = (time) => {
  return Math.round(time * 1000);
};

const _handleSubmit = async () => {
  const selections = g.clipperStore.getPageInfo("selections");
  const segments = selections.map((item) => {
    return {
      fileId: 1,
      type: "audio",
      startDt: timeToMs(item.startTime),
      endDt: timeToMs(item.endTime),
    };
  });
  const files = [
    {
      id: 1,
      type: "audio",
      url: g.clipperStore.audioUrl,
    },
  ];
  const param = {
    conferenceId: g.clipperStore.confId,
    libId: storageType.value === 1 ? form.value.folderId : '-',
    title: form.value.name,
    files,
    segments: segments,
  };
  const { uploadedRecord, startTime } = g.clipperStore.recordInfo;
  if (!uploadedRecord && startTime) {
    param.startTime = startTime;
  }

  try {
    const res = await createClipLib(param);
    ElMessage.success("创建成功");
    g.emitter.emit("clipper_cancel_selection");
    onCancel();
  } catch (e) {
    console.error(e);
    ElMessage.error("创建失败");
  }
}

// 提交表单
const handleSubmit = async () => {
  if (refForm.value) {
    refForm.value.validate((valid) => {
      if (valid) {
        _handleSubmit();
      }
    });
  }
};

defineExpose({
  open,
  onCancel,
  handleSubmit,
  visible,
  uploading,
});
</script>

<style lang="scss">
.add-to-library-wrap {
  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 10px 24px;

    .ed_main {
      height: calc(100vh - 100px);

      .av_item {
        .av_item_value {
          width: 90%;
        }
      }
    }
  }

  .add-to-library-container {
    .el-tree-select {
      width: 100%;
    }
  }
}
</style>
