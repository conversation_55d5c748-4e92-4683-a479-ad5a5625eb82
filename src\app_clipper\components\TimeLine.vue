<template>
  <div class="timeline-track" v-loading="loading">
    <div class="timeline-scroll" @wheel="handleWheel">
      <div class="timeline-canvas" ref="timelineRef" :style="{ width: containerWidth + 'px' }"
        @mousedown="handleCanvasClick">
        <div class="time-marks" ref="timeMarksRef">
          <div v-for="mark in timeMarks" :key="mark.time" class="time-mark" :style="{ left: mark.position + 'px' }">
            <div class="mark-line"></div>
            <div class="mark-label">{{ mark.label }}</div>
          </div>
        </div>
      </div>
      <TimelineSelection v-for="(selection, index) in selections" :key="index" v-model="selections[index]"
        :container-width="containerWidth" :duration="duration" :timeline-ref="timelineRef" :all-selections="selections"
        :index="index" @selection-change="handleSelectionChange(index, $event)" @remove="removeSelection" />
      <div class="current-time-indicator" :style="{ left: currentTimePosition + 'px' }" @mousedown="startDragging"
        :class="{ dragging: isDragging }"></div>
      <AiSuggestTrack ref="refAiSuggestTrack" />
      <SubtitleTrack ref="refSubtitleTrack" />
      <AudioTrack ref="refAudioTrack" />
    </div>
  </div>

  <el-tour v-model="tourVisible" :mask="true" type="primary">
    <el-tour-step :target="timeMarksRef" title="时间选择区域" description="按住鼠标左键在此区域拖动，可选择剪辑的时间区间，支持同时选择「多段」区间"
      placement="bottom" />
    <el-tour-step :target="refAiSuggestTrack?.$el" title="AI 推荐" description="AI自动推荐优秀话术片段，点击可快速选择对应的时间范围。"
      placement="bottom" />
    <el-tour-step :target="refAudioTrack?.$el" title="音频轨道" description="点击轨道可选择音频播放位置，使用空格键可暂停/继续播放。"
      placement="top" />
  </el-tour>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { formatTime } from "@/app_clipper/tools/utils.js";
import TimelineSelection from "./TimelineSelection.vue";
import AiSuggestTrack from "@/app_clipper/components/AiSuggestTrack.vue";
import AudioTrack from "@/app_clipper/components/AudioTrack.vue";
import SubtitleTrack from "@/app_clipper/components/SubtitleTrack.vue";

const loading = ref(true);
const refAiSuggestTrack = ref(null);
const refAudioTrack = ref(null);
const refSubtitleTrack = ref(null);
const maxZoomValue = ref(20);
//音频总时长，单位秒
const duration = ref(0);
//当前播放时间，单位秒
const currentTime = ref(0);
//缩放倍数,数字越大，时间轴长，时间刻度单位越小 zoom = 时间轴长/屏幕宽度
const zoom = ref(1);
const isSelecting = ref(false);

//屏幕上显示的时长，单位秒
const screenDuration = computed(() => {
  return duration.value / zoom.value;
});

const timelineRef = ref(null);
const containerWidth = ref(0);

// 容器总宽度
const getContainerWidth = () => {
  const baseWidth = window.innerWidth - 167;
  containerWidth.value = baseWidth * zoom.value;
  g.clipperStore.setPageInfo("containerWidth", containerWidth.value);
};

const timeMarks = ref([]);

// 计算时间刻度
const updateTimeMarks = () => {
  if (duration.value === 0) return; // 如果还没有 duration，不进行更新

  getContainerWidth();
  const marks = [];
  const intervals = [
    { threshold: 3000, interval: 180 },
    { threshold: 1000, interval: 60 },
    { threshold: 600, interval: 30 },
    { threshold: 300, interval: 10 },
    { threshold: 60, interval: 5 },
    { threshold: 40, interval: 2 },
    { threshold: 30, interval: 1 },
  ];
  const interval =
    intervals.find((item) => screenDuration.value >= item.threshold)?.interval || 1;

  // 计算每秒的像素数
  const pixelsPerSecond = containerWidth.value / duration.value;
  // 发送给 AudioTrack
  g.emitter.emit("update_audio_zoom", pixelsPerSecond);

  for (let time = 0; time <= duration.value; time += interval) {
    marks.push({
      time,
      position: (time / duration.value) * containerWidth.value,
      label: formatTime(time),
    });
  }
  timeMarks.value = marks;
};

// 当前时间指示器位置
const currentTimePosition = computed(() => {
  return ((currentTime.value || 0) / duration.value) * containerWidth.value;
});

// 处理滚轮缩放
const handleWheel = (e) => {
  if (e.ctrlKey) {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    zoom.value = Math.max(1, Math.min(maxZoomValue, zoom.value * delta));
  }
};

const isDragging = ref(false);

const startDragging = (e) => {
  isDragging.value = true;

  const handleMouseMove = (e) => {
    if (!isDragging.value) return;

    const timeline = timelineRef.value;
    const rect = timeline.getBoundingClientRect();
    const x = e.clientX - rect.left;

    // 确保位置在有效范围内
    const newPosition = Math.max(0, Math.min(x, containerWidth.value));
    const newTime = (newPosition / containerWidth.value) * duration.value;

    currentTime.value = newTime;
    // 触发时间更新事件
    g.emitter.emit("update_current_time", newTime);
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    // 在拖动结束时发送设置音频时间的事件
    g.emitter.emit("set_audio_time", currentTime.value);
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// 改用数组存储多个选择区域
const selections = ref([]);

// 检查新选择区域是否与现有区域重叠
const checkNewSelectionOverlap = (left, width, currentIndex) => {
  const right = left + width;

  for (let i = 0; i < selections.value.length; i++) {
    if (i === currentIndex) continue; // 跳过当前选择的区域

    const selection = selections.value[i];
    const selectionRight = selection.left + selection.width;

    // 检查是否重叠
    if (!(right <= selection.left || left >= selectionRight)) {
      // 发生重叠，返回限制位置
      if (left < selection.left) {
        // 从左侧接近，返回左侧最大可用位置
        return {
          left,
          width: selection.left - left,
          limited: true,
        };
      } else {
        // 从右侧接近，返回右侧最小可用位置
        return {
          left: selectionRight,
          width: Math.max(0, right - selectionRight),
          limited: true,
        };
      }
    }
  }

  return { left, width, limited: false };
};

// 添加防抖函数
const debounce = (fn, delay = 100) => {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(null, args);
    }, delay);
  };
};

// 创建防抖的事件发送函数
const emitSelectionChange = debounce((data) => {
  g.clipperStore.setPageInfo("selections", data.selections);
  g.emitter.emit("selection_change", data);
}, 50);

// 修改画布点击处理逻辑
const handleCanvasClick = (e) => {
  if (
    e.target.closest(".current-time-indicator") ||
    e.target.closest(".selection-area")
  ) {
    return;
  }

  const rect = timelineRef.value.getBoundingClientRect();
  const startX = e.clientX - rect.left;

  isSelecting.value = true;
  const initialX = startX;

  // 计算开始时间并更新 currentTime
  const startTime = (startX / containerWidth.value) * duration.value;
  currentTime.value = startTime;
  // 通知 AudioTrack 更新当前时间
  g.emitter.emit("set_audio_time", startTime);

  // 创建新的选择区域
  const newSelection = {
    visible: true,
    left: startX,
    width: 0,
    startTime: startTime,
    endTime: startTime,
  };

  // 添加新的选择区域
  const currentIndex = selections.value.length;
  selections.value.push(newSelection);

  const handleMouseMove = (e) => {
    if (!isSelecting.value) return;

    const currentX = e.clientX - rect.left;
    let left = Math.min(initialX, currentX);
    let width = Math.abs(currentX - initialX);

    // 直接更新位置，不检查重叠
    const currentSelection = selections.value[currentIndex];
    currentSelection.left = left;
    currentSelection.width = width;
    currentSelection.startTime = (left / containerWidth.value) * duration.value;
    currentSelection.endTime = ((left + width) / containerWidth.value) * duration.value;

    // 使用防抖的事件发送函数
    emitSelectionChange({
      selections: selections.value.map((s) => ({
        startTime: Math.min(s.startTime, s.endTime),
        endTime: Math.max(s.startTime, s.endTime),
      })),
      currentSelection: currentIndex,
    });
  };

  const handleMouseUp = () => {
    isSelecting.value = false;

    // 鼠标松开时检查重叠
    const currentSelection = selections.value[currentIndex];
    const { limited } = checkNewSelectionOverlap(
      currentSelection.left,
      currentSelection.width,
      currentIndex
    );

    // 如果选择区域太小或发生重叠，则移除
    if (currentSelection.width < 5 || limited) {
      selections.value.splice(currentIndex, 1);
    }

    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// 修改选择区域变化处理函数
const handleSelectionChange = (index, { startTime, endTime }) => {
  const list = selections.value.map((s) => ({
    startTime: Math.min(s.startTime, s.endTime),
    endTime: Math.max(s.startTime, s.endTime),
  }));
  // 使用防抖的事件发送函数
  emitSelectionChange({
    selections: list,
    currentSelection: index,
  });
};

// 添加清除所有选择区域的方法
const clearSelections = () => {
  selections.value = [];
};

// 添加删除指定选择区域的方法
const removeSelection = (index) => {
  selections.value.splice(index, 1);
  // 发送选择变化事件
  emitSelectionChange({
    selections: selections.value.map((s) => ({
      startTime: Math.min(s.startTime, s.endTime),
      endTime: Math.max(s.startTime, s.endTime),
    })),
    currentSelection: -1,
  });
};

// 检查两个区域是否重叠
const isOverlapping = (area1, area2) => {
  return !(area1.endTime <= area2.startTime || area1.startTime >= area2.endTime);
};

// 修改创建选择区域的辅助函数
const createSelection = (startTime, endTime) => {
  // 计算位置和宽度
  const left = (startTime / duration.value) * containerWidth.value;
  const width = ((endTime - startTime) / duration.value) * containerWidth.value;

  // 创建新的选择区域
  const newSelection = {
    visible: true,
    left,
    width,
    startTime,
    endTime,
  };

  // 过滤掉与新区域重叠的现有区域
  selections.value = selections.value.filter(
    (selection) => !isOverlapping(selection, newSelection)
  );

  // 添加新的选择区域
  selections.value.push(newSelection);

  // 发送选择变化事件
  emitSelectionChange({
    selections: selections.value.map((s) => ({
      startTime: Math.min(s.startTime, s.endTime),
      endTime: Math.max(s.startTime, s.endTime),
    })),
    currentSelection: selections.value.length - 1,
  });
};

watch(selections, () => {
  g.clipperStore.setPageInfo("selections", toRaw(selections.value));
});

const timeMarksRef = ref(null);
const tourVisible = ref(false);

onMounted(() => {
  // 监听音频加载完成事件
  g.emitter.on("audio_ready", (audioDuration) => {
    duration.value = audioDuration;
    // 音频加载完成后，初始化时间轴
    updateTimeMarks();
    loading.value = false;
    // 修改 Tour 显示逻辑
    const hasSeenTour = localStorage.getItem("timelineTourSeen") == "true";
    if (!hasSeenTour) {
      // 确保组件完全渲染后再显示 Tour
      setTimeout(() => {
        tourVisible.value = true;
        localStorage.setItem("timelineTourSeen", "true");
      }, 500);
    }
  });

  g.emitter.on("update_zoom", (value) => {
    zoom.value = value;
    updateTimeMarks();
  });
  g.emitter.on("clipper_cancel_selection", () => {
    clearSelections();
  });

  // 添加字幕选择事件监听
  g.emitter.on("subtitle_select", ({ startSeconds, endSeconds }) => {
    createSelection(startSeconds, endSeconds);
  });

  // 添加时间更新事件监听
  g.emitter.on("update_current_time", (time) => {
    currentTime.value = time;
  });
});

onUnmounted(() => {
  g.emitter.off("update_current_time");
  g.emitter.off("audio_ready");
  g.emitter.off("update_zoom");
  g.emitter.off("clipper_cancel_selection");
  g.emitter.off("subtitle_select");
});

const init = () => {
  refAudioTrack.value.init();
};

defineExpose({
  init,
  updateTimeMarks,
  clearSelections,
  removeSelection,
  refAiSuggestTrack,
  refAudioTrack,
  refSubtitleTrack,
});
</script>

<style lang="scss" scoped>
.timeline-track {
  margin-left: 16px;

  .timeline-scroll {
    height: 230px;
    width: calc(100vw - 132px);
    overflow-x: auto;
    position: relative;
  }

  .timeline-canvas {
    height: 50px;
    position: relative;
  }

  .time-marks {
    position: relative;
    height: 100%;
    z-index: 1;
  }

  .time-mark {
    position: absolute;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;

    .mark-line {
      width: 1px;
      height: 10px;
      background-color: #666;
    }

    .mark-label {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      text-align: center;
      user-select: none;
    }
  }

  .current-time-indicator {
    position: absolute;
    top: 0;
    width: 2px;
    height: 100%;
    background-color: #436bff;
    transform: translateX(-50%);
    cursor: ew-resize;
    z-index: 100;

    &.dragging {
      background-color: #436bff;
    }

    &:hover {
      width: 4px;
    }
  }
}

// 为了确保引导能正确显示，添加一些辅助样式
:deep(.el-tour-step) {
  z-index: 2000;
}
</style>
