<template>
  <div class="subtitle-panel">
    <div class="subtitle-title flex-row">
      <div class="title-text">字幕</div>
    </div>
    <div class="subtitle-list">
      <div class="subtitle-item" v-for="(item, index) in subtitles" :key="index"
        :class="{ active: currentSubtitleIndex === index }" :ref="(el) => (subtitleRefs[index] = el)"
        @click="handleSubtitleClick(item)">
        <div class="time">
          {{ item.info.name }} {{ item.startTime }} - {{ item.endTime }}
        </div>
        <div class="content">{{ item.content }}</div>
        <div class="delete-icon" v-if="enableDelete">
          <deleteIcon />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import deleteIcon from "@/icons/delete.vue";
import { formatDuration } from "@/js/utils";

const subtitles = ref([]);
const enableDelete = ref(false);
const currentSubtitleIndex = ref(-1);
const subtitleRefs = ref([]);

// 查找当前时间对应或最近的字幕
const findNearestSubtitle = (currentTime) => {
  const subs = g.clipperStore.subtitles;

  // 首先查找时间范围内的字幕
  const inRangeIndex = subs.findIndex(
    (sub) => currentTime >= sub.startSeconds && currentTime < sub.endSeconds
  );

  if (inRangeIndex !== -1) {
    return inRangeIndex;
  }

  // 如果没有在范围内的，查找最近的字幕
  let nearestIndex = 0;
  let minDiff = Infinity;

  subs.forEach((sub, index) => {
    // 计算与字幕中点的时间差
    const midPoint = (sub.startSeconds + sub.endSeconds) / 2;
    const diff = Math.abs(currentTime - midPoint);

    if (diff < minDiff) {
      minDiff = diff;
      nearestIndex = index;
    }
  });

  return nearestIndex;
};

const scrollToSubtitle = (index) => {
  if (subtitleRefs.value[index]) {
    subtitleRefs.value[index].scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
};

const handleSubtitleClick = (item) => {
  g.emitter.emit("set_audio_time", item.startSeconds);
};

const init = async () => {
  subtitles.value = g.clipperStore.subtitles;

  // 添加时间更新事件监听
  g.emitter.on("update_current_time", (currentTime) => {
    const newIndex = findNearestSubtitle(currentTime);
    if (newIndex !== currentSubtitleIndex.value) {
      currentSubtitleIndex.value = newIndex;
      scrollToSubtitle(newIndex);
    }
  });
};

onUnmounted(() => {
  g.emitter.off("update_current_time");
});

defineExpose({
  init,
  subtitles,
});
</script>

<style lang="scss" scoped>
.subtitle-panel {
  height: 100%;
  overflow-y: auto;
  padding: 10px;

  .subtitle-title {
    margin: 10px;

    .title-text {
      width: 28px;
      font-weight: 500;
      font-size: 14px;
      color: #262626;
    }
  }

  .subtitle-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    position: relative;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;

      .content {
        color: #436bff;
      }

      .delete-icon {
        display: block;
      }
    }

    .time {
      font-size: 12px;
      color: #8c8c8c;
    }

    .content {
      margin-top: 5px;
      font-size: 14px;
      color: #262626;
    }

    .delete-icon {
      display: none;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;

      svg {
        width: 16px;
        height: 16px;
        color: #8c8c8c;

        &:hover {
          color: #ff4d4f;
        }
      }
    }

    &.active {
      background: #F5F5F5;

      .content {
        color: #436BFF;
      }
    }
  }
}
</style>
