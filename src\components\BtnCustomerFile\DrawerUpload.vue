<template>
    <el-drawer v-model="dialogVisible" size="640" modal-class="customer_drawer_upload" :close-on-click-modal="false">
        <template #header>
            <div class="vd_title">
                导入
            </div>
        </template>
        <template #default>
            <ImportDataPad ref="refImport" :isAdmin="isAdmin" />
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleConfirm">关闭</el-button>
            </span>
        </template>
    </el-drawer>
</template>

<script setup>
import { nextTick, ref } from "vue";
import ImportDataPad from "./ImportDataPad.vue";
const dialogVisible = ref(false);
const refImport = ref();

defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})

const handleConfirm = () => {
    dialogVisible.value = false;
}

const show = () => {
    dialogVisible.value = true;
    nextTick(() => {
        refImport.value.reset()
    })
}

defineExpose({
    show
})
</script>

<style lang="scss">
.customer_drawer_upload {
    .el-drawer__header {
        margin-bottom: 0 !important;
    }
}
</style>
