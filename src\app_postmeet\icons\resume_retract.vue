<template>
    <svg width="20px" height="64px" viewBox="0 0 20 64" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="面试助手" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="客户端-简历预览（默认1064）" transform="translate(-624.000000, -303.000000)">
                <g id="编组-2"
                    transform="translate(634.000000, 335.000000) scale(-1, 1) translate(-634.000000, -335.000000) translate(624.000000, 303.000000)">
                    <path
                        d="M0,0 L12,0 C16.418278,-8.11624501e-16 20,3.581722 20,8 L20,56 C20,60.418278 16.418278,64 12,64 L0,64 L0,64 L0,0 Z"
                        id="矩形" fill="#F6F6FA"
                        transform="translate(10.000000, 32.000000) scale(-1, 1) translate(-10.000000, -32.000000) ">
                    </path>
                    <g id="1.基础-/-2.Icon-图标-/-16px-/-arrow_right"
                        transform="translate(10.000000, 32.000000) scale(-1, 1) translate(-10.000000, -32.000000) translate(2.000000, 24.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <g id="编组" fill="#595959">
                            <path
                                d="M6.64644661,4.81644661 C6.82001296,4.64288026 7.08943736,4.62359511 7.2843055,4.75859116 L7.35355339,4.81644661 L10.1819805,7.64487373 C10.3555469,7.81844009 10.374832,8.08786449 10.239836,8.28273263 L10.1819805,8.35198052 L7.35355339,11.1804076 C7.15829124,11.3756698 6.84170876,11.3756698 6.64644661,11.1804076 C6.47288026,11.0068413 6.45359511,10.7374169 6.58859116,10.5425487 L6.64644661,10.4733009 L9.12132034,7.99842712 L6.64644661,5.52355339 C6.47288026,5.34998704 6.45359511,5.08056264 6.58859116,4.8856945 L6.64644661,4.81644661 Z"
                                id="路径">
                            </path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>
