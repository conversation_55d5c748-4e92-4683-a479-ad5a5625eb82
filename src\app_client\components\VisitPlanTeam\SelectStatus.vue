<template>
    <div class="visit_filter_item">
        <el-select v-model="localValue" collapse-tags placeholder="完成状态" popper-class="vf-header"
            style="width: 230px">
            <el-option v-for="item in options" :key="item.description" :label="item.description" :value="item.value" />
        </el-select>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:value', 'reload'])
const localValue = ref(props.value)
const options = ref([
    {
        value: 'all',
        description: '全部'
    },
    {
        value: 'false',
        description: '未完成'
    },
    {
        value: 'true',
        description: '已完成'
    }
])

watch(localValue, (newValue) => {
    emit('update:value', toRaw(newValue))
    emit('reload', '')
})

const updateValue = (v) => {
    localValue.value = v
}

defineExpose({
    options, updateValue
})

</script>

<style lang="scss">
.custom-header {
    .el-checkbox {
        display: flex;
        height: unset;
    }
}
</style>