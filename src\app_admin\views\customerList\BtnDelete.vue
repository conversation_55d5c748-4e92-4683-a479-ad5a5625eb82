<template>
    <div class="customer_btn_delete">
        <el-button type="default" @click="onDelete" :disabled="selectIds.length == 0">批量删除</el-button>
    </div>
</template>

<script setup>
import { confirmDelete } from "@/js/utils.js"
import { deleteCustomer } from "@/js/api.js"

const emit = defineEmits(['reload'])
const selectIds = ref([])
const onDelete = () => {
    const title = `这${selectIds.value.length}个客户`
    confirmDelete(title, (status) => {
        if (status) {
            deleteCustomer({
                source: 0,
                ids: selectIds.value
            }).then(resp => {
                if (resp.code == 0) {
                    emit('reload', selectIds.value)
                }
            })
        }
    })
}

const setIds = (ids) => {
    selectIds.value = ids;
}

defineExpose({ setIds })
</script>

<style lang="scss">
.customer_btn_delete {}
</style>
