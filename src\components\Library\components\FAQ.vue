<template>
    <div class="fragment-faq-list">
        <div class="faq-card" v-for="item in faqList" :key="item.id" :class="{ 'is-active': item.id === activeId }"
            @click="showDetail(item)">
            <p class="faq-question single-line-ellipsis-2">
                <span class="icon-question">问</span>
                <span class="question-title">{{ item.question }}</span>
            </p>
            <p class="faq-answer single-line-ellipsis-2">
                <span class="icon-answer">答</span>
                <span class="answer-content">{{ item.answer }}</span>
            </p>
            <div class="faq-meta">
                <span class="meta-item">
                    <el-icon>
                        <View />
                    </el-icon>{{ item.playCount }} 次播放
                </span>
                <span class="meta-item">
                    <el-icon>
                        <Clock />
                    </el-icon>创建时间：{{ item.createTime }}
                </span>
                <span class="meta-item">
                    <el-icon>
                        <Clock />
                    </el-icon>创建人：{{ item.creator }}
                </span>
            </div>
        </div>
        <div class="faq-count"> {{ faqList.length }} 个剪辑片段</div>
    </div>
    <FAQDetailDrawer ref="faqDetailDrawerRef" />
</template>

<script setup>
import { View, Clock } from '@element-plus/icons-vue'
import FAQDetailDrawer from './FAQDetailDrawer.vue'
const activeId = ref(null)
const faqDetailDrawerRef = ref(false)
const currentDetail = ref(null)
const faqList = ref([
    {
        id: 1,
        question: '应用启动时频繁闪退',
        answer: '“感谢您的反馈！应用闪退确实会严重影响使用体验，我们非常重视这一问题，会全力协助排查解决。我们立即与技术团队同步并为您提供解决方案。”',
        playCount: 100,
        createTime: '2024-10-20',
        creator: '秦大'
    }, {
        id: 1,
        question: '应用启动时频繁闪退',
        answer: '“感谢您的反馈！应用闪退确实会严重影响使用体验，我们非常重视这一问题，会全力协助排查解决。我们立即与技术团队同步并为您提供解决方案。”',
        playCount: 100,
        createTime: '2024-10-20',
        creator: '秦大'
    }, {
        id: 1,
        question: '应用启动时频繁闪退',
        answer: '“感谢您的反馈！应用闪退确实会严重影响使用体验，我们非常重视这一问题，会全力协助排查解决。我们立即与技术团队同步并为您提供解决方案。”',
        playCount: 100,
        createTime: '2024-10-20',
        creator: '秦大'
    }, {
        id: 1,
        question: '应用启动时频繁闪退',
        answer: '“感谢您的反馈！应用闪退确实会严重影响使用体验，我们非常重视这一问题，会全力协助排查解决。我们立即与技术团队同步并为您提供解决方案。”',
        playCount: 100,
        createTime: '2024-10-20',
        creator: '秦大'
    },

])

function showDetail(item) {
    currentDetail.value = {
        ...item,
        originList: [
            {
                question: '我最近时常发生应用启动时频繁闪退的情况？',
                answer: '闪退是启动后立即发生，还是在某个操作后触发？是否有特定提示弹窗（如内存不足，网络错误等）？',
                customer: '智慧星科技',
                creator: '秦大',
                createTime: '2023-05-15'
            },
        ]
    }
    faqDetailDrawerRef.value.init(
        currentDetail.value
    )

}
</script>

<style lang="scss" scoped>
.fragment-faq-list {
    padding: 24px 0;

    p {
        margin: 0;
    }

    .faq-card {
        background: #FFFFFF;
        border-radius: 8px;

        margin-bottom: 20px;
        padding: 22px 24px;
        cursor: pointer;

        &.is-active,
        &:hover {
            box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);

            .question-title {
                color: #436bff;
            }
        }
    }

    .faq-question {
        font-weight: bold;
        margin-bottom: 10px;

        .question-title {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            line-height: 24px;
        }
    }

    .icon-question {
        background: #04CCA4;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        display: inline-block;
        margin-right: 8px;
    }

    .faq-answer {
        margin-bottom: 12px;

        .answer-content {
            font-size: 14px;
            color: #757575;
            line-height: 22px;
            width: calc(100% - 34px);
        }
    }

    .icon-answer {
        background: #436BFF;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        display: inline-block;
        margin-right: 8px;
    }

    .faq-meta {
        display: flex;
        gap: 24px;
        color: #8C8C8C;
        font-size: 12px;
        margin-top: 12px;
        padding: 12px 0 0 30px;
        border-top: 1px solid #E9E9E9;

        .el-icon {
            font-size: 14px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 2px;
        }

    }

    .faq-count {
        color: #8C8C8C;
        font-size: 14px;
        margin-top: 16px;
    }
}
</style>
