<template>
    <div class="custom-radio-group">
        <div v-for="option in options" :key="option.value + localSelectedValue" class=""
            :class="{ 'radio-item-selected': localSelectedValue == option.value, 'radio-item': true }"
            @click="handleSelect(option.value)">
            <span class="radio-label">{{ option.label }}</span>
        </div>
    </div>
</template>

<script setup>
const localSelectedValue = ref('')
const props = defineProps({
    options: {
        type: Array,
        required: true,
        default: () => []
    },
    modelValue: {
        type: [String, Number, Boolean],
        default: ''
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const handleSelect = (value) => {
    localSelectedValue.value = value
    emit('update:modelValue', value)
    emit('change', value)
}

watch(() => props.modelValue, (newValue) => {
    localSelectedValue.value = newValue
}, { immediate: true })
</script>

<style lang="scss" scoped>
.custom-radio-group {
    display: inline-flex;
    align-items: center;


    .radio-item {
        padding: 5px 16px;
        cursor: pointer;
        user-select: none;
        border: 1px solid #D9D9D9;
        // transition: all 0.2s;
        position: relative;

        &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }

        &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        &:not(:first-child) {
            margin-left: -1px;
        }

        &:hover {
            border-color: #436BFF;
            color: #436BFF;
            z-index: 1;
        }
    }

    .radio-item-selected {
        border-color: #436BFF;
        z-index: 1;

        .radio-label {
            color: #436BFF;
        }
    }

    .radio-label {
        font-size: 14px;
        color: #595959;
    }
}
</style>
