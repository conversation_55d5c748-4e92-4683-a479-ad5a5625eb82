<template>
	<div class="id-code-box">
		<div class="login-box">
			<div class="back" @click="$emit('back', 1)">返回</div>
			<h2>输入{{ type === 1 ? '手机' : '邮箱' }}验证码</h2>
			<p class="login-tip">请输入发送至 {{ formatPhone }} 的 6 位验证码，有效期 15 分钟</p>
			<input type="email" class="input-placeholder" placeholder="请输入验证码" v-model="code" autocomplete="off">
			<div v-if="err" style="color: red; font-size: 14px;">{{ err }}</div>
			<div v-if="!err && time > 0" class="id-code-tip"><span>{{ time }}</span>秒后可重新获取验证码</div>
			<div v-if="!err && time <= 0" @click="sendCodeAgain"
				style="cursor: pointer; font-size: 14px; color: #436BFF; margin-top: 4px;">重新发送验证码</div>
		</div>
		<div class="next-btn" @click="getOrgList">下一步</div>
	</div>
</template>

<script>
import api from './api.js'
export default {
	name: 'idCode',
	props: {
		deviceType: Number,
		apiConfig: String,
		productCode: String,
		type: Number,
		phone: String,
		countryCode: String,
		email: String,
		errMsg: String,
		waitTime: Number,
		msg() {
			return {}
		}
	},
	data() {
		return {
			code: '',
			s_errMsg: '',
			timer: null,
			time: this.waitTime
		}
	},
	computed: {
		err() {
			return this.s_errMsg || this.errMsg
		},
		formatPhone() {
			if (this.type === 1) {
				return this.phone.replace(/(\d{3})\d{4}(\d{2})/, '$1******$2')
			} else if (this.type === 2) {
				return this.email.replace(/(\w{2})\w*(\w{2})/, '$1******$2')
			} else {
				return ''
			}
		}
	},
	methods: {
		sendCodeAgain() {
			this.$emit('sendCodeAgain', () => {
				this.setTimeout()
			})
		},
		setTimeout() {
			this.time = this.waitTime
			let timer = setInterval(() => {
				this.time--
				if (this.time <= 0) {
					clearInterval(timer)
				}
			}, 1000)
			this.timer = timer
		},
		getOrgList() {
			if (this.type === 1) {
				this.getOrgListBysms(this.showList)
			} else {
				this.getOrgListByEmail(this.showList)
			}
		},
		showList(res) {
			this.$emit('next', res.data, this.code)
		},
		getOrgListBysms(callback) {
			api({
				baseURL: this.apiConfig,
				headers: {
					source: this.deviceType == 1 ? 501 : 506
				}
			}).post('/core/apaas/sms/orgs', {
				"phone": this.phone,
				"countryCode": this.countryCode,
				"code": this.code,
				"factorCode": this.productCode
			}).then(res => {
				console.log(res)
				callback && callback(res)
			}).catch(err => {
				this.errorHandle(err)
			})
		},
		getOrgListByEmail(callback) {
			api({
				baseURL: this.apiConfig,
				headers: {
					source: this.deviceType == 1 ? 501 : 506
				}
			}).post('/core/apaas/mail/orgs', {
				"email": this.email,
				"code": this.code,
				"factorCode": this.productCode
			}).then(res => {
				console.log(res)
				callback && callback(res)
			}).catch(err => {
				this.errorHandle(err)
			})
		},
		errorHandle(err) {
			console.log(err)
			if (err.response?.data) {
				this.s_errMsg = err.response.data.error.message
			}
		}
	},
	beforeDestroy() {
		clearInterval(this.timer)
	},
	mounted() {
		this.setTimeout()
	}
}
</script>
<style scoped lang="scss">
.id-code-box {
	text-align: left;
	position: relative;
	height: 100%;
	background: #fff;
}

.input-placeholder::placeholder {
	color: #BFBFBF;
	font-size: 14px;
}

.back {
	cursor: pointer;
	color: #000;
	position: relative;
	font-size: 14px;
	display: block;
	padding: 4px;
	border-radius: 6px;

	&:before {
		content: '';
		display: inline-block;
		width: 7px;
		height: 7px;
		border-top: 1px solid #000;
		border-right: 1px solid #000;
		transform: rotate(-135deg);
		margin-right: 6px;
	}

	&:hover {
		color: #1f2329;
		background: rgba(31, 35, 41, .1);
	}
}

h2 {
	font-size: 22px;
	color: #262626;
	margin: 12px 0;
}

.login-tip {
	font-size: 14px;
	color: #646a73;
	margin: 8px 0 0 0;
	line-height: 20px;
	white-space: pre-wrap;
}

.login-box input {
	display: block;
	border: 1px solid #d0d3d6;
	box-sizing: border-box;
	border-radius: 6px;
	height: 40px;
	line-height: 40px;
	padding: 0 15px;
	margin-top: 10px;
	font-size: 14px;
	width: 100%;

	&:focus {
		border-color: #3370ff;
	}

	&:focus-visible {
		outline: none;
	}
}

.id-code-tip {
	font-size: 14px;
	margin-top: 4px;
	font-size: 14px;
	line-height: 22px;
	color: #8C8C8C;
}

.next-btn {
	font-size: 14px;
	bottom: 64px;
	width: 100%;
	height: 40px;
	line-height: 40px;
	text-align: center;
	color: #fff;
	background: #3370ff;
	border-radius: 6px;
	cursor: pointer;
	margin-top: 36px;

	&:hover {
		color: #fff;
		background: #4e83fd;
		border-color: #4e83fd;
	}
}
</style>
