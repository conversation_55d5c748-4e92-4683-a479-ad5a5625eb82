<template>
  <div class="left-menu flex-col">
    <div class="lm_item ais">萃取建议
      <img :src="getOssUrl('yxt_ai_color.png')" alt="ai" />
    </div>
    <div class="lm_item">字幕</div>
    <div class="lm_item">音频</div>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils";

</script>

<style lang="scss" scoped>
.left-menu {
  width: 76px;
  padding: 35px 0px 0 20px;
  border-right: 1px solid #e5e5e5;

  .lm_item {
    margin: 16px 0;
    user-select: none;
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #595959;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }

  .ais {
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 12px;
    color: #595959;
    line-height: 20px;
    background: linear-gradient(227.9970555714091deg, #4CD2F4 0%, #7A1EFF 100%);
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;

    img {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
