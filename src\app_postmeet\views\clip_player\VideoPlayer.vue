<template>
  <div :class="'video_player_wrap ' + (isShowVideo ? 'video_show' : 'video_hide')">
    <div v-if="!errorMsg" class="video_wrap">
      <video id="clip_video_id" ref="refVideo" :src="`${item.url}`" width="100%" height="100%" controls
        controlsList="nodownload" :poster="getOssUrl('postmeet_1.png')" @timeupdate="handleTimeUpdate"></video>
      <div class="video_cover_txt">
        <div class="subject">{{ item.subject }}</div>
        <div class="note customerName">{{ item.salesMateCustomerName }}</div>
        <div class="note">开始时间: {{ item.startTime }}</div>
        <div class="note">沟通主题: {{ item.salesMateTags }}</div>
        <div class="note">时长: {{ getSecondsShowTime(item.duration / 1000) }}</div>
        <div class="note">创建者: {{ item.hostName }}</div>
      </div>
    </div>
    <div class="error_msg" v-else>
      <div class="error_text">{{ errorMsg }}</div>
    </div>
  </div>
</template>

<script setup>
import { getOssUrl, getSecondsShowTime } from "@/js/utils.js";
import { getPlayInfo } from "@/app_client/tools/api";
import { addClipPlayCount } from "@/app_postmeet/tools/api";
import { ref, onMounted, onUnmounted } from "vue";

const errorMsg = ref("");
const hasInit = ref(false);
const emit = defineEmits(["retry", "timeUpdate", 'audioError']);
const refVideo = ref(null);
const player = ref(null);
let hasStartedPlaying = false;
let timer = null;
const isShowVideo = ref(localStorage.getItem("isPlayVideo") == "true");

const getPlayInfoApi = (fileId) => {
  if (!fileId || item.value.url) {
    console.error('no file id?', fileId);
    return;
  }
  getPlayInfo(fileId).then((res) => {
    if (res.length > 0) {
      item.value.url = res[0].url;
      hasInit.value = true;
    } else {
      errorMsg.value = "音频文件处理失败";
      emit('audioError')
    }
  });
};

const item = ref({
  url: "",
  subject: "",
  customerName: "",
  startTime: "",
  tags: "",
  videoTime: "",
  hostName: "",
});

const init = (data) => {
  if (hasInit.value) {
    return;
  }
  item.value = data;
  switch (data.fileMergeStatus) {
    case 2:
      getPlayInfoApi(data.fileId);
      break;
    case 1:
      ElMessage.warning("视频合并中");
      timer = setTimeout(() => {
        emit("retry");
      }, 10 * 1000);
      break;
    case 3:
      errorMsg.value = "音频文件处理失败";
      emit('audioError')
      break;
  }
};

const handlePlay = () => {
  if (!hasStartedPlaying && item.value.id) {
    hasStartedPlaying = true;
    addClipPlayCount(item.value.id);
  }
};

const initVideoListeners = () => {
  player.value = document.getElementById("clip_video_id");
  if (!player.value) return;

  // 播放计数监听
  player.value.addEventListener("play", handlePlay);

  // 播放状态监听
  player.value.addEventListener("play", () => {
    g.emitter.emit("videoStatus", "playing");
  });
  player.value.addEventListener("pause", () => {
    g.emitter.emit("videoStatus", "paused");
  });
  g.emitter.on("updateVideoSpeed", (speed) => {
    player.value.playbackRate = speed;
  });
  g.emitter.on("updateVideoVolume", (volume) => {
    player.value.volume = volume;
  });
  g.emitter.on("updateVideoTime", (currentTime) => {
    player.value.currentTime = currentTime;
  });

  g.emitter.on("playing_video", (isPlayVideo) => {
    isShowVideo.value = isPlayVideo;
  });

  // 时间更新监听
  player.value.addEventListener("timeupdate", (e) => {
    // 将当前播放时间转换为毫秒
    const currentTimeMs = Math.floor(e.target.currentTime * 1000);
    g.emitter.emit("videoTimeUpdate", currentTimeMs);
    g.emitter.emit("videoTimeUpdateNoStart", Math.floor(e.target.currentTime * 1000));
  });

  // 播放控制监听
  g.emitter.on("video_control", (action) => {
    if (action === "play") {
      player.value.play();
    } else if (action === "pause") {
      player.value.pause();
    }
  });
};

const removeVideoListeners = () => {
  if (!player.value) return;

  player.value.removeEventListener("play", handlePlay);
  player.value.removeEventListener("play", () => {
    g.emitter.emit("videoStatus", "playing");
  });
  player.value.removeEventListener("pause", () => {
    g.emitter.emit("videoStatus", "paused");
  });
  g.emitter.off("video_control");
};

onMounted(() => {
  initVideoListeners();
});

onUnmounted(() => {
  timer && clearTimeout(timer);
  removeVideoListeners();
});

const handleRetry = () => { };

const handleTimeUpdate = (e) => {
  const currentTime = e.target.currentTime;
  g.emitter.emit("timeUpdate", currentTime);
};

defineExpose({
  init,
  getOssUrl,
  getSecondsShowTime,
});
</script>

<style lang="scss" scoped>
.video_show {
  visibility: visible;
}

.video_hide {
  height: 0;
  visibility: hidden;
}

.video_player_wrap {
  .video_wrap {
    position: sticky;
    top: 0;
    z-index: 2;

    video {
      background: #436BFF;
      max-height: 400px;
      object-fit: contain;
    }

    .video_cover_txt {
      position: absolute;
      top: 10px;
      left: 10px;
      padding-top: 41px;
      padding-left: 41px;
      pointer-events: none;

      .subject {
        font-size: 31px;
        font-weight: 500;
        color: #ffffff;
        line-height: 45px;
      }

      .note {
        font-size: 17px;
        font-weight: 500;
        color: #ffffff;
        line-height: 31px;
      }

      .customerName {
        margin-bottom: 16px;
      }
    }
  }

  .error_msg {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .error_text {
      color: #ff0000;
      font-size: 16px;
      margin-bottom: 20px;
    }

    .retry_btn {
      padding: 8px 24px;
      background-color: #436bff;
      color: #ffffff;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background-color: #66b1ff;
      }
    }
  }
}
</style>
