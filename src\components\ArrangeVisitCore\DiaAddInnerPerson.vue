<template>
  <el-dialog v-model="dialogVisible" title="添加内部参会人" width="500" :before-close="handleClose"
    class="av_add_inner_person_dia">
    <AddContactInnerForm ref="formRef" @cancel="handleClose" @submit="onSubmit" />
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['callback'])
const dialogVisible = ref(false)
const formRef = ref()

const show = (p) => {
  console.log('p', p)
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.setFormData(p || {})
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

const onSubmit = (data) => {
  emit('callback', 'add_person', data)
  handleClose()
}

defineExpose({
  show, formRef
})
</script>

<style lang="scss">
.av_add_inner_person_dia {
  .vbp_box {
    margin-bottom: 12px;

    .vbp_title {
      margin-bottom: 12px;

      span {
        color: #F56C6C;
      }
    }
  }
}
</style>
