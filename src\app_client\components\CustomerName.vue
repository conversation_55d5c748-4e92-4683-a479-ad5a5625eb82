<template>
  <div class="ccol_customername flex-row" @click="onClick" :id="props.row.id">
    <div class="cicon flex-center">
      <img :src="getCompanyLogoUrl(props.row.companyLogoUrl)" />
    </div>
    <div class="txt">
      {{ props.row.name }}
    </div>
  </div>
</template>

<script setup>
import { getCompanyLogoUrl } from "@/js/utils.js";
import riskCountIcon from "@/app_client/icons/riskCount.vue";

const props = defineProps(["row"]);
const emit = defineEmits(["callback"]);

const onClick = () => {
  emit("callback", props.row);
};

defineExpose({ riskCountIcon, props, onClick, getCompanyLogoUrl });
</script>

<style lang="scss">
.ccol_customername {
  cursor: pointer;
  width: 320px;

  .cicon {
    margin: 0 12px;

    img {
      width: 44px;
      height: 44px;
    }
  }

  .txt {
    margin-top: 12px;
    font-size: 14px;
    color: #262626;
    cursor: pointer;
  }
}
</style>
