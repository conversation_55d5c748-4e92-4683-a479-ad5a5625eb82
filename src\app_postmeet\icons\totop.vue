<template>
    <svg width="81.5px" height="89px" viewBox="0 0 81.5 89" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <circle id="path-1" cx="20" cy="20" r="20"></circle>
            <filter x="-96.2%" y="-86.2%" width="292.5%" height="292.5%" filterUnits="objectBoundingBox" id="filter-2">
                <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1">
                </feMorphology>
                <feOffset dx="0" dy="4" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
                <feGaussianBlur stdDeviation="12" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
                <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1">
                </feComposite>
                <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix"
                    in="shadowBlurOuter1"></feColorMatrix>
            </filter>
        </defs>
        <g id="k8--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="分析-概览" transform="translate(-1198.500000, -1132.500000)">
                <g id="沟通内容" transform="translate(640.000000, 72.000000)">
                    <g id="返回顶部常态" transform="translate(583.000000, 1081.000000)">
                        <g id="椭圆形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                            <use stroke="#DEDEDF" stroke-width="1" fill="#FFFFFF" fill-rule="evenodd"
                                xlink:href="#path-1"></use>
                        </g>
                        <g id="编组" transform="translate(14.000000, 13.000000)" fill="#595959" fill-rule="nonzero">
                            <path
                                d="M7.17241381,13.3333333 C7.17241381,13.7015232 6.87136389,14 6.50000001,14 C6.12863612,14 5.82758621,13.7015232 5.82758621,13.3333333 L5.82758621,4.72977778 C5.81694702,4.73815339 5.80648498,4.74674785 5.7962069,4.75555556 L5.74465517,4.80266667 L1.9529641,8.58266667 C1.69971097,8.83689535 1.28932585,8.84795665 1.0225151,8.60774548 C0.755704355,8.36753431 0.72735507,7.96147804 0.958239956,7.6871111 L1.00172272,7.63955556 L4.79386208,3.85999999 C5.70027588,2.96133332 7.16658621,2.96133332 8.11603448,3.83111111 L8.18775863,3.89955555 L11.9252083,7.65777777 C12.1792518,7.90995096 12.1883058,8.3156309 11.9457616,8.57869836 C11.7032173,8.84176582 11.2948738,8.86916172 11.0187945,8.64088889 L10.970829,8.59733334 L7.23606897,4.8408889 L7.17331035,4.78311111 L7.17241381,13.3333333 Z M12.3275862,0 C12.6989501,0 13,0.29847684 13,0.666666668 C13,1.0348565 12.6989501,1.33333334 12.3275862,1.33333334 L0.672413782,1.33333334 C0.301049902,1.33333334 0,1.0348565 0,0.666666668 C0,0.29847684 0.301049902,0 0.672413782,0 L12.3275862,0 L12.3275862,0 Z"
                                id="形状"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
