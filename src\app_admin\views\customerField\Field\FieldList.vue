<template>
  <div class="cu_field_list_wrap">
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
      <template #col_fieldType="{ row }">
        {{ getLang(row.fieldType) }}
      </template>
    </MyTable>
    <DrawerFields ref="refDiaField" @callback="onCallback" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getFormFields } from "@/js/api.js"
import { deleteFormField } from "@/app_admin/tools/api"
import DrawerFields from "./DrawerFields.vue";
import getLang from '@/js/lang'
const refDiaField = ref()

const getFormFieldsList = () => {
  return new Promise((resolve, reject) => {
    getFormFields('CUSTOMER_TPL').then(res => {
      if (res.code == 0) {
        resolve({
          code: 0,
          data: {
            datas: res.data,
            totalNum: res.data.length
          }
        })
      } else {
        reject(res.message)
      }
    })
  })
}

const refTable = ref(null);
const datas = reactive({
  tableid: 'admin_customer_field_list',
  param: {
    pageSize: 999,
  },
  need_header: true,
  need_init_load: false,
  show_search: false,
  form: {},
  pk: 'id',
  columns: ["fieldName", "fieldType", "createdTime"],
  template: ["fieldType"],
  show_btn_add: true,
  show_link_column: true,
  show_link_edit: true,
  show_link_delete: true,
  delete_hint_column: 'fieldName',
  show_link_view: false,
  urlGet: getFormFieldsList,
  urlDelete: deleteFormField,
});

const onCallback = (action, data) => {
  if (action == 'reload') {
    refTable.value.search()
  }
}

const cbDatas = (action, data) => {
  if (action == 'init_add') {
    refDiaField.value.showAdd()
  } else if (action == 'init_edit') {
    refDiaField.value.showEdit(data)
  }
}
const initLoad = () => {
  refTable.value.search()
}

defineExpose({
  MyTable,
  refDiaField,
  getLang,
  refTable,
  cbDatas,
  initLoad
})
</script>

<style lang='scss'>
.cu_field_list_wrap {
  padding: 24px 0;

  .dates_picker {
    margin-right: 10px;
  }

  .table_box .table_class {
    height: calc(100vh - 178px) !important;
    .col_operation_ {
      width: 104px
    }
  }
}
</style>