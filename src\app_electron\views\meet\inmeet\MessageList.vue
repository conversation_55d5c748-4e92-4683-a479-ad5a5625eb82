<template>
    <div class="messages_wrap" v-ai-tip="'center'">
        <div class="messages_content custom-scrollbar" ref="messagesContainer">
            <WarningTag v-if="isConnecting" hint="当前网络不稳定，正在尝试重连...已开启本地录制，文件不会丢失" />
            <div v-if="messageList.length > 0" class="messages">
                <div class="message" v-for="msg in messageList" :key="msg.Data.seq">
                    <component :is="components[msg.MsgType]" :msg="msg.Data" />
                </div>
            </div>

            <div v-else class="no_data flex-center">
                <el-empty :image="getAssetUrl('inmeet_5.svg')" description="暂无消息" />
            </div>
            <!-- <div class="divider_container" v-if="!isUseAsr">
                <el-divider>本地录制中，实时转写已暂停</el-divider>
            </div> -->
            <!-- <WarningTag v-if="!isUseAsr" hint="当前网络不稳定，已暂停实时录制，启用本地录制；会后生成AI分析结果的时间可能会稍长。" /> -->
        </div>
    </div>
</template>

<script setup>
import WarningTag from '@/app_electron/components/WarningTag.vue'
import { getAssetUrl, now } from '@/js/utils'
import { handleTwMsg, handleSummaryMsg } from './misc.js'
import itemMessage from './itemMessage.vue'
import itemSummary from './itemSummary.vue'
const components = {
    TwMsg: itemMessage,
    TwSummary: itemSummary
}
const meetingStatus = ref('')
const messageList = ref([])
const emit = defineEmits(['callback'])
const refTime = ref('')
const messagesContainer = ref(null)
const isConnecting = ref(false)
// const isUseAsr = ref(true)

const setIsReconnecting = (val) => {
    isConnecting.value = val
}

const setStatus = (status) => {
    meetingStatus.value = status
    scrollToBottom()
}

const setTime = (time) => {
    refTime.value = time
}

const scrollToBottom = () => {
    nextTick(() => {
        if (messagesContainer.value) {
            messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
        }
    })
}

const recoverMsgList = () => {
    const list = g.appStore.getStore(g.cv.keyMeetChatList)
    if (list) {
        messageList.value = list
    }
}

const addAsrResultListener = () => {
    g.emitter.on('livekitData', (data) => {
        const fnMap = {
            TwMsg: handleTwMsg,
            TwSummary: handleSummaryMsg
        }
        if (fnMap[data.MsgType]) {
            data.Data.showTime = now('hh:mm:ss')
            messageList.value = fnMap[data.MsgType](messageList.value, data)
            g.appStore.setStore(g.cv.keyMeetChatList, toRaw(messageList.value))
            emit('callback', 'new_msg')
            scrollToBottom()
        } else {
            g.elog.log('unknown data type', data)
        }
    })
}

onMounted(() => {
    g.ipcRenderer.on('window-event', (_, data) => {
        if (data === 'restore') {
            scrollToBottom()
        }
    });
    addAsrResultListener()
})

onUnmounted(() => {
    g.emitter.off('livekitData')
    g.ipcRenderer.removeAllListeners('window-event');
})

defineExpose({
    setStatus, setTime, meetingStatus, recoverMsgList, scrollToBottom, setIsReconnecting
})
</script>

<style lang="scss">
.messages_wrap {
    width: 100%;
    background: #FFFFFF;
    z-index: 3;
    border-radius: 24px 24px 0 0;
    position: relative;
    padding-top: 1px;

    .messages_content {
        margin-top: 10px;
        overflow-y: auto;
        height: calc(100vh - 200px);

        .warning_tag_wrap {
            margin: 10px 15px 0 15px;
            position: fixed;
        }

        .messages {
            display: flex;
            flex-direction: column;
            align-items: center;

            .message {
                width: calc(100vw - 50px);
            }
        }

        .no_data {
            height: 200px;
        }

        .paused_text {
            height: 20px;
            font-size: 12px;
            color: #FF5219;
            line-height: 20px;
            margin: 10px;
        }

        .divider_container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 24px;

            .el-divider {
                width: calc(100% - 48px);
                margin: 0 auto;

                .el-divider__text {
                    width: 184px;
                    color: #8C8C8C;
                }
            }
        }
    }
}

.mac-content {
    .messages_wrap {
        margin: 70px auto 12px auto;

        .messages {
            height: calc(100vh - 199px);
        }
    }
}

.win-content {
    .messages_wrap {
        margin: 78px auto 12px auto;

        .messages {
            height: calc(100vh - 162px);
        }
    }
}
</style>
