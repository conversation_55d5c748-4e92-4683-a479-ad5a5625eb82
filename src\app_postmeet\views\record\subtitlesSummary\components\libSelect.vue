<template>
  <div class="pr">
    <span class="yxtbiz-joinlib_selectmask pa z-19 hand nowrap ellipsis bg-white" @click.stop="selectHandle">
      {{ selectLabel[0] }}{{ selectLabel[2] }}
    </span>
    <el-select ref="select" v-model="selectId" popper-class="yxtbiz-joinlib_selectpop" class="width-percent-100"
      @change="selectChange">
      <template #fixed-header>
        <Search ref="search" :readonly="loading" @search="search" />
      </template>
      <template #empty>
        <DataEmpty :loading="loading" />
      </template>
      <el-option v-for="item of list" :key="item.id" :label="item.label" :value="item.id"
        class="yxtbiz-joinlib_flex yxtbiz-joinlib_flexcenter">
        <div class="yxtbiz-joinlib_flex1 pr">
          <i>&nbsp;&nbsp;</i>
          <label class="pa nowrap ellipsis">{{ item.labels[0] }}{{ item.labels[2] }}</label>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import Search from './search.vue';
import DataEmpty from './dataEmpty.vue';
import { getLibs } from './service.js';

export default {
  name: '',
  // inject: ['svgUrl'],
  components: { Search, DataEmpty },
  props: {
    fileId: String, // 文件 id
    storageType: [String, Number] // 文件的存储方式：1-公有云存储，2-私有云存储
  },
  data() {
    return {
      loading: false,
      selectId: '',
      selectLabel: [],
      list: []
    };
  },
  methods: {
    initData(id, list) {
      this.selectId = id;
      this.setData(list);
    },
    setData(list) {
      list = list || [];
      this.list = list.map(item => ({
        ...item,
        labels: this.formatItem(item)
      }));
      this.selectChange(this.selectId);
    },
    formatItem(data) {
      if (data.libType === 3 && data.createUserId === g.postmeetStore.data.user.id) {
        data.label = '我的知识库'
      }
      let libName = data.label || '';
      let match = libName.match(/\$.+?\$/);
      let arr = ['', libName, ''];
      if (match) {
        let index = match.index;
        arr[0] = libName.substring(0, index);
        arr[1] = match[0];
        arr[2] = libName.substring(index + arr[1].length);
      }
      return arr;
    },
    // 搜索
    search(keyword) {
      this.loading = true;
      this.list = [];
      getLibs({
        fileId: this.fileId,
        keyword,
        storageType: this.storageType
      }).then(res => {
        this.setData(res.kngLibList);
      }).catch(e => { }).then(() => {
        this.loading = false;
      });
    },
    resetSearch() {
      this.$refs.search.searchText = '';
    },
    // 设置选中的 label
    // setSelectLabel (id) {
    //   let selectData = this.list.find(item => item.id === id)
    //   this.selectLabel = selectData.labels
    //   return selectData
    // },
    // 选择选项
    selectChange(id) {
      let selectData = this.list.find(item => item.id === id);
      this.selectLabel = selectData.labels;
      this.$emit('change', selectData);
    },
    // 模拟点击事件
    selectHandle() {
      this.$refs.select.toggleMenu();
    }
  }
};
</script>
