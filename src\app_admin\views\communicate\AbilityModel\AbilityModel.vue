<template>
  <div class="ability-model-page">
    <el-tabs v-model="activeTab" class="model-tabs">
      <el-tab-pane label="能力项" name="ability">
        <AbilityListTable ref="abilityListRef" />
      </el-tab-pane>
      <el-tab-pane label="能力模型" name="behavior">
        <AbilityModelTable ref="abilityModelTableRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import AbilityListTable from './components/AbilityListTable.vue';
import AbilityModelTable from './components/AbilityModelTable.vue';

const activeTab = ref('ability');
const abilityListRef = ref();
const abilityModelTableRef = ref();

</script>

<style lang="scss" scoped>
.ability-model-page {
  padding: 20px;
}
</style>