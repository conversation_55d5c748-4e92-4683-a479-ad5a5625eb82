<template>
    <el-dialog title="待办" v-model="isShow" width="400px" :append-to-body="true" :modal-append-to-body="false"
        class="todo_edit_wrap">
        <div class="cd_main">
            <div class="be_row">
                <div class="title">内容 <span></span></div>
                <el-input type="textarea" v-model.trim="form.todoContent" :rows="4" show-word-limit placeholder="请输入"
                    @keyup.enter.prevent required maxlength="500" />
            </div>
            <div class="be_row">
                <div class="title">截止时间</div>
                <el-date-picker 
                    v-model="form.deadline"
                    type="datetime" 
                    placeholder="选择日期"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate"
                    :default-time="new Date(2000, 0, 1, 16, 0, 0)"
                />
            </div>
            <div class="be_row">
                <div class="title">执行人</div>
                <el-input v-model.trim="form.executor" :maxlength="50" show-word-limit placeholder="请输入"
                    @keyup.enter.prevent />
            </div>
        </div>
        <template #footer class="dialog-footer">
            <el-button type="default" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { addTodo, updateTodo } from '@/app_postmeet/tools/api'
import { formatDate } from "@/js/utils.js"

const _default_todo_form = {
    todoContent: '',
    timestamp: '',
    originalWords: '',
    executor: '',
    deadline: '',
}

const form = reactive({..._default_todo_form})
const mode = ref('add')
const title = ref('待办')
const isShow = ref(false)

const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7 //当天之后的时间可选
}

const showAdd = () => {
    mode.value = 'add'
    form.todoContent = ''
    form.timestamp = ''
    form.originalWords = ''
    form.executor = ''
    form.deadline = ''
    title.value = '添加待办'
    isShow.value = true
}

const showEdit = (row) => {
    Object.assign(form, row)
    mode.value = 'edit'
    title.value = '更新待办'
    isShow.value = true
}

const onCancel = () => {
    isShow.value = false
}

const onConfirm = () => {
    const { todoContent } = form
    if (!todoContent) {
        ElMessage.warning('内容不可以为空!')
        return
    }

    if (mode.value == 'add') {
        _add()
    } else {
        _edit()
    }
}

const _getForm = () => {
    const param = { ...form }
    param['deadline'] = _getDL(param['deadline'])
    delete param['id']
    return param
}

const _getDL = (dts) => {
    let dt = ''
    if (dts) {
        if (typeof dts === 'string' && dts.includes(':00')) {
            return dts
        }
        
        const dateObj = dts instanceof Date ? dts : new Date(dts)
        let deadline = formatDate(dateObj, "yyyy-MM-dd hh:mm")
        dt = deadline + ":00"
    }
    return dt
}

const emit = defineEmits(['callback'])

const _add = () => {
    addTodo(_getForm()).then(resp => {
        if (resp.code == 0) {
            ElMessage.success("添加成功")
            
            form.id = resp.data
            form.deadline = _getDL(form.deadline)
            emit('callback', 'add', form)
            onCancel()
        } else {
            ElMessage.error("添加失败")
        }
    })
}

const _edit = () => {
    const { id } = form
    updateTodo(id, _getForm()).then(resp => {
        if (resp.code == 0) {
            ElMessage.success("更新成功")
            emit('callback', 'edit', form)
            onCancel()
        } else {
            ElMessage.error("更新失败")
        }
    })
}

defineExpose({
    showAdd,
    showEdit
})
</script>
<style lang="scss">
.todo_edit_wrap {
    .cd_main {
        .be_row {
            .title {
                margin: 10px 0;

                span {
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: red;
                }
            }

            .el-date-editor.el-input,
            .el-date-editor.el-input__inner {
                width: 359px;
            }
        }
    }

}
</style>