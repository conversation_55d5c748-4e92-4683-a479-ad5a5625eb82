<template>
    <div class="category_list_wrap">
        <el-menu ref="menu" :default-active="activeIndex" :default-openeds="defaultOpeneds" class="category_menu"
            @select="handleSelect">
            <template v-for="category in categoriesList">
                <!-- 有子菜单的情况使用折叠菜单 -->
                <!--  <el-sub-menu v-if="category.children && category.children.length > 0" :key="category.id"
                    :index="category.id">
                    <template #title>
                        <div class="menu-title">
                            <img :src="getOssUrl('star2.png', 2)" alt="star" class="star2" />
                            <span>{{ category.name }}</span>
                        </div>
                        <div class="item_count">{{ category.count }}</div>
                    </template>
<el-menu-item v-for="item in category.children" :key="item.id" :index="item.id">
    <div>{{ item.name }}</div>
    <div class="item_count">{{ item.count }}</div>
</el-menu-item>
</el-sub-menu> -->
                <!-- 没有子菜单的情况使用普通菜单项 -->
                <el-menu-item :index="category.id">
                    <div>
                        <img :src="getOssUrl('star2.png', 2)" alt="star" class="star2" />
                        <span class="el-menu-item-else-title">{{ category.name }}</span>
                    </div>
                    <div class="item_count">{{ category.cnt }}条</div>
                </el-menu-item>
            </template>
        </el-menu>
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
const props = defineProps(['type']);
const activeIndex = ref('');
const menu = ref(null);


// 默认展开第一项
const defaultOpeneds = ref([]);

// 当前选中的菜单项
const filterConditionLevelFirst = computed(() => g.clientBiStore.filterConditionObj.levelFirst || '');
const filterConditionLevel = computed(() => g.clientBiStore.filterConditionObj.levelSelect || '');

// 定义分类数据结构
const categoriesList = computed(() => {
    const list = g.clientBiStore.cvDimensionList || [];
    if (filterConditionLevelFirst.value) {
        return list.filter(item => item.id === filterConditionLevelFirst.value);
    } else {
        return list;
    }
});

// 监听分类列表变化，设置默认展开第一项
watch(() => categoriesList.value, (newList) => {
    if (newList && newList.length > 0) {
        const firstItem = newList[0];

        // 如果第一项有子菜单，展开它
        if (firstItem && firstItem.children && firstItem.children.length > 0) {
            defaultOpeneds.value = [firstItem.id];
            // 如果没有选中项，默认选中第一个子项
            if (!activeIndex.value && firstItem.children.length > 0) {
                activeIndex.value = firstItem.children[0].id;
                g.clientBiStore.setFilterCondition({
                    levelSelect: activeIndex.value
                });
            }
        } else {
            // 如果第一项没有子菜单，直接选中它
            if (!activeIndex.value) {
                activeIndex.value = firstItem.id;
                g.clientBiStore.setFilterCondition({
                    levelSelect: activeIndex.value
                });
            }
        }
    }
}, { immediate: true });

watch(() => filterConditionLevel.value, () => {
    activeIndex.value = filterConditionLevel.value;
    g.clientBiStore.updateCvFbVersion();
});

// 菜单选择事件处理
const handleSelect = (index) => {
    activeIndex.value = index;
    g.clientBiStore.setFilterCondition({
        levelSelect: activeIndex.value
    })
}

const init = async () => {
    await g.clientBiStore.getCvDimensionList();
    // 设置默认展开第一个菜单
    // setDefaultOpenMenu();
}

// 设置默认展开第一个菜单
const setDefaultOpenMenu = () => {
    if (categoriesList.value && categoriesList.value.length > 0) {
        // 找到第一个有子菜单的项
        const firstWithChildren = categoriesList.value.find(item => item.children && item.children.length > 0);
        if (firstWithChildren) {
            // 确保在DOM更新后设置默认展开项
            nextTick(() => {
                defaultOpeneds.value = [firstWithChildren.id];
                // 如果菜单组件已经挂载，则手动调用open方法
                if (menu.value) {
                    menu.value.open(firstWithChildren.id);
                }
            });
        }
    }
}


watch(() => [g.clientBiStore.periodType, g.clientBiStore.cvSearchVersion], () => {
    init()
}, { immediate: true })



onMounted(() => {
    init();
    // 确保在组件挂载后也尝试设置默认展开菜单
    nextTick(() => {
        // 添加一个小延迟，确保菜单组件完全渲染
        setTimeout(() => {
            // setDefaultOpenMenu();

            // 如果有子菜单的项，手动打开第一个
            if (categoriesList.value && categoriesList.value.length > 0) {
                const firstWithChildren = categoriesList.value.find(item => item.children && item.children.length > 0);
                if (firstWithChildren && menu.value) {
                    menu.value.open(firstWithChildren.id);
                }
            }
        }, 300);
    });
})
</script>

<style lang="scss" scoped></style>