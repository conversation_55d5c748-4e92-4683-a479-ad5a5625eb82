<template>
  <el-card class="team_board_card card_no_border" shadow="never">
    <!-- <el-tooltip v-if="boardPage === 'SalesPerformance'" class="box-item" effect="dark" content="所选维度将同步至下方所有图表"
      placement="top">
      <PerfTypeSelector class="perf-type-selector" v-model="perfType" />
    </el-tooltip> -->

    <el-tabs v-model="boardPage" class="board-tabs">
      <el-tab-pane label="沟通情况" name="VisitSituation">
      </el-tab-pane>
      <el-tab-pane label="销售表现" name="SalesPerformance"></el-tab-pane>
    </el-tabs>

    <div v-if="boardPage === 'VisitSituation'">
      <VisitSituation ref="refVisitSituation" :isReport="isReport" />
    </div>
    <div v-if="boardPage === 'SalesPerformance'" class="sales-performance">
      <SalePerfmance ref="refSalePerfmance" />
    </div>
  </el-card>
</template>

<script setup>
import SalePerfmance from "./SalePerfmance/SalePerfmance.vue";
import PerfTypeSelector from "@/app_client/components/TeamBoardCore/components/PerfTypeSelector.vue";
import VisitSituation from "./VisitSituation/VisitSituation.vue";
import { getDefaultPeriodType } from '@/app_client/tools/utils.js';
const refVisitSituation = ref();
const refSalePerfmance = ref();
const perfType = ref()
const props = defineProps({
  isReport: {
    type: Boolean,
    default: false
  }
});
const paramsObj = ref({})

const boardPage = ref('VisitSituation');

const setDept = (data, isBol = false) => {
  if (!isBol) {
    paramsObj.value = data = toRaw(data);
  } else {
    data = paramsObj.value
  }
  g.clientBoardStore.overviewDept = data;
  g.clientBoardStore.regionType = '';
  g.clientBoardStore.regionDept = {}
  g.clientBoardStore.regionData = {}
  g.clientBoardStore.overviewData = {}


  if (boardPage.value === 'VisitSituation') {
    nextTick(() => {
      refVisitSituation.value.setDept();
    })
  }
};
watch(() => boardPage.value, () => {
  setDept({}, true)
  g.clientBoardStore.boardPage = boardPage.value;
})

onMounted(() => {
  g.clientBoardStore.periodType = getDefaultPeriodType();
})

defineExpose({
  setDept,
});
</script>

<style lang="scss">
.team_board_card {
  .title-line-wrap {
    align-items: center;
  }

  .el-card__body {
    padding: 4px 24px;
    box-sizing: border-box;
    position: relative;

    .perf-type-selector {
      position: absolute;
      right: 24px;
      top: 6px;
      z-index: 9;


    }

    .el-tabs {
      margin-bottom: 16px;

      .el-tabs__item {
        font-weight: 400;
        font-size: 16px;
        color: #595959;
        line-height: 24px;
      }

      .is-active {
        color: #436BFF;
        font-weight: 700;
      }
    }
  }

  .sales-performance {
    width: 100%;
    height: 100%;
  }
}
</style>
