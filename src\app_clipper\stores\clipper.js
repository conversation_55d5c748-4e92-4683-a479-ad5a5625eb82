import { defineStore } from "pinia";
import { getClipFileInfo } from "../tools/api";
import { formatDate, secondsToTime } from "@/js/utils";

const ClipperStore = defineStore("clipper", {
  state: () => ({
    confId: "",
    playFileIdx: 0,
    scheduleInfo: {},
    subtitles: [],
    recordInfo: {},
    pageInfo: {},
    audioUrl: "",
  }),
  actions: {
    setPageInfo(key, value) {
      this.pageInfo[key] = value;
    },
    getPageInfo(key) {
      if (key) {
        return this.pageInfo[key];
      }
      return this.pageInfo;
    },
    async fetchClipFileInfo(confId, playIdx) {
      this.confId = confId;
      this.playFileIdx = playIdx;
      let info;
      info = await getClipFileInfo(confId);
      const { recordInfo } = info;
      let sentences = info.sentences
      this.scheduleInfo = recordInfo;
      if (this.playFileIdx < recordInfo.recordList.length) {
        this.recordInfo = recordInfo.recordList[this.playFileIdx];
      } else {
        this.playFileIdx = 0;
        this.recordInfo = recordInfo.recordList[0];
      }
      const { duration } = this.recordInfo;
      this.recordInfo.startDt = "00:00:00";
      this.recordInfo.endDt = secondsToTime(duration / 1000);
      this.subtitles = sentences.map((item) => {
        const startTime = item.startTime.includes(" ")
          ? item.startTime.split(" ")[1]
          : item.startTime;
        const endTime = item.endTime.includes(" ")
          ? item.endTime.split(" ")[1]
          : item.endTime;
        return {
          ...item,
          startSeconds: this.timeToSeconds(startTime),
          endSeconds: this.timeToSeconds(endTime),
        };
      });
      if (g.config.isDev) {
        this.audioUrl = "1.mp3";
        console.warn("WARNING using local audio file:", this.audioUrl);
      } else {
        this.audioUrl = g.appStore.getRecordUrl(this.recordInfo.recordingPath);
      }
    },
    timeToSeconds(time) {
      const recordStartTime = '00:00:00'
      const [h1, m1, s1] = time.split(":");
      const [h2, m2, s2] = recordStartTime.split(":");
      const timeInSeconds =
        parseInt(h1) * 3600 + parseInt(m1) * 60 + parseInt(s1);
      const startTimeInSeconds =
        parseInt(h2) * 3600 + parseInt(m2) * 60 + parseInt(s2);

      return timeInSeconds - startTimeInSeconds;
    },
  }
});

export default ClipperStore;
