import axios from "axios";
import { getUser, getYxtOrgDomain } from "@/js/utils";
// 创建一个 CancelToken.source 实例
let source;

export function abortAxiosRequest() {
  try {
    source && source.cancel("请求取消的原因");
  } catch (e) {
    console.log("abortRequest error", e);
  }
}

export default function request(url, opt = {}, token = "") {
  const init = {
    cache: "no-cache",
    method: "GET",
    mode: "cors",
    redirect: "follow",
    referrer: "no-referrer",
    timeout: 300000,
  };

  const options = {
    ...init,
    ...opt,
    headers: {
      "Content-Type": "application/json",
      ...opt.headers,
    },
  };

  if (opt.body) {
    options.data = JSON.stringify(opt.body);
  }

  let apiToken = token || g.appStore?.user?.token || "";
  if (!apiToken) {
    const user = getUser();
    apiToken = user.token || "";
    if (apiToken) {
      g.appStore.user = user;
    }
  }
  if (apiToken) {
    options.headers["token"] = apiToken;
  }

  const share_token = localStorage.getItem("share_token") || "";
  if (share_token) {
    options.headers["Authorization"] = share_token;
  }

  options.headers["Client-ID"] = g.appStore.getStore(g.cv.keyClientId) || "";

  const domain = getYxtOrgDomain();
  if (domain) {
    options.headers["yxt-orgdomain"] = domain;
  }

  return new Promise((resolve, reject) => {
    source = axios.CancelToken.source();
    options["cancelToken"] = source.token;
    // console.log('request options', url, options);
    axios(url, options)
      .then((response) => {
        if (response.status >= 200 && response.status < 300) {
          let contentType = response.headers["content-type"] || "";
          const isBlob = response.data instanceof Blob;
          if (contentType?.indexOf("officedocument") > -1 || isBlob) {
            var blob = response.data;
            var reader = new FileReader();
            reader.readAsDataURL(blob); // 转换为base64，可以直接放入a标签href
            reader.onload = function (e) {
              let downloadElement = document.createElement("a");
              downloadElement.href = e.target.result;
              downloadElement.download = `${opt.body.filename}.xlsx`;
              document.body.appendChild(downloadElement);
              downloadElement.click(); // 点击下载
              document.body.removeChild(downloadElement); // 下载完成移除元素
              window.URL.revokeObjectURL(blob); // 释放掉blob对象
              ElMessage.success("导出成功");
            };
            resolve(true);
          } else {
            resolve(response.data);
          }
        } else {
          console.log("response may error", response);
          reject(new Error(`http status:${response.status}`));
        }
      })
      .catch((e) => {
        console.log("request error", e);
        if (e?.request?.code == 401 || e.status == 401) {
          const url = `${location.origin}/#/login`;
          console.log("401 to login", url);
          g.appStore.logout();
          window.location.href = url;
        }
        reject(e);
      });
  });
}

function getFullUrl(_host, str) {
  const host = _host || `${g.config.meetApiHost}/rest`;
  if (str.indexOf("http") === 0) {
    return str;
  } else {
    return `${host}/${str}`;
  }
}

export const getHttp = (_host = "", _token = "") => {
  const cmap = ["get", "post", "put", "patch", "delete"];
  const _http = {};
  for (let fn of cmap) {
    _http[fn] = (str, data = {}, token) => {
      // 处理 GET 请求的参数
      if (fn === "get" && Object.keys(data).length > 0) {
        const params = new URLSearchParams(data).toString();
        str = `${str}${str.includes("?") ? "&" : "?"}${params}`;
        data = {}; // GET 请求清空 body
      }

      return request(
        getFullUrl(_host, str),
        {
          method: fn.toUpperCase(),
          body: data,
        },
        _token || token
      );
    };
  }
  _http["download"] = (str, data = {}, method = "POST") =>
    request(getFullUrl(_host, str), {
      method,
      body: data,
      responseType: "blob",
    });
  return _http;
};
