<template>
    <div class="la_risk_menu_wrap flex-row">
        <div :class="`lam ${activeId == tag.id ? 'active' : 'normal'}`" v-for="tag in tags" :key="tag.id"
            @click="onClick(tag)">
            {{ `${tag.name}(${tag.count})` }}
        </div>
    </div>
</template>

<script>

export default {
    data() {
        return {
            activeId: 'all',
            tags: []
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
    },
    methods: {
        init() {
            const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
            if (ar) {
                const t1 = ar.find(x => x.systemId == 104);
                const item = t1.report['风险项'] || [];
                const tags = [
                    { id: 'all', name: '全部', count: item.length },
                    { id: 'high', name: '高风险', count: item.filter(x => x['风险等级'] == '高').length },
                    { id: 'middle', name: '中风险', count: item.filter(x => x['风险等级'] == '中').length },
                    { id: 'low', name: '低风险', count: item.filter(x => x['风险等级'] == '低').length },
                ]
                this.tags = tags.filter(x => x.count > 0)
                if (this.tags.length == 0) {
                    this.$emit('callback', '')
                }
            }
        },
        onClick(tag) {
            this.activeId = tag.id;
            this.$emit('callback', tag)
        },
    }
}

</script>

<style lang="scss">
.la_risk_menu_wrap {
    .lam {
        padding: 8px 12px;
        height: 20px;
        line-height: 20px;
        border-radius: 4px;
        font-size: 14px;
        margin: 12px 12px 0 0;
        cursor: pointer;
    }

    .active {
        color: #436BFF;
    }

    .normal {
        color: #262626;
    }
}
</style>