<template>
    <div class="cmc-wrap">
        <div class="header">
            <el-icon class="back-icon" @click="handleBack">
                <ArrowLeft />
            </el-icon>
            <span>参会人属性设置</span>
        </div>

        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="客户参会人属性" name="outer">
                <FieldList formCode="OUTER_PARTNER" :fields="fieldDatas" @callback="onCallback" />
            </el-tab-pane>
            <el-tab-pane label="内部参会人属性" name="inner">
                <FieldList formCode="INTERNAL_PARTNER" :fields="fieldDatas" @callback="onCallback" />
            </el-tab-pane>
        </el-tabs>

        <DrawerFields ref="refDrawerFields" @callback="onCallback" />
    </div>
</template>

<script setup>
import { ref } from 'vue'
import DrawerFields from "./DrawerFields.vue"
import FieldList from './components/FieldList.vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { deleteFormField, updateFormField } from '@/app_admin/tools/api';
import { getFormFieldsWrap } from '@/app_admin/views/globalConfig/misc';

const fieldDatas = ref({
    INTERNAL_PARTNER: [],
    OUTER_PARTNER: []
})

const refDrawerFields = ref()

const activeName = ref('outer')
const emit = defineEmits(['callback']);

const handleBack = () => {
    emit('callback', 'back');
}

const showConfig = (data) => {
    fieldDatas.value = data;
}

const _delete = (formCode, field) => {
    ElMessageBox.confirm(
        `您确定要删除 ${field.fieldName} 吗？删除后不可恢复`,
        "删除提示",
        {
            confirmButtonText: "确认删除",
            cancelButtonText: "取消",
            type: "warning",
        }
    )
        .then(() => {
            deleteFormField(field).then(res => {
                if (res.code == 0) {
                    ElMessage.success("删除成功");
                    _reload(formCode);
                } else {
                    ElMessage.error(res.message);
                }
            });
        })
        .catch(() => { });
}
const _reload = (formCode) => {
    getFormFieldsWrap(formCode).then(res => {
        fieldDatas.value[formCode] = res;
    });
}

const _update = (formCode, field) => {
    updateFormField(field.id, field).then(res => {
        if (res.code == 0) {
            ElMessage({
                grouping: true,
                message: '修改成功',
                type: 'success'
            });
            _reload(formCode)
        } else {
            ElMessage.error(res.message);
        }
    });
}

const onCallback = (action, formCode, field) => {
    if (action == 'add') {
        refDrawerFields.value.showAdd(formCode, field)
    } else if (action == 'delete') {
        _delete(formCode, field)
    } else if (action == 'edit') {
        refDrawerFields.value.showEdit(formCode, field)
    } else if (action == 'reload') {
        _reload(formCode)
    } else if (action == 'update') {
        _update(formCode, field)
    }
}

defineExpose({
    fieldDatas,
    DrawerFields,
    refDrawerFields,
    showConfig
});
</script>

<style lang="scss" scoped>
.cmc-wrap {
    min-height: 100vh;
    padding: 16px;

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    .back-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 8px;
        cursor: pointer;

        &:hover {
            background-color: #f5f6f7;
        }
    }
}
</style>