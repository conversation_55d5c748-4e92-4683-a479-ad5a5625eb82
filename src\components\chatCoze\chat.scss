// 基础布局
.chat_wrap {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #ebf2ff;
    justify-content: center;
    align-items: center;

    // 主体内容区
    .center_box {
        width: 100%;
        max-width: 975px;
        height: 100vh;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        justify-content: center;
        align-items: center;
        background: #ebf2ff;
    }

    .msgs_list {
        flex: 1;
        width: 90%;
        overflow-y: auto;
        padding: 20px;
        position: relative;

        // 添加滚动条样式
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 2px;
        }
    }

    .btn_re_answer {
        width: fit-content;
    }
}

// 全屏状态控制
.full {
    height: 100vh;
}

.nofull {
    height: 100%;
}

// 背景装饰
.back_pic1,
.back_pic2 {
    position: absolute;
    opacity: 0.6;
    pointer-events: none;
}

.back_pic1 {
    right: 0;
    top: 0;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(var(--theme-color-rgb), 0.1) 0%, transparent 70%);
}

.back_pic2 {
    left: 0;
    bottom: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(var(--theme-color-rgb), 0.1) 0%, transparent 70%);
}

// 消息样式
.msg_ai {
    justify-content: flex-start;
    position: relative;
    margin-bottom: 24px;

    .mbody {
        border-radius: 0px 12px 12px 12px;
        background-color: var(--chat-msg-bg, #F7F8FA);
        padding: 1px 16px;
        font-size: 14px;
        line-height: 1.6;
        color: var(--chat-text-color, #262626);

        // 复制按钮
        .copy-btn {
            position: absolute;
            right: 12px;
            top: 12px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        &:hover .copy-btn {
            opacity: 1;
        }
    }

    // 问题建议区域
    .question_box {
        padding: 16px 12px;

        .q_hint {
            font-size: 14px;
            color: #8C8C8C;
            line-height: 22px;
            margin-bottom: 12px;
        }

        .q_list {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .q_answer {
                padding: 8px 16px;
                background: var(--chat-msg-bg, #F7F8FA);
                border-radius: 8px;
                font-size: 14px;
                color: #757575;
                width: fit-content;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    color: #436BFF;
                    background: rgba(67, 107, 255, 0.1);
                }
            }
        }
    }
}