import html2pdf from 'html2pdf.js'

/**
 * 将指定 DOM 节点导出为 PDF 并下载
 * @param {HTMLElement} element - 要导出的 DOM 节点
 * @param {string} filename - 下载的文件名（不含 .pdf）
 */
export default function exportElementToPDF(element, filename = 'document') {
  console.log('exportElementToPDF', filename)
  const width = element.clientWidth
  const height = element.clientHeight

  // 添加 PDF 导出时的样式处理
  const style = document.createElement('style')
  style.textContent = `
    .pdf_change_style { 
      -webkit-text-fill-color: initial !important; 
      color: #1D65FF !important; 
      background: #F1F3FB !important; 
    }
    .pdf_change_style::before {
    content: none !important;
    }
  `
  document.head.appendChild(style)

  return new Promise((resolve, reject) => {
    const opt = {
      margin: [20, 22],
      filename: `${filename}.pdf`,
      image: { type: 'jpeg', quality: 0.99 },
      html2canvas: { 
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
      },
      jsPDF: { 
        unit: 'px', 
        format: [width, height > 2000 ? 2000 : height],
        orientation: 'portrait' 
      },
      pagebreak: { 
        mode: ['avoid-all', 'css', 'legacy'],
        before: '.page-break-before',
        after: '.page-break-after',
        avoid: ['img', 'table', 'div'] // 避免在这些元素处分页
      }
    }

    html2pdf()
      .set(opt)
      .from(element)
      .save()
      .then(() => {
        // 移除临时添加的样式
        document.head.removeChild(style)
        resolve()
      })
      .catch(err => {
        // 确保在出错时也移除样式
        document.head.removeChild(style)
        console.error('导出失败:', err)
        reject(err)
      })
  })
}