<template>
    <div class="left_summary_wrap" v-ai-tip="'center'">
        <MenuTags ref="refMenuTags" @callback="cbMenuTags" />
        <div :style="leftStyle" v-if="saleTags.length == 0" class="left_main">
            <div class="over_btns">
                <btnParseSaleMail v-if="!isShowSetup && rdViewType == 'sale_meet' && needAsrMail" />
                <btnGoodsRecommend v-show="isHost && rdViewType == '需求调研报告'" />
            </div>
            <setupRole ref="refSetupRole" @callback="cbSetupRole" type="summary" />
            <div v-if="!isShowSetup">
                <SaleOverview ref="refSaleOverview" v-if="!isShowSetup && rdViewType == 'sale_meet'"></SaleOverview>
                <Overview ref="refOverview" v-if="!isShowSetup && rdViewType == 'normal_meet'"></Overview>
                <SegmentedSummary ref="refSegmentedSummary" v-if="!isShowSetup && rdViewType == 'segmented_summary'">
                </SegmentedSummary @callback="cbSegment">
                <SpeecherSummary ref="refSpeecherSummary" v-if="!isShowSetup && rdViewType == 'speecher_summary'">
                </SpeecherSummary>
                <KeyPoints ref="refKeyPoints" v-if="!isShowSetup && rdViewType == 'key_points'"> </KeyPoints>
                <MdViewer :md="md" v-if="isShowMd" :edit="isHost && enable_edit" />
                <ChartMind ref="refChartMind" v-if="rdViewType == 'chart_mind'"></ChartMind>
            </div>

            <el-empty v-show="!rdViewType">
                <template #text class="description">暂无数据</template>
            </el-empty>
        </div>
        <div v-else>
            <div v-if="isHost">
                <setupRole ref="refSetupRole" :show_hint="true" @callback="cbSetupRole" type="summary" />
            </div>
            <el-empty v-else>
                <template #text class="description">暂无数据</template>
            </el-empty>
        </div>
    </div>
</template>

<script>
import MdViewer from "@/components/Markdown/MdViewer.vue"
import CopyIcon from "@/icons/copy.vue"
import Overview from './Overview/Overview.vue'
import SaleOverview from './SaleOverview/SaleOverview.vue'
import SpeecherSummary from "./speecher_summary/SpeecherSummary.vue"
import KeyPoints from "./key_points/KeyPoints.vue"
import ChartMind from '@/app_postmeet/components/chart_mind/ChartMind.vue'
import MenuTags from './menu_tags/menuTags.vue'
import SegmentedSummary from "./segmented_summary/SegmentedSummary.vue"
import btnGoodsRecommend from "./btnGoodsRecommend/btnGoodsRecommend.vue"
import setupRole from '@/app_postmeet/components/setup_role/setupRole.vue'
import btnParseSaleMail from "./btnParseSaleMail/btnParseSaleMail.vue"


export default {
    name: 'LeftSummary',
    components: {
        Overview, SaleOverview, SpeecherSummary, KeyPoints, SegmentedSummary, MdViewer, ChartMind, MenuTags,
        btnGoodsRecommend, setupRole, CopyIcon, btnParseSaleMail
    },
    data() {
        return {
            saleReport: {},
            isHost: false,
            leftStyle: {},
            isShowSetup: false,
            needAsrMail: false,
            isShowMd: false,
            rdViewType: '',
            isSetupLoading: false,
            enable_edit: false,
            saleTags: [],
            currLabel: { systemPreset: true },
            md: '',
            mdid: ''
        }
    },
    methods: {
        cbSegment(action, data) {
            if (action == "clickLi") {
                const { item, type } = data;
                this.$refs.refOverview.clickLi(item, type)
            }
        },
        cbMenuTags(action, data) {
            if (action == "onChangeViewType") {
                const { rdViewType, from } = data;
                this.rdViewType = rdViewType;
                this.onChangeViewType(from)
            }
        },
        _setMdid() {
            const as = this.saleReport.analysisReports;
            if (as) {
                const data = as.find(x => x.label == this.rdViewType);
                this.$nextTick(() => {
                    this.mdid = data.id || '';
                    g.postmeetStore.setValue('summaryMdId', this.mdid)
                })
            } else {
                console.log('_setMdid', 'no data')
                this.mdid = ''
            }
        },
        onChangeViewType(from = '') {
            if (this.isShowSetup || !this.rdViewType || this.saleTags.length > 0) {
                return
            }
            this.isShowMd = false;
            this.$nextTick(() => {
                if (this.rdViewType == 'sale_meet') {
                    this.$refs.refSaleOverview && this.$refs.refSaleOverview.init()
                } else if (this.rdViewType == 'normal_meet') {
                    this.$refs.refOverview.init()
                } else if (this.rdViewType == 'segmented_summary') {
                    this.$refs.refSegmentedSummary.init()
                } else if (this.rdViewType == 'speecher_summary') {
                    this.$refs.refSpeecherSummary.init()
                } else if (this.rdViewType == 'key_points') {
                    this.$refs.refKeyPoints.init()
                } else if (this.rdViewType == 'chart_mind') {
                    this.$refs.refChartMind.init()
                } else if (this.rdViewType == 'meet_minutes') {
                    console.log('this.rdViewType', this.rdViewType)
                    this.isShowMd = true;
                    const as = this.saleReport.analysisReports;
                    if (as) {
                        const data = as.find(x => x.label == this.rdViewType);
                        this.$nextTick(() => {
                            this.md = data.report || '';
                        })
                    }
                } else {

                    this.isShowMd = true;
                    const as = this.saleReport.analysisReports;
                    if (as) {
                        const data = as.find(x => x.name == this.rdViewType);
                        this.$nextTick(() => {
                            this.md = data.report || '';
                        })
                    }
                }
                if (from == 'sale') {
                    this.$nextTick(() => {
                        this.btnRegen_setCurrItem(this.saleReport.analysisReports.filter(x => x.label == this.rdViewType)[0]);
                    })
                };
                this.$nextTick(() => { this._setMdid() })
            })
        },
        init(that) {
            const { isHost, leftStyle, isShowSetup, needAsrMail, isShowMd, rdViewType, isSetupLoading, saleTags, currLabel, saleReport } = that;
            this.isHost = isHost;
            this.leftStyle = leftStyle;
            this.isShowSetup = isShowSetup;
            this.needAsrMail = needAsrMail;
            this.isShowMd = isShowMd;
            this.rdViewType = rdViewType;
            this.isSetupLoading = isSetupLoading;
            this.saleTags = saleTags;
            this.currLabel = currLabel;
            this.saleReport = saleReport;
            this.$refs.refMenuTags.init(that);
            this.enable_edit = g.postmeetStore.data.enable_edit;
        },
        setupRoleInit() {
            this.$refs.refSetupRole && this.$refs.refSetupRole.init()
        },
        btnRegen_setCurrItem(_currLabelItem) {
            this.currLabel = _currLabelItem;
            this.$refs.refBtnRegen && this.$refs.refBtnRegen.setCurrItem(this.currLabel);
        },
        update_menu_type(type) {
            this.rdViewType = type;
            this.$refs.refMenuTags.update(type)
        },
        cbSetupRole(action, data) {
            if (action == "loading") {
                this.isSetupLoading = data;
                this.isShowSetup = data;
            } else if (action == 'update') {
                this._setSaleReport(data)
            }
        },
        changeMd(md) {
            this.md = md;
        }
    }
}

</script>
<style lang="scss">
.left_summary_wrap {
    margin-bottom: 20px;
}
</style>