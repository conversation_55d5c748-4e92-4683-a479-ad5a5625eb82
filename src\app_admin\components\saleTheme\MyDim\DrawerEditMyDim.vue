<template>
  <el-drawer v-model="is_show" direction="rtl" class="drawer_sale_edit_my_dim" :close-on-click-modal="true"
    @close="onCancel">
    <template #header>
      <div class="vd_title">{{ title }}</div>
    </template>
    <template #default>
      <div class="ed_main flex-row">
        <MyTable ref="refTable" :cfg="datas" @callback="cbTable">
          <template #col_ability_name="{ row }">
            {{ row['name'] }}
          </template>
          <template #col_task_name="{ row }">
            {{ row['name'] }}
          </template>
          <template #col_behavior="{ row }">
            <div v-for="(item, index) in row['details']" :key="index">
              {{ item }}
            </div>
          </template>
        </MyTable>
      </div>
    </template>
    <template #footer>
      <div class="flex-row flex-end">
        <el-button @click="onCancel">关闭</el-button>
      </div>
    </template>
  </el-drawer>
  <dialogEditMyDim ref="refDialogEditMyDim" @callback="onCallback" :systemId="domInfo.systemId" />
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { deleteCustomizeDimension } from "@/app_admin/tools/api.js";
import dialogEditMyDim from "./dialogEditMyDim.vue";
import { systemIdTypeMap } from "@/js/const_value.js";
import { getSalesAllDims } from "@/js/api.js";

const is_show = ref(false);
const refTable = ref(null);
const domInfo = ref({});
const tableDatas = ref([]);
const refDialogEditMyDim = ref(null);
let setting_column = '';
let dimension_type = '';
const emit = defineEmits(["callback", "onCancel"]);
const title = ref('')

const props = defineProps({
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const getTabledata = () => {
  getSalesAllDims().then((res) => {
    const list = res.data[setting_column].customizedAssessments || [];
    tableDatas.value = list;
    list.forEach((item) => {
      item["is_checked_"] = item["enabled"];
    });
    refTable.value.update_data(list, list.length);
  });
};

const apiDeleteWrap = (data) => {
  return new Promise((resolve, reject) => {
    deleteCustomizeDimension(dimension_type, data.id).then((resp) => {
      if (resp.code == 0) {
        getTabledata()
      }
      resolve(resp);
    })
  });
};

const datas = reactive({
  tableid: '',
  param: {
  },
  need_header: true,
  need_init_load: false,
  form: {},
  pk: "name",
  delete_hint_column: 'name',
  show_btn_add: true,
  show_search: false,
  show_btn_column: false,
  show_search_btn: false,
  show_link_column: true,
  show_link_edit: true,
  show_link_delete: true,
  show_pager: false,
  columns: ["ability_name", "behavior"],
  template: ["ability_name", "behavior"],
  urlDelete: apiDeleteWrap,
  system_id: 0,
});

const cbTable = (action, data) => {
  if (action == "init_add") {
    refDialogEditMyDim.value.show_add(dimension_type);
  } else if (action == "init_edit") {
    refDialogEditMyDim.value.show_edit(dimension_type, data);
  }
};

const onCancel = () => {
  is_show.value = false;
  emit("onCancel");
};

const onCallback = (action, data) => {
  if (action == "reload") {
    getTabledata();
  }
};

const show = (data) => {
  domInfo.value = data;
  const updateMap = {
    205: 'SALES_ABILITY_ASSESSMENT',
    206: 'TASK_COMPLETION_ASSESSMENT'
  }
  datas.columns = data.systemId == 205 ? ['ability_name', 'behavior'] : ['task_name', 'behavior'];
  datas.template = data.systemId == 205 ? ['ability_name', 'behavior'] : ['task_name', 'behavior'];
  title.value = data.systemId == 205 ? '自定义能力项' : '自定义任务项';
  dimension_type = updateMap[data.systemId];
  setting_column = systemIdTypeMap[data.systemId];
  is_show.value = true;

  nextTick(() => {
    getTabledata();
  });
};

const cancel = () => {
  is_show.value = false;
};



defineExpose({
  show,
  onCancel,
  is_show,
  datas,
  cancel,
});
</script>

<style lang="scss">
.drawer_sale_edit_my_dim {
  width: 876px !important;

  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    .ed_main {
      height: calc(100vh - 120px);
      padding: 24px;

      .scc_left {
        width: 70%;
        border-right: 1px solid #e9e9e9;
        padding: 16px;

        .dim_source_radio {
          margin-bottom: 12px;

          .is-active {
            .el-radio-button__inner {
              background-color: #fff;
              color: #436BFF;
            }
          }
        }
      }

      .scc_right {
        width: 30%;
        padding: 16px;

        .selected-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .clear-btn {
            color: #436BFF;
            cursor: pointer;
            font-size: 14px;
          }
        }

        .selected-tags {
          .tag-item {
            display: inline-flex;
            align-items: center;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 0 8px 8px 0;
            font-size: 14px;

            i {
              margin-left: 4px;
              cursor: pointer;
              font-size: 12px;
              color: #999;

              &:hover {
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}
</style>
