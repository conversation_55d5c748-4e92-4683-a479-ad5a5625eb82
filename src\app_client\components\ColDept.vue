<template>
    <el-tooltip class="ccol_dept" effect="dark" :content="props.name" placement="top-start">
        <div class="dtxt">
            {{ getLastName(props.name) }}
        </div>
    </el-tooltip>
</template>

<script setup>
const props = defineProps(['name']);

const getLastName = (name) => {
    if (!name) return '';

    const departments = name.split(',').filter(x => !!x);
    const lastNames = departments.map(dept => {
        const parts = dept.split('->');
        return parts[parts.length - 1] || '';
    });

    // 使用 Set 去重，然后转回数组并用逗号连接
    return [...new Set(lastNames)].join(', ');
}

defineExpose({ props })
</script>

<style lang="scss">
.ccol_dept {
    .dtxt {
        font-size: 14px;
        color: #262626;
    }
}
</style>