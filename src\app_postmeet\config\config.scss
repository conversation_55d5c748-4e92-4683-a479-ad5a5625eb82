$warning-color: #f5483f;
$error-color: #f5483f;
$success-color: green;
$yellow: #fc7200;
$--color-primary: #436bff !default;

body,
html {
    line-height: calc(100% + 8px);
    font-family: -apple-system, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", <PERSON><PERSON><PERSON>, Simsun, sans-serif;
    font-size: 14px;
}

ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.el-button--primary {
    background-color: #436bff;
    border: none;
}

.input {
    outline: none
}

.el-switch.is-checked .el-switch__core {
    background: #436bff;
    border-color: #436bff;
}

.el-button--primary:focus,
.el-button--primary:hover {
    background: #436bff;
    border-color: #436bff;
    color: #FFF;
}

.el-button--default:hover {
    background: #f0f0f0;
    border-color: #d9d9d9;
    color: #262626;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
    color: unset;
    background-color: #f5f5f5;
}

.fold_icon {
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flexgrow {
    flex-grow: 1;
}

.fold_icon:hover {
    color: #436BFF;
}

.el-dialog {
    .el-dialog__header {
        border-bottom: 1px solid #d9d9d9;
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #d9d9d9;
    }
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-grow {
    flex-grow: 1;
}

.el-slider__bar {
    background-color: #436BFF;
}

.el-popover {
    min-width: unset !important;
}

.el-tabs__item {
    user-select: none;
}

.d_internal {
    background-color: #436BFF;
}

.d_customer {
    background-color: #52C41A;
}

.d_na {
    background-color: #FA541C;
}


.d_gray {
    background-color: #8c8c8c;
}

.t_internal {
    color: #436BFF;
}

.t_customer {
    color: #52C41A;
}

.t_na {
    color: #FA541C;
}

.el-tabs__nav-wrap::after {
    background-color: #fff !important;
}

.s0 {
    color: #FF4D4F;
}

.s1 {
    color: #436BFF;
}

.s2 {
    color: #52C41A;
}