<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="智能商品推荐师2期" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="智能产品推荐师-已选" transform="translate(-564.000000, -728.000000)">
                <g id="编组-8备份" transform="translate(330.000000, 728.000000)">
                    <g id="警示" transform="translate(234.000000, 0.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path
                            d="M8,0 C3.58125,0 0,3.58125 0,8 C0,12.41875 3.58125,16 8,16 C12.41875,16 16,12.41875 16,8 C16,3.58125 12.41875,0 8,0 L8,0 Z"
                            id="Path" fill="#C2C5DD"></path>
                        <path
                            d="M4.25341729,4.25341729 C4.51558744,3.99124714 4.92811912,3.9710802 5.21342182,4.19291649 L5.28193624,4.25341729 L11.7465827,10.7180638 C12.0306004,11.0020814 12.0306004,11.462565 11.7465827,11.7465827 C11.4844126,12.0087529 11.0718809,12.0289198 10.7865782,11.8070835 L10.7180638,11.7465827 L4.25341729,5.28193624 C3.96939962,4.99791858 3.96939962,4.53743496 4.25341729,4.25341729 Z"
                            id="直线" fill="#FFFFFF" fill-rule="nonzero"></path>
                        <path
                            d="M4.25341729,4.25341729 C4.51558744,3.99124714 4.92811912,3.9710802 5.21342182,4.19291649 L5.28193624,4.25341729 L11.7465827,10.7180638 C12.0306004,11.0020814 12.0306004,11.462565 11.7465827,11.7465827 C11.4844126,12.0087529 11.0718809,12.0289198 10.7865782,11.8070835 L10.7180638,11.7465827 L4.25341729,5.28193624 C3.96939962,4.99791858 3.96939962,4.53743496 4.25341729,4.25341729 Z"
                            id="直线" fill="#FFFFFF" fill-rule="nonzero"
                            transform="translate(8.000000, 8.000000) rotate(-90.000000) translate(-8.000000, -8.000000) ">
                        </path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>
