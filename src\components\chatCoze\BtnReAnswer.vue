<template>
    <div class="reanswer_btn" @click="onReAnswer">
        <div class="ra_left">
            <ReloadIcon />
        </div>
        <div class="ra_right">
            重新回答
        </div>
    </div>
</template>

<script setup>
import ReloadIcon from "@/icons/reload.vue"
const emit = defineEmits(['callback'])

const onReAnswer = () => {
    emit('callback', 're_answer')
}

defineExpose({ onReAnswer, ReloadIcon })

</script>

<style lang="scss">
.reanswer_btn {
    background: #FFFFFF;
    width: 100px;
    height: 32px;
    border-radius: 16px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .ra_left {
        width: 10px;
        height: 10px;
        border-radius: 2px;
        margin-right: 10px;
        margin-bottom: 6px;
    }

    .ra_right {
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 22px;
    }
}
</style>