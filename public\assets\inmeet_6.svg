<?xml version="1.0" encoding="UTF-8"?>
<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>日历</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#889FFB" offset="0%"></stop>
            <stop stop-color="#5066F6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页" transform="translate(-25.000000, -239.000000)" fill-rule="nonzero">
            <g id="编组-6" transform="translate(15.000000, 225.000000)">
                <g id="日历icon" transform="translate(10.000000, 14.000000)">
                    <g id="编组-5" transform="translate(0.000000, 0.500000)">
                        <path d="M3.75,0.785714293 L8.25,0.785714293 L8.25,0 L9.75,0 L9.75,0.785714293 L12,0.785714293 L12,11 L0,11 L0,0.785714293 L2.25,0.785714293 L2.25,0 L3.75,0 L3.75,0.785714293 Z M1.5,2.35714286 L1.5,9.42857144 L10.5,9.42857144 L10.5,2.35714286 L1.5,2.35714286 Z" id="形状" fill="url(#linearGradient-1)"></path>
                        <path d="M4.5,3.14285715 L4.5,4.71428572 L2.25,4.71428572 L2.25,3.14285715 L4.5,3.14285715 L4.5,3.14285715 Z M4.5,5.14285715 L4.5,6.71428572 L2.25,6.71428572 L2.25,5.14285715 L4.5,5.14285715 L4.5,5.14285715 Z M7.5,3.14285715 L7.5,4.71428572 L5.25,4.71428572 L5.25,3.14285715 L7.5,3.14285715 L7.5,3.14285715 Z" id="形状结合" fill="#72E4FE" style="mix-blend-mode: multiply;"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>