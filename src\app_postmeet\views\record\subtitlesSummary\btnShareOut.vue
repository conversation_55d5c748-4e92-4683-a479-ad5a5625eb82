<template>
  <div class="btn_share_out_wrap">
    <el-button size="small" type="primary" @click="onShare">分享给外部用户</el-button>
    <el-dialog title="分享给外部用户" v-model="isShowModal" width="745px" :append-to-body="true" :modal-append-to-body="false"
      class="share_modal_wrap">
      <div class="shour_out_main">
        <div class="so_item">
          <div class="so_title">访问地址:</div>
          <div class="so_value" @click="copyLink">{{ getShareUrl() }}</div>
        </div>
        <div class="so_item">
          <div class="so_title">访问密码:</div>
          <div class="so_value" @click="copyPwd">{{ shareInfo.shareCode }}</div>
        </div>
      </div>
      <template #footer class="dialog-footer">
        <el-button @click="isShowModal = false">关闭</el-button><el-button type="primary" @click="onCopy">复制</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import { shareGetId, shareUpdatePwd } from '@/app_postmeet/tools/api'
import { randomString } from "@/app_postmeet/tools/tools"
export default {
  name: 'BtnShareOut',
  data() {
    return {
      isShowModal: false,
      confId: '',
      shareInfo: { shareCode: '', shareId: '' }
    }
  },
  mounted() {
    this.confId = this.$route.params.confId
  },
  methods: {
    getShareUrl() {
      return `${location.origin}/postmeet/record/${this.confId}`;
    },
    onCopy() {
      const clipInfo = `云录制分享链接：${this.getShareUrl()} 
密码：${this.shareInfo.shareCode}`
      g.appStore.doCopy(clipInfo, '分享链接已复制到剪切板！')
    },
    copyLink() {
      g.appStore.doCopy(this.getShareUrl(), '分享链接已复制到剪切板！')
    },
    copyPwd() {
      g.appStore.doCopy(this.shareInfo.shareCode, '分享密码已复制到剪切板！')
    },
    onShare() {
      shareGetId(this.confId).then(resp => {
        if (resp.code == 0) {
          if (resp.data.hasOwnProperty("shareId")) {
            this.shareInfo = resp.data
            this.isShowModal = true;
          } else {
            const code = randomString(4)
            shareUpdatePwd(this.confId, code).then(resp2 => {
              this.shareInfo = {
                shareCode: code,
                shareId: resp2.data
              }
              this.isShowModal = true;
            }).catch(e => {
              console.log('error2', e)
            })
          }
        }
      }).catch(e => {
        console.log('shareGetId fail', e)
      })
    }
  }
}
</script>

<style lang='scss'>
.shour_out_main {
  .so_item {
    display: flex;

    .so_title {
      padding-top: 2px;
      line-height: 25px;
    }

    .so_value {
      padding-top: 3px;
      line-height: 25px;
      padding-left: 9px;
      cursor: pointer;
    }

    input {
      margin-left: 8px;
      line-height: 25px;
    }
  }
}
</style>
