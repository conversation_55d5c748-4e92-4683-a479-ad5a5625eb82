<template>
    <Premeet v-if="page === 'premeet'" ref="refPremeet" @callback="cbMeet" />
    <Inmeet v-else ref="refInmeet" @callback="cbMeet" />
    <EndMeetDialog v-model="dialogVisible" :ongoing-meet-subject="ongoingMeetSubject" @end-meet="handleEndMeet" />
</template>

<script setup>
import Premeet from './Premeet.vue';
import Inmeet from './inmeet/Inmeet.vue';
import EndMeetDialog from '@/app_electron/components/EndMeetDialog.vue';
import { reloadSchedule } from '@/app_electron/tools/utils';

const refPremeet = ref(null);
const refInmeet = ref(null);
const plan = ref({})
const page = ref('premeet')
const dialogVisible = ref(false)
const ongoingMeetSubject = ref('')
const meetType = ref('computer') // 电脑拜访
let startData = {}

const cbMeet = async (action, data) => {
    if (action === 'start_computer') {
        startData = data;
        refPremeet.value.setLoading(true)
        meetType.value = 'computer'
        g.meetStore.createMeeting(toRaw(plan.value), data).then(status => {
            refPremeet.value.setLoading(false)
            console.log('startMeeting status', status)
            if (status) {
                page.value = 'inmeet';
                nextTick(() => {
                    reloadSchedule()
                    refInmeet.value.startMeeting()
                })
            } else {
                const errorId = g.meetStore.errorId;
                console.log('errorId', errorId)
                if (errorId === 1653) {
                    // 您有拜访正在进行
                    confirmStopOtherMeeting(g.meetStore.meetingInfo)
                } else {
                    const error = g.meetStore.error || '启动录制失败，请检查您的网络或稍后再试！';
                    if (error == 'Network Error') {
                        refPremeet.value.setError('网络错误，请检查您的网络或稍后再试！')
                    } else {
                        refPremeet.value.setError(error)
                    }
                }

            }
        }).catch(err => {
            refPremeet.value.setError('启动录制失败，请检查您的网络或稍后再试！')
        })
    } else if (action === 'start_card') {
        console.log('start_card')
        const scheduleId = plan.value.scheduleId || '';
        g.meetStore.openCardMeetWindow(scheduleId, false);
        reloadSchedule()
        g.electronStore.closeWin('meet');
    } else if (action === 'stop') {
        g.ipcRenderer.send('update_closeable', 'meet', true)
        setTimeout(() => {
            g.electronStore.closeWin('meet_setting')
            g.electronStore.closeWin('rename_speaker')
            reloadSchedule()
            g.electronStore.closeWin('meet');
        }, 100)
    }
}



const confirmStopOtherMeeting = async (res) => {
    dialogVisible.value = true
    ongoingMeetSubject.value = res?.data?.subject || ''
};

const handleEndMeet = async () => {
    if (meetType.value === 'card') {
        if (await g.meetStore.endCardMeeting()) {
            dialogVisible.value = false
            reloadSchedule()
            cbMeet('start_card', {})
        } else {
            dialogVisible.value = false;
            ElMessage.error('结束拜访失败，请稍后重试')
        }
    } else {
        let status = await g.meetStore.endOrderMeeting()
        if (status) {
            setTimeout(() => {
                dialogVisible.value = false
                cbMeet('start_computer', startData)
            }, 1000)
        } else {
            ElMessage.error('结束拜访失败，请稍后重试')
            setTimeout(() => {
                cbMeet('stop')
            }, 1000)
        }
    }
}


watch(page, (newVal) => {
    if (newVal === 'premeet') {
        g.electronStore.closeWin('meet_setting');
        g.electronStore.closeWin('rename_speaker');
    }
})

onMounted(() => {
    g.ipcRenderer.send('update_closeable', 'meet', true)
    g.electronStore.waitPageReady().then(data => {
        g.appStore.reloadUserInfo().then((status) => {
            if (!status) {
                g.electronStore.logout()
            } else {
                if (data.fromStore) {
                    // 从本地存储恢复会议
                    g.meetStore.restoreMeetingInfo();
                    plan.value = g.meetStore.plan;
                    cbMeet('start_computer', g.meetStore.meetingOptions);
                } else {
                    plan.value = data
                    g.meetStore.setPlan(data)
                    refPremeet.value.init()
                }
            }
        })
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

onBeforeUnmount(() => {
    // 确保在组件销毁时清理所有资源
    g.meetStore.cleanup()
})

defineExpose({
    page,
    Premeet,
    Inmeet,
    refPremeet,
    refInmeet
});
</script>

<style lang="scss">
.electron-layout {
    .mac-titlebar {
        background: #E9F1FF;
    }
}

.el-message {
    width: 90%;
}
</style>
