<template>
    <div class="sg_box">
        <MyTable ref="refTable" :cfg="datas" @callback="cbTable">
            <template #_header_left>
                <el-select v-model="selectCate" placeholder="请选择" class="sg_select" @change="onCateChange">
                    <el-option v-for="item in categoryTemplates" :key="item.categoryId" :label="item.categoryName"
                        :value="item.categoryId">
                    </el-option>
                </el-select>
            </template>
        </MyTable>
        <div class="br_bottom">
            <div class="br_info">
                {{ check_hint }}
            </div>
            <div class="br_space">
            </div>
            <el-button type="default" @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </div>
        <GoodsDetail ref="refDetail" />
    </div>
</template>

<script setup>
import { getCategoryGoods, getGoodsDetails } from "@/js/api"
import { mergeColumnsAndData, calcCategoryIdNameMap } from "@/js/utils.js"
import MyTable from "@/components/Table.vue"
import GoodsDetail from "./goodsDetail.vue"

const emit = defineEmits(['callback'])
const selectCate = ref('')
const check_hint = ref("已选 0/10")
const preGoodsIds = ref([])
const checked_data = ref([])
const titles = ref([])
const cateIdNameMap = ref({})
const categoryTemplates = ref([{ categoryId: '0', categoryName: '全部选择' }])
const refTable = ref(null)
const refDetail = ref(null)
const tableData = ref([])

// 改用 ref 变量
const topicId = ref('')
const preChoosedIds = ref([])

// 添加一个标记变量，用于标识是否正在切换页面
const isPageChanging = ref(false)

// 在 script setup 顶部添加节流函数
const throttleMessage = (function () {
    let timer = null;
    return function (message, type = 'warning') {
        if (!timer) {
            ElMessage({
                message,
                type
            });
            timer = setTimeout(() => {
                timer = null;
            }, 2000); // 2秒内只显示一次
        }
    }
})();

// 原来的所有方法保持不变...
const getApiData = (p) => {
    return new Promise((resolve) => {
        isPageChanging.value = true  // 标记开始切换页面
        const param = {
            ...{
                "searchKey": '',
                "pageSize": 10,
                "pageNumber": 1
            },
            ...p
        }
        getCategoryGoods(selectCate.value, param).then((resp) => {
            if (resp.code == 0) {
                titles.value = resp.data.title
                datas.value.columns = [...titles.value.slice(0, 2), '所属分类']
                tableData.value = mergeColumnsAndData(resp.data.title, cateIdNameMap.value, resp.data.datas)
                datas.value.columns = titles.value
                refTable.value.cbColumn(titles.value)
                refTable.value.update_data(tableData.value, resp.data.totalNum)

                // 在数据更新后，重新设置选中状态
                nextTick(() => {
                    const currentCheckedIds = checked_data.value.map(x => x.id)

                    refTable.value.manualCheck(currentCheckedIds)
                    isPageChanging.value = false  // 标记页面切换完成
                    resolve(true)
                })
            } else {
                isPageChanging.value = false
                resolve(false)
            }
        }).catch(e => {
            console.error('error getApiData', e)
            isPageChanging.value = false
            resolve(false)
        })
    })
}

const datas = ref({
    tableid: 'choose_sale_goods',
    pk: "id",
    param: {},
    need_header: true,
    need_init_load: false,
    show_btn_column: false,
    form: {},
    modal_type: "link",
    show_btn_add: false,
    enable_checkbox: true,
    view_txt: "查看详情",
    show_link_column: true,
    show_link_view: true,
    urlGet: getApiData,
    columns: [],
})

const onCancel = () => {
    emit('callback', 'cancel')
}

const onConfirm = () => {
    const data = toRaw(checked_data.value).map(x => toRaw(x))
    emit('callback', 'confirm', data)
}

// 修改 init 方法，接收参数
const init = async (param) => {
    topicId.value = param.topicId
    preChoosedIds.value = param.preGoodsIds
    if (await getCategorys()) {
        if (!await getApiData({})) {
            console.log('fail3')
        }
    } else {
        console.log('fail1')
    }
}

const onCateChange = () => {
    getApiData()
}


// 修改 getCategorys 方法，使用 topicId.value
const getCategorys = () => {
    return new Promise((resolve) => {
        g.cacheStore.getXMTopicDetail(topicId.value).then((templates) => {
            if (templates.length > 0) {
                cateIdNameMap.value = calcCategoryIdNameMap(templates)
                categoryTemplates.value = templates
                if (templates.length > 0) {
                    selectCate.value = templates[0].categoryId
                }
                resolve(true)
            } else {
                resolve(false)
            }
        }).catch(e => {
            console.log('error getXMTopicDetail', e)
            resolve(false)
        })
    })
}

const setChecked = (data) => {
    preGoodsIds.value = data
    check_hint.value = `已选 ${data.length}/10`
    _queryGoodsDetail()
    if (refTable.value) {
        refTable.value.manualCheck(preGoodsIds.value)
    }
}

const _queryGoodsDetail = () => {
    const ids = preGoodsIds.value
    if (ids.length > 0) {
        getGoodsDetails(ids).then((resp) => {
            try {
                if (resp.code == 0) {
                    const { datas, header } = resp.data
                    if (datas && datas.length > 0) {
                        const list = mergeColumnsAndData(header, toRaw(cateIdNameMap.value), datas)
                        checked_data.value = list
                    } else {
                        checked_data.value = []
                    }
                }
            } catch (e) {
                console.log('getGoodsDetails error', e)
                checked_data.value = []
            }
        })
    } else {
        checked_data.value = []
    }
}

const cbTable = (action, data) => {
    if (action == "check_row") {
        const { checked, unchecked } = data

        // 如果是页面切换中，不处理选中状态的变化
        if (isPageChanging.value) {
            return
        }

        // 保存当前页面选中的数据
        let newCheckedData = [...checked_data.value]

        // 添加新选中的数据
        checked.forEach(item => {
            if (!newCheckedData.find(x => x.id === item.id)) {
                newCheckedData.push(item)
            }
        })

        // 移除取消选中的数据
        if (unchecked.length > 0) {
            newCheckedData = newCheckedData.filter(item =>
                !unchecked.find(x => x.id === item.id)
            )
        }

        if (newCheckedData.length > 10) {
            throttleMessage('选择不能超过10条记录')
            const currentCheckedIds = checked_data.value.map(x => x.id)
            refTable.value.manualCheck(currentCheckedIds)
        } else {
            checked_data.value = newCheckedData
            preGoodsIds.value = newCheckedData.map(x => x.id)
            check_hint.value = `已选 ${newCheckedData.length}/10`
        }
    } else if (action == 'init_view') {
        refDetail.value.show(data)
    } else if (action == 'try_search') {
        const { searchKey } = refTable.value.config.param
        getApiData({ searchKey })
    }
}

// 监听 props 变化
watch(() => preChoosedIds.value, (newVal) => {
    if (newVal && newVal.length) {
        preGoodsIds.value = newVal
        nextTick(() => {
            if (refTable.value) {
                setChecked(newVal)
            }
        })
    }
}, { immediate: true })

defineExpose({
    init,
    setChecked
})
</script>

<style lang="scss">
.sg_box {
    display: flex;
    flex-direction: column;
    min-width: 600px;

    .table_wrap {
        width: 100%;

        .el-table__header-wrapper {
            .el-checkbox {
                display: none;
            }
        }

        .sg_select {
            margin-right: 12px;
            width: 200px;
        }

        // .el-input {
        //     width: 200px;
        // }

        .table_class {
            height: 400px !important;

            .el-table__row {
                .cell {
                    div {
                        width: 118px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    .br_bottom {
        display: flex;
        flex-direction: row;
        padding: 0 27px;

        .br_space {
            flex-grow: 1;
        }
    }
}
</style>