<template>
    <div :class="`text-wrapper_17 flex-col ${props.choosed == 6 ? 'active' : ''}`" @click="onClick(6)">
        <span class="text_40">需求调研报告</span>
        <dashline />
        <div class="flex-col marginl7">
            <page :id="6" />
            <div class="image-wrapper_1 flex-row" v-if="props.choosed">
                <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
            </div>
        </div>
    </div>

</template>

<script setup>
import dashline from './dashline.vue';
import page from '../pages/page.vue';
const props = defineProps(['choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])
import { r_png } from './image.js';

const onClick = (value) => {
    if (props.enableChoose) {
        emit('update:choosed', value)
    }
}

defineExpose({ onClick, page, dashline })

</script>
