import { AVLocalRecorder } from './recordHelper';
import { pathJoin, now } from '@/js/utils';

let avlocalRecord = null;
let filePath = ''
let desktopStream = null;
let macAudioDesktopStream = null;
let micStream = null;
let recordStream = null;
let options = null;

// 获取录制文件名
function getRecordName() {
    const record_base_path = options.recording.localRecordingPath;
    const path_date_name = now('YYYYMMdd');

    const fs = window.nodeRequire('fs');
    // 确保目录存在
    const dir = pathJoin(record_base_path, path_date_name);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    let subject = g.meetStore.plan?.subject || '即时录制'
    //把空格换成_
    subject = subject.replace(/\s+/g, '_');
    const name = `${subject}_${now('YYYYMMdd_hhmmss')}.mp4`;
    return pathJoin(record_base_path, path_date_name, name)
}

// 获取桌面应用源
async function getDesktopAppSource() {
    return new Promise(async (resolve) => {
        const ipcRenderer = window.nodeRequire("electron").ipcRenderer;
        const hasPermission = await ipcRenderer.invoke("ensurePermissions", 'screen')
        if (hasPermission) {
            const sources = await ipcRenderer.invoke("get_desktopCapturer_sources", '')
            resolve(sources[0])
        } else {
            resolve(null)
        }
    })
}

// 获取系统音频流
const getSystemAudioStream = async () => {
    return new Promise(async (resolve) => {
        if (g.config.isMac) {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioVirtual = devices.find(x => x.kind === 'audioinput' && x.label.toLowerCase().includes('xuanxingaudiodevice'));
                if (audioVirtual) {
                    macAudioDesktopStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            deviceId: audioVirtual.deviceId,
                            advanced: [
                                { noiseSuppression: false },
                                { autoGainControl: true },
                                { echoCancellation: true },
                            ]
                        },
                        video: false
                    });
                    resolve(macAudioDesktopStream)
                } else {
                    resolve(null)
                }
            } catch (error) {
                console.error('Mac system audio capture error:', error);
                resolve(null)
            }
        } else {
            const stream = await getDesktopAppStream(options)
            if (stream.getAudioTracks().length > 0) {
                resolve(stream)
            } else {
                resolve(null)
            }
        }
    })
}



// 获取录制选项
async function getRecordOption() {
    let autioOption = false;
    let videoOption = false;

    if (options.audio.recordSystemAudio) {
        if (g.config.isMac) {
            autioOption = false;
        } else {
            autioOption = {
                mandatory: {
                    chromeMediaSource: 'desktop',
                    noiseSuppression: false,
                    autoGainControl: true,
                    echoCancellation: true
                },
            }
        }
    };

    // 目前发现，windows及时只录声音，也必须录制桌面应用，mac可以不录制桌面应用
    let app = await getDesktopAppSource()
    videoOption = {
        mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: app.id,
        }
    }

    const option = {
        audio: autioOption,
        video: videoOption
    }
    return option;
}

// 获取桌面应用流
function getAppStream() {
    return new Promise(async (resolve, reject) => {
        try {
            // 创建新的 MediaStream
            const combinedStream = new MediaStream();

            if (options.recording.recordSystemVideo) {
                // 添加视频轨道
                desktopStream = await getDesktopAppStream(options)
                desktopStream.getVideoTracks().forEach(track => {
                    combinedStream.addTrack(track);
                });
            }

            // 合并音频轨道并添加
            const combinedAudioTrack = await combineAudioTracks(options);
            combinedStream.addTrack(combinedAudioTrack);

            resolve(combinedStream);
        } catch (error) {
            g.elog.error("Error getting app stream:", error);
            reject(error);
        }
    })
}

// 合并桌面音频和麦克风音频
export const combineAudioTracks = async (_options) => {
    options = _options
    try {
        micStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                deviceId: options.audio.audioInputDevice ? { exact: options.audio.audioInputDevice } : undefined,
            },
            video: false
        });

        // 创建新的 AudioContext
        if (!window.audioContext) {
            window.audioContext = new AudioContext();
        }
        const destination = window.audioContext.createMediaStreamDestination();

        // 处理麦克风声音
        if (micStream.getAudioTracks().length > 0) {
            const micSource = window.audioContext.createMediaStreamSource(micStream);
            micSource.connect(destination);
        }

        // 处理系统声音
        if (options.audio.recordSystemAudio) {
            const systemAudioStream = await getSystemAudioStream();
            if (systemAudioStream) {
                const systemAudioSource = window.audioContext.createMediaStreamSource(systemAudioStream);
                systemAudioSource.connect(destination);
            }
        }

        return destination.stream.getAudioTracks()[0];
    } catch (error) {
        g.elog.error("Error combining audio tracks:", error);
        throw error;
    }
}

// 开始录制
export const startLocalRecorder = async (_options) => {
    if (!g.config.isElectron) return false
    if (!avlocalRecord) avlocalRecord = new AVLocalRecorder();
    options = _options;
    return new Promise(async (resolve) => {
        try {
            recordStream = await getAppStream();
            const streamType = recordStream.getVideoTracks().length > 0 ? "display" : "mic";
            if (avlocalRecord.getState() === 'inactive') {
                filePath = getRecordName()
                g.elog.log("startLocalRecorder", filePath, streamType)
                await avlocalRecord.updateStream(recordStream, streamType);
                await avlocalRecord.startRecording(filePath);

                // 发送录制状态
                g.emitter.emit('recordingStatus', 'recording');
            } else {
                await avlocalRecord.updateStream(recordStream, streamType);
            }
            resolve(true);
        } catch (error) {
            g.elog.error("Recording error:", error);
            resolve(false);
        }
    });
}

// 暂停录制
export const pauseLocalRecord = () => {
    // 停止音频分析
    return new Promise((resolve) => {
        try {
            avlocalRecord.pauseRecording();
            resolve(true);
        } catch (error) {
            g.elog.error('pauseLocalRecord error', error)
            resolve(false);
        }
    })
}

// 恢复录制
export const resumeLocalRecord = () => {
    return new Promise((resolve) => {
        try {
            avlocalRecord.resumeRecording();
            resolve(true);
        } catch (error) {
            g.elog.error('resumeLocalRecord error', error)
            resolve(false);
        }
    })
}

// 停止录制
export const stopLocalRecorder = () => {
    return new Promise((resolve) => {
        try {
            if (avlocalRecord.getState() !== 'inactive') {
                // 停止录制
                avlocalRecord.stopRecording()
                //TODO 暂时不启用上传功能。
                //g.ipcRenderer.send('process-recording', filePath, options.recording.recordSystemVideo, g.meetStore.conferenceId)
                g.ipcRenderer.send('process-recording', filePath, options.recording.recordSystemVideo)

                if (desktopStream) {
                    desktopStream.getTracks().forEach(track => track.stop())
                    desktopStream = null
                }

                if (micStream) {
                    micStream.getTracks().forEach(track => track.stop())
                    micStream = null
                }
                if (recordStream) {
                    recordStream.getTracks().forEach(track => track.stop())
                    recordStream = null
                }

                if (g.config.isMac && options.audio.recordSystemAudio && macAudioDesktopStream) {
                    macAudioDesktopStream.getTracks().forEach(track => track.stop())
                    macAudioDesktopStream = null
                }

                // 关闭 AudioContext
                if (window.audioContext) {
                    window.audioContext.close()
                    window.audioContext = null
                }

                g.ipcRenderer.send('kill-virtual-audio')

                // 清理文件路径
                filePath = ''
            }
            resolve(true)
        } catch (error) {
            g.elog.error('Error stopping recorder:', error)
            resolve(false)
        }
    })
}


export const getDesktopAppStream = async (_options) => {
    return new Promise(async (resolve) => {
        if (desktopStream) {
            resolve(desktopStream)
        } else {
            options = _options;
            const option = await getRecordOption()
            desktopStream = await navigator.mediaDevices.getUserMedia(option)
            resolve(desktopStream)
        }
    })
}
