<template>
    <div class="log_app_wrap">
        <MyTable ref="refTable" :cfg="datas">
            <template #_header_left>
                <div class="ta_search_item">
                    <div class="input_title">时间类型</div>
                    <el-select v-model="datas.param.timeType" class="sel_time" placeholder="请选择"
                        @change="onChangeTimeType">
                        <el-option v-for="item in optionsTime" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
                <div class="ta_search_item">
                    <div class="input_title">时间范围</div>
                    <DatePicker ref="refDatePicker" @callback="cbChangeDateRange" />
                </div>
                <div class="ta_search_item">
                    <div class="input_title">应用类型</div>
                    <el-select v-model="selectApp" class="sel_app" placeholder="所有类型" @change="onChangeApp">
                        <el-option v-for="item in optionsApp" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
                <div class="ta_search_item">
                    <div class="input_title">选择部门</div>
                    <DrawerSelectDept ref="refTree" @callback="cbTree" />
                </div>

                <div class="txt_btn" @click="onReset">
                    重置筛选
                </div>

            </template>
            <template #_header_right>
                <el-button type="default" @click="onExport">导出</el-button>
            </template>

            <template #col_deptName="scope">
                {{ deptIdNames[scope.row.departmentId] }}
            </template>
        </MyTable>
    </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue"
import DatePicker from "@/app_admin/components/DatePicker.vue"
import DrawerSelectDept from "@/components/DrawerSelect/DrawerSelectDept.vue"
import { nowFormat, apiHintWrap, calcDays, autoDate } from "@/app_admin/tools/utils.js"
import {
    timeTypes, timeTypeRangeMap, getSpanColumns,
    getSpanKeyColumn, getTableColumns, getAppOptions
} from "./const_value.js"
import { getLandingExport } from "@/app_admin/tools/api.js"
import { nextTick, toRaw } from "vue"
import { ElMessage } from "element-plus"

const props = defineProps(['type']);
const emit = defineEmits(['callback']);
const refTable = ref();
const refDatePicker = ref('')
const selectApp = ref('')
const refTree = ref()
const deptIdNames = ref({})
const optionsApp = ref([])
const optionsTime = ref(timeTypes)

const defaultParam = {
    startTime: nowFormat(-30),
    endTime: nowFormat(0),
    reportType: props.type,
    timeType: 'ACCUMULATE',
    departmentIds: [],
    applications: [],
    searchKey: '',
    showEmpty: false,
}

const _checkFilter = (param) => {
    const days = calcDays(param.startTime, param.endTime);
    if (days > 366) {
        ElMessage.warning(`时间跨度不能超过一年`);
        return false
    } else {
        return true
    }
}

const onQuery = () => {
    if (!refTable.value || defaultParam.applications.length == 0) return;
    const param = refTable.value.getSearchParam()

    if (param.applications.length == 0) {
        param.applications = defaultParam.applications
    }

    if (_checkFilter(param)) {
        refTable.value.setLoading(true);
        g.usageStore.getLandingData(param, defaultParam.applications).then(({ data, count }) => {
            deptIdNames.value = g.cacheStore.deptIdNames
            if (!refTable.value) {
                return
            }
            refTable.value.update_data(data, count)
            refTable.value.setLoading(false);
        })
    }
}

const datas = reactive({
    tableid: 'usage_main',
    param: { ...defaultParam },
    need_header: true,
    need_init_load: false,
    form: {},
    modal_type: "link",
    search_txt: "查询",
    show_btn_add: false,
    show_search: false,
    show_search_btn: true,
    show_link_column: false,
    columns: getTableColumns(defaultParam),
    columns_span: [],
    column_key_span: [],
    pageQuery: onQuery,
});


const _set_columns = (param) => {
    datas.columns = getTableColumns(param)
    datas.columns_span = getSpanColumns(param)
    datas.column_key_span = getSpanKeyColumn(param)
}

const onChangeTimeType = () => {
    const type = timeTypeRangeMap[datas.param.timeType];
    const [start, end] = autoDate(type, datas.param);
    datas.param.startTime = start;
    datas.param.endTime = end;
    refDatePicker.value.setRangeType(type, start, end)
    _set_columns(datas.param)
    onQuery()
}

const cbChangeDateRange = (dates) => {
    datas.param.startTime = dates[0]
    datas.param.endTime = dates[1]
    _set_columns(datas.param)
    onQuery()
}

const onChangeApp = (name) => {
    datas.param.applications = name ? [name] : []
    onQuery()
}

const cbTree = (ids) => {
    datas.param.departmentIds = ids;
    onQuery()
}

const onExport = () => {
    const param = toRaw(datas.param);
    if (_checkFilter(param)) {
        param['showEmpty'] = true;
        const filename = `xmate${props.type == 'APPLICATION' ? '应用' : '个人'}使用情况报表`
        getLandingExport(filename, param)
    }
}

const onReset = () => {
    datas.param = { ...defaultParam }
    selectApp.value = ''
    refTree.value.reset()
    onChangeTimeType()
}

const init = () => {
    if (props.type == 'USER') {
        refTable.value.cfg.search_ph = '请输入姓名/账号'
        refTable.value.cfg.show_search = true
    } else {
        refTable.value.cfg.show_search = false
    }
    const apps = g.clientStore.getMenuList().data.map(x => x.title)
    defaultParam.applications = apps
    _set_columns(defaultParam)
    nextTick(() => {
        onChangeTimeType()
        optionsApp.value = getAppOptions(apps)
    })
}


defineExpose({
    MyTable, datas, refTable, optionsTime, optionsApp, DrawerSelectDept, DatePicker, refTree, deptIdNames, init,
    onExport, onQuery, onChangeTimeType, cbChangeDateRange, onChangeApp
})

</script>

<style lang="scss">
@media screen and (max-width: 1529px) {
    .log_app_wrap {
        .table_wrap {
            .table_box .table_class {
                height: calc(100vh - 264px) !important;
            }
        }
    }
}

@media screen and (min-width: 1529px) {
    .log_app_wrap {
        .table_wrap {
            .table_box .table_class {
                height: calc(100vh - 229px) !important;
            }
        }
    }
}

.log_app_wrap {
    .table_wrap {
        padding: 12px 24px;
    }

    .ta_search_item {
        padding: 0 12px;
        display: flex;

        .input_title {
            line-height: 40px;
            min-width: 50px;
            margin-right: 12px;
            font-size: 14px;
            color: #262626;
        }

        .sel_time {
            width: 100px;
        }

        .sel_app {
            width: 165px;
        }

        .dept_box {
            border: 1px solid #e9e9e9;
            border-radius: 3px;
            width: 120px;
        }

        .el-select,
        .el-date-editor,
        .dept_input_box {
            margin-top: 5px;
        }

    }

    .txt_btn {
        font-size: 14px;
        font-weight: 400;
        color: #436BFF;
        cursor: pointer;
        margin-top: 5px;
    }
}
</style>