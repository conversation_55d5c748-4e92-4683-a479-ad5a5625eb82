<template>
    <svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="lgsend-1">
                <stop stop-color="#6B90FF" offset="0%"></stop>
                <stop stop-color="#436BFF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="软件报价小助理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="输入框最大高度" transform="translate(-1084.000000, -830.000000)">
                <g id="编组-4" transform="translate(214.000000, 712.000000)">
                    <g id="编组-2" transform="translate(870.000000, 118.000000)">
                        <circle id="椭圆形备份" fill="url(#lgsend-1)" cx="20" cy="20" r="20"></circle>
                        <g id="编组" transform="translate(10.000000, 12.307692)" fill="#FFFFFF" fill-rule="nonzero">
                            <path
                                d="M16.7168078,0.0614345972 L0.951412967,4.91056947 C-0.145628414,5.24807523 -0.34514938,6.71470287 0.622009045,7.33303155 L5.27998132,10.3106614 C5.77716487,10.6284154 6.4232725,10.5856211 6.8742056,10.2050694 L12.6878447,5.29925172 C13.0029972,5.03332829 13.4282748,5.45828753 13.1623548,5.77376796 L8.25692472,11.5884543 C7.87643271,12.0393225 7.83376519,12.6853828 8.15165799,13.1823754 L11.1286014,17.8397603 C11.7465981,18.8066074 13.2135306,18.6070839 13.551032,17.5103521 L18.4001039,1.74507652 C18.7178475,0.711505666 17.7500413,-0.256313117 16.7168078,0.0614345972 Z"
                                id="路径"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'SendIcon',
}
</script>
