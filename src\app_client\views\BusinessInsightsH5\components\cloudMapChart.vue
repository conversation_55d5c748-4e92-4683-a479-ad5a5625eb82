<template>
  <div class="competitiveRadar_word_cloud_wrapper" v-loading="loading">
    <p class="title">{{ props.config.title }} <el-tooltip raw-content content="<span style=' font-weight: 700;
        font-size: 14px; color: #262626;'>字体大小：</span><span style='font-weight: 400;
font-size: 14px;
color: #595959;'>词汇越大，在用户反馈中出现频率越高</span>" placement="top" effect="light">
        <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon" />
      </el-tooltip></p>
    <el-empty v-if="!data.length" style="margin: 0 auto;" :image="getOssUrl('no-data.png', 3)" description="暂无数据" />
    <template v-else>
      <div class="legend">
        <span class="legend-item positive"></span> 积极评价为主
        <span class="legend-item negative"></span> 消极评价为主
        <span class="legend-item neutral"></span> 中性评价为主
      </div>
      <div ref="chartDom" class="competitiveRadar_word_cloud_container"></div>
    </template>

  </div>
</template>

<script setup>
import echarts from '@/js/echarts'
import { checkDomReady } from "@/app_client/tools/utils.js"
import { getOssUrl } from '@/js/utils.js';
const props = defineProps({
  // 词云数据数组，格式：[{ name: '操作简单', value: 10, color?: '#4CAF50' }]
  data: {
    type: Array,
    default: () => []
  },
  // 可选：颜色映射规则，如 { positive: ['#4CAF50', ['操作简单', '价格合理']] }
  colorMap: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 可选：自定义样式配置
  config: {
    type: Object,
    default: () => ({
    })
  }
})

const emit = defineEmits(['word-click'])

const chartDom = ref(null)
const chartInstance = ref(null)

// 根据 colorMap 映射颜色
const applyColorMapping = (data) => {
  // if (!props.colorMap.length) return data

  return data.map(item => {
    const matchedRule = props.colorMap.find(rule =>
      rule.words.includes(item.name)
    )
    return {
      ...item,
      color: matchedRule ? matchedRule.color : item.color
    }
  })
}

// 初始化或更新图表
const initChart = () => {
  if (!chartDom.value || !props.data.length || !checkDomReady(chartDom)) return

  const mappedData = applyColorMapping(props.data)
  const options = { ...props.config, ...g.clientBiStore.defCloudWordConfig }
  if (mappedData.length == 1) {
    options.rotationRange = [0, 0]
    options.rotationStep = 0
  }
  const option = {
    tooltip: {
      show: options.tooltip !== false,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 14,
        fontWeight: 500
      },
      padding: [10, 10],
      borderRadius: 6,
      boxShadow: '0 2px 2px 0 rgba(0, 0, 0, 0.1)',
      formatter: function (params) {
        let result = `<div style="color: #8C8C8C; font-size: 14px; width: 120px;padding-left: 8px;">
                    ${params.data.name}</div>`;
        params.data.valueList.forEach((param, idx) => {
          result += `
                        <div style="display: inline-block; width: 45%; min-width: 80px; vertical-align: top; padding: 12px 0 8px 12px;">
                            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${param.color}; margin-right: 8px; vertical-align: middle;"></span>
                            <span style="color: #757575; font-size: 14px; vertical-align: middle;">${param.name}</span>
                            <div style="color: #262626; font-size: 14px; font-weight: bold; margin-top: 2px; padding-left: 28px;">${param.value}</div>
                        </div>
                        ${idx % 2 === 1 ? '<br/>' : ''}
                    `;
        });
        return result;
      }
    },
    series: [
      {
        type: 'wordCloud',
        shape: options.shape,
        sizeRange: options.sizeRange,
        rotationRange: options.rotationRange,
        rotationStep: options.rotationStep,
        gridSize: options.gridSize,
        textStyle: {
          color: function (params) {
            return params.data.color || '#333'
          }
        },
        data: mappedData
      }
    ]
  }

  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  chartInstance.value = echarts.init(chartDom.value)



  chartInstance.value.setOption(option)
  // 绑定点击事件
  chartInstance.value.on('click', (params) => {
    emit('word-click', params)
  })
}

// 监听数据变化并重绘
watch(() => props.data, () => {
  nextTick(() => {
    initChart()
  })
}, { deep: true })

// 页面加载初始化
onMounted(() => {
  nextTick(() => {
    initChart()
  })

  window.addEventListener('resize', handleResize)
})

// 卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
})

// resize 事件处理
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}
</script>

<style lang="scss" scoped>
.competitiveRadar_word_cloud_wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px;
  background: #fff;
  box-sizing: border-box;
  // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px;

  .faq_icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
  }

  .title {
    font-weight: 700;
    font-size: 16px;
    color: #262626;
    line-height: 24px;
    margin: 12px 0;
    display: flex;
    align-items: center;
  }
}

.legend {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #8C8C8C;
  margin-left: 4px;


  .legend-item {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
    margin-left: 26px;

    &.positive {
      background: #04CCA4;
    }

    &.negative {
      background: #FF6B3B;
    }

    &.neutral {
      background: #D9D9D9;
    }
  }

  .legend-item:first-child {
    margin-left: 0;
  }
}

.competitiveRadar_word_cloud_container {
  width: 100%;
  height: 350px;

}
</style>