<template>
  <div class="arrange_visit_core_wrap" v-loading="loading">
    <div class="av_item">
      <IconPicker iconName="customer" isStar />
      <div class="av_item_value customer-picker">
        <CustomerNamePicker ref="refCustomerNamePicker" v-model="formdata.salesMateCustomerName"
          v-model:customerId="formdata.customerId" />
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="visit_tag" isStar />
      <TagPicker v-model="formdata" @change="onTagChange" ref="refTagPicker" />
    </div>
    <div class="av_item">
      <IconPicker iconName="visit_theme" isStar />
      <div class="av_item_value">
        <el-input type="text" placeholder="输入拜访主题" v-model="formdata.subject" maxlength="50" show-word-limit></el-input>
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="goal" isStar />
      <div class="av_item_value">
        <el-input type="textarea" :rows="3" placeholder="输入拜访目标，请详细描述本次拜访想要达到的目的" v-model="formdata.salesGoal"
          maxlength="200" show-word-limit></el-input>
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="time" isStar />
      <DateTimePicker v-model="formdata" />
    </div>
    <div class="av_item">
      <IconPicker iconName="participants" isStar />
      <div class="av_item_value flex-column">
        <ContactInnerPicker v-model="innerParticipantList" ref="refContactInnerPicker" />
        <ContactOuterPicker v-model="outerParticipantList" ref="refContactOuterPicker" />
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="meeting" />
      <div class="av_item_value">
        <el-input type="text" placeholder="添加在线拜访地址：https://" v-model="formdata.onlineMeetUrl" maxlength="200"
          show-word-limit></el-input>
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="location" />
      <div class="av_item_value">
        <el-input type="text" placeholder="添加地点" v-model="formdata.location" maxlength="100" show-word-limit></el-input>
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="note" />
      <div class="av_item_value">
        <el-input type="textarea" :rows="3" placeholder="添加描述" v-model="formdata.description" maxlength="2000"
          show-word-limit></el-input>
      </div>
    </div>
    <div class="av_item">
      <IconPicker iconName="attachment" />
      <AttachmentUploader v-model="formdata.fileList" @callback="onUploadCallback" />
    </div>
  </div>
</template>

<script setup>
import AttachmentUploader from "./AttachmentUploader.vue";
import DateTimePicker from "./DateTimePicker.vue";
import ContactInnerPicker from "./ContactInnerPicker.vue";
import ContactOuterPicker from "./ContactOuterPicker.vue";
import CustomerNamePicker from "@/components/CustomerNamePicker/CustomerNamePicker.vue";
import TagPicker from "./TagPicker.vue";
import IconPicker from "@/components/IconPicker.vue";
import { defaultParam, checkParam, getParList } from "./misc";
import { createSchedule, updateSchedule } from "@/js/api";
import { getScheduleDetail } from "@/js/api";
import { updatePlanApiResult, trimObject } from "@/js/utils";

const refContactOuterPicker = ref();
const formdata = ref({ ...defaultParam() });
const emit = defineEmits(["callback"]);
const loading = ref(false);
const refTagPicker = ref();
const innerParticipantList = ref([]);
const outerParticipantList = ref([]);
const refContactInnerPicker = ref();
const refCustomerNamePicker = ref();


const initParList = () => {
  const allPars = formdata.value.participantList;
  innerParticipantList.value = getParList(allPars, "PARTNER_ATTENDEE");
  outerParticipantList.value = getParList(allPars, "CUSTOMER_ATTENDEE");
};

const getParam = () => {
  let param = toRaw(formdata.value);
  param.participantList = [
    ...toRaw(innerParticipantList.value),
    ...toRaw(outerParticipantList.value),
  ];
  param.participantList = param.participantList.filter(
    (item) => item.userId !== g.appStore.user.id
  );
  param = trimObject(param);
  return param;
};

const onConfirm = () => {
  const param = getParam();
  if (!checkParam(param)) {
    emit("callback", "param_error");
    return;
  }
  loading.value = true;
  const apiFunc = param.conferenceId ? updateSchedule : createSchedule;
  apiFunc(param)
    .then((res) => {
      if (res.code == 0) {
        emit("callback", "reload");
      } else {
        ElMessage.error(res.message);
        emit("callback", "api_error");
      }
    })
    .catch((err) => {
      emit("callback", "api_error");
    })
    .finally(() => {
      loading.value = false;
    });
};

const showEdit = (plan) => {
  if (plan.scheduleId) {
    document.title = "编辑拜访";
    getScheduleDetail(plan.scheduleId)
      .then((res) => {
        if (res.code == 0) {
          formdata.value = updatePlanApiResult(res.data);
          initParList();
          refTagPicker.value.init(toRaw(formdata.value));
          refContactInnerPicker.value.getSuggestions();
        } else {
          ElMessage.error(res.message);
          emit("callback", "has_error");
        }
      })
      .catch((err) => {
        emit("callback", "has_error");
      });
  } else {
    init();
  }
};

const _updateTheme = () => {
  if (formdata.value.salesMateTags && formdata.value.salesMateCustomerName) {
    if (!formdata.value.subject || !formdata.value.conferenceId) {
      formdata.value.subject =
        formdata.value.salesMateCustomerName + " - " + formdata.value.salesMateTags;
    }
  }
};


watch(
  () => formdata.value.salesMateCustomerName,
  (newVal) => {
    _updateTheme();
    nextTick(() => {
      refContactOuterPicker.value.queryCompanyContactList(newVal);
    });
  },
  { immediate: true, deep: true }
);

const onTagChange = (val) => {
  _updateTheme();
};

const onUploadCallback = (action, data) => {
  if (action === "uploading") {
    emit("callback", "uploading", data);
  }
};

const init = (init_data = {}) => {
  formdata.value = { ...defaultParam(), ...init_data };

  const { mail, name, id, phone } = g.appStore.user;
  const my = {
    attendeeType: "PARTNER_ATTENDEE",
    contactType: 0,
    mail,
    name,
    optional: false,
    phone,
    server: "",
    userId: id,
  };
  formdata.value.participantList = [my];

  initParList();
  refCustomerNamePicker.value.reset(formdata.value.salesMateCustomerName);
  refTagPicker.value.init(toRaw(formdata.value));
  refContactInnerPicker.value.getSuggestions();
};

onMounted(() => {
  if (g.config.isElectron) {
    g.ipcRenderer.on("forward_message", (_, { action, data }) => {
      if (action === "add-contact-outer") {
        outerParticipantList.value.push(data);
      } else if (action === "add-contact-inner") {
        innerParticipantList.value.push(data);
      }
    });
  }
});

defineExpose({
  onConfirm,
  formdata,
  showEdit,
  refTagPicker,
  innerParticipantList,
  outerParticipantList,
  refContactOuterPicker,
  init,
  loading,
  ContactInnerPicker,
  ContactOuterPicker,
});
</script>

<style lang="scss">
.arrange_visit_core_wrap {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  width: 97%;
  position: relative;

  .el-loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  .av_item {
    width: 99%;
    display: flex;
    flex-direction: row;
    margin: 10px 0;

    .av_item_icon {
      width: 30px;
    }

    .av_item_value {
      width: 90%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
