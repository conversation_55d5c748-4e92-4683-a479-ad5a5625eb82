<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="暂停&amp;结束录制状态优化" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="录制中断网状态提示" transform="translate(-25.000000, -332.000000)">
                <g id="控件/bars/statusbar/黑备份-2" transform="translate(10.000000, 320.000000)">
                    <g id="图标/基础图标/提示" transform="translate(15.000000, 12.000000)">
                        <circle id="Oval-7" fill="#FA8C16" cx="8" cy="8" r="8"></circle>
                        <path
                            d="M7.25,4 L8.75,4 L8.5,10 L7.5,10 L7.25,4 Z M8,12 C8.27614237,12 8.5,11.7761424 8.5,11.5 C8.5,11.2238576 8.27614237,11 8,11 C7.72385763,11 7.5,11.2238576 7.5,11.5 C7.5,11.7761424 7.72385763,12 8,12 Z"
                            id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'MicIcon',
}
</script>
