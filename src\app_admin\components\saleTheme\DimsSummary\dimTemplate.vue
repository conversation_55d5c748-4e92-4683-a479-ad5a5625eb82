<template>
    <div class="section_8 bkgray flex-row justify-between sale_dim_temp_wrap">
        <div class="block_1 flex-col">
            <Example v-for="item in data" :item="item" :key="item.id" v-model:choosed="choosed" :enableChoose="true" />
        </div>
    </div>
</template>
<script setup>
import Example from '@/app_admin/components/PromptEditList/examples';

const emit = defineEmits(['callback'])
const props = defineProps(['id', 'choosed']);

const choosed = ref('');
const data = ref([])

const setChoosed = (id) => {
    data.value = g.saleStore.getCurrSystemDims();
    nextTick(() => {
        choosed.value = id;
        const item = data.value.filter(x => parseInt(x.id) == id)[0];
        emit('callback', item)
    })
}

watch(choosed, (id) => {
    setChoosed(id)
})


defineExpose({
    data, choosed, setChoosed
})
</script>

<style lang="scss">
@import url("@/app_admin/components/PromptEditList/examples/style.scss");

.sale_dim_temp_wrap {
    .block_1 {
        margin-left: -14px;
        margin-top: -16px;
    }
}
</style>