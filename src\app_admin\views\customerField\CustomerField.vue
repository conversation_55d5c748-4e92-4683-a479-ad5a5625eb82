<template>
  <div class="customer_field_wrap">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="字段设置" name="field">
      </el-tab-pane>
      <el-tab-pane label="页面设置" name="page">
      </el-tab-pane>
      <FieldList ref="refFieldList" v-if="activeName == 'field'" />
      <PageList ref="refPageList" v-if="activeName == 'page'" />
    </el-tabs>
  </div>
</template>
<script setup>
import FieldList from './Field/FieldList.vue';
import PageList from "./Page/PageList.vue"

const activeName = ref('field')
const refFieldList = ref(null)
const refPageList = ref(null)

const handleClick = () => {
  nextTick(() => {
    if (activeName.value == 'field') {
      refFieldList.value.initLoad()
    } else if (activeName.value == 'page') {
      refPageList.value.initLoad()
    }
  })
}

defineExpose({
  activeName,
  FieldList,
  PageList
})

onMounted(() => {
  handleClick()
})

</script>

<style lang="scss">
.customer_field_wrap {
  padding: 12px 24px 24px 24px;
}
</style>
