import { getHttp } from "@/js/request.js";
import uploadFile from "@/js/upload_file.js";
const _http = getHttp();

// 获取全部竞争对手
export const getCompetitor = () => _http.get(`api/competitor`);

// 创建竞争对手
export const createCompetitor = (data) => _http.post(`api/competitor`, data);

// 更新竞争对手
export const updateCompetitor = (competitorId, data) =>
    _http.put(`api/competitor/${competitorId}`, data);

// 获得竞争对手信息
export const getCompetitorDetail = (competitorId) =>
    _http.get(`api/competitor/${competitorId}`);

// 删除竞争对手
export const deleteCompetitor = (competitorId) =>
    _http.delete(`api/competitor/${competitorId}`);

// 下载竞争对手模板
export const downloadCompetitorTemplate = (data) =>
    _http.download(`api/competitor/download/template`, data, "GET");

// 批量导入竞争对手
export const batchUploadCompetitor = (
    formData,
    onProgress,
    onSuccess,
    onFail
) => {
    const url = `${g.config.meetApiHost}/rest/api/competitor/batchupload`;
    return uploadFile(url, formData, onProgress, onSuccess, onFail);
};