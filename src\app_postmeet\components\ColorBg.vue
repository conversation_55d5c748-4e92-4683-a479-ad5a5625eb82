<template>
    <div class="color_bg_wrap">
        <div class="bg1"></div>
        <div class="bg2"></div>
        <slot></slot>
    </div>
</template>

<style lang='scss'>
.color_bg_wrap {
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);
    padding: 20px;
    overflow-x: hidden;
    display: flex;
    justify-content: center;

    .bg1 {
        position: fixed;
        left: 0;
        top: 0;
        width: 361px;
        height: 130px;
        background: radial-gradient(0% 54% at 25% 60%, rgba(140, 213, 255, 0.62) 0%, rgba(111, 255, 151, 0.28) 100%);
        opacity: 0.4;
        filter: blur(32px);
    }

    .bg2 {
        position: fixed;
        right: 0;
        top: 0;
        width: 829px;
        height: 130px;
        background: radial-gradient(0% 92% at 83% 37%, rgba(205, 235, 255, 0.48) 0%, rgba(255, 207, 237, 0.39) 100%);
        filter: blur(32px);
    }

}
</style>