<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close class="dia_edit_102_wrap">
    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules"
      v-show="!formData.systemPreset">
      <el-form-item label="名称" prop="commonName">
        <el-input v-model.trim="formData.commonName" maxlength="50" show-word-limit placeholder="请输入竞争对手常用称呼" />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-autocomplete v-model="formData.companyName" :fetch-suggestions="querySearchAsync" placeholder="请输入并选择公司名称"
          maxlength="50" :debounce="1000" />
      </el-form-item>
      <el-form-item label="别称" prop="alternativeName">
        <el-input v-model="new_tag" maxlength="50" show-word-limit placeholder="添加竞争对手其他称呼，输入后，按回车创建"
          @keyup.enter.native="onNewTag" :disabled="tags.length >= 20" />
        <el-tag v-for="tag in tags" :key="tag" closable type="info" @close="onRemoveTag(tag)">
          {{ tag }}
        </el-tag>
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { nextTick, reactive, ref, toRaw } from "vue";
import { createCompetitor, updateCompetitor } from "@/app_admin/tools/api.js";
import { searchCompanyAPI } from "@/js/api.js";
import Modal from "@/components/Modal.vue";

const emit = defineEmits(["callback"]);
const refModal = ref();
const title = ref("");
const refForm = ref("");
const new_tag = ref("");
const tags = ref([]);
let lastKeyword = "";
let lastList = [];

const defaultForm = {
  commonName: "", //内部常用名
  companyName: "", //公司官方全称
  alternativeName: "", //代用名列表，
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "700px",
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const querySearchAsync = (keyword, cb) => {
  if (keyword) {
    if (keyword == lastKeyword) {
      cb(lastList);
    } else {
      searchCompanyAPI(keyword).then((resp) => {
        if (resp.hasOwnProperty("data") && resp.data.length > 0) {
          lastKeyword = keyword;
          lastList = resp.data.map((x) => {
            return {
              value: x.name,
            };
          });
          cb(lastList);
        } else {
          cb([]);
        }
      });
    }
  } else {
    cb([]);
  }
};

const onNewTag = () => {
  tags.value.push(new_tag.value);
  new_tag.value = "";
};

const onRemoveTag = (tag) => {
  tags.value = tags.value.filter((x) => x != tag);
};

const show_add = () => {
  new_tag.value = "";
  formData.value = { ...defaultForm };
  cfg["title"] = "添加竞争对手";
  tags.value = [];
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (data) => {
  formData.value = { ...data };
  cfg["title"] = "编辑竞争对手";
  tags.value = data["alternativeName"].split(",").filter((x) => !!x);
  refModal.value.show(cfg);
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;
  const fn = () => {
    const data = toRaw(formData.value);
    data["alternativeName"] = tags.value.join(",");
    if (!data.id) {
      createCompetitor(data)
        .then((resp) => {
          if (resp.code == 0) {
            ElMessage.success(`${cfg["title"]}成功`);
            emit("callback", "reload");
            btnCancel();
          } else {
            ElMessage.error(`${resp.message}`);
          }
        })
        .catch((e) => {
          ElMessage.error(`${cfg["title"]}失败`);
        });
    } else {
      const { companyName, commonName, alternativeName } = data;
      const data2 = { companyName, commonName, alternativeName };
      updateCompetitor(data.id, data2)
        .then((resp) => {
          if (resp.code == 0) {
            ElMessage.success(`${cfg["title"]}成功`);
            emit("callback", "reload");
            btnCancel();
          } else {
            ElMessage.error(`${resp.message}`);
          }
        })
        .catch((e) => {
          ElMessage.error(`${cfg["title"]}失败`);
        });
    }
  };
  refForm.value.validate((valid, fields) => {
    if (valid) {
      fn();
    } else {
      console.log("not valid", fields);
    }
  });
};

const rules = reactive({
  commonName: [{ required: true, message: "请输入名称", trigger: "blur" }],
  // companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
});

defineExpose({
  title,
  show_add,
  show_edit,
  cbModal,
  formData,
  rules,
  querySearchAsync,
});
</script>

<style lang="scss">
.dia_edit_102_wrap {
  .dim_title {
    margin: 4px 0;
  }

  .wd_radio {
    margin-bottom: 8px;
  }

  .el-dialog__body {
    padding: 15px 24px 5px 24px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .el-tag {
    margin-top: 5px;
    margin-right: 5px;
  }
}
</style>
