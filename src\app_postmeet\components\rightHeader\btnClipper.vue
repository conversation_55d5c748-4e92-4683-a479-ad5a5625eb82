<template>
  <div class="hicon" @click="onClick">
    <el-tooltip class="item" effect="dark" content="案例萃取" placement="top">
      <img :src="getOssUrl('cutter.png')" alt="scissor" />
    </el-tooltip>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";

const onClick = () => {
  const confId = g.postmeetStore.data.confId;
  const playIdx = g.postmeetStore.data.playIdx;
  const url = `${g.config.publicPath}/#/clipper/${confId}/${playIdx}`;
  window.open(url, "_blank");
};

defineExpose({
  onClick,
  getOssUrl,
});
</script>

<style lang="scss" scoped>
.hicon {
  img {
    width: 20px;
    height: 20px;
  }
}
</style>
