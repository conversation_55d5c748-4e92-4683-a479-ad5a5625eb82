<template>
    <div class="team_visit_btn_export">
        <el-button type="primary" @click="onExport">导出数据</el-button>
    </div>
</template>

<script setup>
import { exportMyTeamPlans } from "@/app_client/tools/api.js"
import { toRaw } from "vue";
import { now } from "@/js/utils.js"

const props = defineProps({
    param: {
        type: Object,
        required: true
    },
    team: {
        type: Boolean,
        required: false,
        default: false
    }
})

const onExport = () => {
    const data = toRaw(props.param);
    data.filename = '团队沟通计划_' + now('yyyyMMddhhmmss');
    exportMyTeamPlans(data)
}


defineExpose({ onExport, props })
</script>

<style lang="scss">
.team_visit_btn_export {
    margin-right: 12px;
}
</style>
