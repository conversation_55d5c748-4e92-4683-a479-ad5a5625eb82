<template>
  <div class="course-loading">
    <Vue3Lottie
      ref="refLottie"
      :animationData="animationLink"
      :height="120"
      :width="120"
    />
    <div class="loading-text">正在为您生成个性化的能力提升方案，请稍等片刻...</div>
  </div>
</template>

<script setup>
import { Vue3Lottie } from "vue3-lottie";
import waitingCourseReport from "./waiting_course_report.json";

const refLottie = ref(null);
const animationLink = ref();

onMounted(() => {
  animationLink.value = waitingCourseReport;
});

onUnmounted(() => {
  animationLink.value = null;
});
</script>

<style scoped lang="scss">
.course-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;

  .loading-text {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}
</style>
