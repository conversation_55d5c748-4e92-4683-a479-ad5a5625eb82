<template>
    <div class="form_title">
        <div v-if="required" class="required">*</div>
        <div class="title">{{ title }}</div>
    </div>
</template>

<script setup>
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    required: {
        type: Boolean,
        default: false
    }
})

</script>

<style lang="scss" scoped>
.form_title {
    display: flex;
    align-items: center;

    .required {
        color: red;
    }

    .title {
        font-size: 14px;
        font-weight: 500;
    }
}
</style>