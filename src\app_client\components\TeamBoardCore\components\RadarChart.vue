<template>
  <div class="radar_chart_wrap">
    <div v-if="dataLength > 0">
      <div class="score-legend" v-if="config.isShowLegend">
        <div class="legend-item">
          <span class="dot green"></span>
          <span class="legend-text">90分以上</span>
        </div>
        <div class="legend-item">
          <span class="dot blue"></span>
          <span class="legend-text">80 - 90分</span>
        </div>
        <div class="legend-item">
          <span class="dot orange"></span>
          <span class="legend-text">60 - 80分</span>
        </div>
        <div class="legend-item">
          <span class="dot red"></span>
          <span class="legend-text">60分以下</span>
        </div>
      </div>
      <div ref="refChart" class="Chart"></div>
    </div>

    <div class="no_data" v-else>
      <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)"></el-empty>
    </div>
  </div>
</template>


<script setup>
import { getOssUrl } from "@/js/utils.js";
import echarts from '@/js/echarts'
import { checkDomReady } from "@/app_client/tools/utils.js"

const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
const radarData = ref(null)

const tooltipIndicatorIdx = ref(null)
const refChart = ref(null)
const chart = ref(null)
const dataLength = computed(() => props.data.length || 0)
const radarIndicator = computed(() => props.data[0]?.radarIndicator || [])
const getScoreColor = (score) => {
  if (score >= 90) return "#52C41A" // 绿色
  if (score >= 80) return "#436BFF" // 蓝色
  if (score >= 60) return "#FA8C16" // 橙色
  return "#FF4D4F" // 红色
}
// 初始化或更新图表
const initChart = () => {
  console.log('initChart', !refChart.value, !checkDomReady(refChart))
  if (!refChart.value || !checkDomReady(refChart)) return
  if (chart.value) {
    chart.value.dispose()
  }
  const splitNumber = 5; // 分圈数

  // 获取容器宽高
  const domRect = refChart.value?.getBoundingClientRect?.();
  const domWidth = domRect?.width || 400; // 默认400
  const domHeight = domRect?.height || 350; // 默认350
  if (domWidth === 0 || domHeight === 0) return
  const centerX = domWidth / 2;
  const centerY = domHeight / 2;
  const radiusPercent = 0.6; // 60%
  const radius = Math.min(domWidth, domHeight) / 2 * radiusPercent;

  // 计算"核心功能"这条轴的角度（第一个维度，ECharts 默认从正上方顺时针）
  const angle = -Math.PI / 2; // 正上方

  // 计算每个分圈的值和位置
  let graphics = [];
  for (let i = 1; i <= splitNumber; i++) {
    const value = 100 / splitNumber * i;
    const r = (radius / splitNumber) * i;
    const x = centerX + r * Math.cos(angle);
    const y = centerY + r * Math.sin(angle) - 12;
    graphics.push({
      type: 'text',
      style: {
        text: value,
        fill: '#BFBFBF',
        font: '10px sans-serif',
      },
      left: x,
      top: y,
      z: 100
    });
  }
  const option = {
    radar: {
      radius: '60%',
      indicator: radarIndicator.value.map((item) => ({
        name: item.name.trim() + " " + item.value + " 分",
        max: item.max,
        color: getScoreColor(item.value),
      })),
      splitNumber: 5,
      shape: 'circle',
      splitArea: {
        areaStyle: {
          color: [
            'rgba(255,255,255,0)',
            'rgba(75,124,246,0.06)',
          ]
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(75,124,246,0.15)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(75,124,246,0.15)'
        }
      },
      axisName: {
        color: '#8C8C8C',
        fontSize: 12,
        fontWeight: 400,
        padding: [0, 0, 8, 0]
      },
      alignTicks: false
    },
    series: [
      {
        type: 'radar',
        data: props.data.map(item => ({
          name: item.name || '',
          value: item.value || [],
          lineStyle: { color: item.color, width: 2, opacity: 0.5 },
          areaStyle: { color: item.color, opacity: 0.1 },
          itemStyle: {
            color: '#fff',
            borderColor: item.color,
            borderWidth: 1
          },
          emphasis: {
            itemStyle: {
              color: '#fff',
              borderColor: item.color,
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowBlur: 10
            }
          },
          // label: {
          //   show: true,
          //   position: 'top',
          //   formatter: function (params) {
          //     return `${params.value}`;
          //   },

          //   padding: [2, 6],
          //   borderRadius: 8,
          //   fontSize: 12,

          // }
        })),
        symbolSize: 4,
        symbol: 'circle',
        showSymbol: true,
        zlevel: 2,
        z: 2,
        silent: false,
        areaStyle: {
          opacity: 0.1
        },
        lineStyle: {
          width: 1
        },
        symbolKeepAspect: true,
        connectNulls: true
      }
    ],

    animation: true,
    graphic: graphics,
  }

  chart.value = echarts.init(refChart.value)
  chart.value.setOption(option)


  chart.value.on('mouseover', (params) => {
    // 鼠标悬停事件处理
    //判断params.event.target.__dimIdx不为undefined，则表示在拐点
    if (params.event.target.__dimIdx !== undefined) {
      //存储变量下标
      tooltipIndicatorIdx.value = params.event.target.__dimIdx
      //显示tooltip
      chart.value.dispatchAction({
        type: 'showTip',
        seriesIndex: params.seriesIndex,
        dataIndex: params.dataIndex,
        position: () => {
          return [params.event.offsetX + 30, params.event.offsetY - 40]
        },
      })
    }
    //不在拐点则隐藏tooltip
    else {
      chart.value.dispatchAction({
        type: 'hideTip',
      })
    }
  })

  chart.value.on('mouseout', () => {
    chart.value.dispatchAction({
      type: 'hideTip',
    })
  })
}
watch(() => props.data, () => {
  nextTick(() => {
    initChart()
  })
}, { immediate: true, deep: true })


// 添加 resize 监听器
const resizeHandler = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

// 页面加载完成后初始化图表
onMounted(() => {
  // nextTick(() => {
  //   initChart()
  // })

  window.addEventListener('resize', resizeHandler)

})

// 在组件卸载时移除监听器并销毁图表
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler)
  if (chart.value) {
    chart.value.dispose()
    chart.value = null
  }
})
</script>


<style lang="scss" scoped>
.radar_chart_wrap {
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: flex;
  flex-direction: column;

  .no_data {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .Chart {
    flex: 1;
    min-height: 280px;
    width: 100%;
  }

  .score-legend {
    display: flex;
    // justify-content: center;
    margin-bottom: 24px;
    gap: 24px;

    .legend-item {
      display: flex;
      align-items: center;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .legend-text {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
      }

      .red {
        background-color: #ff4d4f;
      }

      .orange {
        background-color: #fa8c16;
      }

      .blue {
        background-color: #436bff;
      }

      .green {
        background-color: #52c41a;
      }
    }
  }
}
</style>
