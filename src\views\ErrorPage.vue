<template>
    <div class="error-page flex-center flex-col">
        <el-empty :description="defaultMsg" />
        <div v-if="error_id == 'err_noaccess' || error_id == 'err_login'">
            <el-button type="primary" @click="goPage('login')">重新登录</el-button>
        </div>
        <div v-if="error_id == 'err_admin'">
            <el-button type="primary" @click="goPage('client')">返回用户端</el-button>
        </div>
    </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { isPC } from '@/js/utils.js'

const error_id = ref('')
const defaultMsg = ref('页面不存在')
const route = useRoute()


const idMap = {
    'err_noaccess': '抱歉，您没有当前平台访问权限',
    'err_admin': '抱歉，您没有管理后台权限',
    'err_login': '抱歉，登录失败',
}

onMounted(() => {
    const id = route.query.id
    if (Object.keys(idMap).includes(id)) {
        defaultMsg.value = idMap[id]
        error_id.value = id
    }
})

const goPage = (page) => {
    if (page == 'login') {
        g.appStore.logout()
        if (isPC()) {
            g.router.push('/login')
        } else {
            g.router.push('/login-h5')
        }
    } else if (page == 'client') {
        g.router.push('/')
    }
}

defineExpose({
    defaultMsg
})

</script>

<style lang="scss" scoped></style>
