<template>
    <div class="competitiveRadar_radar_chart" v-loading="loading">
        <div ref="chartDom" v-if="data.length > 0"></div>
        <el-empty style="margin: 0 auto;" :image="getOssUrl('no-data.png', 3)" v-else description="暂无数据" />
    </div>
</template>

<script setup>
import echarts from '@/js/echarts'
import { checkDomReady } from "@/app_client/tools/utils.js"
import { getOssUrl } from "@/js/utils.js";

const props = defineProps({
    config: {
        type: Object,
        default: () => ({})
    },
    data: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    select: {
        type: Array,
        default: () => []
    }
})

const radarIndicator = ref([])
const radarData = computed(() => {
    return props.select.length > 0 ? props.select : props.data
})

watch(() => radarData.value, () => {
    if (radarData.value.length > 0) {
        radarIndicator.value = radarData.value[0].radarIndicator

    }
}, { deep: true, immediate: true })

const tooltipIndicatorIdx = ref(null)
const chartDom = ref(null)
const chart = ref(null)


// 添加计算最大值的函数
const calculateMaxValue = (data) => {
    // 获取所有数据中的最大值并向上取整到10位
    const dataArr = data.flatMap(item => item.value)
    const maxDataValue = Math.max(...dataArr)
    return (Math.ceil(maxDataValue / 2) + 1) * 2
    // return (Math.ceil(maxDataValue / 10)) * 10
}

// 添加计算最小值的函数
const calculateMinValue = (data) => {
    // 获取所有数据中的最小值并向下取整到10位
    const minDataValue = Math.min(...data.flatMap(item => item.value))
    return (Math.floor(minDataValue / 2) - 1) * 2
}

// 初始化或更新图表
const initChart = () => {
    if (!chartDom.value || !checkDomReady(chartDom)) return
    if (chart.value) {
        chart.value.dispose()
    }

    // 计算动态最大值和最小值
    const dynamicMax = calculateMaxValue(props.data)
    const dynamicMin = calculateMinValue(props.data)

    // 更新雷达图指标的最大值和最小值
    const updatedIndicators = radarIndicator.value.map(indicator => ({
        ...indicator,
        max: dynamicMax,
        min: dynamicMin
    }))
    const indicatorIndex = 0; // "核心功能"在 indicator中的索引
    const splitNumber = 4; // 分圈数
    const max = updatedIndicators[indicatorIndex].max;
    const min = updatedIndicators[indicatorIndex].min;

    // 获取容器宽高
    const domRect = chartDom.value?.getBoundingClientRect?.();
    const domWidth = domRect?.width || 400; // 默认400
    const domHeight = domRect?.height || 350; // 默认350
    const centerX = domWidth / 2;
    const centerY = domHeight / 2;
    const radiusPercent = 0.6; // 60%
    const radius = Math.min(domWidth, domHeight) / 2 * radiusPercent;

    // 计算"核心功能"这条轴的角度（第一个维度，ECharts 默认从正上方顺时针）
    const angle = -Math.PI / 2; // 正上方

    // 计算每个分圈的值和位置
    const graphics = [];
    for (let i = 0; i <= splitNumber; i++) {
        const value = min + ((max - min) / splitNumber) * i;
        const r = (radius / splitNumber) * i;
        const x = centerX + r * Math.cos(angle);
        const y = centerY + r * Math.sin(angle) - 12;
        graphics.push({
            type: 'text',
            style: {
                text: value.toFixed(2),
                fill: '#BFBFBF',
                font: '10px sans-serif',
            },
            left: x,
            top: y,
            z: 100
        });
    }

    const option = {

        legend: {
            data: props.config.isShowLegend ? props.data.map(item => item.name) : [],
            bottom: '10px'
        },
        // tooltip: {
        //     trigger: 'item',
        //     // 表示不使用默认的“显示”“隐藏”触发规则。
        //     // 仅使用 dispatchAction 控制tooltip显隐时需设置
        //     triggerOn: 'none',
        //     formatter: function (params) {
        //         if (!params) return '';
        //         //根据存储的变量下标，获取对应数据并显示
        //         let { name } = radarIndicator.value[tooltipIndicatorIdx.value]
        //         let result = `<div style="color: #8C8C8C; font-size: 14px; margin-bottom: 8px; width: 120px;">${name}</div>`;
        //         // 获取当前维度的所有数据
        //         const allData = props.data.map(item => ({
        //             name: item.name,
        //             value: item.value[radarIndicator.value.findIndex(indicator => indicator.name === name)],
        //             color: item.color
        //         }));

        //         // 按值从大到小排序
        //         allData.sort((a, b) => b.value - a.value);

        //         // 生成每个数据的展示
        //         allData.forEach((item, index) => {
        //             result += `
        //                 <div style="display: inline-block; width: 48%; min-width: 60px; margin-bottom: 8px; vertical-align: top;">
        //                     <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${item.color}; margin-right: 8px; vertical-align: middle;"></span>
        //                     <span style="color: #8C8C8C; font-size: 14px; vertical-align: middle;">${item.name}</span>
        //                     <div style="color: #222; font-size: 14px; font-weight: bold; margin-top: 2px; padding-left: 28px;">${item.value}</div>
        //                 </div><br/>
        //             `;
        //         });
        //         return result
        //     },
        // },
        radar: {
            radius: '60%',
            indicator: updatedIndicators,
            splitNumber: 4,
            shape: 'circle',
            splitArea: {
                areaStyle: {
                    color: [
                        'rgba(75,124,246,0.06)',
                        'rgba(255,255,255,0)',
                    ]
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(75,124,246,0.15)'
                }
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(75,124,246,0.15)'
                }
            },
            axisName: {
                color: '#8C8C8C',
                fontSize: 12,
                fontWeight: 400,
                padding: [0, 0, 8, 0]
            },
            alignTicks: false
        },
        series: [
            {
                type: 'radar',
                data: radarData.value.map(item => ({
                    name: item.name,
                    value: item.value,
                    lineStyle: { color: item.color, width: 2, opacity: 0.5 },
                    areaStyle: { color: item.color, opacity: 0.1 },
                    itemStyle: {
                        color: '#fff',
                        borderColor: item.color,
                        borderWidth: 1
                    },
                    emphasis: {
                        itemStyle: {
                            color: '#fff',
                            borderColor: item.color,
                            borderWidth: 1,
                            shadowColor: 'rgba(0, 0, 0, 0.2)',
                            shadowBlur: 10
                        }
                    }
                })),
                symbolSize: 4,
                symbol: 'circle',
                showSymbol: true,
                zlevel: 2,
                z: 2,
                silent: false,
                areaStyle: {
                    opacity: 0.1
                },
                lineStyle: {
                    width: 2
                },
                symbolKeepAspect: true,
                connectNulls: true
            }
        ],

        animation: true,
        graphic: graphics,
    }

    chart.value = echarts.init(chartDom.value)
    chart.value.setOption(option)


    chart.value.on('mouseover', (params) => {
        // 鼠标悬停事件处理
        //判断params.event.target.__dimIdx不为undefined，则表示在拐点
        if (params.event.target.__dimIdx !== undefined) {
            //存储变量下标
            tooltipIndicatorIdx.value = params.event.target.__dimIdx
            //显示tooltip
            chart.value.dispatchAction({
                type: 'showTip',
                seriesIndex: params.seriesIndex,
                dataIndex: params.dataIndex,
                position: () => {
                    return [params.event.offsetX + 30, params.event.offsetY - 40]
                },
            })
        }
        //不在拐点则隐藏tooltip
        else {
            chart.value.dispatchAction({
                type: 'hideTip',
            })
        }
    })

    chart.value.on('mouseout', () => {
        chart.value.dispatchAction({
            type: 'hideTip',
        })
    })
}

// 监听数据变化并重绘
watch(() => radarData.value, () => {
    nextTick(() => {
        initChart()
    })
}, { deep: true })
// 页面加载完成后初始化图表
onMounted(() => {
    initChart()

    // 添加 resize 监听器
    const resizeHandler = () => {
        if (chart.value) {
            chart.value.resize()
        }
    }

    window.addEventListener('resize', resizeHandler)

    // 在组件卸载时移除监听器并销毁图表
    onUnmounted(() => {
        window.removeEventListener('resize', resizeHandler)
        if (chart.value) {
            chart.value.dispose()
            chart.value = null
        }
    })
})
</script>

<style lang="scss" scoped>
.competitiveRadar_radar_chart {
    width: 100%;
    height: 280px;

    >div {
        height: 100%;
        width: 100%;

    }
}
</style>