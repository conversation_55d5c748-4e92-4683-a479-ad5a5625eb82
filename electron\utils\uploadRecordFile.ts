import fs from 'fs'
import path from 'node:path'
import { createFile } from './api'
import { errorWriteLocal } from './errorWriteLocal'


export const _readLocalFile = (filePath) => {
    return new Promise(async (resolve) => {
        try {
            if (!fs.existsSync(filePath)) {
                resolve({ code: 1, message: '文件不存在', data: null })
                return;
            }
            const fileBuffer = await fs.promises.readFile(filePath)
            const fileStats = await fs.promises.stat(filePath)
            resolve({
                code: 0,
                data: {
                    buffer: fileBuffer,
                    fileName: path.basename(filePath),
                    fileSize: fileStats.size,
                    mimeType: path.extname(filePath).toLowerCase()
                }
            })
        } catch (error) {
            resolve({ code: 2, message: error.message, data: null })
        }
    });
}


const _createFile = async (bizId, fileName, module) => {
    const param = {
        module, bizId,
        fileNameExt: fileName.split('.').pop(),
        isPrivate: 1
    }
    const result = await createFile(param)
    errorWriteLocal('createFile result', JSON.stringify(result))
    return result;
}


const _uploadFile = async (uploadUrl, fileBuffer) => {
    try {
        const response: any = await fetch(uploadUrl, {
            method: 'PUT',
            body: fileBuffer,
            headers: {
                'Content-Type': 'application/octet-stream'
            }
        })
        errorWriteLocal("_uploadFile response", JSON.stringify(response))
        if (!response.ok) {
            throw new Error('上传失败')
        }
        return { code: 0, data: response.data }
    } catch (error) {
        return { code: 3, message: error }
    }
}

export const uploadRecordFile = (confId, filePath) => {
    // 示例调用
    // const res = await uploadRecordFile('test_confId', 'C:\\Users\\<USER>\\Documents\\SalesMateRecord\\20250515\\即时录制_20250515_133738.m4a')
    return new Promise(async (resolve, reject) => {
        // 读取本地文件
        const result: any = await _readLocalFile(filePath);
        errorWriteLocal('_readLocalFile result', result.code)
        if (result.code !== 0) {
            return resolve(result)
        }
        const { buffer, fileName, fileSize, mimeType } = result.data;

        // 创建文件
        const createResult = await _createFile(confId, fileName, 'fixAudio')
        if (createResult.code != 0) {
            return resolve(createResult)
        }
        const { fileId, presignedUrl } = createResult.data
        // 上传文件
        const uploadResult = await _uploadFile(presignedUrl, buffer)
        errorWriteLocal('_uploadFile result', uploadResult)
        if (uploadResult.code != 0) {
            return resolve(uploadResult)
        }
        const finalResult = { fileId, fileName, fileSize, mimeType };
        errorWriteLocal('uploadResult final result', finalResult)
        resolve({ code: 0, data: finalResult })
    })
}
