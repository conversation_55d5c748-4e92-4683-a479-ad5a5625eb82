<template>
    <div class="filter">
        <el-radio-group v-model="localModal" @change="handleChange">
            <!-- weekly,monthly,quarterly,yearly -->
            <el-radio-button label="上周" value="lastweek" />
            <el-radio-button label="上月" value="lastmonth" />
            <el-radio-button label="上季度" value="lastquarter" />
        </el-radio-group>
        <slot></slot>
    </div>
</template>

<script setup lang="js">
const localModal = ref(g.clientBiStore.periodType)
const handleChange = (val) => {
    g.clientBiStore.setSelectId(val)
}
</script>

<style lang="scss" scoped>
.filter {
    z-index: 2;

    .is-active {
        :deep(.el-radio-button__inner) {
            background: #436BFF !important;
        }
    }

    :deep(.el-radio-button__inner) {
        color: #436BFF;
    }
}
</style>