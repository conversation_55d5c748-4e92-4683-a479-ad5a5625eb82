<template>
    <div :class="`text-wrapper_16 flex-col ${props.choosed == 5 ? 'active' : ''}`" @click="onClick(5)">
        <span class="text_37">对客纪要</span>
        <dashline />
        <div class="flex-col marginl7">
            <page :id="5" />
            <div class="image-wrapper_1 flex-row" v-if="props.choosed">
                <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
            </div>
        </div>
    </div>
</template>
<script setup>
import dashline from './dashline.vue';
import page from '../pages/page.vue';
import { r_png } from './image.js';

const props = defineProps(['choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])

const onClick = (value) => {
    if (props.enableChoose) {
        emit('update:choosed', value)
    }
}

defineExpose({ onClick, page, dashline })

</script>