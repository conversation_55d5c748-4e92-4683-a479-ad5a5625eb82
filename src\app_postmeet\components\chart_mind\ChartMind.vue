<template>
    <div class="mind_chart_wrap">
        <div class="tc_btns flex-row">
            <div class="tc_btn" @click="onFull">
                <FullscreenIcon />
            </div>
            <div class="tc_btn" @click="onCopy">
                <CopyIcon />
            </div>
            <div class="tc_btn" @click="onEdit" v-show="isHost">
                <EditIcon />
            </div>
        </div>
        <MarkmapRender ref="refMarkmap" :content="markdownContent" />
        <MindEditDialog ref="refEditMind" @callback="cbEdit" />
    </div>
</template>

<script>
import MarkmapRender from "./MarkmapRender.vue"
import MindEditDialog from "./MindEditDialog.vue"
import FullscreenIcon from "@/app_postmeet/icons/fullscreen.vue"
import EditIcon from "@/icons/edit.vue"
import CopyIcon from "@/icons/copy.vue"

export default {
    name: 'ChartMind',
    components: { MarkmapRender, MindEditDialog, FullscreenIcon, EditIcon, CopyIcon },
    data() {
        return {
            isHost: false,
            markdownContent: '',
        }
    },
    methods: {
        init() {
            this.isHost = !g.postmeetStore.isReadonly();
            this.markdownContent = g.postmeetStore.data.MindMapMd;
        },
        onFull() {
            this.$refs.refEditMind.show(this.markdownContent, false)
        },
        onFit() {
            this.$refs.refMarkmap.getDom();
        },
        onCopy() {
            const txt = this.markdownContent;
            g.appStore.doCopy(txt, '复制成功');
        },
        cbEdit(data) {
            this.markdownContent = data;
            g.postmeetStore.setValue('MindMapMd', data);
        },
        onEdit() {
            this.$refs.refEditMind.show(this.markdownContent, true)
        }
    },
}
</script>

<style lang='scss' scoped>
.mind_chart_wrap {
    margin-top: 12px;
    padding-left: 10px;
    height: calc(100vh - 262px);

    .tc_btns {
        justify-content: right;

        .tc_btn {
            margin: 0 10px;
            color: #8c8c8c;
            cursor: pointer;
        }
    }
}
</style>
