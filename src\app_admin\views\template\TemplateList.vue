<template>
    <div class="template_list_wrap">
        <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
            <template #col_createdPptCount="scope">
                <div class="column_link" @click="goCountView(scope.row)">
                    {{ scope.row.createdPptCount }}
                </div>
            </template>
        </MyTable>
        <dialogTemplate ref="refDialog" @callback="cbModal"></dialogTemplate>
    </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import dialogTemplate from "./dialogTemplate.vue";
import { getPptTemplate, deletePptTemplate } from "@/app_admin/tools/api.js"

const refTable = ref(null);
const refDialog = ref(null);

const datas = reactive({
    tableid: 'template_list',
    param: {},
    need_header: true,
    need_init_load: true,
    form: {},
    show_btn_add: true,
    add_txt: "新建模板",
    delete_hint_column: "name",
    show_link_column: true,
    show_link_edit: true,
    show_link_delete: true,
    urlGet: getPptTemplate,
    urlDelete: deletePptTemplate,
    columns: ['name', 'scenario', 'description', 'createdTime', 'createdUserName', 'createdPptCount'],
    template: ['createdPptCount']
});

const cbDatas = (action, data) => {
    if (action == 'init_add') {
        refDialog.value.show_add();
    } else if (action === "init_edit") {
        g.router.push({
            path: `/admin/template/edit/${data.id}`
        })
    }
}

const cbModal = (action, data) => {
    if (action == "reload") {
        refTable.value.search()
    } else if (action = "create_ok") {
        cbDatas('init_edit', data)
    }
}

const goCountView = (row) => {
    g.router.push({
        path: `/admin/template/use_records/${row.id}`
    })
}


defineExpose({
    MyTable,
    refTable,
    cbDatas,
    dialogTemplate,
    refDialog,
    cbModal
})
</script>

<style lang="scss">
.template_list_wrap {

    .col_createdTime {
        width: 180px;
    }

    .col_createdUserName {
        width: 80px;
    }

    .col_createdPptCount {
        width: 110px
    }

    .col_operation_ {
        width: 100px;
    }
}
</style>