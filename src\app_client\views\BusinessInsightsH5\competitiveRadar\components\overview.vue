<template>
    <div class="competitive-radar-overview-h5">
        <titleMenu>
            <template #title>
                总览
            </template>
        </titleMenu>
        <aiAnalysis :data="aiAnalysisAllData" :loading="loadingAiAnalyse" />
        <totalCharts :data="evaluationDistrList" :metrics="totalChartsMetrics" :setting="totalChartsSetting"
            class="total-charts-style" />

        <div class="radar">
            <div class="radar_title">
                <p> 竞品声量雷达图
                    <TooltipCvCalc />
                </p>
                <span v-if="volumeList.length" class="reset-btn" @click="resetRadar">重置</span>

            </div>
            <radarCards ref="radarCardsRef" v-if="volumeList.length" :data="volumeList" :colorObject='colorObject'
                @callback="radarCallback">
            </radarCards>
            <radarEcharts :data="volumedistributionAll" :config="radarEchartsConfig" :select="volumedistributionSelect">
            </radarEcharts>
        </div>
    </div>
</template>

<script setup>
import aiAnalysis from "../../components/aiAnalysis.vue";
import totalCharts from "../../components/totalChart.vue";
import radarEcharts from "../../components/radarChart.vue";
import radarCards from "./radarCards.vue";
import titleMenu from "../../components/titleMenu.vue";
import TooltipCvCalc from '@/app_client/components/TooltipCvCalc.vue'
const radarCardsRef = ref(null)
const colorObject = computed(() => {
    return g.clientBiStore.colorObject || {}
})
const totalChartsMetrics = computed(() => [{
    key: 'positiveCount',
    name: '积极',
    color: colorObject.value['positive']
}, {
    key: 'negativeCount',
    name: '消极',
    color: colorObject.value['negative']
}, {
    key: 'neutralCount',
    name: '中性',
    color: colorObject.value['neutral']
}])

const loadingAiAnalyse = ref(true);
// 从store获取数据
const store = g.clientBiStore
// 构建总览页面的AI分析数据对象
const aiAnalysisAllData = computed(() => store.selectAIAnalysisAll);
const periodType = computed(() => store.periodType || '')
const volumedistributionAll = ref([])
const volumedistributionSelect = ref([])
const volumeList = computed(() =>
    store.volumedistributionAll || []
)
const evaluationDistrList = computed(() => store.evaluationDistrList || [])

const totalChartsSetting = {
    title: '竞品评价分布图',
    width: 82
}
const radarEchartsConfig = {
    isShowLegend: false
}
const init = async () => {
    loadingAiAnalyse.value = true
    await store.getCompetitorAIAnalysisAll()
    loadingAiAnalyse.value = false;
}
watch(() => volumeList.value, () => { volumedistributionAll.value = volumeList.value })

watch(
    () => periodType.value,
    (newValue, oldValue) => {
        init()
        store.getVolumedistributionAll()
    }, {
    immediate: true,
    deep: true
}
)
const radarCallback = (item) => {
    volumedistributionSelect.value = [item]
}
const resetRadar = () => {
    radarCardsRef.value?.reset()
    volumedistributionSelect.value = []
}

onMounted(() => {
    init()
});
</script>
<style lang="scss" scoped>
.competitive-radar-overview-h5 {
    padding: 20px 0;

    .competitive-radar-warp-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .title-text {
            font-weight: 600;
            font-size: 18px;
            color: #262626;
            line-height: 26px;
            padding-left: 8px;
            position: relative;

            &::before {
                content: '';
                display: inline-block;
                position: absolute;
                width: 5px;
                height: 20px;
                background: #436BFF;
                border-radius: 3px;
                left: -11px;
                top: 2px;
            }
        }

        .van-dropdown-menu {
            width: 130px;
        }


        :deep(.van-dropdown-menu__bar) {
            height: 26px;
            background: transparent;
            box-shadow: unset;
        }

    }

    :deep(.analysis) {
        margin-top: 16px;
    }

    .total-charts-style {
        width: 100%;
        height: 300px;
        padding: 4px 16px 16px 16px !important;
        margin-top: 12px;


    }

    .radar {
        width: 100%;
        padding: 4px 16px 16px 16px;
        box-sizing: border-box;
        // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
        background: #FFFFFF;
        border-radius: 12px;
        height: 420px;
        margin-top: 12px;

        &_title {
            font-weight: 700;
            font-size: 16px;
            color: #262626;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;


            .reset-btn {
                font-size: 14px;
                color: #436BFF;
                line-height: 22px;
                cursor: pointer;
                font-weight: 400;
            }
        }

        &_tooltip {
            color: #666666;

            p {
                span:nth-child(1) {
                    color: #262626;
                    font-weight: 700;
                }
            }

            i {
                font-style: normal;
                margin-left: 4px;
            }
        }

        &_icon {
            margin-left: 4px;

            svg {
                background: #999;
                border-radius: 50%;
            }
        }
    }
}
</style>