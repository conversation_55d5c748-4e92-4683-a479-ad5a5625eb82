<template>
  <div class="menu-list flex-row">
    <div class="menu-item" @click="onLink(item.index)" v-for="item in processedMenuList" :key="item.id">
      <div class="icon-wrapper blue-bg">
        <InlineSvg :src="item.cachedIcon || item.icon" :alt="item.name" @error="handleImageError"
          preserveAspectRatio="xMidYMid meet" />
      </div>
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script setup>
import { getAssetUrl } from "@/js/utils";
import InlineSvg from "vue-inline-svg";

const userInfo = ref(g.appStore.user);
const menuList = ref([]);
const processedMenuList = ref([]);

// 处理图标缓存
const processMenuIcons = async (list) => {
  const processed = await Promise.all(
    list.map(async (item) => {
      try {
        const cachedPath = await g.electronStore.getImageCache(item.icon);
        return {
          ...item,
          cachedIcon: cachedPath,
        };
      } catch (error) {
        console.error("处理图标缓存失败:", error);
        return item;
      }
    })
  );
  return processed;
};

const onLink = (url) => {
  console.log("点击菜单项，跳转链接:", url);
  if (url == '/electron/meet') {
    if (g.meetStore.isLocalMeeting) {
      ElMessage({
        message: "当前有正在进行的沟通，无法开始新的沟通",
        type: 'warning'
      });
      return
    }
  }
  g.electronStore.openUrl(url);
};

const handleImageError = (e) => {
  console.error("图片加载失败:", e.target.src);
  e.target.src = getAssetUrl("app.png");
};

const CACHE_KEY = "electron_menu_cache";

const init = async () => {
  userInfo.value = g.appStore.user;

  // 先尝试读取本地缓存
  const cachedMenu = localStorage.getItem(CACHE_KEY);
  if (cachedMenu) {
    const parsed = JSON.parse(cachedMenu);
    menuList.value = parsed;
    processedMenuList.value = await processMenuIcons(parsed);
  }

  // 获取最新数据
  let newList = await g.cacheStore.getUserMenu("electron");
  if (!g.appStore.user.manager) {
    newList = newList.filter((x) => x.code != "app_my_team");
  }

  // 比较新数据和缓存是否相同
  if (!cachedMenu || JSON.stringify(newList) !== JSON.stringify(JSON.parse(cachedMenu))) {
    menuList.value = newList;
    processedMenuList.value = await processMenuIcons(newList);
    // 更新缓存
    localStorage.setItem(CACHE_KEY, JSON.stringify(newList));
  }
};

defineExpose({
  userInfo,
  handleImageError,
  init,
});
</script>

<style lang="scss" scoped>
.menu-list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    width: 20%;

    .icon-wrapper {
      width: 48px;
      height: 48px;
      border-radius: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;
      background-color: #4a90e2;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }

    span {
      font-size: 12px;
    }
  }
}
</style>
