<template>
    <div class="la_goal_wrap flex-column">
        <div class="la_title">
            沟通目标及达成情况
        </div>
        <div class="la_sub" v-if="hasGoal">
            目标回顾
        </div>
        <div class="la_md" v-if="hasGoal">
            {{ salesGoal }}
        </div>
        <div class="la_sub" v-if="hasGoal">
            达成分析
        </div>
        <div :class="` ${hasGoal ? 'la_md' : 'no_goal'}`">
            {{ item.report['理由'] }}
        </div>
        <div class="icon_goal" v-if="hasGoal">
            <component :is="getIconCom" />
        </div>
    </div>
</template>

<script>
import iconGoalReach from "./icons/goal_reach.vue"
import iconGoalNa from "./icons/goal_na.vue"
import iconGoalUnReach from "./icons/goal_unreach.vue"

const _default_la_goal_item = {
    name: '',
    salesGoal: '',
    report: {}
}
export default {
    components: { iconGoalReach, iconGoalNa, iconGoalUnReach },
    data() {
        return {
            status: '未知',
            item: _default_la_goal_item,
            activeName: '概览',
            hasGoal: false,
            tags: ['概览', '客户态度', '风险项']
        }
    },
    mounted() {
        this.init()
        g.emitter.on('after_update_sale', () => {
            this.init()
        })
    },
    computed: {
        getIconCom: function () {
            const map = {
                '达成': iconGoalReach,
                '无法判定': iconGoalNa,
                '未达成': iconGoalUnReach,
            }
            return map[this.status]
        }
    },
    methods: {
        init() {
            this.salesGoal = g.postmeetStore.data.asrRaw.salesGoal;
            const ar = g.postmeetStore.data.saleReport.salesAnalysisReports;
            if (ar) {
                this.item = ar.find(x => x.systemId == 100) || _default_la_goal_item;

                this.hasGoal = this.item.report && this.item.report['理由'] && this.item.report['理由'] !== '安排沟通时未设置沟通目标';
                this.status = this.item.report['评估结果'];
            }
        },
    }
}

</script>

<style lang="scss">
.la_goal_wrap {
    position: relative;
    background: #F9FAFC;
    border-radius: 8px;
    padding: 20px;
    margin: 12px 0;

    .la_title {
        font-weight: 500;
        font-size: 16px;
        color: #262626;
        line-height: 26px;
    }

    .la_sub {
        font-weight: 500;
        font-size: 12px;
        color: #436BFF;
        line-height: 18px;
        width: 56px;
        height: 20px;
        background: #E6EBFD;
        border-radius: 2px;
        text-align: center;
        margin: 12px 0;
    }

    .icon_goal {
        position: absolute;
        top: 20px;
        right: 30px;
    }

    .la_md {
        margin-top: 10px;
    }

    .no_goal {
        color: red;
        margin-top: 12px;
    }
}
</style>