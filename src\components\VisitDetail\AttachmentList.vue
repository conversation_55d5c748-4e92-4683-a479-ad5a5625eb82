<template>
  <div class="attachment-list">
    <div class="attachment-list-title">{{ attachments.length }}个附件</div>
    <ul>
      <li v-for="attachment in attachments" :key="attachment.url" @click="download(attachment)">
        {{ attachment.name }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import { downloadFile } from '@/js/utils';

const props = defineProps({
  attachments: {
    type: Array,
    default: () => []
  }
});

const download = (attachment) => {
  downloadFile(attachment.sharePath, attachment.name)
}
</script>

<style lang="scss">
.attachment-list {
  .attachment-list-title {
    font-size: 14px;
    color: #262626;
    margin-bottom: 10px;
  }

  ul {
    padding: 0;
    list-style: none;

    li {
      margin-bottom: 10px;
      background: #EBEDEE;
      border-radius: 2px;
      padding: 8px;
      font-size: 12px;
      color: #595959;
      cursor: pointer;
    }
  }
}
</style>
