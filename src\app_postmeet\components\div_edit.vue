<template>
  <div :class="`diy_box ${showInput ? 'li_title_edit' : ''}`">
    <div v-if="!showInput" :class="!readonly ? 'pointer' : ''" @click="onClick">
      {{ modelValue }}
    </div>
    <el-input v-if="showInput" type="textarea" autosize ref="refInput" v-model="inputValue" @blur="onEnter"
      @keyup.enter="onEnter" class="diy_input" />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  edit: {
    type: Boolean,
    default: false
  },
  autoFocus: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'onclick', 'callback'])

const readonly = computed(() => g.postmeetStore.isReadonly())
const showInput = ref(false)
const inputValue = ref('')
const refInput = ref(null)
const hasAutoFocused = ref(false)

const onClick = (e) => {
  if (props.edit) {
    if (!showInput.value) {
      showInput.value = true
      inputValue.value = props.modelValue
      nextTick(() => {
        refInput.value?.focus()
      })
    }
  }
  emit('onclick', e)
}

const onEnter = (e) => {
  const value = inputValue.value
  if (!value) {
    ElMessage({
      showClose: false,
      message: '编辑内容不可以为空',
      type: 'error'
    })
    inputValue.value = props.modelValue
  } else {
    showInput.value = false
    hasAutoFocused.value = false
    if (props.modelValue !== value) {
      emit('update:modelValue', value)
      emit('callback', value)
    }
  }
}

// 监听autoFocus变化，当autoFocus为true时自动聚焦
watch(() => props.autoFocus, (newVal) => {
  if (newVal && !hasAutoFocused.value) {
    showInput.value = true
    inputValue.value = props.modelValue
    hasAutoFocused.value = true
    nextTick(() => {
      refInput.value?.focus()
    })
  }
}, { immediate: true })

// 监听edit状态变化，如果退出编辑模式，则重置hasAutoFocused标志
watch(() => props.edit, (newVal) => {
  if (!newVal) {
    hasAutoFocused.value = false
  }
})
</script>

<style lang="scss" scoped>
.diy_box {
  .pointer {
    cursor: pointer;
  }

  input {
    width: 95%;

    &:focus {
      border: 1px #436bff solid;
      border-radius: 3px;
      outline: none;
    }
  }
}
</style>