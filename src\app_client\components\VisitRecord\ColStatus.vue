<template>
    <div class="dtxt">
        {{ props.name }}
        <el-tooltip class="ccol_dept" effect="dark" :content="getShowHint()" placement="top-start" raw-content>
            <el-icon v-show="getShowHint()">
                <QuestionFilled />
            </el-icon>
        </el-tooltip>
    </div>
</template>

<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'
const props = defineProps(['name']);

const getShowHint = () => {
    const status = props.name;
    if (status == '客户不存在') {
        return 'CRM中没有对应的客户'
    } else if (status == '没有纪要') {
        return '云录制时长太短或没有声音'
    } else if (status == '无法生成同步内容') {
        return '1. 云录制时长太短或没有声音。<br/>2. 沟通字幕文本量不足'
    } else if (status == '同步内容暂未生成') {
        return '1. 参会人身份标记完成后，需点击生成内容。<br/>2. 若生成失败，可再次点击生成内容，重新生成。 '
    }
    return ''
};

defineExpose({ props, getShowHint, QuestionFilled })
</script>

<style lang="scss">
.ccol_dept {
    .dtxt {
        font-size: 14px;
        color: #262626;
    }
}
</style>