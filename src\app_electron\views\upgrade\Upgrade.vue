<template>
    <div class="upgrade-container">
        <div class="upgrade-header flex-row">
            <div class="upgrade-icon">
                <img :src="getAssetUrl('logo_mini.png')" alt="logo">
            </div>
            <div class="upgrade-title flex-col">
                <h2>绚星销售助手最新 {{ updateInfo.newVersion }} 版本来了！</h2>
                <p class="version-info">您目前使用版本为 {{ currentVersion }}, 新版本更新日志如下：</p>
            </div>
        </div>
        <div class="upgrade-content">
            <div class="update-info" v-html="mrender(updateInfo.comment)"></div>
        </div>
        <div class="upgrade-actions flex-center">
            <div>
                <div class="skip-version" @click="skipVersion" v-if="!updateInfo.forceUpdate">跳过这个版本</div>
            </div>
            <div>
                <el-button type="default" @click="updateLater" v-if="!updateInfo.forceUpdate">稍后再说</el-button>
                <el-button type="primary" @click="updateNow">立即更新</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { md2html } from "@/js/md.js"
import { getAssetUrl, now } from "@/js/utils.js"
const currentVersion = g.config.version;
const mrender = (content) => {
    return md2html(content.replace(/\n/g, "\n\n")).replaceAll(" href=", " target='_blank' href=")
}
// {
// 		"comment": "",
// 		"forceUpdate": false,
// 		"needUpdate": false,
// 		"newVersion": "",
//      isGray: false
// 	},
const updateInfo = ref({
    version: '', comment: ``, isGray: false
});

const skipVersion = () => {
    g.appStore.setStore(g.cv.keySkipVersion, updateInfo.value.newVersion)
    g.electronStore.closeWin('upgrade')
}

const updateLater = () => {
    g.appStore.setStore(g.cv.keyLastUpdateRemindTime, now('yyyy-MM-dd hh'))
    g.electronStore.closeWin('upgrade')
}

const updateNow = () => {
    // 实现立即更新的逻辑
    console.log("Starting upgrade...");
    g.electronStore.openWin('download', {})
    g.electronStore.closeWin('upgrade')
}

onMounted(() => {
    g.electronStore.waitPageReady().then(data => {
        updateInfo.value = data;
    }).catch(err => {
        console.error('meet waitPageReady error', err)
    })
})

defineExpose({
    updateLater,
    updateNow,
    skipVersion
})

</script>

<style lang="scss" scoped>
.upgrade-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px;
    min-height: 100vh;
    box-sizing: border-box;
    background: linear-gradient(180deg, #E9F1FF 0%, #F5F9FF 100%);

    .upgrade-header {
        margin-bottom: 20px;
        width: 100%;
        margin-top: 12px;

        .upgrade-icon {
            width: 48px;
            height: 48px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .upgrade-title {
            flex: 1;
            margin-left: 16px;

            h2 {
                font-size: 16px;
                color: #1D2129;
                margin: 0 0 8px;
                font-weight: 500;
            }

            .version-info {
                font-size: 14px;
                color: #86909C;
                margin: 0;
            }
        }
    }

    .upgrade-content {
        width: 100%;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 0 16px;
        height: 209px;
        box-sizing: border-box;

        .version-title {
            font-size: 14px;
            color: #1D2129;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .update-info {
            font-size: 14px;
            color: #4E5969;
            line-height: 22px;
            padding-right: 8px;
            height: 200px;
            overflow: auto;

            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #E5E6EB;
                border-radius: 2px;
            }
        }
    }

    .upgrade-actions {
        margin-top: 24px;
        display: flex;
        gap: 16px;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .skip-version {
            font-size: 14px;
            color: #436BFF;
            line-height: 22px;
            cursor: pointer;
        }
    }
}
</style>
