.section_8 {
    width: 100%;
    margin: 12px 0 6px 0px;
    overflow: auto;

    .bkgray {
        background-color: rgba(250, 250, 250, 1);
    }

    .block_1 {
        width: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        justify-content: flex-center;

        .active {
            border: 1px solid rgba(67, 107, 255, 1) !important;

            .image-wrapper_1 {
                display: block !important;
            }
        }

        .image-wrapper_1 {
            width: 16px;
            height: 16px;
            margin-left: 544px;
            display: none;
            position: absolute;
            right: 0;
            bottom: 0;
        }

        .group_8 {
            background-color: rgba(255, 255, 255, 1);
            border: 1px solid rgba(233, 233, 233, 1);
            border-radius: 4px;
            height: 429px;
            width: 97%;
            margin: 16px 0 0 16px;
            position: relative;
        }

        .text-wrapper_14 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 4px;
            width: 97%;
            border: 1px solid rgba(233, 233, 233, 1);
            margin: 16px 0 0 16px;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .text-wrapper_11 {
            width: 56px;
            height: 22px;
            margin: 14px 0 0 16px;
        }

        .text_21 {
            width: 56px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
        }

        .box_10 {
            width: 224px;
            height: 22px;
            margin: 12px 0 0 16px;
        }

        .text-wrapper_12 {
            background-color: rgba(250, 250, 250, 1);
            border-radius: 2px;
            height: 22px;
            width: 48px;
        }

        .text_22 {
            width: 34px;
            height: 20px;
            overflow-wrap: break-word;
            color: rgba(67, 107, 255, 1);
            font-size: 12px;
            font-family: AlibabaPuHuiTiR;
            font-weight: normal;
            text-align: right;
            white-space: nowrap;
            line-height: 20px;
            margin: 1px 0 0 7px;
        }

        .text_23 {
            width: 168px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
        }

        .box_11 {
            width: 528px;
            height: 78px;
            margin: 4px 0 12px 16px;
        }

        .text-wrapper_13 {
            background-color: rgba(250, 250, 250, 1);
            border-radius: 2px;
            height: 22px;
            margin-top: 56px;
            width: 48px;
        }

        .text_24 {
            width: 34px;
            height: 20px;
            overflow-wrap: break-word;
            color: rgba(67, 107, 255, 1);
            font-size: 12px;
            font-family: AlibabaPuHuiTiR;
            font-weight: normal;
            text-align: right;
            white-space: nowrap;
            line-height: 20px;
            margin: 1px 0 0 7px;
        }

        .text-group_2 {
            width: 472px;
            height: 78px;
        }

        .text_25 {
            width: 472px;
            height: 44px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
        }

        .text_26 {
            width: 154px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin-top: 12px;
        }



        .thumbnail_9 {
            width: 16px;
            height: 16px;
        }



        .text_27 {
            width: 56px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 14px 0 0 16px;
        }

        .text_28 {
            width: 386px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 12px 0 0 16px;
        }

        .text_29 {
            width: 528px;
            height: 66px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 16px 16px;
        }

        .group_9 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 4px;
            width: 97%;
            height: 240px;
            border: 1px solid rgba(233, 233, 233, 1);
            margin: 16px 0 0 16px;
            position: relative;
        }

        .text_30 {
            width: 70px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 14px 0 0 16px;
        }

        .text-wrapper_15 {
            width: 528px;
            height: 88px;
            margin: 12px 0 16px 16px;
        }

        .text_31 {
            width: 42px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin-top: 33px;
        }

        .text_32 {
            width: 468px;
            height: 88px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin-left: 14px;
        }

        .group_10 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 4px;
            width: 97%;
            height: 560px;
            border: 1px solid rgba(233, 233, 233, 1);
            margin: 16px 0 0 16px;
            position: relative;
        }

        .text_33 {
            width: 56px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 14px 0 0 16px;
        }

        .text_34 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 12px 0 0 7px;
        }

        .text_35 {
            width: 528px;
            height: 66px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 0 16px;
        }

        .text_36 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 16px 0 0 7px;
        }

        .image-text_2 {
            width: 276px;
            height: 22px;
            margin: 8px 0 16px 16px;
        }

        .thumbnail_10 {
            width: 16px;
            height: 16px;
            margin-top: 3px;
        }

        .text-group_3 {
            width: 252px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin-left: 10px;
        }

        .text-wrapper_16 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 4px;
            width: 97%;
            height: 358px;
            border: 1px solid rgba(233, 233, 233, 1);
            margin: 16px 0 0 16px;
            position: relative;
        }

        .text_37 {
            width: 56px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 14px 0 0 16px;
        }

        .text_38 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 12px 0 0 7px;
        }

        .paragraph_1 {
            width: 483px;
            height: 44px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 0 16px;
        }

        .text_39 {
            width: 98px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 16px 0 0 7px;
        }

        .paragraph_2 {
            height: 44px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 16px 16px;
        }

        .text-wrapper_17 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 4px;
            width: 97%;
            height: 364px;
            border: 1px solid rgba(233, 233, 233, 1);
            margin: 16px 0 16px 16px;
            position: relative;
        }

        .text_40 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 14px 0 0 16px;
        }

        .text_41 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 12px 0 0 7px;
        }

        .text_42 {
            width: 528px;
            height: 44px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 0 16px;
        }

        .text_43 {
            width: 84px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(38, 38, 38, 1);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
            margin: 16px 0 0 7px;
        }

        .paragraph_3 {
            height: 44px;
            overflow-wrap: break-word;
            color: rgba(140, 140, 140, 1);
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            line-height: 22px;
            margin: 8px 0 16px 16px;
        }

        .marginl7 {
            margin-left: 7px;
        }
    }
}