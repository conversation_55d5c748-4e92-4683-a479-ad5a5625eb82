<template>
  <el-drawer v-model="is_show" direction="rtl" class="drawer_sales_capability" :close-on-click-modal="true">
    <template #header>
      <div class="vd_title">选择维度</div>
    </template>
    <template #default>
      <div class="ed_main flex-row">
        <div class="scc_left">
          <el-radio-group v-model="rd_dim_source" class="dim_source_radio" @change="onChangeDim">
            <el-radio-button label="系统预设" value="systemAssessments" />
            <el-radio-button label="自定义维度" value="customizedAssessments" />
          </el-radio-group>
          <MyTable ref="refTable" :cfg="datasSystem" @callback="cbTable">
            <template #col_ability_name="{ row }">
              {{ row['name'] }}
            </template>
            <template #col_task_name="{ row }">
              {{ row['name'] }}
            </template>
            <template #col_behavior="{ row }">
              <div v-for="(item, index) in row['details']" :key="index">
                {{ item }}
              </div>
            </template>
          </MyTable>
        </div>
        <div class="scc_right">
          <div class="selected-header">
            <span>已选择：({{ selectedMerged.length }}/{{ max_select_count }})</span>
            <div class="header-buttons">
              <!-- <span class="reset-btn" @click="resetToDefault">重置默认</span> -->
              <span class="clear-btn" @click="clearSelected">清空</span>
            </div>
          </div>
          <p></p>
          <div class="dim_type" v-if="selectedTags.system.length > 0">系统维度</div>
          <div class="selected-tags">
            <div v-for="(tag, index) in selectedTags.system" :key="index" class="tag-item">
              {{ tag.name }}
              <i class="el-icon-close" @click="removeTag(index)"></i>
            </div>
          </div>
          <div class="dim_type" v-if="selectedTags.custom.length > 0">自定义维度</div>
          <div class="selected-tags">
            <div v-for="(tag, index) in selectedTags.custom" :key="index" class="tag-item">
              {{ tag.name }}
              <i class="el-icon-close" @click="removeTag(index)"></i>
            </div>
          </div>
          <el-alert :title="`超过最大允许维度数${max_select_count}`" type="error"
            v-if="selectedMerged.length > max_select_count" />
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex-row flex-end">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm"
          :disabled="selectedMerged.length < 1 || selectedMerged.length > max_select_count">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { updateXmTopicDimension } from "@/app_admin/tools/api.js";
import { getSalesAllDims } from "@/js/api.js";

import { systemIdTypeMap } from "@/js/const_value.js";

const is_show = ref(false);
const refTable = ref(null);
const domInfo = ref({});
const rd_dim_source = ref("systemAssessments");
const allDimData = ref({})
const selectedTags = ref({ system: [], custom: [] });
const defaultSelectedTags = ref({ system: [], custom: [] });
const hasCleared = ref(false);
const selectedMerged = computed(() => {
  return [...selectedTags.value.system, ...selectedTags.value.custom]
})

const emit = defineEmits(["callback"]);

const props = defineProps({
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
  systemId: {
    type: Number,
    required: false,
    default: 0,
  },
});
const max_select_count = computed(() => {
  return props.systemId == 205 ? 6 : 10;
});

const _updateTableData = () => {
  let list = allDimData.value[systemIdTypeMap[domInfo.value.systemId]][rd_dim_source.value];

  // 只在初始化时从domInfo恢复选中状态到默认数据
  if (defaultSelectedTags.value.system.length === 0 && defaultSelectedTags.value.custom.length === 0) {
    defaultSelectedTags.value['system'] = domInfo.value.content.filter((x) => x["system"]);
    defaultSelectedTags.value['custom'] = domInfo.value.content.filter((x) => !x["system"]);
    // 初始化时，将默认数据复制到当前选中状态
    selectedTags.value = {
      system: [...defaultSelectedTags.value.system],
      custom: [...defaultSelectedTags.value.custom]
    };
  }

  // 使用当前selectedTags状态来确定选中项
  const currentDimName = rd_dim_source.value == 'systemAssessments' ? 'system' : 'custom';
  const currentCheckIds = selectedTags.value[currentDimName].map(item => item.id);

  list.forEach((item) => {
    item["is_checked_"] = currentCheckIds.includes(item.id)
  });
  refTable.value.update_data(list, list.length);
  nextTick(() => {
    refTable.value.manualCheck(currentCheckIds);
  });
}

const onChangeDim = () => {
  _updateTableData()
}

const getTabledata = () => {
  getSalesAllDims().then((res) => {
    allDimData.value = res.data;
    _updateTableData()
  });
};

const datasSystem = reactive({
  tableid: '',
  param: {},
  need_header: false,
  need_init_load: false,
  form: {},
  pk: "id",
  modal_type: "link",
  search_txt: "查询",
  show_btn_add: false,
  show_search: false,
  is_checkable: true,
  enable_checkbox: true,
  show_search_btn: true,
  show_link_column: false,
  show_link_delete: false,
  columns: props.systemId == 205 ? ["ability_name", "behavior"] : ["task_name", "behavior"],
  template: props.systemId == 205 ? ["ability_name", "behavior"] : ["task_name", "behavior"],
});

const cbTable = (action, data) => {
  if (action == "check_row") {
    const dimName = rd_dim_source.value == 'systemAssessments' ? 'system' : 'custom'
    selectedTags.value[dimName] = data.checked;
  }
};

const onCancel = () => {
  is_show.value = false;
};

const onConfirm = () => {
  const submitList = selectedMerged.value.map((x) => {
    return {
      id: x.id,
      name: x.name,
      system: x.system,
      description: x.description,
      details: x.details
    }
  })
  const param = {
    content: JSON.stringify(toRaw(submitList))
  }
  console.log('666', param)
  updateXmTopicDimension(domInfo.value.topicId, domInfo.value.id, param).then((res) => {
    if (res.code == 0) {
      is_show.value = false;
      ElMessage.success("保存成功");
      emit("callback", "reload", toRaw(submitList));
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const show = (data) => {
  rd_dim_source.value = 'systemAssessments';
  data.content = JSON.parse(data.content);
  domInfo.value = data;
  // 重置选中状态和默认数据
  selectedTags.value = { system: [], custom: [] };
  defaultSelectedTags.value = { system: [], custom: [] };
  hasCleared.value = false; // 重置清空标志
  is_show.value = true;
  nextTick(() => {
    getTabledata();
  });
};

const cancel = () => {
  is_show.value = false;
};

const removeTag = (index) => {
  const dimName = rd_dim_source.value == 'systemAssessments' ? 'system' : 'custom';
  const removedTag = selectedTags.value[dimName][index];

  selectedTags.value[dimName].splice(index, 1);

  if (refTable.value && removedTag) {
    const currentList = allDimData.value[systemIdTypeMap[domInfo.value.systemId]][rd_dim_source.value];
    const targetItem = currentList.find(item => item.id === removedTag.id);
    if (targetItem) {
      targetItem.is_checked_ = false;
    }
    const currentCheckIds = selectedTags.value[dimName].map(item => item.id);
    refTable.value.manualCheck(currentCheckIds);
  }
};

const clearSelected = () => {
  selectedTags.value = { system: [], custom: [] };
  hasCleared.value = true;
  // 清空表格选中项
  if (refTable.value) {
    refTable.value.manualCheck([]);
  }
};

// 新增：重置到默认选中状态
const resetToDefault = () => {
  selectedTags.value = {
    system: [...defaultSelectedTags.value.system],
    custom: [...defaultSelectedTags.value.custom]
  };
  hasCleared.value = false;
  // 更新表格选中状态
  if (refTable.value) {
    const currentDimName = rd_dim_source.value == 'systemAssessments' ? 'system' : 'custom';
    const currentCheckIds = selectedTags.value[currentDimName].map(item => item.id);
    refTable.value.manualCheck(currentCheckIds);
  }
};

defineExpose({
  show,
  onCancel,
  onConfirm,
  is_show,
  datasSystem,
  cancel,
  resetToDefault,
});
</script>

<style lang="scss">
.drawer_sales_capability {
  width: 876px !important;

  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    .ed_main {
      height: calc(100vh - 152px);

      .scc_left {
        width: 70%;
        border-right: 1px solid #e9e9e9;
        padding: 16px;

        .dim_source_radio {
          margin-bottom: 12px;

          .is-active {
            .el-radio-button__inner {
              background-color: #fff;
              color: #436BFF;
            }
          }
        }
      }

      .scc_right {
        width: 30%;
        padding: 16px;

        .dim_type {
          border-left: 4px solid #436BFF;
          padding-left: 10px;
          line-height: 20px;
          font-size: 14px;
          margin-bottom: 12px;
        }

        .selected-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .header-buttons {
            display: flex;
            gap: 16px;

            .reset-btn {
              color: #436BFF;
              cursor: pointer;
              font-size: 14px;
            }

            .clear-btn {
              color: #436BFF;
              cursor: pointer;
              font-size: 14px;
            }
          }
        }

        .selected-tags {
          .tag-item {
            display: inline-flex;
            align-items: center;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 0 8px 8px 0;
            font-size: 14px;

            i {
              margin-left: 4px;
              cursor: pointer;
              font-size: 12px;
              color: #999;

              &:hover {
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}
</style>
