import { defineStore } from 'pinia'
import { getPptCategorys, getGoodsCategory } from "@/app_admin/tools/api.js"

export default defineStore('admin_file', {
    state: () => ({
        upload_files: [],
        ppt_options: [],
        comm_options: [],
    }),
    actions: {
        get_ppt_cates() {
            return new Promise((resolve) => {
                const param = {
                    searchKey: '',
                    pageSize: 50,
                    pageNumber: 1,
                }
                getPptCategorys(param).then(resp => {
                    if (resp.code == 0 && resp.data.totalNum > 0) {
                        this.ppt_options = resp.data.datas.map(x => {
                            return {
                                value: x.id,
                                label: x.categoryName
                            }
                        })
                        resolve(this.ppt_options)
                    } else {
                        resolve([])
                    }
                }).catch(e => {
                    console.log('error get_ppts', e)
                    resolve([])
                })
            })
        },
        get_goods_cates() {
            return new Promise((resolve) => {
                const param = {
                    searchKey: '',
                    pageSize: 50,
                    pageNumber: 1,
                }
                getGoodsCategory(param).then(resp => {
                    if (resp.code == 0 && resp.data.length > 0) {
                        this.comm_options = resp.data.map(x => {
                            return {
                                value: x.id,
                                label: x.name
                            }
                        })
                        resolve(this.comm_options)
                    } else {
                        resolve([])
                    }
                }).catch(e => {
                    console.log('error get_commodity_cate_options', e)
                    resolve([])
                })
            })
        },
        get_communications_cates() {
            return new Promise((resolve) => {
                resolve([])
            })
        },
        get_config(type) {
            const config = {
                ppt: {
                    fileTypes: ["ppt", "pptx"],
                    maxSizeMb: 200,
                    api: this.get_ppt_cates,
                },
                goods: {
                    fileTypes: ['xls', 'xlsx'],
                    maxSizeMb: 2,
                    api: this.get_goods_cates,
                },
                communication: {
                    fileTypes: ['xls', 'xlsx', 'pdf', 'doc', 'docx', 'ppt', 'pptx'],
                    maxSizeMb: 10,
                    api: this.get_communications_cates
                }
            }

            const getHint = (c) => {
                return `支持${c.fileTypes.join(',')}格式，单个文件不大于${c.maxSizeMb}M`
            }

            return new Promise((resolve) => {
                config[type].api().then(_options => {
                    const cfg = config[type]
                    cfg['options'] = _options
                    cfg['subHint'] = getHint(cfg);
                    cfg['type'] = type
                    cfg['param'] = {}
                    resolve(cfg)
                })
            })
        },
        add_file(param) {
            // status:waiting,uploading,uploaded,delete,error
            this.upload_files.push(param)
        },
        update_file_status(startTime, new_status) {
            for (let i = 0; i < this.upload_files.length; i++) {
                if (startTime == this.upload_files[i].startTime) {
                    this.upload_files[i]['status'] = new_status;
                }
            }
        },
        get_files() {
            return this.upload_files
                .filter((x) => ['waiting', 'uploading'].includes(x.status))
                .map((x) => {
                    const { subject, startTime, size, status } = x;
                    return { subject, startTime, size, status };
                });
        },

    }
})

