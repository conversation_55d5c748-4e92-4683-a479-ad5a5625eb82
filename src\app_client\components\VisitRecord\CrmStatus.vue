<template>
    <div class="visit_filter_item">
        <el-select v-model="localValue" multiple collapse-tags placeholder="CRM同步状态" popper-class="vf-header"
            style="width: 150px">
            <el-option v-for="item in options" :key="item.type" :label="item.description" :value="item.type" />
        </el-select>
    </div>
</template>

<script setup>
const props = defineProps({
    value: {
        type: Array,
        required: true
    }
})


const emit = defineEmits(['update:value', 'reload'])
const localValue = ref(props.value)
const options = ref([])

watch(localValue, (newValue) => {
    emit('update:value', toRaw(newValue))
    emit('reload', '')
})

const updateValue = (v) => {
    localValue.value = v
}

const query = () => {
    options.value = [
        { type: 'NO_SYNC', description: '未同步' },
        { type: 'SUCCESS', description: '已同步' },
        { type: 'FAIL', description: '同步失败' },
        { type: 'NO_CUSTOMER', description: '客户不存在' }
    ]
}

onMounted(() => {
    query();
})

defineExpose({
    options, updateValue
})

</script>

<style lang="scss">
.custom-header {
    .el-checkbox {
        display: flex;
        height: unset;
    }
}
</style>