<template>
  <div class="table_wrap" ref="refContainer">
    <!-- 搜索过滤条 -->
    <div class="table_box">
      <div class="search_box" v-if="cfg.need_header">
        <el-button type="primary" v-if="cfg.show_btn_add" @click="btnAdd" class="btn_add">{{ cfg.add_txt }}</el-button>
        <slot name="_header_left"> </slot>
        <div class="flex-grow"></div>
        <slot name="_header_filter"> </slot>
        <el-input v-model="cfg.param.searchKey" :placeholder="cfg.search_ph" class="search_input" v-if="cfg.show_search"
          @keyup.enter="search" clearable :prefix-icon="Search" @clear="search">
        </el-input>
        <el-button type="primary" @click="search" v-if="cfg.show_search_btn">{{
          cfg.search_txt
        }}</el-button>
        <!-- <div class="flex_blank"></div> -->
        <slot name="_header_right_pre"></slot>
        <BtnColumn ref="refColumns" v-if="cfg.show_btn_column" @callback="cbColumn" />
        <slot name="_header_right"></slot>
      </div>
      <slot name="_header_bottom"></slot>
      <!-- 表格 -->
      <el-table :class="`table_class ${cfg.enable_checkbox_1 ? 'max_select_1' : ''}`" :data="cfg.data" ref="refTable"
        header-cell-class-name="table-header" table-layout="auto" :highlight-current-row="cfg.enable_highlight"
        :row-class-name="get_row_class_name" @current-change="handleCurrentChange" v-loading="loading"
        @sort-change="handleSortChange" @selection-change="handleSelectionChange" :span-method="spanColumnMethod">
        <el-table-column type="selection" width="30" v-if="cfg.enable_checkbox" :selectable="checkSelectable" />
        <el-table-column type="expand" v-if="cfg.need_expand_column">
          <template #default="props">
            <slot name="expand_row" :row="props.row" />
          </template>
        </el-table-column>
        <el-table-column v-for="col in showColumns" :key="col" :prop="col" alignment="center"
          :sortable="cfg.sortables.includes(col) ? cfg.sortable : false" :class-name="`col_${col}`"
          :width="getColumnWidth(col)" :fixed="cfg.fixed_column === col">
          <template #header="scope">
            <div v-if="cfg.template_header.indexOf(col) > -1">
              <slot :name="'header_' + col" :row="scope.row" />
            </div>
            <div v-else>
              {{ trans(col, cfg.lang_pre) }}
            </div>
          </template>
          <template #default="scope">
            <div v-if="cfg.template.indexOf(col) > -1">
              <slot :name="'col_' + col" :row="scope.row" />
            </div>
            <div v-else>
              {{ scope.row[col] }}
            </div>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column v-if="cfg.show_link_column" label="操作" alignment="center" class-name="col_operation_"
          fixed="right">
          <template #default="scope">
            <slot name="_link_pre" :row="scope.row" />
            <el-button type="primary" text @click="linkClick(scope.row, 'view')" v-if="cfg.show_link_view">{{
              cfg.view_txt }}
            </el-button>
            <el-button type="primary" text @click="linkClick(scope.row, 'edit')" v-if="cfg.show_link_edit">编辑
            </el-button>
            <el-button type="primary" text @click="linkClickDelete(scope.row)"
              v-if="cfg.show_link_delete && cfg.fnIsRowNeedDelBtn(scope.row)">
              删除
            </el-button>
            <slot name="_link_post" :row="scope.row" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pagination_box" v-if="cfg.show_pager && cfg.pageTotal > cfg.param.pageSize">
      <el-pagination background :layout="cfg.pager_layer" :current-page="cfg.param.pageNumber"
        :page-size="cfg.param.pageSize" :total="cfg.pageTotal" @current-change="handlePageChange"
        @size-change="handleSizeChange"></el-pagination>
    </div>

    <!-- 新增、编辑弹出框 -->
    <el-dialog :title="getModalTitle()" v-model="showModal" width="30%">
      <el-form :label-width="cfg.form_label_width" ref="refForm" :model="props.cfg.form" :rules="cfg.rules">
        <slot name="_form"></slot>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="btnModalCancel">取 消</el-button>
          <el-button type="primary" @click="btnModalSave">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  deepMerge,
  getSpanArr,
  difference,
  require_factory,
  confirmDelete,
} from "@/js/utils.js";
import { Search } from "@element-plus/icons-vue";
import trans from "@/js/lang.js";
import BtnColumn from "./BtnColumn.vue";
const { proxy } = getCurrentInstance();
const refContainer = ref();
let pressShfit = false;
const props = defineProps({
  cfg: {
    type: Object,
  },
});
const emit = defineEmits(["callback"]);
const refTable = ref(null);
const refForm = ref(null);
const refColumns = ref();
const loading = ref(false);

const showColumns = ref([])




const _cfg = {
  tableid: "",
  title: "",
  param: {
    searchKey: "",
    pageSize: 10,
    pageNumber: 1,
    orderBy: "",
    asc: true,
  },
  pageTotal: 1,
  pageSizes: [10, 20, 40, 100],
  form: {},
  data: [],
  sortable: false,
  min_width: "200px",
  fixed_column: "",
  columns: [],
  column_widths: {},
  columns_span: [],
  column_key_span: "",
  template: [],
  template_header: [],
  use_cache: false,
  show_pager: true,
  get_list: "get_list",
  pk: "id",
  rules: {},
  requires: [],
  sortables: [],
  always_show_columns: [],
  need_header: true,
  search_txt: "搜索",
  search_ph: "搜索关键字",
  add_txt: "新建",
  view_txt: "查看",
  edit_txt: "编辑",
  need_init_load: true,
  layout: "total,  prev, pager, next, jumper",
  show_btn_add: false,
  show_search: true,
  show_search_btn: false,
  enable_highlight: false,
  show_link_column: false,
  show_link_view: false,
  enable_checkbox: false,
  show_link_delete: false,
  show_btn_column: true,
  need_expand_column: false,
  is_checkable: true,
  enable_select: false,
  modal_type: "modal",
  lang_pre: "",
  delete_hint_column: "",
  urlGet: undefined,
  urlDelete: undefined,
  urlHttp: undefined,
  max_search_length: undefined,
  pageQuery: undefined,
  delete_hint: "",
  delete_title: "",
  enable_checkbox_1: false,
  ObjectSpanMethod: () => {
    return {
      rowspan: 1,
      colspan: 1,
    };
  },
  fnIsRowNeedDelBtn: (row) => {
    return true;
  },
};
if (props.cfg.requires) {
  _cfg.rules = require_factory(props.cfg.requires);
}
let cfg = reactive(deepMerge(_cfg, props.cfg));

//弹窗模式，''/add/edit
const modalMode = ref("");
let showModal = ref(false);

const cbColumn = (new_columns) => {
  const selects = [...cfg.always_show_columns, ...new_columns];
  g.appStore.setStore(_key_columns(), selects);
  _updateShowColumns();
};
const addCols = (records) => {
  if (cfg.columns.length == 0 && records.length > 0) {
    cfg.columns = Object.keys(records[0]);
  }
};

const setTableDefaultSelectColumns = () => {
  const savedColumns = g.appStore.getStore(_key_columns(), []);
  if (savedColumns.length === 0) {
    g.appStore.removeStore(_key_columns());
    if (cfg.default_select_columns) {
      g.appStore.setStore(_key_columns(), cfg.default_select_columns);
    } else {
      g.appStore.setStore(_key_columns(), cfg.columns);
    }
  } else {
    cfg.default_select_columns = savedColumns;
  }
};

const getSearchParam = () => {
  return deepMerge(toRaw(cfg.param), props.cfg.param);
};

const setLoading = (status) => {
  loading.value = status;
};

const setAlwaysShowColumns = (columns) => {
  cfg.fixed_column = columns[0];
  cfg.always_show_columns = columns;
};

const search = () => {
  cfg.param.pageNumber = 1;
  _search();
}

//搜索
const _search = () => {
  if (!cfg.urlGet) {
    if (typeof cfg.pageQuery == "function") {
      cfg.pageQuery();
    }
    return;
  }
  const p = getSearchParam();

  loading.value = true;
  cfg
    .urlGet(p)
    .then((resp) => {
      if (resp.code == 0) {
        if (resp.data.datas) {
          update_data(resp.data.datas, resp.data.totalNum || 0);
          emit("callback", "after_search", toRaw(cfg.data));
        } else {
          ElMessage.error(`获取数据失败`);
          loading.value = false;
        }
      }
    })
    .catch((error) => {
      loading.value = false;
      console.log("err search table", error);
    });
};

const update_data = (data, count) => {
  if (typeof count == "undefined") {
    console.log(" table update data error no count", data);
    return "";
  }
  cfg.pageTotal = count;
  cfg.columns_span = props.cfg.columns_span || [];
  cfg.column_key_span = props.cfg.column_key_span || [];
  cfg.data = getSpanArr(data, cfg.columns_span, cfg.column_key_span);
  cfg.data.forEach((element, idx) => {
    element["_index"] = idx;
  });
  cfg.pageTotal = count;
  addCols(data);
  _updateShowColumns()
  if (cfg.enable_checkbox) {
    setCheckable(cfg.is_checkable);
  }
  //操作数据后更新视图
  loading.value = false;
};

const spanColumnMethod = ({ row, column, rowIndex, columnIndex }) => {
  let rowspan = 1;
  let col = column.property;
  if (cfg.columns_span.includes(col)) {
    rowspan = row[`rowspan_${col}`];
  }
  return {
    rowspan,
    colspan: 1,
  };
};

// 分页导航切换
const handlePageChange = (val) => {
  cfg.param.pageNumber = val;
  _search();
};

//分页 每页行数切换
const handleSizeChange = (val) => {
  cfg.param.pageSize = val;
  _search();
};

//每行数据的编辑按钮
const linkClick = (row, mode) => {
  modalMode.value = mode;
  if (cfg.modal_type == "modal") {
    showModal.value = true;
  }
  emit("callback", "init_" + mode, toRaw(row));
};
//每行数据的删除按钮
const linkClickDelete = (row) => {
  const title = `【${row[cfg.delete_hint_column]}】`;
  confirmDelete(title, (status) => {
    if (status) {
      linkDelete(row);
    }
  });
};

//每行数据的删除按钮
const linkDelete = (row) => {
  cfg
    .urlDelete(toRaw(row))
    .then((resp) => {
      if (resp.code == 0) {
        ElMessage.success("删除成功");
        search();
      } else {
        ElMessage.error(`删除失败!错误信息 ${resp.message}`);
      }
    })
    .catch((e) => {
      ElMessage.error(`删除失败`);
    });
};

//右上角的添加按钮
const btnAdd = () => {
  modalMode.value = "add";
  if (cfg.modal_type == "modal") {
    showModal.value = true;
  }
  emit("callback", "init_add", "");
};

const doHttp = (action, body) => {
  cfg.urlHttp(action, body).then((resp) => {
    if (resp.code == 0) {
      ElMessage.success("操作成功");
      search();
      btnModalCancel();
    } else {
      console.log("resp error", resp);
      ElMessage.error(`操作失败！错误信息 ${resp.message}`);
    }
  });
};

//弹框里的保存按钮
const btnModalSave = () => {
  refForm.value.validate((valid) => {
    if (valid) {
      const action = modalMode.value == "add" ? "create" : "update";
      emit("callback", "before_" + action, "");
    } else {
      return false;
    }
  });
};

//隐藏弹框
const btnModalCancel = () => {
  modalMode.value = "";
  showModal.value = false;
};

//当启用行选择时，用户点选某行时触发
const currSelectRow = ref({});
const handleCurrentChange = (row) => {
  if (cfg.enable_select) {
    currSelectRow.value = toRaw(row);
    emit("callback", "select_row", toRaw(row));
  }
};

const manualCheck = (pks) => {
  for (let i = 0; i < cfg.data.length; i++) {
    const row = cfg.data[i];
    const selected = pks.includes(row[cfg.pk]);
    refTable.value.toggleRowSelection(row, selected);
  }
  proxy.$forceUpdate();
};

const manualSelect = (row) => {
  refTable.value.setCurrentRow(row);
};

const get_row_class_name = ({ row, index }) => {
  let cname = "";
  if (cfg.pk && row[cfg.pk]) {
    cname = row[cfg.pk] == currSelectRow.value[cfg.pk] ? "active_row" : "";
  }
  return cname;
};

const handleSortChange = ({ prop, order }) => {
  if (order == "ascending") {
    cfg.param.orderBy = prop;
    cfg.param.asc = true;
  } else if (order == "descending") {
    cfg.param.orderBy = prop;
    cfg.param.asc = false;
  } else {
    cfg.param.orderBy = "";
    cfg.param.asc = "";
  }
  search();
};

const getModalTitle = () => {
  let title = "";
  if (modalMode.value) {
    const map = {
      add: cfg.add_txt,
      edit: cfg.edit_txt,
      view: cfg.view_txt,
    };
    title = map[modalMode.value] + cfg.title;
  }
  return title;
};

const gotoIdxPage = (idx) => {
  let page = idx / cfg.param.pageSize;
  page = Math.max(1, Math.round(page, 0));
  cfg.param.pageNumber = page;
  _search();
};

// 在页面钩子 mounted() 处调用此函数，增加按键监听事件
const setKeyStatus = (key, status) => {
  switch (key) {
    case "Shift":
      if (!pressShfit === status) {
        pressShfit = status;
      }
      break;
  }
};

const onkeydown = (e) => {
  setKeyStatus(e.key, true);
};
const onkeyup = (e) => {
  setKeyStatus(e.key, false);
};

const handleSelectionChange = (row) => {
  console.log('handleSelectionChange', row)
  const all_pks = cfg.data.map((x) => x[cfg.pk]);
  const ck_pks = row.map((x) => x[cfg.pk]);
  const uncks = difference(all_pks, ck_pks);
  const data = {
    checked: row,
    unchecked: cfg.data.filter((x) => uncks.includes(x[cfg.pk])),
  };
  emit("callback", "check_row", data);
};

const setCheckable = (status) => {
  cfg.is_checkable = status;
  for (let i = 0; i < cfg.data.length; i++) {
    cfg.data[i]["is_checkable_"] = status;
  }
  //操作数据后更新视图
  proxy.$forceUpdate();
};

const checkSelectable = (row) => {
  return row.is_checkable_;
};

const init = (cfg_) => {
  cfg = reactive(deepMerge(_cfg, cfg_));
  search();
};

let boxDom;
const addMouseWheel = (event) => {
  if (pressShfit) {
    event.preventDefault();
    boxDom.scrollBy({
      left: event.deltaY,
    });
  }
};

const _key_columns = () => {
  return `${cfg.tableid}_columns2`;
};

const _updateShowColumns = () => {
  let columns = cfg.columns;
  if (columns.length == 0) return;
  if (cfg.tableid) {
    columns = g.appStore.getStore(_key_columns(), cfg.columns);
  }
  showColumns.value = columns;
  emit("callback", "update_columns", columns);
  proxy.$forceUpdate();
};

const getColumnWidth = (col) => {
  const columns = Object.keys(cfg.column_widths);
  if (columns.includes(col)) {
    return cfg.column_widths[col];
  } else {
    return "";
  }
};

onMounted(() => {
  showColumns.value = props.cfg.columns || []
  if (cfg.need_init_load && !!cfg.urlGet) {
    search();
  }
  boxDom = document.querySelector(".el-scrollbar__wrap");
  // boxDom.addEventListener("wheel", addMouseWheel);
  boxDom.addEventListener("wheel", addMouseWheel, { passive: false });
  document.addEventListener("keydown", onkeydown);
  document.addEventListener("keyup", onkeyup);
  setTableDefaultSelectColumns();
  const selects = cfg.columns.filter((x) => !cfg.always_show_columns.includes(x));
  if (cfg.need_header && cfg.show_btn_column) {
    refColumns.value.init(
      _key_columns(),
      toRaw(selects),
      toRaw(cfg.default_select_columns)
    );
  }
});

onUnmounted(() => {
  boxDom.removeEventListener("wheel", addMouseWheel);
  document.removeEventListener("keydown", onkeydown);
  document.removeEventListener("keyup", onkeydown);
});

defineExpose({
  props,
  trans,
  refTable,
  refForm,
  cfg,
  cbColumn,
  showModal,
  refContainer,
  BtnColumn,
  init,
  setCheckable,
  checkSelectable,
  getModalTitle,
  spanColumnMethod,
  handleSortChange,
  getSearchParam,
  setLoading,
  handlePageChange,
  search,
  Search,
  linkClick,
  linkDelete,
  btnAdd,
  doHttp,
  update_data,
  setAlwaysShowColumns,
  gotoIdxPage,
  btnModalSave,
  btnModalCancel,
  manualSelect,
  manualCheck,
  handleCurrentChange,
  getColumnWidth,
});
</script>

<style lang="scss">
.table_wrap {
  width: 100%;
  height: 100%;
  // padding: 24px;
  box-sizing: border-box;

  .table_box {
    width: 100%;
    box-sizing: border-box;

    .table-header {
      .cell {
        display: flex;
        flex-direction: row;

        .caret-wrapper {
          margin-top: 6px;
        }
      }
    }

    .search_box {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 16px;
      gap: 12px;

      .search_input {
        width: 200px;
      }
    }

    .table_class {
      border-bottom: 1px solid #ebeef5;

      table {
        overflow-x: auto;
        width: 100%;

        thead {
          tr {
            th {
              background-color: #F9FAFC !important;
              color: #262626;
              font-weight: 500;
            }
          }
        }

        .col_operation_ {
          width: 104px;

          .cell {
            display: flex;
            flex-direction: row;
          }

          .el-button {
            padding: 0;
          }
        }
      }
    }

    .el-table__inner-wrapper::before {
      background-color: #fff;
    }
  }

  .pagination_box {
    display: flex;
    justify-content: end;
    margin-top: 6px;
    padding: 0 16px;
  }
}

.max_select_1 .table-header .el-checkbox {
  display: none !important;
}
</style>
