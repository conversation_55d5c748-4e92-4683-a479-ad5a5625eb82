<template>
    <div class="ai_tg_box">
        <AppList ref="refFuncList" />
    </div>
</template>

<script setup>
import AppList from '@/components/AppList.vue';

const lists = ref({ data: [] })
const refFuncList = ref(null)

onMounted(() => {
    lists.value = g.clientStore.getMenuList();
    refFuncList.value.init(lists.value.data)
})

defineExpose({
    AppList,
})

</script>

<style lang="scss">
.electron-layout {
    .mac-titlebar {
        background: #E9F1FF;
    }
}

.ai_tg_box {
    background-color: aliceblue;

    .app_list_wrap {
        padding-top: 24px;
    }
}

.mac-content {
    .app_list_wrap {
        height: calc(100vh - 65px);
    }
}

.win-content {
    .app_list_wrap {
        height: calc(100vh - 24px);
    }
}
</style>
