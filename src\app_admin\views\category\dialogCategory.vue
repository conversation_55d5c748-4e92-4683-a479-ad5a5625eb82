<template>
  <Modal
    ref="refModal"
    @callback="cbModal"
    class="create_cate_modal"
    destroy-on-close
  >
    <el-form
      ref="refForm"
      :model="formData"
      label-width="auto"
      label-position="top"
      size="default"
      :rules="rules"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入分类名称，最多10个字"
          maxlength="10"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { addPptCategory, updatePptCategory } from "@/app_admin/tools/api.js";
import {
  apiHintWrap,
  checkHasSpecChar,
  removeSpaces,
} from "@/app_admin/tools/utils.js";
import Modal from "@/components/Modal.vue";
const emit = defineEmits(["callback"]);

const refModal = ref();
const refForm = ref();
let mode = "";
let categoryId = "";

const defaultForm = {
  name: "",
};

const formData = ref(defaultForm);

const cbModal = (action) => {
  if (action == "confirm") {
    if (!refForm.value) return;
    refForm.value.validate((valid, fields) => {
      if (valid) {
        const categoryName = removeSpaces(formData.value.name);

        if (checkHasSpecChar(categoryName)) {
          ElMessage.error(`请输入数字、字母、汉字，不可以含有特殊字符`);
          return;
        }

        if (mode == "add") {
          const param = {
            categoryName,
          };
          apiHintWrap(addPptCategory(param), "创建分类").then(({ status }) => {
            if (status) {
              formData.value.name = "";
              emit("callback", "reload");
              refModal.value.hide();
            }
          });
        } else {
          const param = { categoryName, categoryId };
          apiHintWrap(updatePptCategory(param), "更新分类").then(
            ({ status }) => {
              if (status) {
                formData.value.name = "";
                emit("callback", "reload");
                refModal.value.hide();
              }
            }
          );
        }
      }
    });
  }
};

const show = () => {
  mode = "add";
  const cfg = {
    title: "创建分类",
    width: "480px",
  };
  formData.value.name = "";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = ({ id, categoryName }) => {
  mode = "edit";
  categoryId = id;
  const cfg = {
    title: "编辑分类",
    width: "480px",
  };
  formData.value.name = categoryName;
  refModal.value.show(cfg);
};

const rules = reactive({
  name: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    { min: 2, max: 10, message: "长度需要在2到10之间", trigger: "blur" },
  ],
});

defineExpose({
  show,
  formData,
  refModal,
  show_edit,
  refForm,
});
</script>
<style lang="scss">
.create_cate_modal {
  .el-dialog__body {
    height: 120px;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }
}
</style>
