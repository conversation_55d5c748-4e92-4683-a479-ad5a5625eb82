<template>
    <div>
        <DrawerSelectMixed @callback="onSelectUser" ref="refUser" type="dept" :allDept="false" :enableClose="true"
            :mustSubDept="true" />
        <br />
        DrawerSelectMixed: {{ dataSelect }}
    </div>
</template>

<script setup>
import DrawerSelectMixed from '@/components/DrawerSelect/DrawerSelectMixed.vue';

const refUser = ref();
const dataSelect = ref({});

const onSelectUser = (action, data) => {
    dataSelect.value = data;
}

defineExpose({
    dataSelect,
    refUser,
    onSelectUser,
    DrawerSelectMixed
})
</script>

<style lang="scss">
.mixed_input_box {
    width: 200px;
}
</style>