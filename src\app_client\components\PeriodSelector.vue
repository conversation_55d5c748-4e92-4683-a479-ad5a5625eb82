<template>
    <el-radio-group :model-value="modelValue" @change="onChange">
        <el-radio-button value="weekly">本周</el-radio-button>
        <el-radio-button value="monthly">本月</el-radio-button>
        <el-radio-button value="quarterly">本季</el-radio-button>
        <el-radio-button value="yearly">本年</el-radio-button>
    </el-radio-group>
</template>

<script setup>
defineProps({
    modelValue: {
        type: String,
        required: true,
    }
});
const emit = defineEmits(['update:modelValue', 'change']);
const onChange = (val) => {
    emit('update:modelValue', val);
    emit('change', val);
}
</script>