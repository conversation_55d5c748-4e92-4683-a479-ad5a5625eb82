<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close class="sale_edit_mydim_wrap">
    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules">
      <el-form-item :label="props.systemId == 205 ? '能力名称' : '任务名称'" prop="name" required>
        <el-input v-model.trim="formData.name" maxlength="50" show-word-limit placeholder="例如：产品讲解能力" />
      </el-form-item>
      <el-form-item label="行为表现描述" prop="details" required>
        <div class="input_wrapper" v-for="(item, idx) in details">
          <div class="input_row">
            <div class="seq">{{ idx + 1 }}</div>
            <el-input type="textarea" class="md_input" v-model.trim="details[idx]" maxlength="200" show-word-limit
              placeholder="例如：能清晰讲解产品功能和使用场景" @change="onChange" />
            <el-tooltip :content="details.length === 1 ? '不可删除，至少有一条描述' : ''" placement="top"
              :disabled="details.length > 1">
              <el-icon @click="onDelete(idx)">
                <Delete />
              </el-icon>
            </el-tooltip>
          </div>
        </div>
        <div class="demd_btn flex-row" v-show="details.length < max_length" @click="onAdd">
          <el-icon>
            <Plus />
          </el-icon>
          <div>
            添加描述 ({{ details.length }}/{{ max_length }})
          </div>
        </div>
      </el-form-item>
    </el-form>
  </Modal>
</template>

<script setup>
import { Plus, Delete } from "@element-plus/icons-vue";
import Modal from "@/components/Modal.vue";
import { genUUID } from '@/js/utils.js'
import { createCustomizeDimension, updateCustomizeDimension } from "@/app_admin/tools/api.js";
import { ElMessage } from "element-plus";
const props = defineProps(["systemId"]);
const emit = defineEmits(["callback"]);
const refModal = ref();
const title = ref("");
const refForm = ref("");
const details = ref([""])
const isAdd = ref(true);
const max_length = ref(5);

let dimension_type = '';
const defaultForm = {
  id: '',
  name: "",
  details: []
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "600px",
};

const onAdd = () => {
  const lastValue = details.value[details.value.length - 1]
  if (lastValue.trim() !== '') {
    details.value.push('')
    return
  } else {
    ElMessage.warning('请先输入最后一条描述')
  }
}

const onDelete = (index) => {
  if (details.value.length > 1) {
    details.value.splice(index, 1)
    onChange()
  }
}
const onChange = () => {
  formData.value.details = details.value.filter((item) => item.trim() !== "");
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const show_add = (dimension_type_, datas) => {
  isAdd.value = true;
  dimension_type = dimension_type_;
  formData.value = { ...defaultForm };
  details.value = [""];
  formData.value.id = genUUID();
  cfg["title"] = "添加维度";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (dimension_type_, row) => {
  console.log(3323, dimension_type_, row)
  isAdd.value = false;
  dimension_type = dimension_type_;
  formData.value = { ...row };
  details.value = [...row.details];
  cfg["title"] = "编辑维度";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};


const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const _getData = () => {
  return toRaw(formData.value);
}

const _add = () => {
  const param = _getData()
  createCustomizeDimension(dimension_type, param)
    .then((resp) => {
      if (resp.code == 0) {
        ElMessage.success(`${cfg["title"]}成功`);
        emit("callback", "reload");
        btnCancel();
      } else {
        ElMessage.error(`${resp.message}`);
      }
    })
    .catch((e) => {
      ElMessage.error(`${cfg["title"]}失败`);
    });
}

const _update = () => {
  const param = _getData()
  updateCustomizeDimension(dimension_type, formData.value.id, param)
    .then((resp) => {
      if (resp.code == 0) {
        ElMessage.success(`${cfg["title"]}成功`);
        emit("callback", "reload");
        btnCancel();
      } else {
        ElMessage.error(`${resp.message}`);
      }
    })
    .catch((e) => {
      ElMessage.error(`${cfg["title"]}失败`);
    });
};

const btnOK = () => {
  if (!refForm.value) return;
  refForm.value.validate((valid, fields) => {
    if (valid) {
      if (isAdd.value) {
        _add();
      } else {
        _update();
      }
    } else {
      console.log("not valid", fields);
    }
  });
};

const rules = reactive({
  name: [{ required: true, message: "请输入分析维度", trigger: "blur" }],
  details: [{ required: true, message: "行为表现描述", trigger: "blur" }],
});

defineExpose({
  title,
  show_add,
  show_edit,
  cbModal,
  formData,
  rules,
});
</script>

<style lang="scss">
.sale_edit_mydim_wrap {
  .dim_title {
    margin: 4px 0;
  }

  .input_wrapper {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    width: 100%;

    .input_row {
      display: flex;
      flex-direction: row;
      align-items: start;

      .seq {
        height: 24px;
        margin-right: 8px;
      }

      .md_input {
        flex: 1;
        margin-right: 8px;
      }

      .el-icon {
        cursor: pointer;
        padding: 0 2px;
        height: 24px;
      }
    }
  }

  .el-dialog__body {
    padding: 15px 24px 5px 24px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    .md_input {
      width: 90%;
      margin: 3px 0;
    }

    .demd_btn {
      margin-top: 10px;
      cursor: pointer;
      font-size: 14px;
      color: #436BFF;

      .el-icon {
        margin-right: 5px;
        color: #436BFF;
        margin-top: 9px;
      }
    }
  }
}
</style>
