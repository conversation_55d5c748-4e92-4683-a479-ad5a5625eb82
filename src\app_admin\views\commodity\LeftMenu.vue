<template>
    <EditMenu ref="refMenu" @callback="cbMenu" />
    <dialogType ref="refDialog" @callback="cbModal" />
</template>

<script setup>
import dialogType from "./dialogType.vue";
import EditMenu from "@/app_admin/components/EditMenu.vue";
import { getGoodsCategory, deleteGoodsCategory } from "@/app_admin/tools/api.js"

const refMenu = ref(null);
const emit = defineEmits(['callback']);
const refDialog = ref(null);

const cbMenu = (action, data) => {
    if (action == 'dialog_add') {
        refDialog.value.show_add()
    } else if (action == 'dialog_edit') {
        refDialog.value.show_edit(data)
    } else if (action == 'click') {
        emit('callback', action, data)
    }

}

const query = () => {
    return new Promise((resolve) => {
        getGoodsCategory().then(resp => {
            if (resp.code == 0) {
                resolve(resp.data)
            } else {
                resolve([])
            }
        }).catch(() => {
            resolve([])
        })
    })
}


const cbModal = (action) => {
    if (action == "reload") {
        refMenu.value.query()
    }
}

const init = () => {
    const cfg = {
        name: '商品分类',
        methodGet: query,
        methodDelete: deleteGoodsCategory,
        label: "name",
        canDeleteAll: true,
        maxRows: 0
    }
    refMenu.value.init(cfg)
}

defineExpose({
    init,
    refMenu,
    EditMenu,
    getGoodsCategory,
    deleteGoodsCategory,
    dialogType,
    cbModal,
    refDialog
})

</script>