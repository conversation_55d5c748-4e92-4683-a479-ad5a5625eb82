<template>
  <MyTable ref="refTable" :cfg="datas" class="member_table" @callback="cbTable">
    <template #_header_left>
      <div>
        <el-radio-group v-model="viewType" @change="onTypeChange" v-show="needShowTeam">
          <el-radio-button value="team">团队</el-radio-button>
          <el-radio-button value="member">成员</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <template #_header_right_pre>
      <DrawerSelectMixed @callback="onSelectUser" ref="refUser" type="user" :rootDeptId="deptId" />
      <div class="filter-group">
        <el-button type="default" @click="exportData" :disabled="exportLoading">
          <el-icon style="vertical-align: middle" v-if="exportLoading">
            <Loading />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </template>
    <template #header_userName>
      <LowHintHeader title="姓名" />
    </template>
    <template #header_visitPlanCompletionRate>
      <HintHeader column="visitPlanCompletionRate" />
    </template>
    <template #header_totalVisitCount>
      <HintHeader column="totalVisitCount" />
    </template>
    <template #header_visitTargetAchievementRate>
      <HintHeader column="visitTargetAchievementRate" />
    </template>
    <template #header_salesAbilityPassRate>
      <HintHeader column="salesAbilityPassRate" />
    </template>
    <template #header_performanceExpectationsRate>
      <HintHeader column="performanceExpectationsRate" />
    </template>
    <template #col_totalVisitDuration="scope">
      <TableCell :scope="scope" column="totalVisitDuration" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitPlanCompletionRate="scope">
      <TableCell :scope="scope" column="visitPlanCompletionRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
    <template #col_visitTargetAchievementRate="scope">
      <TableCell :scope="scope" column="visitTargetAchievementRate" :overviewData="overviewDataAvg"
        valueType="percent" />
    </template>
    <template #col_performanceExpectationsRate="scope">
      <TableCell :scope="scope" column="performanceExpectationsRate" :overviewData="overviewDataAvg"
        valueType="percent" />
    </template>
    <template #col_totalVisitCount="scope">
      <TableCell :scope="scope" column="totalVisitCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitedCustomerCount="scope">
      <TableCell :scope="scope" column="visitedCustomerCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_totalVisitPlanCount="scope">
      <TableCell :scope="scope" column="totalVisitPlanCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_completedVisitPlanCount="scope">
      <TableCell :scope="scope" column="completedVisitPlanCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitPlanCustomerCount="scope">
      <TableCell :scope="scope" column="visitPlanCustomerCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_visitTargetAchievedCount="scope">
      <TableCell :scope="scope" column="visitTargetAchievedCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_salesAbilityPassCount="scope">
      <TableCell :scope="scope" column="salesAbilityPassCount" :overviewData="overviewDataAvg" />
    </template>
    <template #col_salesAbilityPassRate="scope">
      <TableCell :scope="scope" column="salesAbilityPassRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
    <template #col_taskCompletePassRate="scope">
      <TableCell :scope="scope" column="taskCompletePassRate" :overviewData="overviewDataAvg" valueType="percent" />
    </template>
  </MyTable>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import MyTable from "@/components/Table.vue";
import { now } from "@/js/utils.js";
import { column_widths, getColumns, numal_member_column, getReportDate, type_column_hints, default_column_width } from "../misc.js";
import { getReportSubUserPager, getUserVisitReportExport } from "@/app_client/tools/api.js";
import BtnDate from "@/app_client/components/BtnDate.vue";
import { Loading } from "@element-plus/icons-vue";
import LowHintHeader from "./LowHintHeader.vue";
import HintHeader from "./HintHeader.vue";
import TableCell from "./TableCell.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";

const emit = defineEmits(["typeChange"]);
const viewType = ref("member");
const refTable = ref();
const exportLoading = ref(false);
const deptId = computed(() => g.clientBoardStore.getUserTableDeptId());
const overviewDataAvg = computed(() => g.clientBoardStore.getMemberOverviewData_average);
const needShowTeam = ref(false);
const columns = getColumns(false);
const periodType = computed(() => g.clientBoardStore.periodType);
let hasInit = false;
const reportDate = getReportDate();
const refUser = ref();
const tableWidth = ref(columns.length * default_column_width + 'px');

const cbTable = (type, columns) => {
  if (type == "update_columns") {
    tableWidth.value = columns.length * default_column_width + 'px';
  }
};

const datas = reactive({
  tableid: "team_situation_member",
  param: {
  },
  need_init_load: false,
  need_header: true,
  show_pager: true,
  show_search: false,
  fixed_column: "userName",
  sortable: "custom",
  sortables: [...numal_member_column],
  columns,
  always_show_columns: ["userName"],
  default_select_columns: columns,
  column_widths,
  template_header: ["userName", ...Object.keys(type_column_hints)],
  template: numal_member_column,
  urlGet: (p) => {
    return new Promise((resolve) => {
      hasInit = true;
      console.log(12, deptId.value)
      if (!deptId.value) {
        return resolve({
          code: 0,
          data: {
            datas: [],
            totalNum: 0,
          },
        });
      }
      getReportSubUserPager(deptId.value, periodType.value, p, reportDate).then((resp) => resolve(resp));
    });
  },
});

const onSelectUser = (action, data) => {
  datas.param.ssoUserIds = data.users.map((x) => x.ssoUserId);
  refTable.value.search();
};

const onTypeChange = (value) => {
  emit("typeChange", value);
};

const onTimeChange = (value) => {
  if (hasInit) {
    datas.param.startTime = value.startTime;
    datas.param.endTime = value.endTime;
    refTable.value.search();
  }
};


const init = () => {
  needShowTeam.value = g.clientBoardStore.detailNeedShowTeam();
  viewType.value = 'member';
  refUser.value.reset(false);
  refTable.value.search();
};

const exportData = () => {
  // 实现导出逻辑
  const data = toRaw(datas.param);
  data.filename = "成员情况导出_" + now("yyyyMMddhhmmss");
  if (datas.param?.ssoUserIds?.length == 0) {
    data.ssoUserIds = datas.param.ssoUserIds;
  }
  exportLoading.value = true;
  getUserVisitReportExport(deptId.value, periodType.value, data, reportDate).then((resp) => {
    exportLoading.value = false;
  });
};

const _team_board_region_click = () => {
  if (g.clientBoardStore.tableType !== 'member') {
    return;
  }
  if (g.clientBoardStore.regionType === 'user') {
    onSelectUser('setUser', { users: [g.clientBoardStore.regionData.data] })
  } else {
    datas.param.ssoUserIds = []
  }
}

onMounted(() => {
  g.emitter.on("team_board_region_click", _team_board_region_click);
});

onUnmounted(() => {
  g.emitter.off("team_board_region_click", _team_board_region_click);
});


defineExpose({
  init,
  onTimeChange,
  exportData,
  BtnDate,
  refTable,
  exportLoading,
  Loading,
});
</script>

<style lang="scss" scoped>
.member_table {
  .filter-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .dept-select {
      width: 160px;
      margin-left: 12px;
    }
  }

  :deep(.el-table) {
    th {
      .cell {
        width: 100%;
        justify-content: center;
      }
    }

    td {
      padding: 0 !important;
      height: 40px;
      text-align: center;
      line-height: 40px;
    }

    .col_userName {
      width: 200px;
    }

    .cell {
      padding: 0px !important;
    }
  }

  :deep(table) {
    width: v-bind(tableWidth) !important;
  }
}
</style>
