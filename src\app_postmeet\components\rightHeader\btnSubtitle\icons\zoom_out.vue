<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="n332--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="播放器-收起-音频条-交互" transform="translate(-481.000000, -619.000000)">
                <g id="编组-4" transform="translate(355.000000, 546.000000)">
                    <g id="Icon/16px/plus-circle-o" transform="translate(126.000000, 73.000000)">
                        <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                        <g id="编组" transform="translate(2.500000, 2.500000)" fill="currentColor">
                            <path
                                d="M7.5,0 C11.6421356,0 15,3.35786438 15,7.5 C15,11.6421356 11.6421356,15 7.5,15 C3.35786438,15 0,11.6421356 0,7.5 C0,3.35786438 3.35786438,0 7.5,0 Z M7.5,1.25 C4.04822031,1.25 1.25,4.04822031 1.25,7.5 C1.25,10.9517797 4.04822031,13.75 7.5,13.75 C10.9517797,13.75 13.75,10.9517797 13.75,7.5 C13.75,4.04822031 10.9517797,1.25 7.5,1.25 Z M7.5,4.375 C7.84517797,4.375 8.125,4.65482203 8.125,5 L8.125,6.875 L10,6.875 C10.345178,6.875 10.625,7.15482203 10.625,7.5 C10.625,7.84517797 10.345178,8.125 10,8.125 L8.125,8.125 L8.125,10 C8.125,10.345178 7.84517797,10.625 7.5,10.625 C7.15482203,10.625 6.875,10.345178 6.875,10 L6.875,8.125 L5,8.125 C4.65482203,8.125 4.375,7.84517797 4.375,7.5 C4.375,7.15482203 4.65482203,6.875 5,6.875 L6.875,6.875 L6.875,5 C6.875,4.65482203 7.15482203,4.375 7.5,4.375 Z"
                                id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>
