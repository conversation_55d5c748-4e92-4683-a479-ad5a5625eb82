require('dotenv').config();
const {notarize} = require('electron-notarize');

exports.default = async function notarizing(context) {
    const {electronPlatformName, appOutDir} = context;
    if (electronPlatformName !== 'darwin') {
        return;
    }

    const appName = context.packager.appInfo.productFilename;

    return await notarize({
        tool: "notarytool",
        appBundleId: 'com.yunxuetang.novaguidemac',
        appPath: `${appOutDir}/${appName}.app`,
        appleId: '<EMAIL>',
        appleIdPassword: 'fqeh-kgxn-jkcj-igwk',
        ascProvider: '6F8YPTA92C',
        teamId: '6F8YPTA92C',
    });
};
