import { shell, systemPreferences } from 'electron';
import { platform } from 'os';
import {errorWriteLocal} from './errorWriteLocal';

const checkMacOS: boolean = platform() === 'darwin';

const openSystemPreferences = (path: string): Promise<void> => {
    return shell.openExternal(`x-apple.systempreferences:com.apple.preference.security?${path}`);
};

export type PermissionType = 'screen' | 'microphone';

const PERMISSION_PATHS = {
    'screen': 'Privacy_ScreenCapture',
    'microphone': 'Privacy_Microphone'
} as const;

export const ensurePermissions = async (type: PermissionType): Promise<boolean> => {
    if (!checkMacOS) {
        return true;
    }

    const hasAccess: string = systemPreferences.getMediaAccessStatus(type);
    errorWriteLocal(`have Permission: ${type}:`, hasAccess);

    if (hasAccess === 'granted') {
        return true;
    }

    await openSystemPreferences(PERMISSION_PATHS[type]);
    return false;
};

