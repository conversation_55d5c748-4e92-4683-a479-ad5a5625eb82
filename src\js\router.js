import { createRouter, createWeb<PERSON>ash<PERSON><PERSON><PERSON> } from "vue-router";
import adminRouter from "@/app_admin/tools/router";
import clientRouter from "@/app_client/tools/router";
import electronRouter from "@/app_electron/tools/router";
import postmeetRouter from "@/app_postmeet/tools/router";
import clipperRouter from "@/app_clipper/tools/router";
import { getStore, checkInElectron } from "@/js/utils.js";
import { ConstValue } from "@/js/const_value";

const isElectron = checkInElectron();
const isDev = import.meta.env.NODE_ENV != "production";

// 检查版本更新
const checkVersion = async () => {
  try {
    const localVersion = window.__version__;
    if (!localVersion) {
      return
    }
    const url_json = `${import.meta.env.VITE_OSS_PATH}version.json?t=${new Date().getTime()}`
    const response = await fetch(url_json);
    // console.log('checkVersion', url_json)
    const { version: remoteVersion } = await response.json();
    console.log('check version', localVersion, remoteVersion)
    if (localVersion !== remoteVersion) {
      console.log('检测到新版本，正在更新...');
      window.location.reload();
    }
  } catch (error) {
    console.error('版本检查失败:', error);
  }
};

const getHomeRedirect = () => {
  const user = getStore(ConstValue.keyUserInfo, {});
  if (isElectron) {
    return ConstValue.defaultElectronHomePage;
  } else {
    if (user.token) {
      return ConstValue.defaultClientHomePage;
    } else {
      return "/login";
    }
  }
};

const routes = [
  {
    path: "/",
    name: "Home",
    redirect: getHomeRedirect(),
  },
  {
    path: "/login",
    name: "webLogin",
    component: isElectron
      ? () => import("@/app_electron/views/login/Login.vue")
      : () => import("@/views/Login/LoginWeb.vue"),
  },
  {
    path: "/auth",
    name: "auth",
    component: () => import("@/views/Auth.vue"),
  },
  {
    path: "/login-h5",
    name: "loginH5",
    component: () => import("@/views/Login/LoginH5.vue"),
  },
  {
    path: "/error",
    name: "errorPage",
    component: () => import("@/views/ErrorPage.vue"),
  },
];

if (isElectron) {
  routes.push(...electronRouter);
} else {
  if (isDev) {
    routes.push({
      path: "/test",
      name: "test",
      component: () => import("@/views/Test.vue"),
    });
  }
  routes.push(...adminRouter);
  routes.push(...clientRouter);
  routes.push(...clipperRouter);
  routes.push(...postmeetRouter);
}
// const restRouter = {
//   path: '/:pathMatch(.*)',
//   redirect: '/'
// }
// routes.push(restRouter);

const router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_WEB_URL),
  routes,
});

function checkNoNeedLogin(to) {
  const routers = ["login", "auth", "postmeet/share", "postmeet/record"];
  return routers.some((route) => to.fullPath.includes(route));
}

// 全局守卫：登录拦截 本地没有存token,请重新登录
router.beforeEach(async (to, from) => {
  // 设置窗口标题放在最前面
  if (to.meta?.title) {
    document.title = to.meta.title;
  }
  // console.log('isDev', isDev)
  // 检查版本更新
  if (!isElectron) {
    checkVersion();
  }

  const code = to.meta && to.meta.code;
  const token = localStorage.getItem("token");
  // 判断有没有登录
  if (!token) {
    if (checkNoNeedLogin(to)) {
      return true;
    } else {
      console.log("没有登录");
      // if (isElectron) {
      //   g.electronStore.logout();
      // } else {
      //   return { name: 'webLogin' }
      // }
    }
  } else if (!!code) {
    // 如果需要权限管控
    const userMeuns = getStore(ConstValue.keyMenuPermission, []);
    const currMenu = userMeuns.find((x) => x.code == code);
    if (currMenu && currMenu.showed == 1) {
      return true;
    } else {
      console.log("没有权限");
      // 还是在当前页面，不跳转
      // return false
    }
  } else {
    return true;
  }
});

export default router;
