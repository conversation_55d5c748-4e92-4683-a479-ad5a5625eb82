<template>
    <div class="list-menu">
        <div class="list-menu-header">
            <div class="list-menu-header-title" @click="onOpenMenu">
                {{ activeName || `${selectCategoriesObj.name}` }} <van-icon name="arrow-down" />
            </div>
            <div :class="['list-menu-button', { 'active': actionSheetShow }]" @click="onActionSheet">
                <img :src="getOssUrl('filter.png', 2)" alt="" v-if="!actionSheetShow">
                <img :src="getOssUrl('filtered.png', 3)" alt="" v-else>
            </div>
        </div>
        <filterPopup ref="filterPopupRef" @close="onClose"></filterPopup>
        <dimensionPopup ref="dimensionPopupRef" @success="onSuccess"></dimensionPopup>
    </div>


</template>
<script setup>
import { getOssUrl } from '@/js/utils.js';
import search from './search.vue';
import dimensionPopup from './dimensionPopup.vue';
import filterPopup from './filterPopup.vue';
const activeName = ref('')
const filterPopupRef = ref(null)
const dimensionPopupRef = ref(null)
const showMenu = ref(false);
const actionSheetShow = ref(false);
const store = g.clientBiStore
const selectCategoriesObj = ref({})

const onActionSheet = () => {
    nextTick(() => {
        filterPopupRef.value.init()
    })
}
const onOpenMenu = () => {
    nextTick(() => {
        dimensionPopupRef.value.init()
    })
}
const onClose = (val) => {
    actionSheetShow.value = val

}

const onSuccess = (str) => {
    activeName.value = str

}

watch(
    () => [store.crDrdimensionList],
    (newValue) => {
        selectCategoriesObj.value = store.crDrdimensionList[0] || {}
        g.clientBiStore.setCrFilterCondition({
            parentId: selectCategoriesObj.value.id,
        })
    }, {
    immediate: true,
    deep: true
}
)

</script>
<style lang="scss" scoped>
.list-menu {
    padding-bottom: 16px;

    .list-menu-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 16px 16px 0 16px;

        .list-menu-header-title {
            font-weight: 400;
            font-size: 14px;
            color: #262626;
            line-height: 20px;
            cursor: pointer;
        }

        .list-menu-button {
            img {
                width: 16px;
                height: 16px;
            }
        }

        .active {
            // background: #436BFF;

        }
    }
}
</style>