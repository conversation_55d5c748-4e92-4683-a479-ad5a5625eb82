<template>
  <el-dialog :title="`重命名${old_name}`" v-model="is_show" width="480px" :append-to-body="false"
    :modal-append-to-body="false" class="dialog_rename_wrap">
    <div>
      <div class="rd_box">
        <div class="rd_title"> 新名称： </div>
        <el-input type="text" v-model="new_name" />
      </div>

      <div class="rd_box">
        <div class="rd_title">修改范围： </div>
        <el-radio-group v-model="range">
          <el-radio label="all">全局</el-radio>
          <el-radio label="one">单个</el-radio>
        </el-radio-group>
      </div>
    </div>
    <template #footer class="dialog-footer">
      <el-button @click="is_show = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { updateParName, getAsrStoreUpdated, updateSubTitle } from '@/app_postmeet/tools/api'
export default {
  name: 'DialogRename',
  props: ['names', 'roomInfo', 'messages', 'asrContent', 'asrSummary'],
  data() {
    return {
      range: 'all',
      old_name: '',
      new_name: '',
      is_show: false,
      value: ''
    }
  },
  methods: {
    show(item) {
      if (!g.postmeetStore.data.editmode) {
        return
      }
      this.item = item
      const name = this.name_filter(item.name)
      this.old_name = name || ''
      this.new_name = name || ''
      this.is_show = true;
    },
    name_filter(name) {
      // const name = "<span style='color: blue;background: #D6DDFFFF'>Jack</span>";
      let new_name = name
      const regex = /(?:>)(.*?)(?:<)/;
      const match = regex.exec(name);
      if (match) {
        new_name = match[1];
      }
      return new_name
    },
    confirm() {
      this.is_show = false;
      if (this.range == 'all') {
        this._updateAllName()
      } else {
        this.updateSignalName()
      }
    },
    _updateAllName() {
      const confId = g.postmeetStore.data.confId
      const data = { id: this.item.ui, value: this.new_name }
      updateParName(confId, data).then(resp => {
        if (resp.code == 0) {
          getAsrStoreUpdated().then(resp2 => {
            if (resp2.code == 0) {
              if (resp2.data.hasUpdatedContent) {
                g.postmeetStore.setUpdatedContent(resp2.data.asrUpdatedContent)
                g.postmeetStore.setValue('allowSaleReGen', true)
                g.emitter.emit('updatedAsrContent', '')
              }
            }
          })
        }
      })
    },
    updateSignalName() {
      const data = { id: this.item.bt, value: '', ui: this.new_name }
      updateSubTitle(data).then(resp => {
        if (resp.code == 0) {
          getAsrStoreUpdated().then(resp2 => {
            if (resp2.code == 0) {
              if (resp2.data.hasUpdatedContent) {
                g.postmeetStore.setUpdatedContent(resp2.data.asrUpdatedContent)
                g.postmeetStore.setValue('allowSaleReGen', true)
                g.emitter.emit('updatedAsrContent', '')
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style lang='scss'>
.dialog_rename_wrap {
  .el-dialog__body {
    display: flex;
    flex-direction: column;

    .rd_title {
      margin-top: 10px;
    }

    .el-checkbox {
      margin: 5px 0;
    }
  }
}
</style>
