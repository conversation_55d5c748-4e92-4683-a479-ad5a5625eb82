<template>
  <div :class="['library-container', hasAdminPermission ? 'is-lib1-admin' : 'is-lib1-user']">
    <!-- 左侧导航 -->
    <div class="left-nav">
      <LibraryNav ref="libraryNavRef" :isadmin="isadmin" @callback="handleCallback" />
    </div>

    <!-- 右侧内容区 -->
    <div class="right-content">
      <!-- 搜索区域 -->
      <!-- <LibraryRightSearch ref="libraryRightSearchRef" :isadmin="isadmin" @callback="handleCallback" />
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="剪辑片段" name="fragment"></el-tab-pane>
        <el-tab-pane label="FAQ" name="faq"></el-tab-pane>
      </el-tabs>
      <div v-show="activeName === 'fragment'">
   
      </div> -->
      <!-- 上方卡片 -->
      <LibraryCards ref="libraryCardsRef" :isadmin="isadmin" @callback="handleCallback" />

      <!-- 下方列表 -->
      <LibraryList ref="libraryListRef" :isadmin="isadmin" />
      <!-- <FAQ v-show="activeName === 'faq'"></FAQ> -->

    </div>
  </div>
</template>

<script setup>
import LibraryNav from "./LibraryNav.vue";
import LibraryCards from "./LibraryCards.vue";
import LibraryList from "./LibraryList.vue";
import LibraryRightSearch from "./LibraryRightSearch.vue";
import FAQ from "./components/FAQ.vue";

const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

const libraryNavRef = ref(null);
const libraryCardsRef = ref(null);
const libraryListRef = ref(null);
// const libraryRightSearchRef = ref(null);
const nodeData = ref(null);
const hasAdminPermission = ref(false);
// const activeName = ref('faq');

const handleCallback = async (action, data) => {
  console.log('action', action, data)
  if (action == "node-click") {
    nodeData.value = data;
    // libraryRightSearchRef.value.init(data);
    libraryCardsRef.value.search(data);
    libraryListRef.value.init(hasAdminPermission.value, data);
  } else if (action == "node-card") {
    nodeData.value = data;
    // libraryRightSearchRef.value.init(data);
    libraryListRef.value.init(hasAdminPermission.value, data);
    libraryNavRef.value.updateTreeNode(data);
  } else if (action == "search-input") {
    libraryListRef.value.setSearchKeyword(data);
  } else if (action == "my-clipping") {
    // libraryRightSearchRef.value.init_my();
    libraryCardsRef.value.init_my();
    libraryListRef.value.setHostUserId(g.appStore.user.id);
  }
};

const setAccessCache = () => {
  //   	文件夹管理操作	folder_manage_opr
  // 		子文件夹管理操作	sub_folder_manage_opr
  // 		案例管理操作	case_manage_opr
  // 		案例萃取操作	script_extraction_opr
  const list = ["folder_manage_opr", "sub_folder_manage_opr", "case_manage_opr", "script_extraction_opr"];
  for (let code of list) {
    const access = g.cacheStore.checkPointActionByCode("case_library", code)
    console.log('code', code, access)
  }
};

onMounted(() => {
  g.cacheStore.getUserMenu("admin").then(() => {
    hasAdminPermission.value = props.isadmin && g.cacheStore.checkPermission("case_library");
    libraryNavRef.value.init(hasAdminPermission.value);
    if (props.isadmin) {
      setAccessCache();
    }
  });
});

defineExpose({
  libraryNavRef,
  libraryCardsRef,
  libraryListRef,
  // libraryRightSearchRef,
});
</script>

<style lang="scss" scoped>
.library-container {
  display: flex;
  background: #f7f9fe;
  height: 100vh;
  overflow: hidden;

  .left-nav {
    width: 280px;
    padding: 12px;
    border-right: 1px solid #eee;
  }

  .right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }
}


.is-lib1-admin {
  height: calc(100vh - 60px);
}

.is-lib1-user {
  height: 100vh;
}
</style>