<template>
    <div class="doclist_wrap">
        <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
            <template #_header_left>
                <BtnSet />
            </template>

            <template #_header_filter>
                <el-select v-model="selectCate" class="sel_cate" placeholder="请选择" @change="onSelectChange">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </template>

            <template #col_fileName="{ row }">
                <div @click="viewDoc(row)" class="column_link">
                    {{ row.fileName }}
                </div>
            </template>

            <template #col_fileSize="{ row }">
                <div @click="viewDoc(row)">
                    {{ formatFileSize(row.fileSize) }}
                </div>
            </template>

            <template #col_status="{ row }">
                <div class="status_wrap">
                    <div class="dot" :style="{ background: getDocColor(row.status) }"></div>
                    <div>
                        {{ trans(row.status) }}
                    </div>
                    <div v-if="row.moreImages">
                        <el-tooltip class="box-item" effect="dark" content="文档中包含较多图片，可能无法有效解析图片中文字，建议您将重要的文字用写入页面备注"
                            placement="top">
                            <el-icon>
                                <Warning />
                            </el-icon>
                        </el-tooltip>
                    </div>
                </div>
            </template>

            <template #_link_post="{ row }">
                <el-button type="primary" text @click="cbDatas('init_view', row)"
                    :disabled="row.status.indexOf('PROCESS') > -1 || row.status == 'PARSE_FAIL'">查看解析
                </el-button>
                <el-dropdown @command="(key) => onMore(key, row)">
                    <el-button type="primary" text class="btn_more">更多
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="delete">删除</el-dropdown-item>
                            <el-dropdown-item command="download">下载</el-dropdown-item>
                            <el-dropdown-item command="re_analyse"
                                :disabled="row.status.indexOf('PROCESS') > -1 || row.status == 'PARSE_FAIL'">重新解析</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </template>
        </MyTable>
    </div>
    <UploadModal ref="refUpload">
        <template #right_bottom>
            <div class="class_line">
                <div class="title">文件标签</div>
                <el-input v-model="new_tag" maxlength="20" show-word-limit placeholder="输入并回车后生成标签"
                    @keyup.enter="onNewTag" :disabled="fileConfig.tags.length >= 10" />
                <div class="tags_boxs">
                    <el-tag v-for="tag in fileConfig.tags" :key="tag" closable type="info" @close="onRemoveTag(tag)">
                        {{ tag }}
                    </el-tag>
                </div>
            </div>
        </template>
    </UploadModal>
    <ViewDoc ref="refView" @callback="cbViewDoc" />
    <DialogEditFile ref="refEdit" @callback="cbEditDoc" />

</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getPptDocs, deletePpt, getPptCateDocInfo, parseDoc } from "@/app_admin/tools/api.js";
import { getDocColor, apiHintWrap } from '@/app_admin/tools/utils.js'

import { formatFileSize, confirmDelete, jsOpenNewWindow } from '@/js/utils.js'
import UploadModal from "@/app_admin/components/UploadModal.vue";
import ViewDoc from "@/app_admin/components/ViewDoc"
import trans from "@/js/lang.js";
import config from '@/js/config';
import DialogEditFile from "./dialogEditFile.vue"
import { Warning } from '@element-plus/icons-vue'
import BtnSet from './BtnSet.vue';

const fileConfig = ref({})
const selectCate = ref("")
const refUpload = ref();
const refView = ref();
const refTable = ref();
const refEdit = ref()
const new_tag = ref('')
const options = ref([{ value: '', label: '全部分类' }])

const cbEditDoc = (action, data) => {
    if (action == "reload") {
        refTable.value.search()
    }
}

const onMore = (key, item) => {
    const data = toRaw(item);
    switch (key) {
        case "re_analyse":
            const { categoryId, id } = data;
            apiHintWrap(parseDoc(categoryId, id), "", "已提交解析", "解析提交失败").then(({ status }) => {
                if (status) {
                    refTable.value.search()
                }
            })
            break;
        case "delete":
            confirmDelete(data.fileName, (status) => {
                if (status) {
                    deletePpt(data).then((resp) => {
                        if (resp.code == 0) {
                            ElMessage.success("删除成功");
                            refTable.value.search();
                        } else {
                            ElMessage.error(
                                `删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`
                            );
                        }
                    });
                }
            })
            break;
        case "download":
            const url = config.publicPath + data.downloadPath;
            jsOpenNewWindow(url)
            break;
    }
}

const datas = reactive({
    tableid: 'doc_list',
    param: {
        "categoryIds": [],
        "searchKey": "",
    },
    need_init_load: true,
    show_search: true,
    need_header: true,
    show_btn_add: true,
    form: {},
    modal_type: "link",
    search_ph: "文件名称",
    add_txt: "上传",
    delete_hint_column: 'fileName',
    show_link_column: true,
    show_link_edit: true,
    show_link_view: false,
    show_link_delete: false,
    columns: ["fileName", "categoryName", "createdTime", "createdUserName", "fileSize", "slideSize", "status"],
    template: ["status", "fileName", "fileSize"],
    urlGet: getPptDocs,
    urlDelete: deletePpt
});
const cbDatas = (action, data) => {
    if (action === "init_view") {
        const { categoryId, id } = data;
        getPptCateDocInfo(categoryId, id).then(resp => {
            if (resp.code == 0) {
                refView.value.init(resp.data);
            }
        })
    } else if (action == 'before_create') {
        if (refUpload.value.onOk()) {
            refTable.value.btnModalCancel()
        }
    } else if (action == "init_add") {
        g.adminFileStore.get_config('ppt').then(cfg => {
            fileConfig.value = cfg;
            fileConfig.value['tags'] = []
            refUpload.value.show(cfg, '');
        })
    } else if (action == "init_edit") {
        refEdit.value.show_edit(data)
    }
}


const onSelectChange = (cateId) => {
    datas.param['categoryIds'] = [cateId]
    refTable.value.search()
}

const viewDoc = (row) => {
    if (row.fileSize < 100 * 1024 * 1024) {
        const url = config.publicPath + row.downloadPath;
        const ext = row.fileName.split('.').pop()
        g.emitter.emit('app_preview_file', { url, ext });
    } else {
        const docId = row.id
        const url = `${config.publicPath}/#/vp/0/${docId}`
        jsOpenNewWindow(url)
    }
}

const cbViewDoc = (action) => {
    if (action == 'reload') {
        refTable.value.search()
    }
}

const onNewTag = () => {
    fileConfig.value.tags.push(new_tag.value);
    new_tag.value = '';
}

const onRemoveTag = (tag) => {
    fileConfig.value.tags = fileConfig.value.tags.filter(x => x != tag);
}

onMounted(async () => {
    const a = await g.adminFileStore.get_ppt_cates();
    options.value = [...[{ value: '', label: '全部分类' }], ...a];
    g.emitter.on('file_uploaded', () => {
        !!refTable.value && refTable.value.search()
    })
})

onUnmounted(() => {
    g.emitter.off("file_uploaded");
});

defineExpose({
    MyTable, refUpload, refView, ViewDoc, UploadModal,
    refTable, selectCate, getDocColor, trans, BtnSet, DialogEditFile,
    viewDoc, onMore, onSelectChange, cbDatas
})
</script>

<style lang='scss'>
.doclist_wrap {
    .table_box .table_class {
        height: calc(100vh - 179px);

        .sel_cate {
            margin-right: 10px;
            width: 200px;
        }
    }

    .sel_cate {
        margin-right: 10px;
        width: 200px;
    }


    .status_wrap {
        display: flex;
        flex-direction: row;

        .dot {
            width: 6px;
            height: 6px;
            margin: 9px 9px;
            border-radius: 50%;
        }

        .el-icon {
            color: #FAC450;
            margin-top: 5px;
            margin-left: 2px;
        }
    }

    .btn_more {
        margin-left: 12px;
    }
}
</style>
