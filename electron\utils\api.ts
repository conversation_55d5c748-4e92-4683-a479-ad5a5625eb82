import { getHttp } from "./request";
import config from './config'
import { getUserInfo, getStore, keyClientId } from './store';
const _http: any = getHttp()

// releaseType: formal:正式， gray: 灰度；platform: 系统平台， ver: 版本号
export const checkUpdateApi = (releaseType: string) => {
    const data = {
        clientId: getStore(keyClientId),
        token: getUserInfo('token')
    }
    return _http.post(`product/${config.name}/release/${releaseType}/version/${config.platform}/${config.version}`, data);
}

// 创建文件对象 
export const createFile = (data: any) => _http.post(`api/xmatefile/createFile`, data);