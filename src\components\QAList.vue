<template>
    <div class="qa-list" v-loading="loading">
        <template v-if="qaList.length > 0">
            <div v-for="(qa, index) in qaList" :key="index" class="qa-item" @click="onDetail(qa)">
                <div class="qa-item-content">
                    <div class="qa-item-content-left ">
                        <span class="qa-bubble question">
                            <span class="qa-tag customer">问</span>
                            <span class="qa-text-customer">客户</span>
                        </span>
                        <el-tooltip :content="qa.question" :disabled="isHide" placement="top"
                            popper-class="custom-tooltip">
                            <div class="single-line-ellipsis qa-text" @mouseover="isShowTooltip(qa.question, $event)">
                                {{ qa.question }}
                            </div>
                        </el-tooltip>
                    </div>
                    <div class="qa-detail-link">详情</div>
                </div>
                <div class="qa-item-content">
                    <div class="qa-item-content-da-left">
                        <span class="qa-bubble answer">
                            <span class="qa-tag partner">答</span>
                            <span class="qa-text-answer">伙伴</span>
                        </span>
                        <el-tooltip :content="qa.answer" :disabled="isHide" popper-class="custom-tooltip"
                            :show-after="500">
                            <div class="single-line-ellipsis qa-text-2" @mouseover="isShowTooltip(qa.answer, $event)">
                                {{ qa.answer }}
                            </div>
                        </el-tooltip>
                    </div>
                </div>
                <el-divider border-style="double" />
                <div class="qa-footer">
                    <el-tooltip :content="qa.customerName" :disabled="isHide" placement="top"
                        popper-class="custom-tooltip">
                        <span class="qa-footer-item-1 single-line-ellipsis"
                            @mouseover="isShowTooltip(qa.customerName, $event)">客户名称：{{ qa.customerName }}</span>
                    </el-tooltip>

                    <span class="qa-footer-item">创建人：{{ qa.createdUser }}</span>
                    <span class="qa-footer-item">{{ qa.createdTime?.substring(0, 10) }}</span>
                </div>
            </div>
        </template>
        <el-empty description="暂无数据" style="margin: 0 auto;" :image="getOssUrl('no-data.png', 3)" v-else>
        </el-empty>
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
const props = defineProps({
    qaList: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    }
})

const isHide = ref(true)
const isShowTooltip = (val, e) => {
    isHide.value = g.appStore.isShowTooltip(val, e, 14, 1);
}

const onDetail = (item) => {
    console.log(item);
    g.clientStore._openMeetRecord(item.conferenceId, 'summary', 'key_points')
}
</script>

<style lang="scss" scoped>
.qa-list {
    p {
        margin: 0;
    }

    .qa-item {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
        padding: 20px 20px 14px 20px;
        margin-bottom: 12px;
        cursor: pointer;

        &:hover {
            box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);

            .qa-text {
                color: #436BFF;
            }
        }

        &:last-child {
            margin-bottom: 0;
        }

        .qa-item-content {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;


            .qa-item-content-left {
                width: calc(100% - 48px);
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 8px;
            }

            .qa-item-content-da-left {
                width: 100%;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                // margin-bottom: 8px;
            }
        }

        .qa-detail-link {
            top: 16px;
            right: 20px;
            color: #436BFF;
            font-size: 14px;
            text-decoration: none;
            z-index: 2;
            line-height: 24px;
            cursor: pointer;
        }

        .qa-bubble {
            align-items: center;
            border-radius: 4px;
            display: inline-block;
            font-size: 12px;
            // display: flex;
            flex-direction: row;


            .qa-text-customer {
                color: #04CCA4;
                padding: 0px 6px;
            }

            .qa-text-answer {
                color: #436BFF;
                padding: 0px 6px;
            }

            .qa-tag {
                display: inline-block;
                border-radius: 4px;
                font-size: 12px;
                width: 20px;
                height: 20px;
                font-weight: 500;
                text-align: center;
                line-height: 20px;

            }

            &.question {
                background: rgba(4, 204, 164, 0.1);

                .qa-tag.customer {
                    background: #04CCA4;
                    color: #fff;
                }
            }

            &.answer {
                background: #E6EEFF;
                color: #436BFF;

                .qa-tag.partner {
                    background: #436BFF;
                    color: #fff;
                }
            }
        }

        .qa-text {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            width: calc(100% - 80px);
            margin-left: 6px;
            line-height: 24px;
        }

        .qa-text-2 {
            color: #8C8C8C;
            font-size: 14px;
            width: calc(100% - 80px);
            margin-left: 6px;
            line-height: 24px;
        }

        .qa-footer {
            display: flex;
            gap: 12px;
            color: #8C8C8C;
            font-size: 12px;

            box-sizing: border-box;

            span:not(:first-child)::before {
                content: '';
                display: inline-block;
                width: 1px;
                height: 12px;
                background: #E0E0E0;
                margin-right: 12px;
                vertical-align: middle;
            }

            .qa-footer-item-1 {
                display: inline-block;
                max-width: 50%;
                cursor: pointer;
            }


        }
    }

    .el-divider {
        margin: 12px 0;
        border-top: 1px solid #E9E9E9;
    }
}
</style>

<style lang="scss">
.custom-tooltip {
    max-width: 500px !important;
    word-break: break-all;
}
</style>