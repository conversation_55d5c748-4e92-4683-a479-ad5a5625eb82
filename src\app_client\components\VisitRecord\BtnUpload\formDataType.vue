<template>
    <div class="vbp_box">
        <div class="vbp_title">
            主题标签<span>*</span>
        </div>
        <div class="vbp_value">
            <ul class="tags_item">
                <li v-for="(label, index) in tags['customer']" :key="label" @click="onClickOne(label)"
                    :class="localParam.salesMateTags == label ? 'choose txt' : 'txt'">
                    {{ label }}
                </li>
            </ul>
        </div>
        <div v-if="currItem.salesRelated || localParam['salesGoodsCategories']">
            <SalesRelatedComponent ref="refSalesRelated" v-model="localParam" />
        </div>
    </div>
</template>

<script setup>

import SalesRelatedComponent from "@/components/SalesRelated/SalesRelatedComponent.vue"
import { getSalesTagsConverted } from "@/js/utils"

const emit = defineEmits(['update:param'])

const props = defineProps({
    param: {
        type: Object,
        required: true
    },
})
const refSalesRelated = ref();
const localParam = ref(props.param)
const currItem = ref({ salesRelated: false })
const refGoodsModal = ref()
const tags = ref([])

watch(localParam, (newValue) => {
    emit('update:param', newValue)
}, { deep: true })

const getClass1 = (label) => {
    return localParam.value.salesMateTags == label ? 'choose txt' : 'txt'
}

const getClass2 = (item) => {
    let cname = 'txt';
    if (localParam.value && localParam.value.salesGoodsCategories) {
        const temp = localParam.value.salesGoodsCategories.split(',').filter(x => !!x);
        if (temp.includes(item)) {
            cname += " choose";
        }
    }
    return cname;

}

const onClickOne = (value) => {
    localParam.value.salesMateTags = value;
    currItem.value = tags.value.data['customerTopics'].find(x => x.label == value) || {};
    nextTick(() => {
        refSalesRelated.value.setTags(tags.value);
        refSalesRelated.value.setCurrItem(currItem.value);
        localParam.value.salesGoodsCategories = ''
        localParam.value.salesRelatedType = 1;
    })
}

const init = () => {
    g.cacheStore.getSalesConfigure().then((data) => {
        tags.value = getSalesTagsConverted(data)
    })
}

defineExpose({
    localParam, init, onClickOne, tags, currItem, refGoodsModal, getClass1, getClass2, refSalesRelated, SalesRelatedComponent
})

</script>
