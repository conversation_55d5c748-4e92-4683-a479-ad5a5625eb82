<template>
  <div class="top_menu flex-row">
    <div v-for="item in menuItems" :key="item.id" :class="getMenuItemClass(item.id)" @click="goPage(item)">
      {{ item.name }}
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

const menuItems = ref([]);
const menuIdIndexs = {};

const setMenuIdIndexs = (menus) => {
  menus.forEach((menu) => {
    if (!menuIdIndexs[menu.id]) {
      menuIdIndexs[menu.id] = [];
    }

    // 递归收集所有子菜单的路由地址
    const collectRoutes = (item) => {
      if (item.index) {
        menuIdIndexs[menu.id].push(item.index);
      }
      if (item.children) {
        item.children.forEach((child) => collectRoutes(child));
      }
    };

    if (menu.children) {
      menu.children.forEach((child) => collectRoutes(child));
    }
  });
};

const getMenuItemClass = (id) => {
  return {
    top_menu_item: true,
    active: menuIdIndexs[id]?.includes(route.path),
  };
};

const goPage = (item) => {
  router.push({ path: item.children[0].children[0].index });
  g.emitter.emit("update_admin_menu", item.children);
};

const _updateMenu = () => {
  menuItems.value = g.cacheStore.userMenu["admin"];
  setMenuIdIndexs(menuItems.value);



  if (g.config.isDev) {
    const currentMenu = menuItems.value[0];
    if (currentMenu?.children) {
      g.emitter.emit("update_admin_menu", currentMenu.children);
    }
  } else {
    // 只有当路由不存在于当前菜单中时，才跳转到第一个菜单
    const currentPath = route.path;
    const isValidPath = menuItems.value.some(menu =>
      menuIdIndexs[menu.id]?.includes(currentPath)
    );
    if (!isValidPath) {
      goPage(menuItems.value[0]);
    } else {
      // 更新左侧菜单
      const currentMenu = menuItems.value.find(menu =>
        menuIdIndexs[menu.id]?.includes(currentPath)
      );
      if (currentMenu?.children) {
        g.emitter.emit("update_admin_menu", currentMenu.children);
      }
    }
  }


};

onMounted(() => {
  g.cacheStore.getUserMenu("admin").then(() => {
    _updateMenu();
  });
  g.emitter.on("switch_admin_page", () => {
    _updateMenu();
  });
});

onUnmounted(() => {
  g.emitter.off("switch_admin_page");
});

defineExpose({ goPage });
</script>

<style scoped lang="scss">
.top_menu {
  align-items: center;
  margin-left: 21px;

  .top_menu_item {
    padding: 0 10px;
    cursor: pointer;

    &.active {
      color: #436bff;
    }
  }
}
</style>
