<template>
    <div class="cards" v-loading="loading">
        <div v-for="item in data" :key="item.name"
            :class="['cards__item', (competitorId === item.competitorId) && 'active']" @click="handleClick(item)">
            <div class="cards__info">
                <div class="cards__dot" :style="{ background: item.color }"></div>
                <div class="cards__name">{{ item.name }}</div>
            </div>
            <div class="cards__score" :style="{ color: item.cardColor }">{{ item.totalVolume }}</div>
        </div>
    </div>
</template>

<script setup lang="js">
const emit = defineEmits(['callback']);
const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    colorObject: {
        type: Object,
        default: () => { }
    },
    loading: {
        type: Boolean,
        default: false
    }
})
const competitorId = ref('')
const handleClick = item => {
    competitorId.value = item.competitorId
    emit('callback', item)
}

const reset = () => {
    competitorId.value = ''
}

defineExpose({
    reset
})

</script>

<style lang="scss">
.cards {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    margin-bottom: 8px;
    overflow: scroll;


    &__item {
        display: flex;
        flex-direction: column;
        border-radius: 12px;
        padding: 8px 12px 8px 20px;
        background: #F9FAFC;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        min-width: 100px;
        justify-content: space-between;
        cursor: pointer;
        box-sizing: border-box;
        border: 1px solid #F9FAFC;

        &.active {
            background: rgba(67, 107, 255, 0.04);
            border-radius: 8px;
            border: 1px solid #436BFF;
        }
    }

    &__dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;

    }

    &__info {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        // justify-content: center;
    }

    &__name {
        font-size: 12px;
        color: #8C8C8C;
        margin-right: 12px;
    }

    &__score {
        font-weight: 700;
        font-size: 16px;
        margin-left: 18px;
    }
}
</style>