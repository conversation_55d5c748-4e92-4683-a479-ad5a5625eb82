<template>
  <div class="header-bar flex-row">
    <div class="left-content">
      <el-icon class="back-btn" @click="goPremeet">
        <ArrowLeft />
      </el-icon>
      <div class="title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft } from "@element-plus/icons-vue";
import { useRoute, useRouter } from "vue-router";
import usericon from "@/app_postmeet/components/usericon.vue";
const route = useRoute();
const router = useRouter();

const title = ref("");

const goPremeet = () => {
  const confId = route.params.confId || "";
  router.push({ path: `/postmeet/record/${confId}` });
};

const init = () => {
  title.value = g.clipperStore.scheduleInfo.subject;
};

defineExpose({
  init,
  goPremeet,
  usericon,
  title,
});
</script>

<style lang="scss" scoped>
.header-bar {
  height: 50px;
  border-bottom: 1px solid #eee;

  .left-content {
    display: flex;
    align-items: center;
    padding: 0 20px;

    .back-btn {
      cursor: pointer;
    }

    .title {
      margin-left: 20px;
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #262626;
      line-height: 24px;
    }
  }

  .right-content {
    padding: 10px 20px;
  }
}
</style>
