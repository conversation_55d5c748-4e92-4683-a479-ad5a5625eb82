<template>
    <el-drawer v-model="is_show" direction="rtl" class="vd_input_wrap">
        <template #header>
            <div class="vd_title">
                查看输入项
            </div>
        </template>
        <template #default>
            <div class="vd_main flex-column">
                <div class=vd_header>
                    沟通方案：{{ info.fileName }}
                </div>
                <div class="vd_list">
                    <div class="line">
                        <div class="v1">
                            客户名称
                        </div>
                        <div class="v2">
                            {{ info.companyName }}
                        </div>
                    </div>
                    <div class="line">
                        <div class="v1">
                            所属行业
                        </div>
                        <div class="v2">
                            {{ info.industryName }}
                        </div>
                    </div>
                    <div class="line">
                        <div class="v1">
                            客户关注点
                        </div>
                        <div class="v2">
                            {{ info.focus }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
const is_show = ref(false)
const title = ref('')
const info = ref({});

const onCancel = () => {
    is_show.value = false;
}

const show = (e) => {
    info.value = e;
    is_show.value = true;
}

defineExpose({ show, onCancel, is_show, info, title })

</script>

<style lang="scss">
@import url('./ViewUseInput.scss');
</style>