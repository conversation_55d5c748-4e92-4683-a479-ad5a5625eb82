<template>
    <div class="blank_setting_wrap">
        <OutlineIcon />
        <div class="bs_note">
            新建PPT大纲，完善模板内容
        </div>
        <el-button type="primary" @click="onConfirm">
            新建PPT大纲
        </el-button>
    </div>
</template>

<script setup>
import OutlineIcon from "@/app_admin/icons/outline.vue"
const emit = defineEmits(['callback']);

const onConfirm = () => {
    emit('callback', 'open_edit')
}

defineExpose({ onConfirm })

</script>


<style lang="scss">
.blank_setting_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: calc(100vh - 229px);

    .bs_note {
        height: 22px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #8C8C8C;
        line-height: 22px;
        margin: 10px 0;
    }
}
</style>