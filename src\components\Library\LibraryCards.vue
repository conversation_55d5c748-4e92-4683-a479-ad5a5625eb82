<template>
  <div class="library-cards" v-if="childrenData.length > 0">
    <div class="total-count">{{ childrenData.length }} 个文件夹</div>
    <div class="cards-container">
      <div class="card" v-for="item in childrenData" :key="item.id" @click="handleCardClick(item)">
        <div class="card-left">
          <el-icon class="folder-icon">
            <img :src="getOssUrl('folder.png')" :alt="item.name" />
          </el-icon>
          <el-tooltip class="box-item" effect="dark" :content="item.name" :show-after="1000">
            <div class="card-title">
              {{ item.name }}
            </div>
          </el-tooltip>
        </div>
        <div class="card-info">
          <div class="clip-count">{{ item.clips }}个剪辑片段</div>
          <div class="update-time">最后更新: {{ item.updateTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils";
import { getClipLibChildrenInfo } from "@/app_client/tools/api";

const emit = defineEmits(["callback"]);

const props = defineProps({
  isadmin: {
    type: Boolean,
    default: false,
  },
});

// {
// 	"clips":0,
// 	"id":"919ebe7e-b679-464d-bf8b-8b61a1812b70",
// 	"name":"3434",
// 	"sortNum":1,
// 	"updateTime":"2025-01-07 10:38:37"
// }
const childrenData = ref([]);

const search = (data) => {
  if (data?.children?.length > 0) {
    getClipLibChildrenInfo(data.id, props.isadmin).then((res) => {
      childrenData.value = res;
    });
  } else {
    childrenData.value = [];
  }
};

const handleCardClick = (item) => {
  getClipLibChildrenInfo(item.id, props.isadmin).then((res) => {
    childrenData.value = res;
  });
  emit("callback", "node-card", item);
};

const init_my = () => {
  childrenData.value = [];
};

defineExpose({
  search,
  init_my,
});
</script>

<style lang="scss" scoped>
.library-cards {
  margin-bottom: 20px;

  .total-count {
    font-size: 14px;
    color: #666;
    margin: 16px 0;
  }

  .cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 229px;

    .card-left {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .folder-icon {
        width: 32px;
        height: 32px;
        background: #436bff;
        border-radius: 6px;
        color: #fff;
        margin-right: 12px;

        img {
          width: 16px;
          height: 16px;
        }
      }

      .card-title {
        font-size: 16px;
        color: #262626;
        font-weight: 500;
        width: 230px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .card-info {
      padding-left: 36px;

      .clip-count {
        color: #666;
        font-size: 14px;
        margin-bottom: 4px;
      }

      .update-time {
        color: #999;
        font-size: 12px;
      }
    }

    &:hover {
      box-shadow: 0px 4px 12px 0px rgba(36, 104, 242, 0.08);

      .card-title {
        color: #436bff;
      }
    }
  }

  .card:hover {}
}
</style>
