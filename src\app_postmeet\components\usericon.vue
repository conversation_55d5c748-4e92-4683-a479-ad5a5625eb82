<template>
  <span class="user_photo_wrap">
    <img width="32" v-if="photo" :src="photo" alt="" />
    <i v-if="!photo && userName" class="aname">
      {{ userName.slice(0, 1) }}
    </i>
  </span>
</template>

<script>
export default {
  props: ["name", "pic", "usemy"],
  data() {
    const { photo, name } = g.appStore.user;
    let photo2 = "";
    if (this.usemy) {
      photo2 = photo || "";
    } else if (this.pic) {
      photo2 = this.pic || "";
    }

    return {
      photo: photo2,
      userName: this.name || name,
    };
  },
};
</script>

<style lang="scss">
.user_photo_wrap {
  font-size: 18px;

  img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }

  .aname {
    display: inline-block;
    font-style: normal;
    border: 1px solid #436bff;
    height: 32px;
    line-height: 32px;
    width: 32px;
    text-align: center;
    border-radius: 50%;
    background: #436bff;
    margin-right: 10px;
    color: #fff;
  }
}
</style>
