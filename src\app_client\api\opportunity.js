// 获取商机列表
export const getOpportunityList = (params) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: {
                    datas: [
                        {
                            id: 1,
                            opportunity_name: "智慧城市数字化转型项目",
                            riskCount: 1,
                            meddicList: [
                                { meddicType: "METRICS", meddicStatus: "FULL" },
                                { meddicType: "ECONOMIC_BUYER", meddicStatus: "FULL" },
                                { meddicType: "DECISION_CRITERIA", meddicStatus: "INSUFFICIENT" },
                                { meddicType: "DECISION_PROCESS", meddicStatus: "FULL" },
                                { meddicType: "IDENTIFY_PAIN", meddicStatus: "FULL" },
                                { meddicType: "CHAMPION", meddicStatus: "FULL" }
                            ],
                            communicationCount: 1,
                            todosCount: 3
                        },
                        {
                            id: 2,
                            opportunity_name: "企业数字化管理平台建设",
                            riskCount: 4,
                            meddicList: [
                                { meddicType: "METRICS", meddicStatus: "FULL" },
                                { meddicType: "ECONOMIC_BUYER", meddicStatus: "FULL" },
                                { meddicType: "DECISION_CRITERIA", meddicStatus: "FULL" },
                                { meddicType: "DECISION_PROCESS", meddicStatus: "FULL" },
                                { meddicType: "IDENTIFY_PAIN", meddicStatus: "FULL" },
                                { meddicType: "CHAMPION", meddicStatus: "FULL" }
                            ],
                            communicationCount: null,
                            todosCount: 2
                        },
                        {
                            id: 3,
                            opportunity_name: "智能制造解决方案项目",
                            riskCount: 2,
                            meddicList: [
                                { meddicType: "METRICS", meddicStatus: "NOT_FILLED" },
                                { meddicType: "ECONOMIC_BUYER", meddicStatus: "FULL" },
                                { meddicType: "DECISION_CRITERIA", meddicStatus: "INSUFFICIENT" },
                                { meddicType: "DECISION_PROCESS", meddicStatus: "FULL" },
                                { meddicType: "IDENTIFY_PAIN", meddicStatus: "FULL" },
                                { meddicType: "CHAMPION", meddicStatus: "NOT_FILLED" }
                            ],
                            communicationCount: 1,
                            todosCount: 5
                        }
                    ],
                    totalNum: 3,
                }
            });
        }, 500);
    });
};

// 获取商机状态列表
export const getOpportunityStatusList = () => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: [
                    { value: "进行中", label: "进行中" },
                    { value: "已完成", label: "已完成" },
                    { value: "已暂停", label: "已暂停" },
                    { value: "已取消", label: "已取消" }
                ]
            });
        }, 300);
    });
}; 