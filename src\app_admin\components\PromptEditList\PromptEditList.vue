<template>
  <div :class="`rl_box ${item.systemPreset ? 'sys_dom' : 'diy_dom'}`" v-for="item in datas" :key="item.id">
    <div class="rl_header">
      <div class="rlh_left">
        <div class="icon">
          <ivstar />
        </div>
        <div class="title">
          {{ item[props.title] }}
        </div>
        <div class="score_info" v-if="item.systemId == 205 || item.systemId == 206">
          （满分100分，达标值 {{ item.targetSettings }} 分）
        </div>
        <!-- 4 会议纪要 8 商机识别 -->
        <template v-if="[4, 8].includes(+item.systemId)">
          <div v-if="item.systemId == 8" class="sales_methodology">
            {{analysisTypes.find(x => x.value == item.defaultSalesMethodology)?.label}}
          </div>
          <div v-else class="sales_methodology">
            通用
          </div>
        </template>
      </div>
      <div v-if="item.needSave">
        <div class="rlh_right" v-if="!item.edit_mode">
          <span class="btn" @click="onBtn(item, 'edit_standard')" v-if="item.systemId == 206">
            达标设置 </span>
          <span class="btn" @click="onBtn(item, 'select_ability')" v-if="item.systemId == 206">
            选择评估任务项
          </span>
          <span class="btn" @click="onBtn(item, 'edit_ability')" v-if="item.systemId == 206">
            自定义任务项
          </span>
          <span class="btn" @click="onBtn(item, 'edit')" v-if="!(item.systemId == 206)"> 编辑
          </span>
          <span class="line" v-if="item.showDelete"> </span>
          <span class="btn" @click="onBtn(item, 'delete')" v-if="item.showDelete">
            删除
          </span>
        </div>
        <div class="rlh_right" v-else>
          <span class="btn" @click="onBtn(item, 'save')"> 保存 </span>
          <span class="line"> </span>
          <span class="btn" @click="onBtn(item, 'cancel')"> 取消 </span>
        </div>
      </div>

      <div class="rlh_right" v-else-if="alwaysShowEdit || !item.systemPreset || item.showEdit">
        <span class="btn" @click="onBtn(item, 'edit_standard')" v-if="item.systemId == 205">
          达标设置 </span>
        <span class="btn" @click="onBtn(item, 'select_ability')" v-if="item.systemId == 205">
          选择评估能力项
        </span>
        <span class="btn" @click="onBtn(item, 'edit_ability')" v-if="item.systemId == 205">
          自定义能力项
        </span>
        <span class="btn" @click="onBtn(item, 'edit')" v-if="!(item.systemId == 205)"> 编辑
        </span>
        <span class="line" v-if="item.showDelete"> </span>
        <span class="btn" @click="onBtn(item, 'delete')" v-if="item.showDelete">
          删除
        </span>
      </div>
    </div>
    <slot name="header_bottom" />
    <div class="rl_body">
      <div class="section_8 flex-row justify-between" v-if="item.systemPreset">
        <div class="block_1 flex-col">
          <slot :row="item" />
        </div>
      </div>
      <el-input v-model="item[props.prompt]" autosize readonly type="textarea" v-else />
    </div>
  </div>
</template>

<script setup>
import ivstar from "@/app_admin/icons/ivstar.vue";
const datas = ref([]);
const props = defineProps(["title", "prompt", "dtype"]);
const emit = defineEmits(["callback"]);
const alwaysShowEdit = ref(true);
const analysisTypes = ref(g.cacheStore.salesMethodology);

const init = (_alwaysShowEdit, d) => {
  alwaysShowEdit.value = _alwaysShowEdit;
  datas.value = d;
};

const onBtn = (item, type) => {
  if (type == "edit") {
    item.edit_mode = true;
  } else if (type == "save") {
    //set edit_mode false when save success
  } else if (type == "cancel") {
    item.edit_mode = false;
  }
  if (type == "delete") {
    onDelete(item);
  } else {
    emit("callback", type, item);
  }
};

const onDelete = (item) => {
  ElMessageBox.confirm(
    `您确定要删除 ${item[props.title]} 吗？删除后不可恢复`,
    "删除提示",
    {
      confirmButtonText: "确认删除",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      emit("callback", "delete", item);
    })
    .catch(() => { });
};

const exitEdit = () => {
  datas.value.forEach((item) => {
    item.edit_mode = false;
  });
};

onMounted(() => {
  g.emitter.on("prompt_save_status", (status) => {
    if (status) {
      datas.value.forEach((item) => {
        item.edit_mode = false;
      });
    }
  });
});

onUnmounted(() => {
  g.emitter.off("prompt_save_status");
});

defineExpose({ props, init, datas, onBtn, exitEdit, analysisTypes });
</script>

<style lang="scss">
@import url("./examples/style.scss");
</style>
