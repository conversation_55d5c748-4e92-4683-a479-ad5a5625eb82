<template>
    <div class="topic_show_switch">
        <el-switch v-model="isshow" @change="onChange"></el-switch>
        <div class="txt">显示摘要</div>
    </div>
</template>


<script>

export default {
    name: 'SwitchShowTopic',
    data() {
        return {
            isshow: true,
        }
    },
    mounted() {
        this.onChange()
    },
    methods: {
        onChange() {
            g.postmeetStore.setValue('isshow', this.isshow)
            this.$emit('callback', this.isshow)
        },
    }
}
</script>

<style lang='scss'>
.topic_show_switch {
    padding-right: 12px;
    display: flex;
    align-items: center;

    .txt {
        margin-right: 3px;
        line-height: 12px;
        font-size: 14px;
        margin-left: 10px;
    }
}
</style>
