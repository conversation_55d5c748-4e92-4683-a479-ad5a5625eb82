<template>
    <div class="details">
        <div class="details_filter">
            <RadioGroup v-model="competitorId" class="fch_radio" :options="options" />
        </div>
        <aiAnalysis :data="selectAIAnalysis" :loading="loadingAiAnalyse" style="margin-bottom: 24px;" />
        <div class="details_charts">
            <totalCharts :data="totalChartsData" :metrics="props.totalChartsMetrics" :setting="{
                title: `${competitorName}评价分布图`
                ,
                width: 82
            }
                " class="w50" :loading="loading1" />
            <div class="details_radar">
                <p class="details_radar-title">
                    {{ competitorName }}声量雷达图
                    <TooltipCvCalc />
                </p>
                <radarEcharts :data="volumedistribution" :config="radarEchartsConfig" :loading="loading3">
                </radarEcharts>
            </div>
        </div>
    </div>
</template>

<script setup lang="js">
import { debounce } from "@/js/utils.js"
import totalCharts from '@/app_client/views/BusinessInsights/components/totalChart.vue'
import cloudMapChart from '../chartsComponents/cloudMap.vue';
import radarEcharts from '../chartsComponents/radar.vue'
import aiAnalysis from '../../components/aiAnalysis.vue'
import RadioGroup from '@/app_client/components/RadioGroup.vue'
import TooltipCvCalc from '@/app_client/components/TooltipCvCalc.vue'

const emit = defineEmits(['wordClickToTable'])
const props = defineProps(['colorObject', 'totalChartsMetrics'])
const store = g.clientBiStore
const options = computed(() => store.competitorsArr.map((i) => ({
    label: i.alternativeName || i.commonName,
    value: i.competitorId,
}))) || []
const competitorId = ref('')
const competitorName = computed(() => options.value.find(i => i.value === competitorId.value)?.label || '')
const radarEchartsConfig = {
    isShowLegend: false
}
const loadingAiAnalyse = ref(true)
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)

// 从store获取数据
const totalChartsData = computed(() => store.sentimentDistribution || [])
// const cloudMapChartData = computed(() => store.keywordSentiment || [])
const volumedistribution = computed(() => store.volumedistribution || [])
const selectAIAnalysis = computed(() => store.selectAIAnalysis)

// 点击事件处理
const handleWordClick = (params) => {
    emit('wordClickToTable', params)

}
const init = async () => {
    if (!competitorId.value) return;

    const f1 = async () => {
        loading1.value = true;
        await store.getSentimentDistribution(competitorId.value);
        loading1.value = false;
    }

    // const f2 = async () => {
    //     loading2.value = true;
    //     await store.getKeywordSentiment(competitorId.value);
    //     loading2.value = false;
    // }

    const f3 = async () => {
        loading3.value = true;
        await store.getVolumedistribution(competitorId.value);
        loading3.value = false;
    }

    const f4 = async () => {
        loadingAiAnalyse.value = true;
        await store.getCompetitorAIAnalysis(competitorId.value);
        loadingAiAnalyse.value = false;
    }
    Promise.all([f1(), f3(), f4()])
}

// 创建防抖函数包装init
const debouncedInit = debounce(init, 200)

// 同时监听多个值
watch(() => [competitorId.value, g.clientBiStore.periodType], (arr) => {
    debouncedInit()
}, { immediate: true, deep: true })

watch(() => options.value, () => competitorId.value = options.value[0]?.value || "")


</script>

<style lang="scss">
.details {
    &_title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        margin-bottom: 12px;
    }

    .cr_title {
        margin: 0;
        font-weight: 700;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 14px;
            background: #436BFF;
            margin-right: 8px;
            border-radius: 2px;
        }
    }

    &_filter {
        margin: 10px 0 24px 0;
    }

    &_charts {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 24px;
    }

    &_radar {
        width: calc(50% - 12px);
        text-align: left;
        background: #fff;
        padding: 24px;
        box-sizing: border-box;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
        border-radius: 8px;
        margin-bottom: 24px;
        height: 460px;

        &-title {
            font-weight: 700;
            font-size: 16px;
            color: #262626;
            margin: 0;
            display: flex;
            align-items: center;
        }

        &-icon svg {
            background: #999;
            border-radius: 50%;
        }
    }

    .fch_title {
        border-left: 4px solid #436BFF;
        padding-left: 8px;
        margin-left: -12px;
    }

    .w50 {
        width: calc(50% - 12px);
        height: 460px;
    }

    .details_radar {
        .competitiveRadar_radar_chart {
            height: 420px;
        }
    }
}
</style>