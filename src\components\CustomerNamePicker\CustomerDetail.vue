<template>
    <div class="customer-detail">
        <div class="basic-info">
            <div class="title">客户信息</div>
            <div class="info-item">
                <span class="label">客户名称</span>
                <span class="value">{{ item.name }}</span>
            </div>
            <div class="info-item" v-for="x in item.formFieldValues">
                <span class="label">{{ x.fieldName }}</span>
                <span class="value">{{ x.fieldValue }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    item: Object,
});
</script>

<style lang="scss" scoped>
.customer-detail-popover {
    .customer-detail {
        padding: 16px;

        .basic-info {
            margin-bottom: 20px;

            .title {
                font-size: 16px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 16px;
                position: relative;
                padding-left: 10px;

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 16px;
                    background-color: var(--el-color-primary);
                    border-radius: 2px;
                }
            }

            .info-item {
                display: flex;
                margin-bottom: 12px;
                font-size: 14px;
                line-height: 22px;

                .label {
                    color: #8c8c8c;
                    width: 80px;
                    flex-shrink: 0;
                }

                .value {
                    color: #262626;
                    flex: 1;
                }
            }
        }
    }
}
</style>
