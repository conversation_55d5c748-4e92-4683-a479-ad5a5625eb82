<template>
    <div class="uploading_wrap" v-show="is_show">
        <div class="win_title">
            <div class="left">
                {{ title }}
            </div>
            <div class="flex-grow"> </div>
            <div class="right">
                <el-tooltip content="最小化" placement="top-end" v-if="is_show_list">
                    <img :src="getIcon('min')" class="icon" @click="onMin" />
                </el-tooltip>
                <el-tooltip content="最大化" placement="top-end" v-else>
                    <img :src="getIcon('max')" class="icon" @click="onMax" />
                </el-tooltip>
                <el-tooltip content="取消上传" placement="top-end">
                    <img :src="getIcon('close')" class="icon" @click="onClose" />
                </el-tooltip>
            </div>
        </div>
        <div class="files_box" v-for="row in list" :key="row.startTime" v-show="is_show_list">
            <div class="file_up">
                <img :src="getIcon('ppt')" class="file_icon" />
                <div class="up_mid flex-grow">
                    <div class="title">{{ row.subject }}</div>
                    <div class="size">{{ row.size }}</div>
                </div>
                <div class="up_right">
                    <el-tooltip content="取消上传" placement="top-end">
                        <img :src="getIcon('delete')" class="icon" @click="onDelete(row)" title="取消上传" />
                    </el-tooltip>
                </div>
            </div>
            <div class="file_bottom">
                <div class="process_box">
                    <div class="process" :style="{ width: procStr(row) }"></div>
                </div>
                <div class="note"> {{ getProcessText(row) }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { uploadPpt, uploadGoodsList, updateXmFile } from '@/app_admin/tools/api.js';
import { getOssUrl } from '@/js/utils.js';

const is_show = ref(false);
const is_show_list = ref(true);
const loading = ref(false);
const cancelTokenSource = {};
const processes = ref({});
const list = ref([]);
const totalCount = ref(0);
const title = ref('上传中，请不要关闭浏览器页面');
let intervalId;

const getIcon = (fname) => {
    return getOssUrl(`${fname}.svg`)
}

const procStr = computed(() => {
    return function (row) {
        if (processes.value.hasOwnProperty(row.startTime)) {
            return processes.value[row.startTime] + '%';
        } else {
            return ''
        }
    }
})

const getProcessText = computed(() => {
    const statusMap = {
        waiting: "排队中", uploading: "正在上传", uploaded: "上传完成", delete: "已取消", error: "上传错误"
    }
    return function (row) {
        return statusMap[row.status];
    }
})

const onMin = () => {
    is_show_list.value = false;
};

const onMax = () => {
    is_show_list.value = true;
};

const onClose = () => {
    ElMessageBox.confirm(
        '文件还在上传中，取消后不可恢复',
        '确定取消上传吗？',
        {
            confirmButtonText: '取消上传',
            cancelButtonText: '继续上传',
            type: 'warning',
        }
    )
        .then(() => {
            const startTimes = g.adminFileStore.get_files().map(x => x.startTime)
            for (let i = 0; i < startTimes.length; i++) {
                _deleteFile(startTimes[i]);
            }
        })
        .catch(() => { });
};

const onDelete = (row) => {
    ElMessageBox.confirm(
        '文件还在上传中，取消后不可恢复',
        '确定取消上传吗？',
        {
            confirmButtonText: '取消上传',
            cancelButtonText: '继续上传',
            type: 'warning',
        }
    )
        .then(() => {
            _deleteFile(row.startTime);
        })
        .catch(() => { });
};

const _deleteFile = (startTime) => {
    cancelTokenSource[startTime].abort();
    processes.value[startTime] = -1;
    g.adminFileStore.update_file_status(startTime, 'delete')
    updateList();
    ElMessage({ type: 'success', message: '取消成功' });
};

const show = () => {
    is_show.value = true;
    function upload_func(param) {
        const { startTime, status } = param;
        if (['waiting', 'uploading'].includes(status)) {
            processes.value[startTime] = 0;
            cancelTokenSource[startTime] = null;
            g.adminFileStore.update_file_status(startTime, 'uploading')
            upload(param);
        }
    }

    let index = 0;
    function executeFunc() {
        if (index < g.adminFileStore.upload_files.length) {
            const param = g.adminFileStore.upload_files[index];
            upload_func(param);
            index++;
        }

        if (index == g.adminFileStore.upload_files.length) {
            intervalId && clearInterval(intervalId);
            intervalId = null;
        }
        updateList();
    }
    executeFunc();
    intervalId = setInterval(executeFunc, 800);
};

const updateList = () => {
    list.value = g.adminFileStore.get_files()
    totalCount.value = list.value.length;
    title.value = `正在上传${totalCount.value}个文件,请不要关闭浏览器页面!`;
    is_show.value = totalCount.value > 0 || !!intervalId;
};

const upload = (param) => {
    const { file, subject, startTime, source, tags } = param;
    const formData = new FormData();
    if (source == 'ppt') {
        formData.append('ppt', file);
        formData.append('fileName', subject);
        if (!!tags && Array.isArray(tags) && tags.length > 0) {
            formData.append('tags', tags.join(','));
        }
    } else if (source == 'goods') {
        formData.append('file', file);
        formData.append('name', subject);
    } else if (source == 'communication') {
        formData.append('template', file);
        formData.append('templateName', subject);
    }

    const onProgress = (xhr, process) => {
        cancelTokenSource[startTime] = xhr;
        processes.value[startTime] = process;
    };

    const onSuccess = (data) => {
        g.adminFileStore.update_file_status(startTime, 'uploaded')
        updateList();
        g.emitter.emit('file_uploaded', '');
        ElMessage({ type: 'success', message: '上传成功' });
    };

    const onFail = (error) => {
        g.adminFileStore.update_file_status(startTime, 'error')
        if (error.code == 20) {
            console.log('Request canceled', error);
        } else {
            ElMessage.error(`上传错误${error.code}-${error.message}`);
        }
    };
    if (source == 'ppt') {
        uploadPpt(param.categoryId, formData, onProgress, onSuccess, onFail);
    } else if (source == 'goods') {
        uploadGoodsList(param.categoryId, formData, onProgress, onSuccess, onFail);
    } else if (source == 'communication') {
        updateXmFile(param.topicId, param.categoryId, formData, onProgress, onSuccess, onFail)
    } else {
        console.error("unkown source", source)
    }
};

onMounted(() => {
    g.emitter.on('add_ppt_file', show);
});

onUnmounted(() => {
    g.emitter.off("add_ppt_file");
});

defineExpose({
    is_show,
    is_show_list,
    loading,
    processes,
    list,
    totalCount,
    title,
    getProcessText,
    procStr,
    onMin,
    onMax,
    onClose,
    onDelete,
    show,
    getIcon,
    updateList,
    upload,
});
</script>

<style lang="scss">
@import url("./UploadingBox.scss");
</style>