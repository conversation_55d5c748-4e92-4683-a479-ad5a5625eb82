<template>
    <div class="dept_tree">
        <el-tree-select v-model="localValue" :data="treeData" :render-after-expand="false" check-strictly
            check-on-click-node placeholder="请选择部门" :default-expand-all="false" @change="onChange" />
    </div>
</template>

<script setup>
import { extractDeptTree, checkChildrenHasSub } from "@/js/utils.js"
const treeDataAll = ref([]);
const treeData = ref([]);
const emit = defineEmits(['callback'])
const localValue = ref('')
const rootDeptId = ref('')

const props = defineProps({
    disabledTopDept: {
        type: Boolean,
        default: false
    },
    allDept: {
        type: Boolean,
        default: false
    }
})


const onChange = (deptId) => {
    const currDept = extractDeptTree(toRaw(treeData.value), deptId)
    let childrenHasSub = false;
    if (currDept.children && currDept.children.length > 0) {
        childrenHasSub = checkChildrenHasSub(currDept.children)
    }
    emit('callback', { value: deptId, children: currDept.children, childrenHasSub })
}

const _updateTreeData = () => {
    if (!treeDataAll.value || treeDataAll.value.length == 0) {
        return
    }
    const aa = extractDeptTree(toRaw(treeDataAll.value), rootDeptId.value);
    treeData.value = rootDeptId.value ? [aa] : treeDataAll.value;
    if (treeData.value.length > 0) {
        if (!rootDeptId.value && treeData.value[0].children && treeData.value[0].children.length > 0) {
            localValue.value = treeData.value[0].children[0].value
        } else {
            localValue.value = treeData.value[0].value
        }
        if (props.disabledTopDept) {
            treeData.value[0].disabled = true
        }
        onChange(localValue.value)
    }
}

const init = () => {
    const fn = props.allDept ? g.cacheStore.getDeptTreeDataAll : g.cacheStore.getDeptTreeData;
    g.emitter.emit('TreeDeptSelector_loading', true)
    fn().then(resp => {
        treeDataAll.value = resp;
        if (resp.length > 0) {
            _updateTreeData()
        }
        g.emitter.emit('TreeDeptSelector_loading', false)
    })
}

const setDeptId = (deptId) => {
    rootDeptId.value = deptId
    _updateTreeData()
}

defineExpose({ localValue, treeData, init, setDeptId })
</script>

<style lang="scss">
.dept_tree {
    width: 500px;
}
</style>