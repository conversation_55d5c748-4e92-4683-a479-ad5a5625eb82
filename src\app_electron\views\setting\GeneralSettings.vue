<template>
  <div class="general-settings">
    <div class="setting-item">
      <el-checkbox v-model="settings.scheduleNotification" label="日程开始通知" @change="onChangeScheduleNotification" />
    </div>
    <div class="setting-item">
      <el-checkbox v-model="settings.showRecordingDuration" label="显示录制持续时长" />
    </div>
  </div>
</template>

<script setup>
const settings = ref(g.electronStore.settings.general);
import { updateNoticeConfig } from '@/app_electron/tools/api.js'

watch(() => settings.value, (newSettings) => {
  g.electronStore.updateSetting('general', newSettings)
}, { deep: true });

const onChangeScheduleNotification = (status) => {
  updateNoticeConfig(status).then((resp) => {
    if (resp.code==0) {
      ElMessage.success(status ? '开启成功' : '关闭成功')
    } else {
      ElMessage.error('更新失败')
    }
  }).catch(() => {
    ElMessage.error('更新失败')
  })
}

onMounted(() => {
  settings.value = g.electronStore.settings.general;
});

defineExpose({
  settings
})

</script>

<style  lang="scss"  scoped>
.general-settings {
  .setting-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }

  .clickable {
    cursor: pointer;
  }

  .clickable:hover {
    background-color: #f5f5f5;
  }
}
</style>
