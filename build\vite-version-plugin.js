// 生成 version.json 的 Vite 插件
const fs = require('fs')
const path = require('path')

module.exports = function viteVersionPlugin() {
  return {
    name: 'vite-version-plugin',
    closeBundle: {
      sequential: true,
      order: 'post',
      handler() {
        const version = new Date().getTime()
        const versionJson = {
          version
        }

        // 确保输出目录存在
        const distDir = path.join(process.cwd(), 'dist')
        if (!fs.existsSync(distDir)) {
          fs.mkdirSync(distDir, { recursive: true })
        }

        // 写入 version.json
        fs.writeFileSync(
          path.join(distDir, 'version.json'),
          JSON.stringify(versionJson, null, 2)
        )

        // 修改 index.html，注入版本信息
        const indexPath = path.join(distDir, 'index.html')
        if (fs.existsSync(indexPath)) {
          let indexContent = fs.readFileSync(indexPath, 'utf-8')
          // 在 head 标签结束前注入版本信息
          indexContent = indexContent.replace(
            '</head>',
            `<script>window.__version__ = ${version};</script>\n</head>`
          )
          fs.writeFileSync(indexPath, indexContent)
        }

        console.log('Version information injected successfully!')
      }
    }
  }
}