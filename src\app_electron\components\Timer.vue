<template>
  <div :class="`timer ${recording ? 'timer_recording' : 'timer_paused'}`">
    {{ time }}
  </div>
</template>

<script setup>
const emit = defineEmits(['callback'])
const props = defineProps({
  isRecording: {
    type: Boolean,
    default: false
  }
})
const recording = ref(props.isRecording)

watch(() => props.isRecording, (newVal) => {
  recording.value = newVal
})

const time = ref("00:00");
let startTime = null;
let elapsedTime = 0;
let interval = null;

const formatTime = (totalSeconds) => {
  let txt = ''
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  if (hours > 0) {
    txt += `${hours}:`;
  }
  txt += `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  return txt;
};

const updateTime = (currentTotalSeconds) => {
  if (currentTotalSeconds && currentTotalSeconds > 0) {
    time.value = formatTime(currentTotalSeconds);
    elapsedTime = currentTotalSeconds;
  } else {
    time.value = "00:00";
    elapsedTime = 0;
  }
  // 如果计时器正在运行，需要重新设置startTime以避免重复计算
  if (interval && startTime) {
    startTime = Date.now();
  }
};

const pauseTimer = () => {
  console.log('pauseTimer')
  if (!interval) return;
  clearInterval(interval);
  interval = null;
  if (startTime) {
    elapsedTime += Math.floor((Date.now() - startTime) / 1000);
    startTime = null;
  }
};

const startTimer = () => {
  console.log('startTimer', 'interval exists:', !!interval)
  if (interval) {
    console.log('Timer already running, skipping startTimer')
    return;
  }
  startTime = Date.now();

  // 立即更新一次显示
  const updateDisplay = () => {
    const now = Date.now();
    const currentTotalSeconds = elapsedTime + Math.floor((now - startTime) / 1000);
    time.value = formatTime(currentTotalSeconds);
    emit('callback', {
      second: currentTotalSeconds,
      time: time.value
    });
  };

  updateDisplay(); // 立即显示当前时间
  interval = setInterval(updateDisplay, 1000);
  console.log('Timer started with interval:', interval)
};

onUnmounted(() => {
  pauseTimer();
});

defineExpose({ startTimer, pauseTimer, updateTime });
</script>

<style scoped lang="scss">
.timer {
  font-size: 20px;
}
</style>
