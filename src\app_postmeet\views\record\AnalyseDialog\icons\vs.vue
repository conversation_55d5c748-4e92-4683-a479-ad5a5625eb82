<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="lgvs-1">
                <stop stop-color="#78A4FF" offset="0%"></stop>
                <stop stop-color="#436BFF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="拜访过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="对话分析--icon集合" transform="translate(-520.000000, -114.000000)">
                <g id="编组-18备份-5" transform="translate(500.000000, 58.000000)">
                    <g id="编组-3" transform="translate(20.000000, 56.000000)">
                        <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                        <g id="编组" transform="translate(1.000000, 1.000000)" fill="url(#lgvs-1)" fill-rule="nonzero">
                            <path
                                d="M5,0 L13,0 C15.7614237,-5.07265313e-16 18,2.23857625 18,5 L18,13 C18,15.7614237 15.7614237,18 13,18 L5,18 C2.23857625,18 3.38176876e-16,15.7614237 0,13 L0,5 C-3.38176876e-16,2.23857625 2.23857625,5.07265313e-16 5,0 Z M12.7285714,5.14285715 C12.4714286,5.14285715 12.0857143,5.14285715 11.8285714,5.27142857 C11.4428572,5.4 11.1857143,5.52857142 10.9285714,5.78571428 C10.6714286,5.91428572 10.4142857,6.17142857 10.2857143,6.42857143 C10.1571428,6.81428572 10.1571428,7.07142858 10.1571428,7.32857143 C10.1571428,7.58571428 10.1571428,7.84285715 10.2857143,8.1 C10.4142857,8.22857143 10.4142857,8.48571428 10.5428572,8.61428572 C10.6714286,8.87142857 10.9285714,9 11.1857143,9.12857143 C11.5714286,9.51428572 11.8285714,9.64285715 12.2142857,9.77142857 L12.3428572,9.9 C12.6,10.0285714 12.8571429,10.1571428 12.9857143,10.2857143 C13.1142857,10.4142857 13.2428571,10.6714286 13.2428572,10.8 C13.2428572,10.9285714 13.1142857,11.1857143 12.9857143,11.1857143 C12.8571428,11.3142857 12.6,11.3142857 12.3428572,11.3142857 C11.9571429,11.3142857 11.5714286,11.3142857 11.1857143,11.1857143 C11.0571428,11.0571428 10.8,10.9285714 10.5428572,10.8 L10.0285714,12.0857143 C10.8,12.6 11.8285714,12.8571428 12.7285714,12.8571428 C13.1142857,12.8571428 13.5,12.7285714 13.8857143,12.6 C14.2714286,12.4714286 14.5285714,12.3428572 14.7857143,12.0857143 C15.0428571,11.8285714 15.1714286,11.5714286 15.3,11.3142857 C15.4285714,11.0571429 15.4285714,10.8 15.4285714,10.5428572 C15.4285714,10.2857143 15.4285714,10.0285714 15.3,9.77142857 C15.1714286,9.51428572 15.0428572,9.25714285 14.9142857,9.12857143 C14.7857143,9 14.5285714,8.87142858 14.4,8.74285715 C14.0142857,8.4857143 13.6285714,8.35714287 13.2428572,8.22857143 C12.9857143,8.1 12.6,7.97142858 12.3428572,7.71428572 C12.3428572,7.71428572 12.2142857,7.58571428 12.2142857,7.45714285 C12.2142857,7.32857142 12.0857143,7.32857142 12.0857143,7.2 C12.0857143,7.07142858 12.2142857,6.81428572 12.3428572,6.81428572 C12.4714286,6.68571428 12.7285714,6.68571428 12.9857143,6.68571428 C13.5,6.68571428 14.1428571,6.81428572 14.5285714,7.07142857 L14.9142857,5.65714285 C14.2714286,5.27142857 13.5,5.14285715 12.7285714,5.14285715 L12.7285714,5.14285715 Z M4.62857143,5.4 L2.57142857,5.4 L4.75714285,12.7285714 L7.71428572,12.7285714 L9.9,5.4 L7.71428572,5.4 L6.17142857,11.1857143 L4.62857143,5.4 Z"
                                id="形状"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>
