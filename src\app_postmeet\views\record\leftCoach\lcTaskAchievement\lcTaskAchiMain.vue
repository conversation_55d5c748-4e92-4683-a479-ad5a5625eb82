<template>
  <div class="la_task_achi_main flex-column" v-for="(item, index) in taskList" :key="index">
    <div class="lt_box flex-row">
      <div class="header-info">
        <div class="stage-title">
          {{ item.content.evaluationResults.taskName }}
        </div>
        <div class="progress">
          {{
            item.content.evaluationResults.subTasks.filter((x) => x.status == "complete")
              .length
          }}/{{ item.content.evaluationResults.subTasks.length }}
        </div>
      </div>

      <div class="split_line"></div>

      <div class="task-list">
        <div class="task-item" v-for="(item2, index2) in item.content.evaluationResults.subTasks" :key="index2">
          <div class="task-header flex-row align-center">
            <!-- 这边的实际图片名错了 -->
            <img class="checkbox" :src="getOssUrl(item2.status == 'complete' ? 'wrong.png' : 'right.png')"
              alt="checkbox" />
            <span class="title">{{ item2.subTaskName }}</span>
          </div>
        </div>
      </div>

      <div class="split_line"></div>

      <div class="expand-arrow" @click="toggleExpand(item)">
        <ArrowDown v-if="!item.isExpanded" />
        <ArrowUp v-else />
      </div>
    </div>
    <div class="expanded-content" v-show="item.isExpanded">
      <MdViewer :md="getMdText(item)" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import ArrowDown from "@/icons/arrow_down.vue";
import ArrowUp from "@/icons/arrow_up.vue";
import MdViewer from "@/components/Markdown/MdViewer.vue"
import { getOssUrl } from "@/js/utils";

const taskList = ref([]);

const totalCount = computed(() => taskList.value.length);
const completedCount = computed(
  () => taskList.value.filter((item) => item.checked).length
);

const getMdText = (item) => {
  return item?.content?.evaluationResults?.comment || "暂无备注";
};

const init = () => {
  const ar =
    g.postmeetStore.data.saleReport.salesCounsellingReports.find((x) => x.systemId == 206)
      ?.report?.tasks || [];
  taskList.value = ar.filter((x) => x.content?.evaluationResults?.subTasks?.length > 0);
};

const toggleExpand = (item) => {
  item.isExpanded = !item.isExpanded;
};

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
.la_task_achi_main {
  position: relative;
  width: 100%;
  min-width: 600px;

  .lt_box {
    padding: 12px 16px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 16px;
  }

  .header-info {
    min-width: 120px;
    width: 140px;
    flex-shrink: 0;
    text-align: center;

    .stage-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      word-break: break-word;
    }

    .progress {
      font-size: 14px;
      color: #666;
    }
  }

  .split_line {
    width: 1px;
    height: 114px;
    flex-shrink: 0;
    background: linear-gradient(180deg,
        rgba(239, 239, 239, 0) 0%,
        #efefef 50%,
        rgba(233, 233, 233, 0) 100%);
  }

  .task-list {
    flex: 1;
    min-width: 200px;
    padding: 0 16px;
    overflow-x: hidden;

    .task-item {
      margin-bottom: 12px;

      .task-header {
        display: flex;
        align-items: center;
        gap: 8px;

        .checkbox {
          width: 16px;
          height: 16px;
          flex-shrink: 0;
        }

        .title {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          word-break: break-word;
        }
      }
    }
  }

  .expand-arrow {
    width: 40px;
    flex-shrink: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .expanded-content {
    margin-top: 16px;
    padding: 16px;
    background: #f7f8fc;
    box-shadow: inset 0px -1px 0px 0px #f0f0f0;
  }
}

@media screen and (max-width: 768px) {
  .la_task_achi_main {
    .lt_box {
      padding: 12px 8px;
      gap: 8px;
    }

    .header-info {
      min-width: 100px;
      width: 120px;
    }

    .task-list {
      padding: 0 8px;
    }

    .expand-arrow {
      width: 32px;
    }
  }
}
</style>
