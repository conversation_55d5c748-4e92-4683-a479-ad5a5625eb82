<template>
  <el-popover
    placement="bottom"
    title=""
    width="166"
    trigger="click"
    popper-class="la_subtitle_wrap"
  >
    <template #reference>
      <div class="hicon">
        <el-tooltip class="item" effect="dark" content="字幕" placement="top">
          <SubtitleIcon />
        </el-tooltip>
      </div>
    </template>
    <div class="sib_main flex-column">
      <div class="sib flex-row" @click="switchTextTrack">
        <div>字幕</div>
        <el-switch v-model="enable_subtitle" active-color="#436bff"> </el-switch>
      </div>
      <div class="line" v-show="enable_subtitle"></div>
      <div class="sic flex-row" v-show="enable_subtitle">
        <div class="zio_txt">字幕大小</div>

        <div :class="`btn ${sb_fontsize < 2 ? 'disable' : ''}`" @click="zoom('-')">
          <ZoomInIcon />
        </div>
        <div :class="`btn  ${sb_fontsize > 4 ? 'disable' : ''}`" @click="zoom('+')">
          <ZoomOutIcon />
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script>
import SubtitleIcon from "@/app_postmeet/icons/right_header/subtitle.vue";
import ZoomInIcon from "./icons/zoom_in.vue";
import ZoomOutIcon from "./icons/zoom_out.vue";
export default {
  components: { SubtitleIcon, ZoomInIcon, ZoomOutIcon },
  data() {
    return {
      enable_subtitle: false,
      sb_fontsize: 3,
    };
  },
  methods: {
    zoom(action) {
      if (action == "-" && this.sb_fontsize > 1) {
        this.sb_fontsize -= 1;
      } else if (action == "+" && this.sb_fontsize < 5) {
        this.sb_fontsize += 1;
      }
      g.emitter.emit("video_sb_zoom", this.sb_fontsize);
    },
    switchTextTrack() {
      g.emitter.emit("update_text_trace", this.enable_subtitle);
    },
  },
};
</script>

<style lang="scss">
.la_subtitle_wrap {
  padding: 16px 20px !important;
  background: #ffffff;
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.12);

  .sib {
    justify-content: space-between;
  }

  .line {
    width: 127px;
    height: 1px;
    background: #e9e9e9;
    margin: 16px 0;
  }

  .sic {
    .zio_txt {
      width: 83px;
    }

    .btn {
      width: 22px;
      cursor: pointer;
      margin-left: 6px;
      color: #595959;
    }

    .btn:hover {
      color: #436bff;
    }

    .disable {
      color: #bfbfbf;
    }
  }
}
</style>
