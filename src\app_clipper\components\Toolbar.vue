<template>
  <div class="toolbar" @wheel.prevent="handleWheel">
    <div class="toolbar_left flex-row">
      <el-link type="primary" @click="addCase" :disabled="selections.length == 0" :underline="false">
        加入案例库
      </el-link>
      <el-link type="primary" @click="cancelSelection" :disabled="selections.length == 0" class="cancel_selection"
        :underline="false">
        取消所有选区
      </el-link>
    </div>
    <div class="toolbar_middle flex-row flex-center">
      <div class="play_icon" @click="togglePlay">
        <PauseIcon v-if="isPlaying" />
        <PlayIcon v-else />
      </div>
      <div class="play_times" v-if="currentTime && duration">
        {{ currentTime }} / {{ duration }}
      </div>
    </div>

    <div class="zoom-controls">
      <img :src="getOssUrl('zoom_in.png')" class="zoom-icon" @click="zoomOut" />
      <el-slider :min="1" :max="maxZoomValue" :step="stepValue" v-model="zoomValue" class="zoom-slider"
        @change="setZoomValue" />
      <img :src="getOssUrl('zoom_out.png')" class="zoom-icon" @click="zoomIn" />
    </div>
    <AddToLibrary ref="addToLibraryRef" />
  </div>
</template>

<script setup>
import PlayIcon from "@/icons/play.vue";
import PauseIcon from "@/icons/pause.vue";
import { getOssUrl, secondsToTime } from "@/js/utils";
import AddToLibrary from "./AddToLibrary.vue";
import { ref, onMounted, onUnmounted } from "vue";

const currentTime = ref("00:00:00");
const duration = ref("00:00:00");

const addToLibraryRef = ref(null);
const stepValue = 1;
const zoomValue = ref(1);
const isPlaying = ref(false);
const maxZoomValue = ref(20);
const selections = ref([]);

const addCase = () => {
  addToLibraryRef.value.open();
};

const togglePlay = () => {
  isPlaying.value = !isPlaying.value;
  g.emitter.emit("clipper_toggle_play", isPlaying.value);
};

const cancelSelection = () => {
  selections.value = [];
  g.emitter.emit("clipper_cancel_selection");
};

const zoomIn = () => {
  if (zoomValue.value >= maxZoomValue.value) return;
  zoomValue.value += stepValue;
  setZoomValue();
};

const zoomOut = () => {
  if (zoomValue.value <= 1) return;
  zoomValue.value -= stepValue;
  setZoomValue();
};

const handleWheel = (e) => {
  if (e.deltaY < 0) {
    zoomValue.value = Math.min(maxZoomValue.value, zoomValue.value + stepValue);
  } else {
    zoomValue.value = Math.max(1, zoomValue.value - stepValue);
  }
  setZoomValue();
};

const setZoomValue = () => {
  g.emitter.emit("update_zoom", zoomValue.value);
};

onMounted(() => {
  // 监听音频加载完成事件，更新总时长
  g.emitter.on("audio_ready", (durationInSeconds) => {
    duration.value = secondsToTime(durationInSeconds);
  });

  g.emitter.on("selection_change", (event) => {
    selections.value = event.selections;
  });
  // 监听当前时间更新事件
  g.emitter.on("update_current_time", (timeInSeconds) => {
    currentTime.value = secondsToTime(timeInSeconds);
  });

  // 添加播放状态变化的监听
  g.emitter.on("audio_play_status_changed", (status) => {
    isPlaying.value = status;
  });
});

onUnmounted(() => {
  g.emitter.off("audio_ready");
  g.emitter.off("update_current_time");
  g.emitter.off("audio_play_status_changed");
  g.emitter.off("selection_change");
});

defineExpose({
  zoomIn,
  isPlaying,
  zoomOut,
});
</script>

<style lang="scss" scoped>
.toolbar {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;

  .toolbar_left {
    padding: 0 20px;
    justify-content: center;
    align-items: center;

    .cancel_selection {
      margin-left: 16px;
    }
  }

  .toolbar_middle {
    svg {
      width: 28px;
      height: 28px;
    }

    .play_times {
      margin-left: 16px;
      margin-bottom: 5px;
    }
  }

  .zoom-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0 20px;
  }

  .zoom-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .zoom-icon:hover {
    opacity: 0.8;
  }

  .zoom-slider {
    width: 80px;
  }
}
</style>
