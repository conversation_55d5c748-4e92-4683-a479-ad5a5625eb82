<template>
  <div class="last-visit-summary prepare_wrap" v-if="visitInfo.subject" v-ai-tip="'bottom-right'">
    <PrepareHeader title="上次沟通回顾" @callback="onDetail" />

    <div class="visit-card">
      <div class="visit-title">
        <span class="camera-icon">📹</span>
        {{ visitInfo.subject }}
      </div>

      <div class="visit-time">
        {{ formatTime(visitInfo.startTime) }} - {{ formatEndTime(visitInfo.endTime) }} | 参会人：{{
          visitInfo.attendeeNames || '-' }}
      </div>
      <div class="detail-item">
        <span class="label">目标：</span>
        <div class="target-wrap">
          <span>{{ visitInfo.customerMeeting.salesGoal }}</span>
          <el-tag size="small" :type="getTagType(visitInfo.customerMeeting.salesAchievementStatus)"
            v-if="visitInfo.customerMeeting.salesAchievementStatus">{{
              visitInfo.customerMeeting.salesAchievementStatus }}</el-tag>
          <el-tag size="small" type="warning" v-else-if="!visitInfo.customerMeeting.reportGenerated">未生成报告</el-tag>
        </div>
      </div>
      <div class="detail-item" v-if="visitInfo.customerMeeting.salesAchievementAnalysis">
        <p class="label">达成分析：</p>
        <p>{{ visitInfo.customerMeeting.salesAchievementAnalysis }}</p>
      </div>
      <div class="detail-item" v-if="visitInfo.customerMeeting.achieveResult">
        <p class="label">成果：</p>
        <p>{{ visitInfo.customerMeeting.achieveResult }}</p>
      </div>
      <div class="detail-item" v-if="visitInfo.customerMeeting.nextPlan">
        <p class="label">下一步：</p>
        <p>{{ visitInfo.customerMeeting.nextPlan }}</p>
      </div>
      <div class="detail-item" v-if="visitInfo.customerMeeting.existRisk">
        <p class="label">风险：</p>
        <p>{{ visitInfo.customerMeeting.existRisk }}</p>
      </div>

      <div class="summary-section" v-if="visitInfo.todos.length > 0">
        <div class="section-title">待办事项</div>
        <div class="section-content">
          <ul>
            <li v-for="(item, index) in visitInfo.todos" :key="index">{{ item.todoContent }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const visitInfo = ref({
  subject: '',
  startTime: '',
  endTime: '',
  attendeeNames: '',
  summary: '',
  conferenceId: '',
  todos: [],
  customerMeeting: {}
})

const onDetail = () => {
  g.clientStore.viewPlanRecord(toRaw(visitInfo.value))
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

const formatEndTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

const init = (data) => {
  if (data.lastVisitReview) {
    visitInfo.value = data.lastVisitReview
  }
}
const getTagType = (status) => {
  if (status == '达成') {
    return 'success'
  } else if (status == '未达成') {
    return 'danger'
  }
  return 'warning'
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.last-visit-summary {

  .visit-card {
    .visit-title {
      font-size: 14px;
      color: #1D2129;
      margin-bottom: 8px;
      display: flex;
      align-items: center;

      .camera-icon {
        margin-right: 8px;
      }
    }

    .visit-time {
      font-size: 12px;
      color: #86909C;
      margin-bottom: 16px;
    }

    .detail-item {
      margin-bottom: 12px;
      line-height: 20px;
      // display: flex;
      font-size: 14px;

      p {
        margin: 0;
      }

      .label {
        color: #909399;
        flex-shrink: 0;
      }

      .target-wrap {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .summary-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        display: inline-block;
        padding: 2px 6px;
        background: #E6EBFD;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        color: #436BFF;
        line-height: 18px;
        margin-bottom: 12px;
      }

      .section-content {
        font-size: 14px;
        color: #262626;
        line-height: 1.6;

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            position: relative;
            padding-left: 12px;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            &:before {
              content: "•";
              position: absolute;
              left: 0;
              color: #262626;
            }
          }
        }
      }
    }
  }
}
</style>
