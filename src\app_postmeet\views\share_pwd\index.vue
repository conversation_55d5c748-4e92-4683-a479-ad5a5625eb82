<template>
    <div>
        <div class="share_wrap">
            <div v-if="loading">
                <i class="el-icon-loading"></i>
            </div>
            <div v-else-if="need_pwd">
                <PwdPage @callback="onPwdCallback" />
            </div>
            <div v-else-if="!has_access">
                <NoAccess ref="refNoAccess" />
            </div>
        </div>
    </div>
</template>

<script>
import PwdPage from "./pwd_page.vue"
import NoAccess from "./no_access.vue"
import { shareCheckPwd, getShareConfld } from '@/app_postmeet/tools/api';
import RecordPage from '../record/index.vue'
import { isPC } from '@/js/utils.js'
export default {
    name: 'sharePwdPage',
    components: { PwdPage, NoAccess, RecordPage },
    data() {
        return {
            shareId: '',
            loading: true,
            need_pwd: false,
            has_access: false,
            confId: '',
        }
    },
    mounted() {
        this.confId = this.$route.query.confId
        console.log('this.$route.params.shareId', this.confId)
        if (!this.confId) {
            getShareConfld(this.$route.params.shareId).then(resp => {
                this.confId = resp.data.conferenceId
                this.$nextTick(() => {
                    const url = `${g.config.publicPath}/#/postmeet/share/${this.$route.params.shareId}?confId=${this.confId}`;
                    console.log('url', url)
                    location.href = url
                    location.reload()
                })


            })
        } else {
            if (isPC()) {
                this.init()
            } else {
                this.goMobileRecord()
            }
        }


    },
    methods: {
        onPwdCallback() {
            this.need_pwd = false;
            this.has_access = true;
            const url = `${g.config.publicPath}/#/postmeet/record/${this.confId}`;
            console.log('url', url)
            location.href = url
            location.reload()
        },
        goMobileRecord() {
            const url = `${g.config.postmeet_h5_record}/#/postmeet/record/${this.confId}`;
            console.log('url', url)
            location.href = url
        },
        init() {
            this.shareId = this.$route.params.shareId
            const data = { password: '' }
            shareCheckPwd(this.shareId, data).then(resp => {
                this.loading = false;
                if (resp.code == 0) {
                    localStorage.setItem("share_token", resp.data)
                    this.onPwdCallback()
                } else if (resp.code == 1315) {
                    this.need_pwd = true;
                } else if (resp.code == 1317) {
                    this.has_access = false;
                    this.$nextTick(() => {
                        this.$refs.refNoAccess.show(`您暂时无权限查看此沟通纪要`, '')
                    })
                }
            }).catch(e => {
                console.log('shareCheckPwd error', e)
                // 1318 分享不存在
                // 1315 请输入密码
                // 1316 密码错误
                // 1317 没有权限查看
                // 1319 登录用户才可查看
                this.loading = false;
                if (e.code == 1315) {
                    this.need_pwd = true;
                } else if (e.code == 1319) {
                    localStorage.setItem("gourl_postmeet", window.location.href)
                    window.location.href = g.config.publicPath
                } else {
                    this.has_access = false;
                    this.$nextTick(() => {
                        this.$refs.refNoAccess.show(`访问出错：${e.message}`)
                    })
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.share_wrap {
    display: flex;
    width: 100vw;
    height: 100vh;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .main_hint {
        width: 154px;
        height: 22px;
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        line-height: 22px;
        margin-bottom: 16px
    }

    .input_box {
        display: flex;
        flex-direction: column;

        .error {
            font-size: 12px;
            color: #F5222D;
            line-height: 20px;
            margin-left: 12px;
        }

        .el-input {
            width: 328px;
            height: 40px;
            background: #FFFFFF;
            border-radius: 4px;
            outline: none;
            padding: 9px 12px;
        }

    }


    .el-button {
        width: 68px;
        height: 40px;
        margin-top: 32px;
    }
}
</style>