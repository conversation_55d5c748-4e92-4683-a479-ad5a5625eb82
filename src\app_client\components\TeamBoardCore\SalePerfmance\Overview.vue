<template>
  <div class="overview-sp flex-col" v-loading="loading" element-loading-text="数据加载中... 请稍等">
    <div class="overview-header-1 flex-row">
      <div class="flex-row title-line-wrap">
        <div class="title-line"></div>
        <div class="title">成员盘点</div>
      </div>
    </div>

    <div class="overview-body-1">
      <SaleScatterChart ref="saleScatterChartRef" v-if="saleScatterChartData.length > 0"
        @showOtherInfo="showOtherInfo" />
      <div v-else>
        <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)" />
      </div>
    </div>
    <SaleScatterDialog v-if="saleScatterOtherInfoShow" :data="saleScatterChartDataArr" @onClose="onClose" />
  </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils.js";
import { getUserInventoryCapability, listSubTeamsReportInThisTeam, getListUserReportInThisTeam } from "@/app_client/tools/api.js"
import SaleScatterChart from '@/app_client/components/TeamBoardCore/components/SaleScatterChart.vue';
import SaleScatterDialog from '@/app_client/components/TeamBoardCore/SaleScatterDialog.vue';
const props = defineProps({
  isReport: {
    type: Boolean,
    default: false
  }
});
const overviewDept = computed(() => {
  return g.clientBoardStore.overviewDept;
});
const periodType = computed(() => {
  return g.clientBoardStore.periodType;
});
const perfType = computed(() => {
  return g.clientBoardStore.perfType;
});
const saleScatterChartRef = ref(null);
const saleScatterChartData = ref([]);
const saleScatterChartDataObj = ref({});

const loading = ref(false);
const saleScatterChartDataArr = ref([]);
const saleScatterOtherInfoShow = ref(false);

const standardSetting = computed(() => {
  return g.clientBoardStore.standardSetting;
});

const init = () => {
  loading.value = true;
  const orgId = g.appStore.user.orgId;
  const deptId = g.clientBoardStore.overviewDept.value;
  if (!orgId || !deptId) return;
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const param = {
    "pageSize": 10,
    "pageNumber": 1,
    "orderBy": "",
    "asc": false,
    startTime, endTime,
    "dataType": periodType.value,
    "pageIndex": 0,
    "startIndex": 0
  }

  getUserInventoryCapability(orgId, deptId, param).then((res) => {
    if (res.code == 0) {
      saleScatterChartData.value = res.data.datas;
      nextTick(() => {
        loading.value = false;
        saleScatterChartData.value.length > 0 && saleScatterChartRef.value.init(saleScatterChartData.value, standardSetting.value);
      })
    }

  })
}

const showOtherInfo = (data) => {
  saleScatterChartDataArr.value = data;
  saleScatterOtherInfoShow.value = true;
}

const onClose = () => {
  saleScatterOtherInfoShow.value = false;
  saleScatterChartDataArr.value = [];
}


watch(() => [periodType.value, overviewDept.value], (newVal) => {
  init()
}, { immediate: true });


defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.overview-sp {
  .no_select_col {
    width: 100%;
    margin: 20px 0;
    font-size: 16px;
    color: #262626;
    height: 250px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .overview-header-1 {
    justify-content: space-between;
    margin-bottom: 24px;
  }

  .overview-body-1 {
    // display: flex;
    // gap: 24px;
    background: #FFFFFF;
    padding: 24px 18px;
    box-sizing: border-box;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
  }
}
</style>
