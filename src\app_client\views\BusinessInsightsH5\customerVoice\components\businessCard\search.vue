<template>
    <van-popup v-model:show="isSelect" round position="bottom" :close-on-click-overlay="false" teleport="body">
        <div class="action-sheet-button">
            <span class="action-sheet-button-p">选择客户</span>
            <span class="action-sheet-button-s" @click="onCancel">
                <van-icon name="cross" @click="onCancel" size="16" />
            </span>
        </div>
        <div class="action-sheet-main-box">
            <van-search v-model="searchValue" show-action placeholder="搜索" @search="onSearch" clearable
                @clear="onClear">
                <template #action>
                    <div @click="onSearch">搜索</div>
                </template>
            </van-search>
            <ul class="action-sheet-main-box-list">
                <van-checkbox-group v-model="checked" ref="checkboxGroup" icon-size="16px">
                    <van-checkbox v-for="item in companyList" :key="item.id" :name="item.id">{{
                        item.name
                    }}</van-checkbox>

                </van-checkbox-group>
            </ul>
            <div class="action-sheet-bottom">
                <van-checkbox v-model="isCheckAll" @change="checkAll" icon-size="16px">
                    全选
                </van-checkbox>

                <van-button type="primary" block @click="onConfirm">
                    确认
                </van-button>
            </div>
        </div>
    </van-popup>
</template>

<script setup>
import { debounce } from "@/js/utils.js";
const props = defineProps({
    data: {
        type: Array,
        required: true,
        default: () => []
    },
})
const emit = defineEmits(['close', 'update:modelValue'])
const isSelect = ref(false)
const searchValue = ref('')
const checked = ref([])
const checkboxGroup = ref(null);
const isCheckAll = ref(false);
const companyList = computed(() => g.clientBiStore.cvCustomerList || [])
const onSearch = async () => {
    await g.clientBiStore.getCvCustomerList(searchValue.value)
}

// 创建防抖版本的搜索函数
const debouncedSearch = debounce(onSearch, 500);

// 监听搜索值变化，触发防抖搜索
watch(searchValue, () => {
    debouncedSearch();
});

const checkAll = () => {
    checkboxGroup.value.toggleAll(true);
}

// 取消
const onCancel = () => isSelect.value = false

const onConfirm = () => {
    isSelect.value = false
    emit('update:modelValue', checked.value)
}

const onClear = () => {
    searchValue.value = '';
    onSearch()
}

const init = async () => {
    isSelect.value = true
    await g.clientBiStore.getCvCustomerList('')
}


watch(() => props.data, () => {
    companyList.value = props.data || []
}, { immediate: true })

defineExpose({
    init
})
</script>
<style lang="scss" scoped>
.action-sheet-main-box {
    height: 80vh;
    overflow: auto;
}

.action-sheet-button {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    line-height: 40px;

    .action-sheet-button-p {
        width: calc(100% - 40px);
        font-weight: 700;
        font-size: 16px;
        text-align: center;
    }

    .action-sheet-button-c {
        padding: 0 8px;
        color: #aaa;
    }

    .action-sheet-button-s {
        padding: 0 10px;
    }
}

.action-sheet-main-box-list {
    height: calc(80vh - 120px);
    padding: 12px;
    box-sizing: border-box;
    overflow: auto;

    .van-checkbox {
        padding: 12px 0;
        font-size: 12px;
    }
}


:deep(.van-cell__title) {
    padding-left: 12px;
}

.action-sheet-bottom {
    .van-checkbox {
        padding: 12px 0;
        font-size: 14px;
    }

    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 20px 16px;

    font-size: 16px;
    color: #262626;

    button {
        width: 64px;
    }
}
</style>
