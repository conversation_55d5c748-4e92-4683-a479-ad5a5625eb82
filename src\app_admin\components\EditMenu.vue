<template>
    <div class="left_post_wrap">
        <div class="left_header">
            <div class="lph_title">
                {{ cfg.name }}
            </div>

            <el-tooltip class="box-item" effect="dark" :content="`添加${cfg.name}`" placement="top"
                v-if="cfg.maxRows == 0 || cfg.maxRows > 0 && datas.length < cfg.maxRows">
                <div class="lph_icon" @click="onAdd">
                    +
                </div>
            </el-tooltip>
        </div>
        <ul class="left_list" v-sortable="{ disabled: !cfg.sortable }" @end="onOrderChange">
            <li v-for="item in datas" :class="currItem.id === item.id ? 'active' : ''" :key="item.id">
                <div class="text" @click="onClick(item)">
                    <el-tooltip class="box-item" effect="dark" :content="item[cfg.label]" placement="top-end"
                        :hide-after="1" :show-after="600">
                        {{ item[cfg.label] }}
                    </el-tooltip>
                </div>
                <div class="right_icon">
                    <el-dropdown @command="(command) => handleCommand(item, command)">
                        <span class="el-dropdown-link">
                            <moreIcon class="more_icon" />
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                                <el-dropdown-item command="delete"
                                    v-if="cfg.canDeleteAll || !cfg.canDeleteAll && datas.length > 1">删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </li>
        </ul>
    </div>
</template>

<script setup>
import MoreIcon from "@/icons/more.vue"
import { apiHintWrap } from "@/app_admin/tools/utils.js"

const emit = defineEmits(['callback']);
const _default_cfg = {
    name: '标签',
    // methodGet: query,
    // methodDelete: deleteXmTopic,
    label: "label",
    canDeleteAll: false,
    maxRows: 0,
    sortable: false,
}

const datas = ref([])
const sortData = ref([])
const currItem = ref({ id: '' })
const cfg = ref(_default_cfg)

const onClick = (item) => {
    currItem.value = item;
    emit('callback', 'click', item)
}

const onOrderChange = (event) => {
    let item = sortData.value.splice(event.oldIndex, 1)[0];
    sortData.value.splice(event.newIndex, 0, item);
    emit('callback', 'sort', toRaw(sortData.value))
}

const onAdd = () => {
    emit('callback', 'dialog_add', {})
}

const handleCommand = (item, command) => {
    if (command === 'edit') {
        emit('callback', 'dialog_edit', item)
    } else if (command == 'delete') {
        ElMessageBox.confirm(
            `您确定要删除 ${item[cfg.value.label]} 吗？删除后不可恢复`,
            '删除提示',
            {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            apiHintWrap(cfg.value.methodDelete(item.id), '删除' + cfg.value.name).then(({ status, resp }) => {
                if (status) {
                    query(true)
                }
            })
        }).catch((e) => {
            console.log('handleCommand err', e)
        })
    }
}

const update_data = (_data) => {
    datas.value = [..._data]
    sortData.value = [..._data]
}

const query = (force = false, choose_data = undefined) => {
    cfg.value.methodGet(force).then(data => {
        update_data(data)
        if (datas.value.length > 0) {
            if (choose_data) {
                const select = datas.value.filter(x => x[cfg.value.label] == choose_data)
                if (!!select && select.length > 0) {
                    onClick(toRaw(select[0]))
                } else {
                    onClick(toRaw(datas.value[0]))
                }
            } else {
                onClick(toRaw(datas.value[0]))
            }
        }
    })
}

const init = (_cfg) => {
    cfg.value = { ..._default_cfg, ..._cfg };
    const _def = { id: '' }
    _def[_cfg.label] = ''
    currItem.value = _def
    query(true)
}

defineExpose({
    init, update_data, onOrderChange,
    cfg, datas, MoreIcon, currItem,
    query, onClick, handleCommand, onAdd
})

</script>

<style lang="scss">
.left_post_wrap {
    height: 72px;

    .left_header {
        display: flex;
        flex-direction: row;
        padding: 24px;
        justify-content: space-between;

        .lph_title {
            height: 24px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #262626;
            line-height: 24px;
            text-align: left;
            font-style: normal;
        }

        .lph_icon {
            cursor: pointer;
            font-size: 20px;
        }
    }

    ul.left_list {
        li {
            padding: 8px 8px 8px 24px;
            height: 22px;
            line-height: 22px;
            font-size: 14px;
            cursor: pointer;
            width: 200px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .text {
                width: 170px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .right_icon {
                padding-top: 7px;

                .more_icon {
                    margin-top: -3px;
                    display: none;
                }
            }
        }

        li.active {
            background-color: #F0F6FF;
            color: #436BFF;
        }

        li:hover {
            background-color: #f5f5f5;

            .right_icon {
                .more_icon {
                    display: block;
                }
            }
        }
    }

}
</style>