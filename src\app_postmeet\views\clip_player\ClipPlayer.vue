<template>
  <div class="clip-player">
    <ClipHeader ref="refHeader" />
    <div v-if="errorMessage">
      <el-empty :description="errorMessage"></el-empty>
    </div>
    <div class="clip-content" v-else>
      <ClipContext ref="refClipContext" @retry="init" />
      <div class="clip-main">
        <RightHeader :hideIcons="['clip', 'ai_analyse', 'subtitle', 'text_polish', 'filter']"
          @callback="cbRightHeader" />
        <VideoPlayer ref="refVideo" @retry="init" @audioError="handleAudioError"></VideoPlayer>
        <SpeakTime ref="refSpeakTime" />
        <ClipSubtitles ref="refSubtitles" :sentences="sentences" />
        <AudioPlayer ref="refAudio" v-show="!isShowVideo" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { getClipInfo } from "@/app_postmeet/tools/api";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import ClipHeader from "./ClipHeader.vue";
import ClipContext from "./ClipContext.vue";
import ClipSubtitles from "./ClipSubtitles.vue";
import RightHeader from "@/app_postmeet/components/rightHeader/rightHeader.vue";
import VideoPlayer from "./VideoPlayer.vue";
import AudioPlayer from "@/app_postmeet/components/audio_player/AudioPlayer.vue";
import SpeakTime from "@/app_postmeet/components/speakTime/speakTime.vue";

const route = useRoute();
const clipInfo = ref({});
const clipId = ref("");
const isShowVideo = ref(localStorage.getItem("isPlayVideo") == "true");
const contextData = ref({});
const refClipContext = ref(null);
const sentences = ref([]);
const refVideo = ref(null);
const refHeader = ref(null);
const refAudio = ref(null);
const refSubtitles = ref(null);
const refSpeakTime = ref(null);
const errorMessage = ref("");

const init = () => {
  clipId.value = route.params.clipId;
  getClipInfo(clipId.value)
    .then((res) => {
      if (res.code == 0) {
        clipInfo.value = res.data;
        sentences.value = clipInfo.value.sentences || [];
        refClipContext.value.init(res.data);
        refVideo.value.init(res.data);
        refAudio.value.init(res.data.duration);
        refHeader.value.init(res.data);
        refSpeakTime.value.init("clip", res.data);
      } else {
        ElMessage.error(res.msg);
      }
    })
    .catch((e) => {
      errorMessage.value = e?.response?.data?.message || "获取数据失败";
      ElMessage.error(errorMessage.value);
    });
};

const cbRightHeader = (action, data) => {
  if (action == "playing_video") {
    isShowVideo.value = data;
  }
};

const handleAudioError = () => {
  refSubtitles.value.setAudioError();
}

onMounted(() => {
  init();
});

defineExpose({
  refVideo,
  clipInfo,
  refClipContext,
  contextData,
  sentences,
  refSpeakTime,
  RightHeader,
});
</script>

<style lang="scss">
@use "@/app_postmeet/config/config.scss";

.clip-player {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .clip-content {
    flex: 1;
    display: flex;

    .clip-main {
      flex: 1;
      padding: 0 20px;
      background: #f7f8fc;
    }
  }
}
</style>
