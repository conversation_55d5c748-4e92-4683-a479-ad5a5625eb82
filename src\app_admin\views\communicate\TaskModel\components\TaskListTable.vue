<template>
  <div class="task-list-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>

      <template #col_taskName="{ row }">
        <div class="task-name">{{ row.taskName }}</div>
      </template>

      <template #col_taskDescription="{ row }">
        <div class="task-description">
          <div v-for="(line, index) in (row.taskDescription || '').split('\n')" :key="index" class="description-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <TaskFormDrawer ref="taskFormDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getTaskListData, deleteTaskModel } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import TaskFormDrawer from './TaskFormDrawer.vue';

const refTable = ref();
const taskFormDrawerRef = ref();
const tableConfig = reactive({
  tableid: 'task_list',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "任务名称",
  delete_hint_column: 'taskName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["taskName", "taskDescription", "creator", "createTime"],
  template: ["taskName", "taskDescription"],
  urlGet: getTaskListData,
  urlDelete: deleteTaskModel
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  taskFormDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  taskFormDrawerRef.value.openDrawer(row);
};

const handleDelete = (row) => {
  confirmDelete(row.taskName, (status) => {
    if (status) {
      deleteTaskModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.task-list-table {
  padding: 24px 0;

  .task-name {
    font-weight: 500;
    color: #333;
  }

  .task-description {
    max-width: 400px;

    .description-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>