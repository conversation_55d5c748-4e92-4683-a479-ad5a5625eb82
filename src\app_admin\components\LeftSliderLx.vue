<template>
    <el-menu class="admin_left_slider" :default-active="currActive" @select="handleSelect" :collapse="isCollapse"
        :collapse-transition="false" mode="vertical" active-text-color="#436BFF" bg-color="#fff" text-color="#595959">
        <template v-for="menu in menuList" :key="menu.id">
            <el-menu-item v-if="!menu.children || menu.children.length == 0" :index="menu.index">
                <div class="menu_icon">
                    <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
                </div>
                <span>{{ menu.name }}</span>
            </el-menu-item>

            <el-sub-menu v-else :index="menu.id">
                <template #title>
                    <div class="menu_icon">
                        <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
                    </div>
                    <span>{{ menu.name }}</span>
                </template>
                <el-menu-item v-for="child in menu.children" :key="child.id" :index="child.index">
                    {{ child.name }}
                </el-menu-item>
            </el-sub-menu>
        </template>
    </el-menu>
</template>

<script setup>
const menuList = ref([])

// https://element-plus.org/zh-CN/component/icon.html#icon-collection
import { Grid, Edit, Position } from '@element-plus/icons-vue'
import { onMounted } from "vue";
import InlineSvg from 'vue-inline-svg';
const currActive = ref(location.hash.replace("#", ""));
const props = defineProps({ isCollapse: { type: Boolean, default: false } })
const route = useRoute()

watch(() => route.path, (newPath) => {
    currActive.value = newPath
}, { immediate: true })

const handleSelect = (path, keyPath) => {
    g.router.push({ path })
}

onMounted(() => {
    g.emitter.on('update_admin_menu', (list) => {
        const isSaleShow = g.appStore.getFuncStatus('sales_product_in_app')
        if (!isSaleShow) {
            list = list.filter(x => x.code != 'sales_product_recommend')
        }
        const hasAiApp = g.appStore.getFuncStatus('sales_ai_apps_icon');
        if (!hasAiApp) {
            list = list.filter(x => (!x.children || x.children.length == 0 || x.children && x.children.length > 0 && x.children.some(y => y.code != 'usage_status')));
        }
        menuList.value = list;
    })
})

onUnmounted(() => {
    g.emitter.off("update_admin_menu");
});

defineExpose({
    isCollapse: props.isCollapse, Grid, Edit, Position, currActive, handleSelect, menuList, InlineSvg
})
</script>

<style lang='scss'>
.admin_left_slider {
    border: none;
    height: calc(100vh - 105px);
    overflow-y: auto;

    .el-sub-menu__title {
        display: flex;
        align-items: center;
    }

    .menu_icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &.el-menu--collapse {
        .menu_icon {
            margin-right: 0;
        }
    }
}
</style>