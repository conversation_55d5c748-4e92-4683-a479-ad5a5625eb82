<template>
  <div class="sales-ability-radar" v-ai-tip="'bottom-right'">
    <div class="box-shadow">
      <div class="radar-header">
        <div class="title">销售能力画像</div>
        <div class="target-score">能力评估得分
          <el-tooltip class="box-item" effect="dark" content="各能力评估维度得分之和 / 能力评估维度数" placement="top">
            <img :src="getOssUrl('question.png', 3)" alt="faq" class="faq_icon_min" />
          </el-tooltip>
          <span :style="{ color: pieData.averageAbilityValue >= abilityStandardScore ? '#52C41A' : '#F5222D' }">{{
            pieData.averageAbilityValue }}分</span>
          <span class="target-score-1">达标值: {{ abilityStandardScore }}分</span>
        </div>
      </div>
      <RadarChart v-if="tableData.length < 20" ref="radarChartRef" class="radar-chart" :config="{ isShowLegend: true }"
        :data="barData" />
      <div v-else>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="能力项" />
          <el-table-column prop="value" label="得分" width="100" />
        </el-table>
      </div>
      <PieChartWithTitle title="能力评估详情" sub-title="点击饼图图例可跳转查看拜访详情" :chart-data="chartData" />
    </div>
  </div>
</template>

<script setup>
import PieChartWithTitle from '@/app_client/components/TeamBoardCore/components/PieChartWithTitle.vue'
import RadarChart from './RadarChart.vue'
import { getOssUrl } from "@/js/utils.js";
const radarChartRef = ref(null)
const userData = ref({})
const initRadarList = ref([])
const props = defineProps({
  pieData: {
    type: Object,
    default: () => ({})
  },
  barData: {
    type: Array,
    default: () => []
  }
})
const tableData = computed(() => {
  return props.barData[0]?.radarIndicator || []
})

// 计算平均值
const averageValue = computed(() => {
  const radarData = props.barData[0]?.value || []
  if (radarData.length === 0) return 0

  const sum = radarData.reduce((total, item) => {
    return total + (+item || 0)
  }, 0)
  console.log('sum', sum, radarData)
  return (sum / radarData.length).toFixed(2) || 0
})

const chartData = computed(() => {
  return [
    {
      value: props.pieData.salesAbilityPassCount,
      name: '达成',
      type: 'achievement',
      color: '#67C23A'
    },
    {
      value: props.pieData.salesAbilityNotPassCount,
      name: '未达成',
      type: 'unAchievement',
      color: '#F56C6C'
    },
    {
      value: unDetermineNum.value,
      name: '结果未评估',
      type: 'unDetermine',
      color: '#909399'
    }
  ]
})
const abilityStandardScore = computed(() => {
  return g.clientBoardStore.standardSetting.abilityScore4Pass;
})


const unDetermineNum = computed(() => {
  return props.pieData.totalVisitCount - props.pieData.salesAbilityPassCount - props.pieData.salesAbilityNotPassCount
})

</script>

<style lang="scss" scoped>
.sales-ability-radar {
  background: #fff;
  border-radius: 8px;
  padding: 1px;
  border: none;
  border-radius: 6px;
  border: 1px solid #E4E7ED;
  overflow: hidden;

  .box-shadow {
    padding: 24px;
    box-sizing: border-box;
    border-radius: 6px;
  }


  .radar-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }

    .target-score,
    .target-score-1 {
      font-size: 12px;
      color: #8C8C8C;
      margin-left: 8px;
      display: flex;
      align-items: center;
      line-height: 22px;
      font-weight: 400;

      img {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }

    .target-score-1 {
      margin-left: 0;
      position: relative;
      margin-left: 16px;

      &::before {
        content: '';
        display: block;
        width: 1px;
        height: 12px;
        background-color: #86909C;
        position: absolute;
        left: -8px;
        top: 4px;
      }
    }
  }

  .radar-chart {
    height: 300px;
    width: 100%;
  }


  .evaluation-list {
    .evaluation-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #E5E6EB;
      cursor: pointer;

      &:hover {
        background-color: #F2F3F5;
      }

      .item-left {
        display: flex;
        flex-direction: column;

        .date {
          color: #86909C;
          font-size: 14px;
          width: 160px;
        }

        .company-score {
          display: flex;

          .company {
            color: #1D2129;
            font-size: 14px;
            margin: 0 24px 0 0;
          }

          .score {
            font-size: 14px;
            font-weight: 500;

            &.passed {
              color: #4080FF;
            }

            &.failed {
              color: rgb(100, 68, 37);
            }
          }
        }
      }

      .arrow-right {
        color: #C9CDD4;
        font-size: 16px;
      }
    }
  }
}
</style>