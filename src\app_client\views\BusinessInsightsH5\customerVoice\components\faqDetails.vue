<template>
    <van-popup v-model:show="isShow" round position="bottom"
        :style="{ height: 'calc(100% - 44px)', display: 'flex', flexDirection: 'column' }" closeable
        class="faq-detail-popup" @closed="onClose" teleport="body">
        <div class="title">
            {{ item.question }}
        </div>
        <div class="content">
            <!-- <aiAnalysis :data="aiAnswer" title="AI 推荐回复"></aiAnalysis> -->
            <div class="popup-content-list" v-ai-tip="'center'">
                <QAList :qa-list="faqList" :loading="loading" :finished="finished" @load-more="handleLoadMore" />
            </div>
            <div class="popup-content-bottom" v-if="total > 0">
                共{{ total }}条
            </div>
        </div>
    </van-popup>
</template>

<script setup>
import {
    getCustomerFaqApi
} from "@/app_client/tools/api.js";
import QAList from "../../components/QAListExample.vue"
import aiAnalysis from "../../components/aiAnalysis.vue";
const isShow = ref(false);
const faqList = ref([])
const total = ref(0)
const item = ref({ question: '' })
const aiAnswer = ref('')
const loadingAiAnswer = ref(false)
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = ref(10)
const emit = defineEmits(['close'])
const onClose = () => {
    emit('close', false)
};

// const queryAiAnswer = async () => {
//     loadingAiAnswer.value = true
//     aiAnswer.value = await g.clientBiStore.getFaqRecomendAnswer(item.value.id)
//     loadingAiAnswer.value = false
// }

const queryData = async (pageNum) => {
    try {
        loading.value = true
        const param = {}
        if (item.value.dimensionId) {
            param.dimensionIds = [item.value.dimensionId]
            param.pageNumber = pageNum
            param.pageSize = pageSize.value
        }
        const response = await getCustomerFaqApi(g.clientBiStore.gFilterDays, param) || {}
        const list = response.data.datas || []
        total.value = response.data.totalNum || 0
        if (pageNum === 1) {
            faqList.value = list
        } else {
            faqList.value.push(...list)
        }
        // 判断是否还有更多数据
        if (list.length < pageSize.value) {
            finished.value = true
        }
        page.value = pageNum

    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        loading.value = false
    }
}
// 处理加载更多
const handleLoadMore = () => {
    if (!loading.value && !finished.value) {
        queryData(page.value + 1)
    }
}

const show = (row) => {
    item.value = row
    isShow.value = true;
    queryData(1)
    // queryAiAnswer()
}

defineExpose({
    show
})

</script>

<style lang="scss" scoped>
.faq-detail-popup {
    .title {
        font-weight: 700;
        font-size: 16px;
        color: #262626;
        line-height: 24px;
        padding: 12px 36px;
        max-width: 90%;
        text-align: center;
        min-height: 44px;
    }

    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;

        .popup-content-list {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 54px;

            :deep(.qa-item) {
                background: #F9FAFC;
                border-radius: 8px;
            }
        }

        .popup-content-bottom {
            width: 100%;
            box-sizing: border-box;
            font-size: 12px;
            color: #8C8C8C;
            line-height: 18px;
            height: 44px;
            background: #FFFFFF;
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.06);
            position: fixed;
            bottom: 0;
            padding: 7px 20px;
            z-index: 9;
        }
    }
}
</style>
