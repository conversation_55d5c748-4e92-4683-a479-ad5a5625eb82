<template>
  <div class="KeyPoints">
    <div class="filter-tabs" v-if="QuestionsAnsweringSummary.length > 0">
      <div class="tabs-left">
        <span class="tab-item" :class="{ active: currentFilter === 'all' }" @click="setFilter('all')">
          全部问答({{ QuestionsAnsweringSummary.length }})
        </span>
        <span class="tab-item" :class="{ active: currentFilter === 'customer' }" @click="setFilter('customer')">
          客户提问({{ getFilteredCount('customer') }})
        </span>
        <span class="tab-item" :class="{ active: currentFilter === 'internal' }" @click="setFilter('internal')">
          内部伙伴提问({{ getFilteredCount('internal') }})
        </span>
      </div>
      <div class="tabs-right">
        <template v-if="!isEdit">
          <el-icon @click="onCopy" class="copy-icon">
            <CopyIcon />
          </el-icon>
          <el-icon @click="onEdit" class="edit-icon" v-if="isHost && enableEdit">
            <EditIcon />
          </el-icon>
        </template>
        <div class="edit-actions" v-else>
          <span class="action-btn" @click="onCancelEdit">取消</span>
          <span class="action-btn confirm" @click="onConfirmEdit">完成编辑</span>
        </div>
      </div>
    </div>
    <ul v-show="filteredList.length > 0" class="key-points-list">
      <li v-for="(item, index) in filteredList" :key="index" :class="`${currId == index ? 'act_qa' : ''}`">
        <div class="rbox">
          <div class="rtop">
            <span class="qa-tag">问</span><span class="role-tag">{{ getTypeName(item.questionUser?.type) }}</span> {{
              item.questionUser?.name }}
            <div-edit v-model="item.Question" @callback="(e) => onQuestionEdit(item, e)" :edit="isEdit" :auto-focus="isEdit && index === 0">
            </div-edit>
          </div>
          <div class="rbottom">
            <span class="qa-tag">答</span><span class="role-tag">{{ getTypeName(item.answerUser?.type) }}</span> {{
              item.answerUser?.name }}
            <div-edit v-model="item.Answer" @callback="(e) => onAnswerEdit(item, e)" :edit="isEdit">
            </div-edit>
          </div>
        </div>
        <div class="back_btn" v-if="recordViewPermission && isShowBtn(item)" @click="onLink(index, item)">
          {{ currId === index ? "取消回顾" : "回顾" }}
        </div>
      </li>
    </ul>
    <el-empty v-show="filteredList.length == 0">
      <template #text class="description">暂无数据</template>
    </el-empty>
  </div>
</template>
<script>
import CopyIcon from "@/icons/copy.vue"
import EditIcon from "@/icons/edit.vue"
import DivEdit from "@/app_postmeet/components/div_edit.vue"


export default {
  name: "KeyPoints",
  components: {
    CopyIcon,
    EditIcon,
    DivEdit
  },
  data() {
    return {
      QuestionsAnsweringSummary: [],
      recordViewPermission: false,
      currId: -1,
      UidInfo: [],
      currentFilter: 'all',
      isEdit: false,
      isHost: false,
      enableEdit: false
    };
  },
  computed: {
    filteredList() {
      if (this.currentFilter === 'all') {
        return this.QuestionsAnsweringSummary;
      }
      return this.QuestionsAnsweringSummary.filter(item =>
        item.questionUser?.type === this.currentFilter
      );
    }
  },
  mounted() {
    this.recordViewPermission = g.postmeetStore.getRecordViewPermission();
    this.isHost = !g.postmeetStore.isReadonly();
    this.enableEdit = g.postmeetStore.data.enable_edit;
  },
  methods: {
    getTypeName(type) {
      return type === 'internal' ? '伙伴' : '客户';
    },
    init() {
      this.UidInfo = g.postmeetStore.getRoleList();
      let list =
        g.postmeetStore.data.QuestionsAnsweringSummary || [];
      list = list.map(x => {
        return {
          ...x,
          answerUser: this.UidInfo.find(y => y.ui == x.UsersOfAnswer[0]),
          questionUser: this.UidInfo.find(y => y.ui == x.UsersOfQuestion[0]),
        }
      })
      this.QuestionsAnsweringSummary = list;
      this.recordViewPermission = g.postmeetStore.getRecordViewPermission();

    },
    isShowBtn(item) {
      const qids = item.SentenceIdsOfQuestion || [];
      const aids = item.SentenceIdsOfAnswer || [];
      return aids.length + qids.length > 0;
    },
    onLink(key, item) {
      let SentenceIdsOfQuestion = [];
      let SentenceIdsOfAnswer = [];
      if (this.currId === key) {
        // 取消
        this.currId = -1;
      } else {
        g.emitter.emit("videoStatus", "pause");
        this.currId = key;
        SentenceIdsOfQuestion = item.SentenceIdsOfQuestion || [];
        SentenceIdsOfAnswer = item.SentenceIdsOfAnswer || [];
      }
      g.emitter.emit("HighLightSentenceIdsOfAnswer", [
        SentenceIdsOfQuestion,
        SentenceIdsOfAnswer,
      ]);
    },
    setFilter(filter) {
      this.currentFilter = filter;
      this.currId = -1;
    },
    getFilteredCount(type) {
      return this.QuestionsAnsweringSummary.filter(item =>
        item.questionUser?.type === type
      ).length;
    },
    onCopy() {
      // 收集所有问答内容
      const content = this.filteredList.map(item => {
        const questionUser = `${this.getTypeName(item.questionUser?.type)} - ${item.questionUser?.name}`;
        const answerUser = `${this.getTypeName(item.answerUser?.type)} - ${item.answerUser?.name}`;
        return `问：${questionUser}: ${item.Question}\n答：${answerUser}: ${item.Answer}\n`;
      }).join('\n');

      // 使用全局方法复制到剪贴板
      g.appStore.doCopy(content, '已复制');
    },
    onEdit() {
      this.isEdit = true;
    },
    onCancelEdit() {
      this.isEdit = false;
      this.init(); // 重新加载数据，取消编辑的更改
    },
    onConfirmEdit() {
      this.isEdit = false;
      // "QuestionsAnsweringSummary":[
      // 	{
      // 		"Answer":"徐凤年是一位美妇和富家小姐眼中的俊美非凡、不食人间烟火的公子哥，腰间配刀，美得令人心惊胆战。",
      // 		"Question":"世子徐凤年的形象如何？",
      // 		"SentenceIdsOfAnswer":[2,3,4,5,6,7,8,9,10,11,12],
      // 		"SentenceIdsOfQuestion":[2,3,4,5],
      // 		"UsersOfAnswer":[
      // 			"user1"
      // 		],
      // 		"UsersOfQuestion":[
      // 			"user1"
      // 		]
      // 	},
      g.postmeetStore.data.QuestionsAnsweringSummary = this.filteredList
      const data = this.filteredList.map(item => {
        return {
          Answer: item.Answer,
          Question: item.Question,
          SentenceIdsOfAnswer: item.SentenceIdsOfAnswer,
          SentenceIdsOfQuestion: item.SentenceIdsOfQuestion,
          UsersOfAnswer: item.UsersOfAnswer,
          UsersOfQuestion: item.UsersOfQuestion,
        }
      })
      g.emitter.emit('setMdTypeEditorValue', { type: 'QA_REVIEW', md: data });
    },
    onQuestionEdit(item, newValue) {
      item.Question = newValue;
    },
    onAnswerEdit(item, newValue) {
      item.Answer = newValue;
    }
  },
};
</script>

<style lang="scss">
@use './style.scss';

.KeyPoints {
  .filter-tabs {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tabs-left {
      flex: 1;
      display: flex;
      gap: 8px;

      .tab-item {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 4px;
        cursor: pointer;
        color: #595959;
        font-size: 13px;
        transition: all 0.2s;

        &:hover {
          color: #436BFF;
        }

        &.active {
          color: #436BFF;
        }
      }
    }

    .tabs-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-icon {
        cursor: pointer;
        font-size: 14px;
        color: #595959;
      }

      .edit-actions {
        display: flex;
        gap: 12px;

        .action-btn {
          cursor: pointer;
          font-size: 14px;
          color: #436BFF;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }
}
</style>
