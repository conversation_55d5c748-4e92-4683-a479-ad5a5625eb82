<template>
  <div class="ability-model-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>

      <template #col_abilityName="{ row }">
        <div class="ability-name">{{ row.abilityName }}</div>
      </template>

      <template #col_behaviorStandard="{ row }">
        <div class="behavior-standard">
          <div v-for="(line, index) in (row.behaviorStandard || '').split('\n')" :key="index" class="standard-line">
            {{ line }}
          </div>
        </div>
      </template>

      <template #col_creator="{ row }">
        <div>{{ row.creator }}</div>
      </template>

      <template #col_createTime="{ row }">
        <div>{{ row.createTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <AbilityFormDrawer ref="abilityFormDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getAbilityListData, deleteAbilityModel, addAbilityModel, updateAbilityModel } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import AbilityFormDrawer from './AbilityFormDrawer.vue';
import { ElMessage } from 'element-plus';

const refTable = ref();
const abilityFormDrawerRef = ref();

const tableConfig = reactive({
  tableid: 'ability_list',
  param: {
    searchKey: "",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  // show_search: false,
  // show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "能力名称",
  delete_hint_column: 'abilityName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["abilityName", "behaviorStandard", "creator", "createTime"],
  template: ["abilityName", "behaviorStandard"],
  urlGet: getAbilityListData,
  urlDelete: deleteAbilityModel
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  abilityFormDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  // 将数据转换为表单所需格式
  const editData = {
    id: row.id,
    name: row.abilityName,
    behaviors: row.behaviorStandard ? row.behaviorStandard.split('\n') : ['']
  };
  abilityFormDrawerRef.value.openDrawer(editData);
};

const handleDelete = (row) => {
  confirmDelete(row.abilityName, (status) => {
    if (status) {
      deleteAbilityModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

const handleFormSuccess = (result) => {
  const { mode, data } = result;

  if (mode === 'add') {
    // 处理添加逻辑
    const params = {
      abilityName: data.name,
      behaviorStandard: data.behaviors.join('\n')
    };

    addAbilityModel(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('添加成功');
        refTable.value.search();
      } else {
        ElMessage.error(`添加失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error('添加失败');
      console.error(err);
    });
  } else if (mode === 'edit') {
    // 处理编辑逻辑
    const params = {
      id: data.id,
      abilityName: data.name,
      behaviorStandard: data.behaviors.join('\n')
    };

    updateAbilityModel(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('编辑成功');
        refTable.value.search();
      } else {
        ElMessage.error(`编辑失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error('编辑失败');
      console.error(err);
    });
  }
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.ability-model-table {
  padding: 24px 0;

  .ability-name {
    font-weight: 500;
    color: #333;
  }

  .behavior-standard {
    max-width: 400px;

    .standard-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>