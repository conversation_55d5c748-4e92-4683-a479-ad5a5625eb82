<template>
    <div class="competitive-radar-warp-h5">
        <div style="position: relative;z-index: 1;">
            <Overview ref="refOverview" />
            <Analysis ref="refAnalysis"></Analysis>
            <Evaluation ref="refEvaluation"></Evaluation>
        </div>
    </div>
</template>

<script setup>
import Overview from './components/overview.vue';
import Analysis from "./components/analysis.vue";
import Evaluation from "./components/evaluation.vue";

// 为子组件添加 ref 引用
const refOverview = ref(null);
const refAnalysis = ref(null);
const refEvaluation = ref(null);

// 点击跳转到对应标题位置
const onClickToTable = (key) => {
    if (key === 'refOverview') {
        refOverview.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    } else if (key === 'refAnalysis') {

        // 竞品雷达页面中，客户反馈对应分析部分
        refAnalysis.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    } else if (key === 'refEvaluation') {
        // 竞品雷达页面中，常见问题对应评估部分
        refEvaluation.value.$el.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
};

const init = async () => {
    await g.clientBiStore.getEvaluationDistribution()
}

watch(() => g.clientBiStore.periodType, () => {
    init()
}, { immediate: true })

// 初始化获取总览数据
onMounted(() => {
    init()
})

// 暴露方法给父组件
defineExpose({
    onClickToTable
});

</script>
<style lang="scss" scoped>
.competitive-radar-warp-h5 {
    padding: 0 12px;
    background: #F5F5F5;
    min-height: 100%;
    position: relative;

    &::after {
        content: '';
        display: block;
        width: 100%;
        height: 200px;
        background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
        position: absolute;
        top: 0;
        z-index: 0;
        left: 0;
    }

    :deep(.title-text) {
        font-weight: 600;
        font-size: 18px;
        color: #262626;
        line-height: 26px;
        padding-left: 8px;
        position: relative;

        &::before {
            content: '';
            display: inline-block;
            position: absolute;
            width: 5px;
            height: 20px;
            background: #436BFF;
            border-radius: 3px;
            left: -11px;
            top: 2px;
        }
    }
}
</style>