<template>
    <el-form-item label="最小值">
        <el-input-number v-model="localMinValue" @change="validateCharLength"></el-input-number>
    </el-form-item>
    <el-form-item label="最大值">
        <el-input-number v-model="localMaxValue" @change="validateCharLength"></el-input-number>
    </el-form-item>
    <div class="num_hint">注：最大值 -1 表示不限制</div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
        default: () => ({
            minValue: 0,
            maxValue: -1,
        }),
    }
})

const emit = defineEmits(['update:modelValue'])

const localMinValue = ref(props.modelValue.minValue)
const localMaxValue = ref(props.modelValue.maxValue)

watch(() => props.modelValue, (newVal) => {
    localMinValue.value = newVal.minValue
    localMaxValue.value = newVal.maxValue
}, { deep: true })

const validateCharLength = () => {
    if (localMinValue.value > localMaxValue.value && localMaxValue.value !== -1) {
        ElMessage.warning('最小值不能大于最大值')
        localMinValue.value = localMaxValue.value
    }

    emit('update:modelValue', {
        ...props.modelValue,
        minValue: localMinValue.value,
        maxValue: localMaxValue.value,
    })
}
</script>

<style lang="scss" scoped>
.num_hint {
    color: #606266;
    font-size: 14px;
}
</style>