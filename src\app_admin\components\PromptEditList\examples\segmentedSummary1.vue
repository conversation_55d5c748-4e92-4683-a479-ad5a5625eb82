<template>
    <div :class="`group_8 flex-col justify-end ${props.choosed == 1 ? 'active' : ''}`" @click="onClick(1)">
        <div class="text-wrapper_11 flex-row">
            <span class="text_21">分段摘要</span>
        </div>
        <dashline />
        <div class="marginl7">
            <page :id="1" />
            <div class="image-wrapper_1 flex-row" v-if="props.choosed">
                <img class="thumbnail_9" referrerpolicy="no-referrer" :src="r_png" />
            </div>
        </div>

    </div>
</template>

<script setup>
import page from '../pages/page.vue';
import dashline from './dashline.vue';
import { r_png } from './image.js';
const props = defineProps(['choosed', 'enableChoose'])
const emit = defineEmits(['update:choosed'])

const onClick = (value) => {
    if (props.enableChoose) {
        emit('update:choosed', value)
    }
}

defineExpose({ onClick, page, dashline })
</script>
