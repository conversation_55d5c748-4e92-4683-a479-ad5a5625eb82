<template>
    <div class="contact_out_picker_wrap">
        <el-select v-model="selectedContactNames" placeholder="添加客户参会人" style="width: 100%" multiple remote filterable
            @change="handleSelectChange" @focus="handleFocus" @blur="handleBlur" popper-class="cp_outer_selector"
            class="cp_outer_select" :reserve-keyword="false" :automatic-dropdown="true">
            <el-option v-for="item in contacts" :key="item.id" :label="item.name" :value="item.name">
                <div class="rdn_item flex-column">
                    <span class="name-text">{{ item.name }}</span>
                    <div class="field-values">
                        <template v-for="(field, index) in item?.formData?.fieldValues.filter(x => !!x.fieldValue)"
                            :key="field.id">
                            <span class="field-item">
                                {{ field.fieldName }} : {{ field.fieldValue || '-' }}
                            </span>
                            <span v-if="index !== item?.formData?.fieldValues.filter(x => !!x.fieldValue)?.length - 1"
                                class="separator">|</span>
                        </template>
                    </div>
                </div>
            </el-option>
            <template #footer>
                <div class="cp_footer flex-row" @click="onAddPerson">
                    <div class="fn_icon flex-center">
                        <el-icon :size="16">
                            <Plus />
                        </el-icon>
                    </div>
                    <div class="fn_txt">
                        添加客户参会人
                    </div>
                </div>
            </template>
        </el-select>
        <div class="add-person-div" v-show="isFocus && contacts.length == 0">
            <div class="cp_footer flex-row" @click="onAddPerson">
                <div class="fn_icon flex-center">
                    <el-icon :size="16">
                        <Plus />
                    </el-icon>
                </div>
                <div class="fn_txt">
                    添加客户参会人
                </div>
            </div>
        </div>
    </div>
    <DiaAddOuterPerson ref="refDiaAddOuterPerson" @callback="cbAddPerson" />
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import DiaAddOuterPerson from './DiaAddOuterPerson.vue';
import { getCompanyContact } from '@/js/api.js'


let customerId = ''
let lastCompanyName = ''
const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue'])
const isFocus = ref(false)
const selectedContacts = ref([])
const selectedContactNames = ref([])
const refDiaAddOuterPerson = ref()
const contacts = ref([])
const loading = ref(false)
const searchQuery = ref('')
let hideTimer = null;

const onAddPerson = () => {
    console.log('onAddPerson')
    const data = { name: lastCompanyName, id: customerId }
    if (g.config.isElectron) {
        g.electronStore.openWin('add_outer_contact', data)
    } else {
        refDiaAddOuterPerson.value.show(data)
    }
}

const queryCompanyContactList = (query) => {
    if (!query || lastCompanyName === query) return;
    loading.value = true
    lastCompanyName = query;
    searchQuery.value = query
    const param = {
        customerName: query,
        pageNum: 1,
        pageSize: 10
    }
    getCompanyContact(param).then(res => {
        loading.value = false
        if (res.code === 0) {
            customerId = res.data.customerId;
            contacts.value = res.data.customerContacts || []
        }
    })
}

const handleFocus = () => {
    if (hideTimer) {
        clearTimeout(hideTimer);
        hideTimer = null;
    }
    isFocus.value = true
    queryCompanyContactList('')
}

const handleBlur = () => {
    hideTimer = setTimeout(() => {
        isFocus.value = false
    }, 500);
}

const handleSelectChange = (names) => {
    const newContacts = contacts.value.filter(item => names.includes(item.name))
    let temp = [...selectedContacts.value, ...newContacts]
    selectedContactNames.value = names
    selectedContacts.value = temp.filter(x => names.includes(x.name)).filter((item, index, self) => self.findIndex(t => t.name === item.name) === index)
    selectedContacts.value.forEach(item => {
        item.attendeeType = 'CUSTOMER_ATTENDEE';
        item.contactId = item.id;
    })
    emit('update:modelValue', toRaw(selectedContacts.value))
}

const cbAddPerson = (type, data) => {
    if (type === 'add_person') {
        selectedContactNames.value.push(data.name)
        selectedContacts.value.push(data)
        const lastCompanyName2 = lastCompanyName;
        lastCompanyName = ''
        queryCompanyContactList(lastCompanyName2)
        emit('update:modelValue', toRaw(selectedContacts.value))
    }
}

onMounted(() => {
    queryCompanyContactList('')
})

watch(() => props.modelValue, (newVal) => {
    selectedContacts.value = newVal
    selectedContactNames.value = newVal.map(item => item.name)
}, { immediate: true, deep: true })


defineExpose({
    contacts,
    DiaAddOuterPerson,
    refDiaAddOuterPerson,
    selectedContacts,
    Plus,
    queryCompanyContactList,
    handleSelectChange
})

</script>

<style lang="scss">
.cp_outer_select {
    margin-top: 10px;
}

.cp_outer_selector {
    .el-select-dropdown__item {
        height: 56px !important;
        padding: 8px 10px;

        .rdn_item {
            display: inline-block;
            vertical-align: middle;
            width: 100%;

            span {
                display: block;
                line-height: 1.4;
            }

            .name-text {
                font-size: 14px;
                color: #595959;
                font-weight: 500;
            }

            .field-values {
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-top: 2px;

                .field-item {
                    display: inline-flex !important;
                    font-size: 12px;
                    color: #8C8C8C;
                }

                .separator {
                    display: inline-flex !important;
                    margin: 0 6px;
                    color: #8C8C8C;
                }
            }
        }
    }

    .is-selected,
    .is-hovering {
        .rdn_item {
            .name-text {
                color: var(--el-color-primary) !important;
            }
        }
    }

    .el-select-dropdown__footer {
        .cp_footer {
            padding: 10px 20px;
            cursor: pointer;

            .fn_icon {
                width: 20px;
                height: 20px;
            }

            .fn_txt {
                width: 174px;
                height: 22px;
                font-size: 14px;
                color: #595959;
                line-height: 22px;
                text-align: left;
            }
        }
    }
}

.contact_out_picker_wrap {
    position: relative;
}

.add-person-div {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    padding: 8px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    cursor: pointer;
    background-color: #fff;
    z-index: 1;

    &:hover {
        background-color: #F5F7FA;
    }

    .cp_footer {
        padding: 2px 10px;

        .fn_icon {
            width: 20px;
            height: 20px;
        }

        .fn_txt {
            width: 174px;
            height: 22px;
            font-size: 14px;
            color: #8C8C8C;
            line-height: 22px;
            text-align: left;
        }
    }
}
</style>
