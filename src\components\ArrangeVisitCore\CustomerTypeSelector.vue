<template>
    <div class="customer-type-selector">
        <div class="selector-container">
            <div class="selector-option" :class="{ active: modelValue === 'customer' }" @click="selectType('customer')">
                客户
            </div>
            <div class="selector-option" :class="{ active: modelValue === 'opportunity' }"
                @click="selectType('opportunity')">
                商机
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: String,
        default: 'customer'
    }
});

const emit = defineEmits(['update:modelValue', 'change']);

const selectType = (type) => {
    emit('update:modelValue', type);
    emit('change', type);
};
</script>

<style lang="scss" scoped>
.customer-type-selector {
    margin-bottom: 15px;

    .selector-container {
        display: flex;
        flex-direction: row;
        border-radius: 6px;
        overflow: hidden;
        width: 470px;
        gap: 10px;

        .selector-option {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #ffffff;
            color: #606266;
            border-radius: 6px;
            min-width: 70px;
            text-align: center;
            user-select: none;
            position: relative;
            flex: 1;

            &:hover {
                background-color: #f5f7fa;
            }

            &.active {
                color: #409eff;
                font-weight: 500;
                border: 1px solid #409eff;
            }

            &:not(.active) {
                border: 1px solid #dcdfe6;
            }
        }
    }
}
</style>