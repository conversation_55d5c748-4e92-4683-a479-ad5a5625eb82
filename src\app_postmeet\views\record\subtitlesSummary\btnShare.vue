<template>
  <div class="btn_share_wrap">
    <el-dialog title="分享" v-model="centerDialogVisible" width="480px" :append-to-body="true"
      :modal-append-to-body="false" class="share_modal_wrap">
      <div>
        <div class="sh_header flex-row">
          <div class="sh_title">链接分享范围</div>

        </div>
        <div class="box2">
          <div class="fl_row">
            <el-select v-model="param.shareType" placeholder="Select" style="width: 240px" @change="onConfirm">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" class="btn_ok" @click="onCopy">{{
              checked_pwd ?
                '复制链接及密码' : '复制链接'
            }}</el-button>
          </div>

          <div class="fl_row" v-if="param.shareType != 'NO_SHARE'">
            <el-checkbox v-model="checked_pwd" @change="onChangePwd"> 启用密码</el-checkbox>
            <div class="fl_pwd" v-if="param.sharePassword">{{ param.sharePassword }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveShareConfig, getShareConfig } from '@/app_postmeet/tools/api'
import { randomString } from '@/app_postmeet/tools/tools'

export default {
  name: 'BtnShare',
  props: [],
  data() {
    return {
      confId: '',
      share_link_txt: '复制链接及密码',
      checked_pwd: false,
      shareLink: '',
      centerDialogVisible: false,
      param: { "shareType": "SHARE_IN_TEAM", "sharePassword": "" },
      options: [
        {
          value: 'NO_SHARE',
          label: '创建人及团队管理者可查看'
        },
        {
          value: 'SHARE_IN_TEAM',
          label: '团队内获得链接的人可查看'
        },
        {
          value: 'SHARE_IN_INTERNAL',
          label: '企业内获得链接的人可查看'
        },
        {
          value: 'SHARE_TO_INTERNET',
          label: '互联网上获得链接的人可查看'
        }
      ]
    }
  },
  methods: {
    onChangePwd() {
      if (this.checked_pwd) {
        this.param.sharePassword = randomString(6)
        this.onConfirm('启用密码成功')
      } else {
        this.onConfirm('')
      }
    },
    show() {
      this.centerDialogVisible = true
      this.confId = g.postmeetStore.data.playItem.confId
      this._getShareConfig((status) => {
        console.log('status', status)
      })
    },
    _getShareConfig(cb) {
      getShareConfig(this.confId).then(resp => {
        if (resp.code == 0) {
          const { shareLink, sharePassword } = resp.data;
          this.param = resp.data;
          this.checked_pwd = !!sharePassword
          this.shareLink = shareLink || ''
          cb(true)
        } else {
          cb(false)
        }
      })
    },
    onConfirm(hint = '') {
      if (this.param.shareType == 'NO_SHARE') {
        this.param.sharePassword = ''
      } else {
        if (this.checked_pwd) {
          if (!this.param.sharePassword) {
            this.param.sharePassword = randomString(6)
          }
        } else {
          this.param.sharePassword = ''
        }
      }
      saveShareConfig(this.$route.params.confId, this.param).then(() => {
        if (hint) {
          // ElMessage.success('修改成功')
        }
      }).catch(() => {
        ElMessage.error("修改失败")
      })
    },
    onCopy() {
      if (this.shareLink) {
        let clipInfo = `绚星销售助手云录制分享链接：${this.shareLink} `
        if (this.param.sharePassword) {
          clipInfo += `\r\n密码：${this.param.sharePassword}`
        }
        g.appStore.doCopy(clipInfo, '分享链接已复制到剪切板！')
      } else {
        ElMessage.error("获取分享链接失败")
      }
    }
  }
}
</script>

<style lang='scss'>
.share_modal_wrap {

  .el-dialog__body {

    .sh_header {
      padding: 10px 0;
      justify-content: space-between;

      .sh_title {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #262626;
        line-height: 22px;
      }
    }

    .box2 {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      height: 153px;

      .fl_row {
        margin: 10px 0;
        display: flex;
        flex-direction: row;

        .ddl_more {
          cursor: pointer;
        }

        .el-checkbox__input.is-checked+.el-checkbox__label {
          color: #595959;
        }


        .copy_link {
          margin-left: 10px;
        }

        .fl_pwd {
          width: 60px;
          background: #F5F5F5;
          border-radius: 4px;
          border: 1px solid #D9D9D9;
          padding: 4px 11px;
          margin-left: 10px;
          line-height: 21px;
          font-size: 12px;
          color: #595959;
        }
      }

      .note {
        margin: 0 5px;
      }

      .btn_ok {
        border-radius: 4px;
        margin-left: 10px;
      }
    }
  }

  .el-dialog__footer {
    .dialog-footer {
      text-align: unset;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
