<template>
    <div class="share_wrap">
        <div class="content">
            <div class="main_hint">
                请输入访问密码
            </div>
            <div :class="{ 'input_box': true, 'error_box': error }">
                <el-input type="password" v-model="pwd" placeholder="请输入密码" @input="onChangeInput"
                    autocomplete="new-password" autofocus />
                <div class="tip" v-if="!error">需要区分大小写</div>
                <div class="error" v-if="error">{{ error }}</div>
            </div>

            <el-button type="primary" :disabled="!pwd" @click="onConfrim">确定</el-button>
            <img class="lock-icon" :src="getOssUrl('pm_lock.png')" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { shareCheckPwd } from '@/app_postmeet/tools/api'
import { getOssUrl } from '@/js/utils.js'

const route = useRoute()
const emit = defineEmits(['callback'])

const pwd = ref('')
const error = ref('')
const shareId = ref('')

onMounted(() => {
    shareId.value = route.params.shareId
})

const onChangeInput = () => {
    error.value = ''
}

const onConfrim = () => {
    const data = { password: pwd.value }
    shareCheckPwd(shareId.value, data).then(resp => {
        if (resp.code == 0) {
            error.value = ''
            localStorage.setItem("share_token", resp.data)
            g.postmeetStore.setValue('user', { token: resp.data })
            emit('callback', '')
        } else {
            console.log('shareCheckPwd error2', resp)
            pwd.value = ''
            error.value = resp.message || '密码输入错误！'
        }
    }).catch(e => {
        console.log('shareCheckPwd error3', e)
        pwd.value = ''
        error.value = '密码输入错误！'
    })
}
</script>

<style lang="scss" scoped>
.share_wrap {
    display: flex;
    width: 100vw;
    height: 100vh;
    align-items: center;
    justify-content: center;
    background-color: #F5F7FA;

    .content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 400px;
        padding: 48px;
        background: #FFFFFF;
        border-radius: 8px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
    }

    .lock-icon {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 64px;
        height: 64px;
    }

    .main_hint {
        font-size: 16px;
        font-weight: 500;
        color: #1F2329;
        line-height: 24px;
        margin-bottom: 24px;
    }

    .input_box {
        display: flex;
        flex-direction: column;
        margin-bottom: 32px;
        align-items: center;

        .tip {
            font-size: 12px;
            color: #86909C;
            line-height: 20px;
            margin-top: 8px;
        }

        .error {
            font-size: 12px;
            color: #F53F3F;
            line-height: 20px;
            margin-top: 8px;
        }

        :deep(.el-input) {
            width: 328px;

            .el-input__wrapper {
                padding: 1px 12px;
                box-shadow: 0 0 0 1px #E5E6EB inset;

                &:hover {
                    box-shadow: 0 0 0 1px #C9CDD4 inset;
                }

                &.is-focus {
                    box-shadow: 0 0 0 1px #4080FF inset;
                }
            }

            .el-input__inner {
                height: 38px;
                font-size: 14px;
                color: #1F2329;

                &::placeholder {
                    color: #86909C;
                }
            }
        }
    }

    .error_box {
        :deep(.el-input__wrapper) {
            box-shadow: 0 0 0 1px #F53F3F inset !important;
        }
    }

    .el-button {
        width: 88px;
        height: 32px;
        font-size: 14px;
        border-radius: 4px;
        padding: 4px 16px;

        &.el-button--primary {
            background-color: #165DFF;
            border-color: #165DFF;

            &:hover {
                background-color: #4080FF;
                border-color: #4080FF;
            }

            &:active {
                background-color: #0E42D2;
                border-color: #0E42D2;
            }

            &.is-disabled {
                background-color: #E5E6EB;
                border-color: #E5E6EB;
                color: #C9CDD4;
            }
        }
    }
}
</style>