<template>
    <div class="customer_list_btn_add">
        <el-button type="primary" @click="onAddOne" v-show="customerTypeList.length == 1">新建</el-button>
        <el-dropdown @command="handleCommand" type="default" v-show="customerTypeList.length > 1">
            <span class="el-dropdown-link flex-row">
                <div class="vbb_title">
                    新建
                </div>
                <el-icon class="el-icon--right">
                    <ArrowDown />
                </el-icon>
            </span>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item :command="item" v-for="item in customerTypeList" :key="item.id">
                        {{ item.name }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { ArrowDown } from '@element-plus/icons-vue'
import { getCustomerTypeList } from "@/js/api.js";
const emit = defineEmits(['callback'])

const customerTypeList = ref([]);


const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})

const onAddOne = () => {
    emit('callback', customerTypeList.value[0])
}

const handleCommand = (item) => {
    console.log('handleCommand', item)
    emit('callback', item)
}

const getCustomerTypes = () => {
    getCustomerTypeList({ status: 1 }).then(res => {
        let list = res.data;
        if (!props.isAdmin) {
            // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
            list = list.filter(x => x?.permissionsStr && x?.permissionsStr?.indexOf('1') > -1)
        }
        customerTypeList.value = list;
    });
};

onMounted(() => {
    getCustomerTypes();
});
</script>

<style lang="scss">
.customer_list_btn_add {
    .el-dropdown {
        background: #436BFF;
        border: none;
    }

    .el-dropdown-link {
        .vbb_title {
            margin-top: 3px;
            color: #fff;
        }

        .el-icon {
            margin-top: 3px;
            color: #fff;
        }
    }

    .el-dropdown {
        height: 18px;
        padding: 6px 12px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
        border-radius: 4px;
    }

    .vbp_title {
        margin-bottom: 12px;
    }
}
</style>
