import * as echarts from "echarts/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  GraphicComponent,
  VisualMapComponent,
  Mark<PERSON>ineComponent,
  DataZoomComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

import 'echarts-wordcloud'

echarts.use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  VisualMapComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  CanvasRenderer,
  LegendComponent,
  GraphicComponent,
  MarkLineComponent,
  DataZoomComponent,
]);

export default echarts;
