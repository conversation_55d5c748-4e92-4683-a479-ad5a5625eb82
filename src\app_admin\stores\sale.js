import { defineStore } from 'pinia'
import { renameKeys } from "@/app_admin/tools/utils.js"
import { getXMTopicSystemDimension } from "@/app_admin/tools/api.js"
import { getXMTopicDetail } from "@/js/api.js"


const dimRenameMap = { salesSummaryDimensions: 'SUMMARY', salesAnalysisDimensions: 'ANALYSIS', salesCounsellingDimensions: 'COUNSELLING' }

export default defineStore('admin_sale', {
    state: () => ({
        topicDetail: {},
        dimensionType: '',//当前维度类型SUMMARY,ANALYSIS,COUNSELLING
        systemDimension: { //维度类型
            SUMMARY: [],//总结
            ANALYSIS: [],//分析，
            COUNSELLING: [],//辅导
        },
    }),
    actions: {
        getCurrSystemDims() {
            return this.systemDimension[this.dimensionType];
        },
        getCurrDims() {
            return this.topicDetail[this.dimensionType];
        },
        setDimensionType(type) {
            this.dimensionType = type;
        },
        setXmItem(item) {
            this.topicDetail = item;
        },
        getTopDetail() {
            return new Promise((resolve) => {
                const { id } = this.topicDetail;
                getXMTopicDetail(id).then(resp => {
                    if (resp.code == 0) {
                        this.topicDetail = renameKeys(resp.data, dimRenameMap);
                        resolve(resp.data);
                    } else {
                        resolve({})
                    }
                }).catch(e => {
                    resolve({})
                })
            });
        },
        fetchSysDims() {
            return new Promise((resolve) => {
                let cache = this.systemDimension;
                getXMTopicSystemDimension().then(resp => {
                    if (resp.code == 0) {
                        this.systemDimension = renameKeys(resp.data, dimRenameMap);
                        resolve(data)
                    } else {
                        resolve({})
                    }
                }).catch(() => {
                    resolve({})
                })
            })
        },
    }

})
