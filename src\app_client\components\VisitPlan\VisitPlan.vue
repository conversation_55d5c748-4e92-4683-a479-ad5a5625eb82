<template>
  <div class="visit_plan_wrap">
    <div class="visit_plan_header_left">
      <PlanCalendar ref="refPlanCalendar" @callback="handleDateClick" />
    </div>
    <div class="visit_plan_header_right">
      <PlanHeader ref="refPlanHeader" @callback="onHeaderCallback" />
      <PlanBoard ref="refPlanBoard" />
      <PlanList ref="refPlanList" @callback="onCallback" v-if="viewType == 'list'" />
      <VisitTimeChart ref="refVisitTimeChart" v-else @callback="onCallback" />
    </div>
  </div>
</template>

<script setup>
import PlanHeader from "./PlanHeader.vue";
import PlanList from "./PlanList.vue";
import VisitTimeChart from "./VisitTimeChart.vue";
import PlanBoard from "./PlanBoard.vue";
import PlanCalendar from "./PlanCalendar.vue";
import { getScheduleList } from "@/js/api.js";
import { debounce } from "@/js/utils.js";


const param = ref({
  startTime: "",
  endTime: "",
  completed: null,
  showAssistMeeting: true,
  dptIds: [],
  visitType: 'all',
});
const refPlanList = ref();
const refPlanHeader = ref();
const refPlanBoard = ref();
const refVisitTimeChart = ref();
const refPlanCalendar = ref();
const emit = defineEmits(["callback"]);
const viewType = ref("list");
let planInfos = ref({
  datas: [],
  totalNum: 0,
  completedNum: 0,
  unfinishNum: 0,
});
let planInfosAll = ref(null);

// 添加一个记录上次查询条件的变量
const lastQueryParam = ref(null);

const handleDateClick = (dateParam) => {
  param.value = { ...param.value, ...dateParam };
  refPlanHeader.value.setDateRange(dateParam);
  reload();
};

const onHeaderCallback = (action, data) => {
  if (action == "change_view") {
    viewType.value = data;
    if (planInfos.value) {
      nextTick(() => {
        const plans = toRaw(planInfos.value.datas);
        if (data == "calendar") {
          refVisitTimeChart.value.init(plans, toRaw(param.value));
        } else {
          refPlanList.value && refPlanList.value.init(plans);
        }
      });
    }
  } else if (action == "update_calendar") {
    refPlanCalendar.value.setCurrentDate(data);
  } else if (action == "update_status") {
    param.value.completed = data == undefined ? undefined : data == 'true';
    reload();
  } else if (action == "update_visit_type") {
    param.value.visitType = data;
    reload(true);
  }
};

const onCallback = (action, plan) => {
  if (action == "reload") {
    reload(true);
  } else {
    emit("callback", action, plan);
  }
};

const setDeptIds = (ids) => {
  param.value.dptIds = ids;
  reload();
};

const filterPlansByVisitType = (plans) => {
  if (!plans || !plans.length) return plans;

  if (param.value.visitType === 'collaborated') {
    return plans.filter(item => item.hostUserId !== g.appStore.user.id);
  } else if (param.value.visitType === 'my') {
    return plans.filter(item => item.hostUserId === g.appStore.user.id);
  }
  return plans;
}

// 将原始的 reload 函数重命名为 _reload
const _reload = (forceLoad = false) => {
  // Check if using cached data is possible
  if (
    !forceLoad &&
    lastQueryParam.value &&
    lastQueryParam.value.startTime === param.value.startTime &&
    lastQueryParam.value.endTime === param.value.endTime &&
    JSON.stringify(lastQueryParam.value.dptIds) === JSON.stringify(param.value.dptIds)
  ) {
    if (planInfosAll.value) {
      // Filter data based on both completed status and visit type
      let filteredData = [...planInfosAll.value.datas];
      // Apply completed status filter
      if (param.value.completed !== undefined) {
        filteredData = filteredData.filter(item => item.completed === param.value.completed);
      }

      // Apply visit type filter
      filteredData = filterPlansByVisitType(filteredData);

      planInfos.value = {
        datas: filteredData,
        totalNum: filteredData.length,
        completedNum: filteredData.filter(item => item.completed).length,
        unfinishNum: filteredData.filter(item => !item.completed).length,
      };

      refPlanBoard.value && refPlanBoard.value.init(planInfos.value);
      onHeaderCallback("change_view", viewType.value);
      return;
    }
  }

  // If cache can't be used, fetch new data
  refPlanCalendar.value.reload();
  lastQueryParam.value = { ...param.value };

  getScheduleList(param.value).then((res) => {
    if (res.code == 0) {
      // Store unfiltered data
      planInfosAll.value = res.data;

      // Apply visit type filter to the response data
      const filteredData = filterPlansByVisitType(res.data.datas);
      planInfos.value = {
        ...res.data,
        datas: filteredData,
        totalNum: filteredData.length,
        completedNum: filteredData.filter(item => item.completed).length,
        unfinishNum: filteredData.filter(item => !item.completed).length,
      };

      refPlanBoard.value && refPlanBoard.value.init(planInfos.value);
      onHeaderCallback("change_view", viewType.value);
    }
  });
};

// 创建一个防抖版本的 reload 函数
const reload = debounce(_reload, 200);

onMounted(() => {
  // 注册消息处理器
  g.emitter.on("xmate_schedule_update", () => {
    reload(true);
  });
});

onUnmounted(() => {
  g.emitter.off("xmate_schedule_update");
});

defineExpose({
  handleDateClick,
  refPlanList,
  VisitTimeChart,
  viewType,
  PlanHeader,
  PlanBoard,
  PlanList,
  onHeaderCallback,
  refPlanHeader,
  refPlanBoard,
  reload,
  refVisitTimeChart,
  setDeptIds,
});

defineOptions({
  name: "VisitPlan",
});
</script>

<style lang="scss">
.visit_plan_wrap {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;

  .visit_plan_header_left {
    width: 325px;
    height: 100%;
    flex-shrink: 0;
  }

  .visit_plan_header_right {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0; // 这个很重要，防止flex子项溢出
    padding: 0;
  }
}
</style>
