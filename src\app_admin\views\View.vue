<template>
    <div  id="ppt_view_container">
    </div>
</template>
  
<script setup>

import { getPptViewToken } from "@/app_admin/tools/api.js"
import { loadScript } from "@/app_admin/tools/utils.js"
import { useRoute } from 'vue-router'
import { ElLoading } from 'element-plus'

const route = useRoute()
let loading;


const preview = (tokenInfo) => {
    let mount = document.querySelector('#ppt_view_container');
    const option = {
        mount,
        url: tokenInfo.webofficeURL,
        mode: 'normal',
        commonOptions: {
            isShowTopArea: false,// 隐藏顶部区域（头部和工具栏）
            isShowHeader: false, // 隐藏头部区域
            isBrowserViewFullscreen: false, // 是否在浏览器区域全屏
            isIframeViewFullscreen: false, // 是否在 iframe 区域内全屏
            acceptVisualViewportResizeEvent: false,// 控制 WebOffice 是否接受外部的 VisualViewport
        },
        pptOptions: {
            isShowBottomStatusBar: false, // 是否展示底部状态栏。
            isShowRemarkView: false, // 是否显示备注视图。
            isShowInsertMedia: false, // 是否显示插入音视频入口。
            isShowComment: false, // 是否显示评论相关入口。
            mobile: {
                isOpenIntoEdit: false, // 有编辑权限时，移动端打开时是否进入编辑。
                showPrevTipWhilePlay: false, // 移动端播放时向上翻页，是否展示 “上一页” 的提示。
                isShowReviewLogo: false, // 移动端是否显示审阅左上logo。
            },
        },
        commandBars: [
            {
                cmbId: "TabPrintPreview", // 组件 ID
                attributes: {
                    visible: false, // 隐藏组件
                    enable: false, // 禁用组件，组件显示但不响应点击事件
                },
            },
            {
                cmbId: "WPPMobileMarkButton", // 组件 ID
                attributes: {
                    visible: false, // 隐藏组件
                    enable: false, // 禁用组件，组件显示但不响应点击事件
                },
            },
        ],
    }
    let instance = aliyun.config(option);
    instance.setToken({ token: tokenInfo.accessToken, timeout: 25 * 60 * 1000 });

    instance.on('fileOpen', async function (data) {
        await instance.ready();
    });

    instance.on('error', (err) => {
        console.log('发生错误：', err);
    });
}

const loadData = () => {
    const templateId = route.params.templateId;
    const docId = route.params.docId;
    getPptViewToken(templateId, docId).then((resp) => {
        if (resp.code == 0) {
            preview(resp.data);
        } else {
            ElMessage.error(`加载失败`);
        }
        loading.close()
    })
}

onMounted(() => {
    loading = ElLoading.service({
        lock: false,
        text: '加载中',
    });
    const jsUrl = "https://g.alicdn.com/IMM/office-js/1.1.15/aliyun-web-office-sdk.min.js"
    loadScript(jsUrl).then(() => {
        loadData()
    }).catch((e) => {
        console.log('e', e)
    })
})

</script>
  
<style lang="scss">
#ppt_view_container {
    width: 100vw;
    height: 100vh;
}
</style>