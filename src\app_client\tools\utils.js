import { useRoute } from "vue-router";
import { getDateRangeStr, getUrlParam } from "@/js/utils.js";
export function getGreeting() {
  const now = new Date();
  const hour = now.getHours();

  if (hour >= 5 && hour < 8) {
    return "早上好";
  } else if (hour >= 8 && hour < 11) {
    return "上午好";
  } else if (hour >= 11 && hour < 14) {
    return "中午好";
  } else if (hour >= 14 && hour < 18) {
    return "下午好";
  } else {
    return "晚上好";
  }
}

export function getUrlParamObj() {
  let arr = location.href.split("?");
  let obj = {};
  if (arr.length > 1) {
    let params = arr[1].split("&");
    for (let i = 0; i < params.length; i++) {
      let param = params[i].split("=");
      obj[param[0]] = param[1];
    }
  }
  return obj;
}

export function removeDup(arrs, key) {
  const uniqueArray = arrs.reduce((accumulator, obj) => {
    const existingObj = accumulator.find(
      (o) => JSON.stringify(o) === JSON.stringify(obj)
    );
    if (!existingObj) {
      accumulator.push(obj);
    }
    return accumulator;
  }, []);
  return uniqueArray;
}

export function unique(arr) {
  if (Array.isArray(arr)) {
    return Array.from(new Set(arr));
  } else {
    return [];
  }
}

export function formatCustNum(num) {
  if (num && num > 0) {
    return num;
  } else {
    return "-";
  }
}

export function getWeekOfMonth(date) {
  // 将输入转换为Date对象
  const inputDate = new Date(date);

  // 获取当月的第一天
  const firstDayOfMonth = new Date(
    inputDate.getFullYear(),
    inputDate.getMonth(),
    1
  );

  // 计算当月第一天是星期几（0是星期日，1是星期一，以此类推）
  const firstDayOfWeek = firstDayOfMonth.getDay();

  // 计算输入日期是当月的第几天
  const dayOfMonth = inputDate.getDate();

  // 计算第一周的天数（7 - 第一天的星期几，如果第一天是星期日，则为7天）
  const daysInFirstWeek = 7 - firstDayOfWeek;

  // 如果输入日期在第一周内
  if (dayOfMonth <= daysInFirstWeek) {
    return 1;
  }

  // 计算剩余的天数
  const remainingDays = dayOfMonth - daysInFirstWeek;

  // 计算剩余的完整周数，并加上第一周
  return Math.ceil(remainingDays / 7) + 1;
}

export const getShowTimeHours = (seconds) => {
  if (!seconds) return "0";
  // 将秒数转换为小时，并保留2位小数
  return (seconds / 3600).toFixed(2);
};

export function getDateRange() {
  const route = useRoute();
  const validPeriodTypes = [
    "week",
    "month",
    "quarter",
    "year",
    "last_week",
    "last_month",
    "last_quarter",
    "last_year",
  ];
  const periodType = route.query.periodType;
  const defaultPeriodType = validPeriodTypes.includes(periodType)
    ? periodType
    : "last_month";
  const dr = getDateRangeStr(defaultPeriodType);
  return dr;
}

export const getDefaultDateRange = () => {
  const dr = getDateRange();
  const startDate = getUrlParam("startDate");
  const endDate = getUrlParam("endDate");

  let startTime = "";
  let endTime = "";
  if (startDate && endDate) {
    startTime = startDate + " 00:00:00";
    endTime = endDate + " 23:59:59";
  } else {
    startTime = dr.startDt;
    endTime = dr.endDt;
  }
  return {
    startTime,
    endTime,
  };
};

export function getUrlSsoUserId() {
  const ssoUser = getUrlParam("ssoUser", "");
  if (ssoUser) {
    const [id, fullname] = ssoUser.split(",");
    return [id];
  }
  return [];
}

export const reportTypeMap = {
  weekly: "周",
  monthly: "月",
  quarterly: "季度",
  yearly: "年",
};

export const dimFilterData = (dimensionList, cvDimensionFbCountList) => {
  dimensionList = dimensionList.map((item) => {
    // 处理每个一级维度的子维度
    if (item.children && item.children.length > 0) {
      // 为每个子维度添加 negativeCount 和 positiveCount
      const filteredChildren = item.children.map((child) => {
        // 在 cvDimensionFbCountList 中查找匹配的维度
        const matchedDimension = cvDimensionFbCountList.find(
          (dim) => dim.dimensionId === child.id
        );

        // 如果找到匹配的维度，添加 negativeCount 和 positiveCount
        if (matchedDimension) {
          return {
            ...child,
            negativeCount: matchedDimension.negativeCount || 0,
            positiveCount: matchedDimension.positiveCount || 0,
          };
        }

        // 如果没有找到匹配的维度，设置默认值为 0
        return {
          ...child,
          negativeCount: 0,
          positiveCount: 0,
        };
      });

      // 过滤掉 negativeCount 和 positiveCount 都为 0 的子维度
      const validChildren = filteredChildren.filter(
        (child) => child.negativeCount > 0 || child.positiveCount > 0
      );

      return {
        ...item,
        children: validChildren,
      };
    }

    return item;
  });

  // 过滤掉没有有效子维度的一级维度
  dimensionList = dimensionList.filter(
    (item) => item.children && item.children.length > 0
  );
  return dimensionList;
};

export const sortNoChildLast = (list) => {
  // if (list.length > 0) {
  //   let noChildList = [];
  //   list.forEach((i) => {
  //     if (i.children?.length == 0) {
  //       noChildList.push(i);
  //     }
  //   });
  //   list = list.filter((x) => x.children?.length > 0);
  //   list = [...list, ...noChildList];
  // }
  return list;
};

export const formatBarValue = (value, dimension, round = 0) => {
  if (dimension.toLowerCase().includes("rate")) {
    return `${(Number(value) * 100).toFixed(round)}%`;
  }
  return Number(value).toFixed(round);
};

export const getDefaultPeriodType = () => {
  const route = useRoute();
  const isReport = location.href.indexOf("team_report") > 0;
  return isReport
    ? route.params.periodType
      ? route.params.periodType
      : "quarterly"
    : "quarterly";
};

export const checkDomReady = (chartDom) => {
  const domWidth = chartDom.value?.offsetWidth || 0;
  const domHeight = chartDom.value?.offsetHeight || 0;
  // console.log('checkDomReady', domHeight, domWidth)
  return domHeight !== 0 && domWidth > 0;
};
