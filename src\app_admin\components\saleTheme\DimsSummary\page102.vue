<template>
    <div class="page_102_wrap">
        <div class="p1_title">识别拜访过程中是否有提及竞争对手，以及提及的次数：</div>
        <div class="flex-row">
            <el-tooltip class="box-item" effect="dark" content="最多添加100个关键词" placement="top"
                v-if="isShow && tags.length < 100">
                <div class="p1_vbox b1" @click="onTag('add')">
                    添加竞争对手
                </div>
            </el-tooltip>
            <div class="p1_vbox b2" @click="onTag('batch_add')">
                批量导入竞争对手
                <input type="file" ref="fileInput" @change="onFileChange" style="visibility: hidden;" />
            </div>
            <div class="flex-col">
                <div class="p1_b3" @click="downloadTemplate">
                    点击下载Excel模板
                </div>
                <div class="p1_b4">
                    根据要求填写Excel后上传，即可批量导入竞争对手
                </div>
            </div>
        </div>

        <div class="p1_tags flex-row">
            <div v-for="tag in tags" :key="tag" closable class="info flex-row">
                <div class="tag_name">
                    {{ tag.commonName }}
                </div>
                <div class="picon" @click="onTag('edit', tag)">
                    <EditIcon />
                </div>
                <div class="picon" @click="onTag('delete', tag)">
                    <DeleteIcon />
                </div>
            </div>
        </div>
    </div>
    <dialogEdit102 ref="refEdit" @callback="onTag" />
</template>

<script setup>
import dialogEdit102 from "./dialogEdit102.vue"
import EditIcon from "@/icons/edit.vue"
import DeleteIcon from "@/app_admin/icons/delete.vue"
import { getCompetitor, deleteCompetitor, downloadCompetitorTemplate, batchUploadCompetitor } from "@/app_admin/tools/api.js"
import { onMounted } from "vue";

const isShow = ref(true)
const tags = ref([])
const refEdit = ref()
const fileInput = ref()

const load = () => {
    getCompetitor().then(resp => {
        if (resp.code == 0) {
            tags.value = resp.data;
        }
    })
}

const downloadTemplate = () => {
    const param = {
        filename: "批量导入竞争对手",
    }
    downloadCompetitorTemplate(param)

}

const onTag = (action, data) => {
    if (action == 'delete') {
        deleteCompetitor(data.id).then(resp => {
            if (resp.code == 0) {
                tags.value = tags.value.filter(x => x.id != data.id);
            }
        })
    } else if (action == "add") {
        refEdit.value.show_add()
    } else if (action == "edit") {
        refEdit.value.show_edit(data)
    } else if (action == "reload") {
        load()
    } else if (action == "batch_add") {
        fileInput.value.click()
    }
}

const clearFileFun = () => {
    fileInput.value.value = '';
};
const onSuccess = (data) => {
    if (data.code == 0) {
        clearFileFun()
        if (data.data.length === 0) {
            ElMessage.success("批量导入成功");
            load()
        } else {
            const tip = data.data.join('<br>')
            ElMessage({
                dangerouslyUseHTMLString: true,
                message: tip,
                type: 'error',
            });
        }
    }
};

const onFail = (error) => {
    clearFileFun()
    ElMessage.success("Excel解析失败，请检查后重新上传");
    console.log('onFail', error);
};

const onFileChange = () => {
    const formData = new FormData()
    formData.append('file', fileInput.value.files[0] || '')
    batchUploadCompetitor(formData, '', onSuccess, onFail)
}


onMounted(() => {
    load()
})

defineExpose({
    dialogEdit102, isShow, tags,
    onTag, EditIcon, DeleteIcon
})
</script>

<style lang="scss">
.page_102_wrap {
    margin-left: 7px;

    .p1_title {
        height: 24px;
        font-size: 14px;
        color: #8C8C8C;
        line-height: 24px;
        text-align: justify;
    }

    .p1_vbox {

        height: 32px;
        background: rgba(255, 255, 255, 0.01);
        border-radius: 4px;
        font-size: 14px;
        border: 1px solid #436BFF;
        color: #436BFF;
        line-height: 32px;
        text-align: center;
        text-transform: none;
        cursor: pointer;
        margin: 12px 0;
        margin-right: 12px;
    }

    .b1 {
        width: 116px;

    }

    .b2 {
        width: 136px;
    }

    .p1_b3 {
        font-size: 12px;
        text-decoration: underline;
        cursor: pointer;
        color: green;
    }

    .p1_b4 {
        font-size: 12px;
        color: #8C8C8C;
    }

    .p1_tags {
        flex-wrap: wrap;
        align-items: center;
        margin-top: 5px;

        .info {
            height: 32px;
            background: #F5F5F5;
            border-radius: 4px;
            margin: 5px 12px;
            padding: 0 6px;
            line-height: 32px;

            .picon {
                margin-left: 12px;
                line-height: 36px;
                color: #8C8C8C;
                cursor: pointer;
            }

            .picon:hover {
                color: #436BFF;
            }
        }
    }
}
</style>
