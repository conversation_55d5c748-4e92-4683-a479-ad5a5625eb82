<template>
    <div class="chat2_wrap">
        <chatCoze ref="refChatWrap" @callback="cbChat">
            <template #input_top>
                <div class="meet_minutes_box">
                    <BtnSelectMM ref="refBtnSelectMM" @callback="cbBtnSelectMM" />
                </div>
            </template>
            <template #input_inside_top>
                <MeetingList ref="refMeetList" @callback="cbMeetList" />
            </template>
        </chatCoze>
    </div>
</template>

<script setup>
import chatCoze from "@/components/chatCoze/index.js";
import MsgHello from "./msg_hello.vue"
import MsgMy from "@/components/chatCoze/msg_my.vue"
import MsgAi from "@/components/chatCoze/msg_ai.vue"
import BtnSelectMM from "./BtnSelectMM.vue"
import MeetingList from "./MeetingList.vue"
import { removeURLParams } from "@/js/utils.js"

const refMeetList = ref()
const refChatWrap = ref();
const config = ref({})
const refBtnSelectMM = ref();
const route = useRoute();

let meetingInfo = {}
const cbChat = (action, data) => {
    if (action == "set_bot_type") {
        config.value['bot_type'] = data;
        refBtnSelectMM.value.setBotType(data);
        refChatWrap.value.updateConfig("bot_type", data);
    } else if (action == "choose_mm") {
        setMeetingInfo(data);
    } else if (action == "after_get_history") {
        const item = data.filter(x => x.messageType == "TOPIC")[0];
        const bot_type = item.message == "软件产品" ? "softs" : 'product';
        cbChat('set_bot_type', bot_type)
    }
}

const cbMeetList = (action, data) => {
    if (action == "clear_meeting_list") {
        setMeetingInfo({});
    }
}

const cbBtnSelectMM = (action, data) => {
    if (action == "choose_mm") {
        setMeetingInfo(data);
        refMeetList.value.show(data)
    }
}

const setMeetingInfo = (data) => {
    const hasM = JSON.stringify(data) != "{}";
    g.emitter.emit('setHasMeetingInfo', hasM);
    meetingInfo = data;
}

const _getMsgComponent = (msg) => {
    if (msg.my) {
        return MsgMy;
    } else {
        if (msg.type == "hello") {
            return MsgHello
        } else {
            return MsgAi
        }
    }
}

const _addHelloMsg = () => {
    refChatWrap.value.setMsgs = [];
    const msg = {
        my: false,
        type: "hello",
        message: config.value.hello_txt,
        questions: [],
        showCate: true
    }
    refChatWrap.value.addMsg(msg);
}

const _onBeforeSend = (txt) => {
    let data = txt
    let status = !!config.value['bot_type'];
    if (status) {
        if (route.query.confId) {
            txt = '';
        }
        if (JSON.stringify(meetingInfo) != "{}") {
            const { subject, salesMateCustomerName, requirement, conferenceId } = meetingInfo;
            data = `来源拜访：${subject} | ${salesMateCustomerName} | ${conferenceId}

${requirement}

${txt}`
        }
        setMeetingInfo({})
        removeURLParams(['category', 'confId', 'subject', 'customerName'])
        refMeetList.value.ClearItem()
    }

    return { status, data }
}


onMounted(() => {
    const appid = route.query.appid;
    config.value = g.clientStore.getAppConfig(appid);
    config.value['getMsgComponent'] = _getMsgComponent;
    config.value['addHelloMsg'] = _addHelloMsg;
    config.value['onBeforeSend'] = _onBeforeSend;
    refChatWrap.value.init(toRaw(config.value));
})


defineExpose({
    refChatWrap, refBtnSelectMM, MsgHello, BtnSelectMM,
    MeetingList, refMeetList, cbBtnSelectMM
})
</script>

<style lang="scss">
.chat2_wrap {
    background-color: #ebf2ff;
}
</style>