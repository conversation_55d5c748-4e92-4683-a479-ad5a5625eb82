import { getHttp } from "@/js/request.js";
const _http = getHttp()

// 开会
export const createLiveMeet = (data) => _http.post(`api/live/create`, data)

//暂停沟通
export const pauseLiveMeet = (conferenceId) => _http.put(`api/conference/${conferenceId}/ongoing/pause`, {})

// 更新ASR状态 event:start|stop|pause|resume
export const updateAsr = (room, identity, status, data = {}) => _http.put(`media/room/${room}/participant/${identity}/asr/event/${status}`, data);

// 更新云录制状态 event:start,stop
export const updateCloudRecord = (room, identity, event, data = {}) => _http.put(`media/room/${room}/participant/${identity}/record/${event}`, data);

// 删除房间
export const endLiveMeet = (room, data) => _http.delete(`media/room/${room}`, data);

// 更新日程通知
export const updateNoticeConfig = (enableNotifications) => _http.put(`api/contact/v1/xmate/client/config`, { enableNotifications });

// 会中标注参会人
export const renameSpeaker = (orgId, conferenceId, data) => _http.post(`api/org/${orgId}/conference/${conferenceId}`, data);

// 根据房间ID查询房间详情，房间内人员都有权限。
export const getRoom = (room, data) => _http.post(`media/room/${room}`, data);


// 绑定录音设备
export const bindDevice = (data) => _http.post(`api/device/bind`, data);

// 解绑录音设备
export const unbindDevice = () => _http.put(`api/device/unbind`, {});

//开始-结束录音
export const updateDeviceRecord = (cmdType, scheduleId) => {
    let url = `api/device/record?cmdType=${cmdType}`;
    if (!!scheduleId) {
        url += `&scheduleId=${scheduleId}`;
    }
    return _http.get(url);
}

//获取消息设置详情
export const getMsgSetting = () => _http.get(`api/msg/setting`);

//更新消息设置 
export const updateMsgSetting = (data) => _http.put(`api/msg/setting`, data);

// 清空用户消息
export const clearUserMsg = () => _http.post(`api/msg/clear`, {});

// 获取未读消息数量
export const getUnReadMsgNum = () => _http.get(`api/msg/msg/unread`);

// /api/device/info
// 获取设备信息
export const getCardStatus = (cardId) => _http.post(`api/device/info`, {
    "deviceType": "",
    "snCodeList": [cardId]
});

// 获取后台结束沟通消息
export const getCloseMsg = (data) => _http.post(`api/msg/conf/close`, data);
