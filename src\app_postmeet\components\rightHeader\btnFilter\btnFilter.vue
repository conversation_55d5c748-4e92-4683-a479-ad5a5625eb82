<template>
  <el-popover
    placement="bottom"
    title="筛选"
    width="200"
    trigger="click"
    popper-class="la_pop_wrap"
  >
    <template #reference>
      <div class="hicon">
        <el-tooltip class="item" effect="dark" content="筛选" placement="top">
          <FilterIcon />
        </el-tooltip>
      </div>
    </template>
    <template #default>
      <el-checkbox
        :indeterminate="isIndeterminate"
        v-model="checkAll"
        @change="handleCheckAllChange"
        >全选</el-checkbox
      >
      <div style="margin: 15px 0"></div>
      <el-checkbox-group v-model="checked" @change="handleCheckedChange">
        <el-checkbox v-for="city in names" :label="city" :key="city" :value="city">{{
          city
        }}</el-checkbox>
      </el-checkbox-group>
    </template>
  </el-popover>
</template>

<script>
import FilterIcon from "@/app_postmeet/icons/right_header/filter.vue";
export default {
  components: { FilterIcon },
  data() {
    return {
      checkAll: true,
      checked: [],
      names: [],
      isIndeterminate: false,
    };
  },
  mounted() {
    const that = this;
    g.emitter.on("setSubTitleAll", () => {
      that.names = g.postmeetStore.data.names;
      that.checked = g.postmeetStore.data.names;
    });
  },
  methods: {
    handleCheckAllChange(value) {
      this.checked = value ? this.names : [];
      this.isIndeterminate = false;
      g.emitter.emit("setSpeakerFilter", this.checked);
    },
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.names.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.names.length;
      g.emitter.emit("setSpeakerFilter", this.checked);
    },
  },
};
</script>

<style lang="scss">
.la_pop_wrap {
  padding: 16px !important;

  .el-checkbox {
    margin: 10px 25px 0 0;
  }
}
</style>
