<template>
    <el-input v-model="localValue" placeholder="输入沟通主题、客户/销售名称搜索" class="vis_search" @keyup.enter="onSearch" clearable
        :prefix-icon="Search" @clear="onClear" />
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
    value: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:value', 'reload'])

const localValue = ref(props.value)

watch(localValue, (newValue) => {
    emit('update:value', newValue)
})

const onClear = () => {
    localValue.value = '';
    emit('update:value', '')
    emit('reload', '')
}

const onSearch = () => {
    emit('reload', localValue.value)
}

</script>

<style lang="scss">
.vis_search {
    width: 260px;
}
</style>
