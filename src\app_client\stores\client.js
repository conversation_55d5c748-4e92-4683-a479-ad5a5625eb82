import { defineStore } from "pinia";
import { saveXmatePoint, getDaokouAuth<PERSON>ey } from "@/app_client/tools/api.js";
import getYxtConfig from "./config.yxt.js";
import { getUser, openWindow } from "@/js/utils.js";
import { ElMessage } from "element-plus";

const ClientStore = defineStore("client", {
  actions: {
    getAppConfig(key) {
      const user = getUser();
      const { id, name } = user;
      const accountNo = localStorage.getItem("username");
      const org_id = localStorage.getItem("orgCode").split(".")[0];
      const base_ = {
        org_id,
        topk: 3,
        query: "",
        accountNo: accountNo,
        userId: id,
        userName: name,
        appid: key,
      };
      const config = {
        product_recommender_c: {
          title: "智能商品推荐师",
          hello_txt:
            "Hello，我是你的智能商品推荐伙伴，请选择你要售卖的商品类型。",
          bot_type: "coze",
          topicType: "PRODUCT_RECOMMENDATION",
          bots: {
            softs: "7367629710522318898",
            product: "7382860756469137471",
          },
          param: {
            bot_id: "",
          },
        },
        value_deduction: {
          title: "产品价值演绎家",
          hello_txt: "嗨！我是产品价值演绎家，产品怎么用，全司我最懂",
          collection_ids: ["ce16f1ace6a63282e758635e76e8b7b7"],
          app_id: "e473f2c1f1315e2938f57b9d4c3a461a",
        },
        product_recommender: {
          title: "智能商品推荐师",
          hello_txt:
            "Hello，我是你的智能商品推荐伙伴，请选择你要售卖的商品类型。",
          collection_ids: ["baf81aa12220cff787f2873b78cf42f8"],
          app_id: "975ab04a4a7a23855507a4afc90866fc",
        },
        gen_prompt_aibox: {
          title: "生成或者优化沟通记录处理的Prompt",
          hello_txt: "",
          app_id: "c80ee5c84945ace3b922128777421089",
          first_param: {
            accountNo: accountNo,
            appId: "c80ee5c84945ace3b922128777421089",
            currNodeInstanceId: "start-0",
            orgId: base_.org_id,
            processStatus: "",
            recordId: "",
            platform: "aibox",
          },
        },
      };
      let key_config = {};
      if (config.hasOwnProperty(key)) {
        key_config = { ...base_, ...config[key] };
      }
      return key_config;
    },
    openUrl(urlParam) {
      console.log(urlParam);
      if (urlParam.url) {
        const user = getUser();
        const _openit = async () => {
          if (urlParam.url == "api_iie") {
            const resp = await getDaokouAuthKey();
            if (resp.code == 0) {
              const { id, key } = resp.data;
              urlParam.url = encodeURI(
                `https://yxt.daokoujinke.com/center/industryChainIndex?ss=${id}&key=${key}`
              );
            }
          } else if (urlParam.url.indexOf("http") == -1) {
            urlParam.url = `${g.config.publicPath}/#/${urlParam.url}?appid=${urlParam.id}`;
          }

          const fn = () => {
            const param = {
              width: 1430,
              height: 856,
              minWidth: 1024,
              minHeight: 768,
            };
            openWindow({ urlParam, param, store: toRaw(user) });
          };
          if (urlParam.title) {
            const { clientId, accessId, clientType, clientVersion } = user;
            const pointParam = {
              clientId,
              accessId,
              clientType,
              clientVersion,
              refererName: "xmate-client",
              nextPageName: urlParam.title,
            };
            saveXmatePoint(pointParam).then(fn).catch(fn);
          } else {
            fn();
          }
        };
        _openit();
      }
    },
    _openMeetRecord(conferenceId, menu1 = '', menu2 = '') {
      if (!conferenceId) {
        console.error("no conferenceId", menu1, menu2);
        return;
      }
      if (conferenceId.indexOf(",") > -1) {
        // 沟通计划
        conferenceId = conferenceId.split(",")[0];
      }
      let url = ''
      let urlParam = menu1 || menu2 ? '?' : ''
      if (menu1) {
        urlParam += `menu1=${menu1}`
      }
      if (menu2) {
        urlParam += menu1 ? '&' : '?'
        urlParam += `menu2=${menu2}`
      }
      if (g.config.clientType == 'mobile') {
        url = `${g.config.postmeet_h5_record}/#/record/${conferenceId}${urlParam}`;
      } else {
        url = `${g.config.publicPath}/#/postmeet/record/${conferenceId}${urlParam}`;
      }
      this.openUrl({ url, id: 'postmeet_record' })
    },
    viewPlanRecord(plan) {
      if (plan.customizeReportStatus == 7) {
        // 处理失败
        ElMessage({
          message: plan.customizeReportFailReason,
          grouping: true,
          type: "warning",
        });
        return;
      }
      if (plan.customizeReportStatus === 4) {
        // 会话字幕量不足
        if (plan.recordingPaths) {
          this._openMeetRecord(plan.conferenceId);
          return;
        } else {
          ElMessageBox.alert(
            '<p style="margin-bottom: 10px;">录制时长过短导致未生成录制文件，为确保录制质量，建议：</p><p>1. 录音设备靠近声源，确保收音清晰</p><p>2. 录制时避免误触界面，防止意外结束</p>',
            "查看详情",
            {
              confirmButtonText: "知道了",
              dangerouslyUseHTMLString: true,
            }
          );
          return;
        }
      }
      if (!plan.recognitionPath) {
        ElMessage({
          message: "录制文件正在生成中...",
          grouping: true,
          type: "warning",
        });
        return;
      }
      this._openMeetRecord(plan.conferenceId);
    },
    getMenuList() {
      let config = getYxtConfig();
      return config;
    },
  },
});

export default ClientStore;
