<template>
  <div class="chat_wrap">
    <div class="chat_header">
      <div class="rh_left">
        <div class="rh_logo" @click="goPremeet">
          <img :src="getOssUrl('logo_mini.png')" />
        </div>
        <div class="rh_meet_info">
          <div class="rh_title">{{ roomInfo.subject }}</div>
          <div class="rh_sub_title" v-show="roomInfo && roomInfo.startTime">
            {{ roomInfo && roomInfo.startTime }} | {{ roomInfo && hostName }}
          </div>
        </div>
      </div>
      <div class="_blank"></div>
      <div class="rh_right">
        <div class="rh_name">
          <div class="rh_abridge">{{ username }}</div>
        </div>
      </div>
    </div>
    <div v-show="hasChat" class="chat_main">
      <ul v-for="(item, i) in chatData" class="chat-subtitles-list" :key="i">
        <li>
          <div class="name_time">
            <div class="t_name" v-html="item.fromName"></div>
            {{ item.sendTime }}
          </div>
          <img :src="imgs[item.data].link" v-if="Object.keys(imgs).includes(item.data)" />
          <div class="txt" v-html="md2html(item.data)" v-else></div>
        </li>
      </ul>
      <div v-if="chatData.length == 0">
        <el-empty description="没有聊天记录"> </el-empty>
      </div>
    </div>
    <div v-if="loading" v-loading="loading" class="loading"></div>
    <div v-show="!hasChat && !loading">
      <el-empty v-if="!loading" description="没有聊天记录"> </el-empty>
    </div>
  </div>
</template>

<script>
import { getChat, getRecordList } from "@/app_postmeet/tools/api.js";
import { md2html } from "@/js/md.js";
import { getOssUrl } from "@/js/utils.js";

export default {
  data() {
    return {
      confId: "",
      username: "",
      hostName: "",
      roomInfo: { subject: "" },
      loading: true,
      hasChat: false,
      chatData: [],
      imgs: {}, //link_data:{key:{name,url}}
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    getOssUrl,
    md2html,
    goPremeet() {
      window.location.href = g.config.publicPath;
    },
    init() {
      document.title = "绚星销售助手录制 - 聊天记录";
      this.confId = this.$route.params.confId || "";
      if (this.confId) {
        const store = JSON.parse(localStorage.getItem("store") || "{}");
        const user = store.user;
        if (user && user.orgId) {
          this.username = user.userName;
          getChat(this.confId)
            .then(({ data }) => {
              this.chatData = data;
              this.gen_imgs();
              this.loading = false;
              this.hasChat = true;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.loading = false;
        }
      }
    },
    getRoomInfo(callback) {
      getRecordList(this.confId)
        .then((res) => {
          this.roomInfo = res.data;
          if (this.roomInfo.recordList.length > 0) {
            this.hostName = this.roomInfo.recordList[0].hostName;
          }
          callback(true);
        })
        .catch(() => {
          callback(false);
        });
    },
    _getFileExt(fileName) {
      // 使用正则表达式匹配文件名的后缀
      const regex = /\.([^.]+)$/;
      const match = regex.exec(fileName);
      if (match) {
        const fileExtension = match[1];
        return fileExtension;
      } else {
        return "";
      }
    },
    gen_imgs() {
      const img_exts = ["jpg", "jpeg", "png", "gif"];
      for (let item of this.chatData) {
        const txt = item.data;
        // 使用正则表达式匹配图片链接
        const regex = /\[(.*?)\]\((.*?)\)/;
        const match = regex.exec(txt);

        if (match) {
          const name = match[1];
          const link = match[2];
          const extension = this._getFileExt(name);
          // 判断后缀是否表示图片
          if (extension && img_exts.includes(extension)) {
            this.imgs[txt] = { name, link };
          }
        }
      }
    },
  },
};
</script>
<style lang="scss">
@use './index.scss';
</style>
