<template>
    <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="ability-model-form-drawer">
        <template #header>
            <div class="drawer-header">
                <el-icon class="close-icon" @click="closeDrawer">
                    <Close />
                </el-icon>
                <span class="drawer-title">{{ drawerTitle }}</span>
            </div>
        </template>

        <div class="drawer-content">
            <el-form ref="formRef" :model="modelForm" :rules="rules" label-width="120px">
                <div>
                    <el-form-item label="模型名称" prop="modelName">
                        <el-input v-model="modelForm.modelName" show-word-limit maxlength="50" />
                    </el-form-item>
                </div>

                <div class="form-section">
                    <el-form-item label="达标分值" prop="testScore">
                        <el-input-number v-model="modelForm.testScore" :min="0" :max="100" />
                        <span class="score-suffix">/100</span>
                    </el-form-item>
                </div>

                <div class="form-section">
                    <el-form-item label="关联能力方向" prop="relatedAbilities">
                        <el-select v-model="modelForm.relatedAbilities" multiple collapse-tags placeholder="请选择关联能力方向">
                            <el-option label="沟通能力" value="沟通能力" />
                            <el-option label="问题分析能力" value="问题分析能力" />
                            <el-option label="情绪管理能力" value="情绪管理能力" />
                        </el-select>
                    </el-form-item>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button class="cancel-btn" @click="closeDrawer">取 消</el-button>
                <el-button class="confirm-btn" type="primary" @click="submitForm">确 定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

const emit = defineEmits(['success'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

const drawerTitle = computed(() => {
    return isEditMode.value ? '编辑能力模型' : '创建能力模型'
})

const modelForm = reactive({
    modelName: '',
    testScore: 80,
    relatedAbilities: []
})

const rules = {
    modelName: [
        { required: true, message: '请输入模型名称', trigger: 'blur' },
        { max: 50, message: '最多输入50个字符', trigger: 'blur' }
    ],
    testScore: [
        { required: true, message: '请输入达标分值', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '分值范围在0-100之间', trigger: 'blur' }
    ],
    relatedAbilities: [
        { required: true, message: '请选择关联能力方向', trigger: 'change' },
        { type: 'array', min: 1, message: '至少选择一个关联能力方向', trigger: 'change' }
    ]
}

const openDrawer = (data = null) => {
    drawerVisible.value = true

    if (data) {
        // 编辑模式
        isEditMode.value = true
        currentId.value = data.id
        modelForm.modelName = data.modelName
        modelForm.testScore = data.testScore || 80
        modelForm.relatedAbilities = data.relatedAbilities ? data.relatedAbilities.split('\n') : []
    } else {
        // 添加模式
        isEditMode.value = false
        currentId.value = null
        modelForm.modelName = ''
        modelForm.testScore = 80
        modelForm.relatedAbilities = []
    }
}

const closeDrawer = () => {
    drawerVisible.value = false
    formRef.value?.resetFields()
}

const submitForm = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            // 处理表单提交
            const formData = {
                id: currentId.value,
                modelName: modelForm.modelName,
                testScore: modelForm.testScore,
                relatedAbilities: modelForm.relatedAbilities.join('\n')
            }

            emit('success', {
                mode: isEditMode.value ? 'edit' : 'add',
                data: formData
            })
            closeDrawer()
        } else {
            ElMessage.error('请检查输入内容')
            return false
        }
    })
}

const handleClose = (done) => {
    closeDrawer()
    done()
}

// 暴露方法给父组件
defineExpose({
    openDrawer,
    closeDrawer
})
</script>

<style scoped lang="scss">
.ability-model-form-drawer {
    :deep(.el-drawer__header) {
        padding: 0;
        margin-bottom: 0;
        border-bottom: 1px solid #f0f0f0;
    }

    :deep(.el-drawer__body) {
        padding: 0;
    }

    :deep(.el-drawer__footer) {
        padding: 16px 24px;
        border-top: 1px solid #f0f0f0;
    }
}

.drawer-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;

    .close-icon {
        font-size: 16px;
        color: #999;
        cursor: pointer;
        margin-right: 16px;

        &:hover {
            color: #666;
        }
    }

    .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }
}

.drawer-content {
    // padding: 24px;
    overflow-y: auto;
}

.form-section {
    margin-bottom: 24px;

    :deep(.el-form-item) {
        margin-bottom: 16px;

        .el-form-item__error {
            position: static;
            margin-top: 4px;
        }
    }
}

.score-suffix {
    margin-left: 8px;
    color: #606266;
}

.drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .cancel-btn {
        padding: 8px 24px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background: #fff;
        color: #666;

        &:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
    }

    .confirm-btn {
        padding: 8px 24px;
        border-radius: 6px;
        background: #1890ff;
        border-color: #1890ff;

        &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
    }
}
</style>