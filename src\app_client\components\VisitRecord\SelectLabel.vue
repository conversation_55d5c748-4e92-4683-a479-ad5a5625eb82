<template>
    <div class="visit_filter_item">
        <el-select v-model="localValue" multiple clearable collapse-tags placeholder="主题标签" popper-class="custom-header"
            :max-collapse-tags="1" style="width: 184px">
            <template #header>
                <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAll">
                    全选
                </el-checkbox>
            </template>
            <el-option v-for="item in tags" :key="item" :label="item" :value="item" />
        </el-select>
    </div>
</template>

<script setup>


const props = defineProps(['value'])
const emit = defineEmits(['update:value', 'callback', 'reload'])
const localValue = ref(props.value)
const checkAll = ref(false)
const indeterminate = ref(false)
const tags = ref([])

watch(localValue, (newValue) => {
    emit('update:value', newValue)
    if (newValue.length === 0) {
        checkAll.value = false
        indeterminate.value = false
    } else if (newValue.length === tags.value.length) {
        checkAll.value = true
        indeterminate.value = false
    } else {
        indeterminate.value = true
    }
    emit('reload', '')
})

const handleCheckAll = (val) => {
    indeterminate.value = false
    if (val) {
        localValue.value = toRaw(tags.value)
    } else {
        localValue.value = []
    }
}

onMounted(() => {
    g.cacheStore.getSalesConfigure().then((resp) => {
        if (resp && resp.customerTopics) {
            tags.value = resp.customerTopics.map(x => x.label)
        }
    })
})

</script>

<style lang="scss">
.visit_filter_item {

    .el-checkbox {
        display: flex;
        height: unset;
    }
}
</style>