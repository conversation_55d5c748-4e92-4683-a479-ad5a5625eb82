<template>
  <div class="analysis-dimension-container">
    <el-button type="primary" @click="handleAdd">创建</el-button>
    <div class="dimension-grid">
      <div class="dimension-card" v-for="dimension in dimensions" :key="dimension.id">
        <div class="card-content">
          <div class="card-placeholder">
            <span>示例图</span>
          </div>
          <div class="card-footer">
            <div class="card-title">
              {{ dimension.name }}
              <el-tag v-if="dimension.tag" :type="dimension.tagType" size="small">{{ dimension.tag }}</el-tag>
            </div>
            <div class="card-meta" v-if="dimension.creator || dimension.createTime">
              <span v-if="dimension.creator" class="creator">{{ dimension.creator }}</span>
              <span v-if="dimension.createTime" class="create-time">{{ dimension.createTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <AnalysisDimensionDialog ref="dialogRef" @submit="handleDialogSubmit" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAnalysisDimensionListData } from "@/app_admin/api/communicate.js";
import AnalysisDimensionDialog from './AnalysisDimensionDialog.vue';
import { ElMessage } from 'element-plus';

const dimensions = ref([]);
const showDialog = ref(false);
const dialogRef = ref(null);

// 获取维度数据
const loadDimensions = async () => {
  try {
    const response = await getAnalysisDimensionListData();
    if (response.code === 0) {
      dimensions.value = response.data.datas;
    }
  } catch (error) {
    console.error('获取维度数据失败:', error);
    ElMessage.error('获取维度数据失败');
  }
};

const handleAdd = () => {
  dialogRef.value.openDrawer();
};

const handleDialogSubmit = (formData) => {
  console.log('提交的表单数据:', formData);
  // 这里可以添加逻辑来处理表单提交，例如调用API添加新的维度
  // 然后重新加载维度列表
  loadDimensions();
};

// 组件挂载时加载数据
onMounted(() => {
  loadDimensions();
});
</script>

<style lang="scss" scoped>
.analysis-dimension-container {
  padding: 24px 0;

  button {
    margin-bottom: 10px;
  }
}

.dimension-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.dimension-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #409eff;
  }
}

.card-content {
  .card-placeholder {
    height: 120px;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;
    border-bottom: 1px solid #e4e7ed;
  }

  .card-footer {
    padding: 16px;

    .card-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;

      .el-tag {
        margin-left: 8px;
      }
    }

    .card-meta {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #909399;

      .creator {
        color: #606266;
      }

      .create-time {
        color: #909399;
      }
    }
  }
}
</style>