<template>
  <div class="edit_template_wrap flex-column">
    <EditTop ref="refEditTop" />
    <BlankSetting v-if="tempInfo.child.length == 0" @callback="callback" />
    <EditBottom v-else ref="refEditBottom" @callback="callback" />
    <EditOutline ref="refOutLine" @callback="callback"></EditOutline>
  </div>
</template>

<script setup>
import { nextTick } from "vue"
import { getPptTemplateSettings } from "@/app_admin/tools/api.js"
import EditOutline from "./EditOutline.vue";
import { useRoute } from 'vue-router'
import EditTop from "./editTop.vue"
import EditBottom from "./EditBottom.vue"
import BlankSetting from "./BlankSetting.vue"
import { ElLoading } from 'element-plus'

const refEditTop = ref();
const refEditBottom = ref();
const refOutLine = ref();
const route = useRoute();
const tempInfo = ref({ child: [] });
let loading;

// 保存需要提交的数据
const templateId = route.params.id;

const getData = () => {
  loading = ElLoading.service({
    lock: false,
    text: '加载中',
  });
  refEditTop.value.init(templateId);
  getPptTemplateSettings(templateId).then(resp => {
    loading.close();
    if (resp.code == 0) {
      tempInfo.value = resp.data;
      const { child } = resp.data;
      if (child.length > 0) {
        nextTick(() => {
          refEditBottom.value.init(resp.data);
        })
      }
    } else {
      ElMessage.error(`获取数据失败`);
    }
  })
}

const callback = (action) => {
  if (action == 'confirm') {
    getData()
  } else if (action == "open_edit") {
    refOutLine.value.show(templateId, tempInfo.value.child);
  }
}

onMounted(() => {

  getData()
})

defineExpose({
  refEditTop,
  refEditBottom,
  tempInfo,
  EditTop,
  EditBottom,
  EditOutline,
  BlankSetting,
})
</script>

<style lang='scss'>
.edit_template_wrap {
  overflow-y: auto;
  height: calc(100vh - 127px);
}
</style>