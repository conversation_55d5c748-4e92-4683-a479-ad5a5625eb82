<template>
    <span class="user-icon">
        <img width="40" v-if="props.photo" :src="props.photo" alt="" />
        <i v-if="!props.photo && !!props.name" class="ui_class">
            {{ props.name.slice(0, 1) }}
        </i>
        <span v-if="isShowName" class="name">{{ props.name }}</span>
    </span>
</template>

<script setup>
const props = defineProps(['name', 'photo', 'showname'])
const isShowName = ref(props.showname)

const showName = (status) => {
    isShowName.value = status
}

defineExpose({
    props, showName
})

</script>

<style lang="scss" scoped>
.user-icon {
    .ui_class {
        display: inline-block;
        font-style: normal;
        border: 1px solid black;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        background: #2b3139;
        margin-right: 10px;
        color: #fff;
        vertical-align: unset;
    }

    img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        vertical-align: middle;
        margin-right: 10px;
        object-fit: cover;
    }

    .name {
        display: inline-block;
        vertical-align: middle;
    }
}
</style>