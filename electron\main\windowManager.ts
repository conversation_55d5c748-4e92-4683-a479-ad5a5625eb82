import { BrowserWindow } from 'electron';

interface WindowInfo {
    window: <PERSON>rowserWindow;
    id: string;
}
let windowManager: WindowManager | null = null;
class WindowManager {
    private windows: Map<string, WindowInfo> = new Map();
    private nonCloseableWindows: Set<string> = new Set();

    getWindow(id: string = 'main'): WindowInfo | undefined {
        return this.windows.get(id);
    }

    addWindow(window: BrowserWindow, id: string = 'main') {
        this.windows.set(id, { window, id });
    }

    getAllWindows(): Map<string, WindowInfo> {
        return this.windows;
    }

    getAllWindowNames(from: string = '') {
        let allWindows = []
        this.windows.forEach((win) => {
            allWindows.push(win.id)
        })
        console.log('printAllWindows', from, allWindows)
        return allWindows
    }

    removeWindow(name: string) {
        this.windows.delete(name)
    }


    hideWindow(name: string) {
        console.log("hideWindow", name)
        const win = this.getWindow(name);
        if (win && !win.window.isDestroyed()) {
            win.window.hide()
        }
    }

    closeWindow(name: string) {
        console.log("closeWindow", name)
        const win = this.getWindow(name);
        if (win && !win.window.isDestroyed()) {
            if (!win.window.isClosable()) {
                win.window.setClosable(true)
            }
            win.window.close()
            this.windows.delete(name)
        }
    }

    closeAllWindows() {
        this.windows.forEach((win) => {
            if (!win.window.isDestroyed()) {
                win.window.close()
            }
        })
    }

    minimizeWindow(name: string) {
        const win = this.getWindow(name);
        if (win && !win.window.isDestroyed()) {
            win.window.minimize()
        }
    }

    updateCloseable(name: string, closeable: boolean) {
        const win = this.getWindow(name);
        if (win && !win.window.isDestroyed()) {
            win.window.setClosable(closeable)
            if (closeable) {
                this.nonCloseableWindows.delete(name);
            } else {
                this.nonCloseableWindows.add(name);
            }
        }
    }

    isWindowCloseable(name: string): boolean {
        return !this.nonCloseableWindows.has(name);
    }

    updateUserAgent(name: string, userAgent: string) {
        const win = this.getWindow(name);
        if (win && !win.window.isDestroyed()) {
            win.window.webContents.userAgent = userAgent
        }
    }

    sendMessage(data: { to: string, message: any }) {
        const win = this.getWindow(data.to)
        let status = 'ok'
        if (win && !win.window.isDestroyed()) {
            win.window.webContents.send('forward_message', data.message)
        } else {
            status = 'error'
        }
        console.log('sendMessage result:', status, ' data:', data)
    }
}
if (!windowManager) {
    windowManager = new WindowManager();
}

export default windowManager;
