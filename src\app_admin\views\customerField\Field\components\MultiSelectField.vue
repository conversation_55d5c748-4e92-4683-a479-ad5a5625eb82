<template>
    <el-form-item label-width="0">
        <div class="options-container">
            <div v-for="(option, index) in localOptions" :key="index" class="option-item">
                <span class="option-index">{{ index + 1 }}</span>
                <el-input v-model="localOptions[index]" placeholder="请输入选项内容" :maxlength="50" @input="updateOptions">
                    <!-- <template #append>
                        <span class="char-count">{{ option ? option.length : 0 }}/50</span>
                    </template> -->
                </el-input>
                <el-icon @click="removeOption(index)" class="delete-btn">
                    <Delete />
                </el-icon>
            </div>
            <div class="add-option" v-if="localOptions.length < 50" @click="addOption">
                + 添加选项 ({{ localOptions.length }}/50)
            </div>
        </div>
    </el-form-item>
    <el-form-item label="最多选择数量">
        <el-input-number v-model="localMultiSelectLimit" :min="1"></el-input-number>
    </el-form-item>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const localOptions = ref(props.modelValue.fieldOptions || [''])
const localMultiSelectLimit = ref(props.modelValue.multiSelectLimit)

watch(() => props.modelValue.fieldOptions, (newVal) => {
    localOptions.value = newVal
}, { deep: true })

const addOption = () => {
    if (localOptions.value.length < 50) {
        localOptions.value.push('')
        updateOptions()
    }
}

const removeOption = (index) => {
    if (localOptions.value.length <= 1) {
        ElMessage.warning('至少需要保留一个选项')
        return
    }
    localOptions.value.splice(index, 1)
    updateOptions()
}

const updateOptions = () => {
    emit('update:modelValue', {
        ...props.modelValue,
        fieldOptions: localOptions.value,
        multiSelectLimit: localMultiSelectLimit.value
    })
}
</script>

<style lang="scss" scoped>
// .options-container {
//     .option-item {
//         display: flex;
//         align-items: center;
//         gap: 10px;
//         margin-bottom: 10px;

//         .option-index {
//             min-width: 20px;
//         }

//         .el-input {
//             width: 454px;
//         }

//         .char-count {
//             font-size: 12px;
//             color: #999;
//             margin: 0 8px;
//         }

//         .delete-btn {
//             cursor: pointer;
//             color: #8C8C8C;
//         }
//     }

//     .add-option {
//         margin-top: 10px;
//         color: #436BFF;
//     }
// }</style>