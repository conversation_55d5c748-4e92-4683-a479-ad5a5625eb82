<template>
    <div class="home_title">
        <img :src="getAssetUrl(img)" />
        <div class="status">{{ title }}</div>
    </div>
</template>

<script setup>
import { getAssetUrl } from "@/js/utils";

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    img: {
        type: String,
        default: ''
    }
})

</script>

<style lang="scss" scoped>
.home_title {
    display: flex;
    flex-direction: row;
    width: 315px;
    height: 20px;
    background: linear-gradient(135deg, #F0F6FF 0%, rgba(240, 246, 254, 0) 100%);
    border-radius: 8px 8px 0px 0px;
    padding: 10px;
    margin-bottom: 15px;

    img {
        width: 14px;
        height: 14px;
        margin-right: 10px;
        margin-top: 3px;
    }

    .status {
        width: 48px;
        height: 20px;
        font-size: 12px;
        color: #595959;
        line-height: 20px;
        text-align: left;
    }
}
</style>
