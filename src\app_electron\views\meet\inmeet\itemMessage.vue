<template>
    <div class="tw_message">
        <div class="message_header">
            <div class="person_icon" :style="{ background: getSpeakerBgColor(), color: getSpeakerColor() }">
                <PersonIcon />
            </div>
            <span>{{ getSpeakerName() }}</span>
            <el-icon class="edit_icon" @click="handleEdit" v-if="false">
                <EditIcon />
            </el-icon>
            <span>{{ msg.showTime }}</span>
        </div>
        <p>{{ msg.asr_result || msg.asr_result_changed }}</p>
    </div>
</template>

<script setup>
import EditIcon from '@/icons/edit.vue'
import PersonIcon from "@/app_electron/icons/person.vue"

const props = defineProps({
    msg: {
        type: Object,
        required: true
    }
})

const personColors = [
    "#72D5FF",
    "#D9A5FF",
    "#FFB49E",
    "#8BAFFF",
    "#79CE83",
    "#FF97F3",
    "#FFC63F",
    "#72C0FF",
    "#A5AAFF",
    "#40E4FF",
]

const personBgColors = [
    "#E5F8FF",
    "#F8EEFF",
    "#FFF3EF",
    "#EAF1FF",
    "#E6F6E8",
    "#FFEBFD",
    "#FFF3D6",
    "#E5F4FF",
    "#EEEFFF",
    "#D9FAFF"
];

const getSpeakerColor = () => {
    const { speaker } = props.msg
    // 将speaker转为数字，并确保索引在personColors数组范围内
    const speakerNum = parseInt(speaker) || 0
    const colorIndex = (speakerNum - 1) % personColors.length
    return personColors[colorIndex >= 0 ? colorIndex : 0]
}

const getSpeakerBgColor = () => {
    const { speaker } = props.msg
    // 将speaker转为数字，并确保索引在personColors数组范围内
    const speakerNum = parseInt(speaker) || 0
    const colorIndex = (speakerNum - 1) % personBgColors.length
    return personBgColors[colorIndex >= 0 ? colorIndex : 0]
}

const getSpeakerName = () => {
    const { speaker } = props.msg
    const renameData = g.meetStore.renameData
    if (renameData?.[speaker]) {
        return renameData[speaker].name
    }
    return '发言人' + speaker
}

const handleEdit = (row) => {
    //  {"asr_result":"喂喂喂。","seq":2,"time":850,"begin_time":12820,"speaker":"1","showTime":"00:18"}
    console.log('edit', JSON.stringify(toRaw(props.msg)))
    const msg = {
        speaker: props.msg.speaker,
        name: getSpeakerName(),
        isQuickMeeting: g.meetStore.isQuickMeeting,
        confId: g.meetStore.conferenceId
    }
    g.electronStore.openWin("rename_inmeet", msg);
}
</script>

<style scoped lang="scss">
.tw_message {
    padding: 5px 10px;

    &:not(:last-child) {
        border-bottom: 1px solid #ccc;
    }

    .message_header {
        display: flex;
        align-items: center;

        .person_icon {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            margin-right: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        span {
            font-size: 12px;
            color: #8C8C8C;
            line-height: 24px;
            margin: 0 5px;
        }
    }

    p {
        margin: 5px 0;
        font-size: 14px;
        color: #262626;
    }
}
</style>