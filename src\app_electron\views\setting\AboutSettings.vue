<template>
  <div class="about-settings flex-col flex-center">
    <img :src="getAssetUrl('logo2.svg')" class="logo" @click="onMagicClick('logo')" />
    <div class="setting-item" @click="onMagicClick('version')">版本号:{{ version }}
      <el-tag v-if="!isProdMode" class="blue_tag">Blue</el-tag>
    </div>
    <div class="setting-item" v-show="devToolsEnable && buildTime">版本时间:{{ buildTime }} </div>
    <el-button class="check-update-btn" type="primary" @click="checkUpdate" :disabled="hasChecked">
      {{ checkBtnTxt }}</el-button>
    <div class="copyright-container flex-col flex-center">
      <div class="copyright">江苏绚星智慧科技有限公司 @2011-{{ new Date().getFullYear() }}</div>
      <div class="copyright">All rights reserved</div>
    </div>
  </div>
</template>

<script setup>
import { getAssetUrl } from "@/js/utils.js"
import { ElMessage } from 'element-plus'

const _formatBuidTime = (dateString) => {
  // const dateString = "2025-05-28T09:03:13.919Z"; // 使用短横线
  const formattedDateString = dateString.replace(/\//g, '-');
  const date = new Date(formattedDateString);
  // 加上 8 小时
  const localDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
  let formattedDate = dateString
  if (!isNaN(localDate)) {
    // 格式化为 yyyy-mm-dd hh:mm:ss
    formattedDate = localDate.toISOString().replace('T', ' ').substring(0, 19);
    console.log(formattedDate);
  } else {
    console.error("Invalid date format");
  }
  return formattedDate
}

const version = ref('')
const hasChecked = ref(false)
const checkBtnTxt = ref("检查更新")
const clickCount = ref(0)
const clickTimer = ref(null)
const isProdMode = ref(true)
const a = "2025/05/28T09:03:13.919Z"
const buildTime = ref(_formatBuidTime(a))
const devToolsEnable = ref(false)

const checkUpdate = () => {
  g.electronStore.checkUpdateBase(true).then((res) => {
    console.log("checkUpdate res", res)
    hasChecked.value = true
    if (!res) {
      checkBtnTxt.value = "已经是最新版本"
    }
  }).catch(() => {
    ElMessage.error("检查更新出错，请稍后再试")
  })
}

const onMagicClick = (type) => {
  clickCount.value++

  // 清除之前的定时器
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
  }

  // 设置新的定时器，1秒后重置点击次数
  clickTimer.value = setTimeout(() => {
    clickCount.value = 0
  }, 1000)

  // 判断是否达到5次点击
  if (clickCount.value === 5) {
    if (type === 'logo') {
      g.electronStore.toggleDevTools().then((isOpen) => {
        devToolsEnable.value = isOpen;
        const status = isOpen ? '启用' : '关闭';
        localStorage.setItem('devToolsEnable', isOpen);
        ElMessage.success(`已${status}开发者模式`)
      })
    } else if (type === 'version') {
      if (g.config.apiEnv == 'prod') {
        g.electronStore.toggleProdHost().then((isProd) => {
          isProdMode.value = isProd;
          ElMessage.success(`已切换成${isProd ? '正式' : 'Blue'}环境`)
        })
      }
    }

    clickCount.value = 0
  }
}

const initBlueTag = () => {
  if (g.config.apiEnv == 'prod') {
    isProdMode.value = g.electronStore.getProdIsProd();
  }
}

const versionVersionTime = async () => {
  try {
    const versionInfo = await g.electronStore.getLocalVersion()
    if (versionInfo.code == 0) {
      buildTime.value = _formatBuidTime(versionInfo.buildTime);
    }
  } catch (e) {
    console.log(e)
  }
}

onMounted(async () => {
  devToolsEnable.value = localStorage.getItem('devToolsEnable') == 'true';
  version.value = await g.electronStore.getAppVersion();
  initBlueTag()
  if (devToolsEnable.value) {
    versionVersionTime()
  }
})

defineExpose({
  version,
  checkUpdate,
})
</script>

<style lang="scss" scoped>
.about-settings {
  .logo {
    width: 196.8px;
    height: 32px;
    margin-top: 72px;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    margin-top: 20px;

    .blue_tag {
      margin-left: 12px;
    }
  }

  .copyright-container {
    margin-top: 16px;
    position: fixed;
    bottom: 26px;

    .copyright {
      font-size: 14px;
      color: #8C8C8C;
      line-height: 22px;
    }
  }


  .clickable {
    cursor: pointer;
  }

  .clickable:hover {
    background-color: #f5f5f5;
  }

  .check-update-btn {
    margin-top: 20px;
  }

  .update-hint {
    margin-top: 20px;
  }

  .update-info {
    margin-top: 20px;
    border: 1px solid #eee;
    padding: 10px;
    border-radius: 5px;
    background-color: #f5f5f5;
    height: 260px;
    overflow-y: auto;
  }

  .update-btn {
    margin-top: 10px;
  }
}
</style>