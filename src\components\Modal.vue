<template>
  <el-dialog v-model="dialogVisible" :title="cfg.title" :width="cfg.width" :before-close="onCancel">
    <slot />
    <template v-if="!cfg.hide_footer" #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
const defaultCfg = {
  title: "",
  width: "40%",
  hide_footer: false,
};

const cfg = ref({ ...defaultCfg });
const emit = defineEmits(["callback"]);
const dialogVisible = ref(false);

const onCancel = () => {
  dialogVisible.value = false;
  emit("callback", "cancel");
};
const onConfirm = () => {
  emit("callback", "confirm");
};

const show = (_cfg) => {
  cfg.value = { ...defaultCfg, ..._cfg };
  dialogVisible.value = true;
};

const hide = () => {
  dialogVisible.value = false;
};

defineExpose({
  show,
  cfg,
  dialogVisible,
  onCancel,
  onConfirm,
  hide,
});
</script>
<style lang="scss">
.dialog-footer button:first-child {
  margin-right: 10px;
}

.el-dialog__header {
  border-bottom: 1px solid #e9e9e9;
  margin-right: 0;
}

.el-dialog__footer {
  border-top: 1px solid #e9e9e9;
  padding-top: 20px;
}
</style>
