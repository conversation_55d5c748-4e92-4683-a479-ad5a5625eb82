<template>
  <div class="course-catalog">
    <!-- 进度条 -->
    <div class="progress-wrapper flex-row">
      <el-progress
        :percentage="progress"
        :show-text="false"
        :stroke-width="6"
        class="custom-progress"
        :color="'#436BFF'"
        :track-color="'#E1E6F0'"
      />
      <div class="progress-text flex-row">
        <div class="progress-text-main">{{ progress }}%</div>
        <div class="progress-text-sub">({{ completedCount }}/{{ totalCount }}任务)</div>
      </div>
    </div>

    <!-- 章节列表 -->
    <div class="chapter-list">
      <div
        v-for="(chapter, chapterIndex) in catalogList"
        :key="chapterIndex"
        class="chapter-item"
      >
        <!-- 章节标题区域 -->
        <div class="chapter-header" @click="toggleChapter(chapterIndex)">
          <img
            class="arrow-icon"
            :class="{ expanded: expandedChapters[chapterIndex] }"
            :src="getOssUrl('cp_down_arrow.png')"
          />
          <div class="chapter-title">{{ chapter.chapterName }}</div>
        </div>

        <!-- 课程列表 -->
        <div class="lesson-list" v-show="expandedChapters[chapterIndex]">
          <div
            v-for="(lesson, lessonIndex) in chapter.kngList"
            :key="lessonIndex"
            class="lesson-item"
            :class="{ active: currentLesson?.id === lesson.id }"
            @click="handleLessonClick(lesson)"
          >
            <div class="lesson-info">
              <span class="lesson-name">{{ lesson.chapterName }}</span>
              <img class="status-icon" :src="getOssUrl(`cp_${lesson.status}.png`)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { getOssUrl } from "@/js/utils";
import { ElMessage } from "element-plus";

const emit = defineEmits(["callback"]);
const currentLesson = ref(null);
const catalogList = ref([]);
const expandedChapters = ref({});

// 计算进度
const totalCount = computed(() => {
  return catalogList.value.reduce((total, chapter) => total + chapter.kngList.length, 0);
});
// 学习状态 0:未学习 1:学习中 2:学习完成
const completedCount = computed(() => {
  return catalogList.value.reduce((total, chapter) => {
    return total + chapter.kngList.filter((lesson) => lesson.status === 2).length;
  }, 0);
});

const progress = computed(() => {
  return Math.round((completedCount.value / totalCount.value) * 100) || 0;
});

const toggleChapter = (index) => {
  expandedChapters.value[index] = !expandedChapters.value[index];
};

const handleLessonClick = (lesson) => {
  currentLesson.value = lesson;
  emit("callback", "click_lesson", lesson);
};

const init = (data) => {
  catalogList.value = data;
  // 默认选中第一个未完成的课程
  if (data && data.length > 0) {
    let firstUncompletedLesson = null;
    let found = false;
    for (const chapterIdx in data) {
      const chapter = data[chapterIdx];
      firstUncompletedLesson = chapter.kngList.find((lesson) => lesson.status !== 2);
      if (firstUncompletedLesson) {
        currentLesson.value = firstUncompletedLesson;
        expandedChapters.value[chapterIdx] = true;
        found = true;
        emit("callback", "click_lesson", firstUncompletedLesson);
        break;
      }
    }
    if (!found) {
      const firstLesson = data[0].kngList[0];
      currentLesson.value = firstLesson;
      emit("callback", "click_lesson", firstLesson);
    }
  } else {
    ElMessage.error("课程数据为空");
  }
};

const updateCatalogStatus = (kngId, status) => {
  // 遍历所有章节
  catalogList.value.forEach((chapter) => {
    // 在每个章节的课程列表中查找对应的课程
    chapter.kngList.forEach((lesson) => {
      if (lesson.id === kngId && lesson.status !== 2) {
        // 更新状态 (0:学习中, 1:已完成)
        lesson.status = status;
      }
    });
  });
};

defineExpose({
  init,
  updateCatalogStatus,
});
</script>

<style lang="scss" scoped>
.course-catalog {
  $border-color: #ebeef5;
  $primary-color: #436bff;
  $hover-bg: #f7f8fa;
  $active-bg: #f0f6ff;
  $text-primary: #262626;
  $text-secondary: #666;

  width: 300px;
  flex-shrink: 0;
  height: 100%;
  border-right: 1px solid $border-color;
  overflow-y: auto;

  // 进度条区域
  .progress-wrapper {
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid $border-color;
    display: flex;
    align-items: center;
    gap: 12px;

    .progress-text {
      font-size: 14px;
      color: $text-primary;
      white-space: nowrap;

      .progress-text-main {
        font-weight: 500;
      }

      .progress-text-sub {
        margin-left: 12px;
        color: $text-secondary;
      }
    }
  }
  .el-progress {
    width: 100px;
    display: block;
  }

  // 章节区域
  .chapter {
    &-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      background-color: $hover-bg;
    }

    &-title {
      font-size: 16px;
      color: $text-primary;
      font-weight: 500;
    }
  }

  // 箭头图标
  .arrow-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    transition: transform 0.3s;

    &.expanded {
      transform: rotate(-180deg);
    }
  }

  // 课程列表
  .lesson {
    &-item {
      padding: 12px 16px 12px 40px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: $hover-bg;
      }

      &.active {
        background-color: $active-bg;

        .lesson-name {
          color: $primary-color;
        }
      }
    }

    &-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &-name {
      font-size: 14px;
      color: $text-secondary;
    }
  }

  .status-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
