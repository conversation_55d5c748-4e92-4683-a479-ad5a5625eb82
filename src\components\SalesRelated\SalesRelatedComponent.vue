<template>
    <div class="sale_related_wrap">
        <div class="vbp_title vbp1">
            关联售卖商品类别
        </div>
        <div class="vbp_value">
            <el-radio-group v-model="localParam.salesRelatedType" class="use-person-mk-radio"
                @change="onChangeGoodsType">
                <el-radio :value="1">选择商品大类</el-radio>
                <el-radio :value="2">选择具体商品</el-radio>
            </el-radio-group>
            <div class="gt_hint">
                {{ localParam.salesRelatedType == 1 ? '当首次接触客户或向客户推荐的商品尚未明确时选择' : '当本次沟通有明确的推荐商品时选择' }}
            </div>
            <ul class="tags_item" v-if="localParam.salesRelatedType == 1">
                <li v-for="(item, index) in tags['categories']" :key="item" @click="onClickMutli(item)"
                    :class="getClass2(item)">
                    {{ item }}
                </li>
            </ul>
            <div v-else class="sale_goods_box" @click="onShowSelectGoods">
                <div class="sg_left">
                    <div v-if="saleGoodsHintList.length == 0" class="onlytxt">
                        选择售卖商品
                    </div>
                    <div v-else>
                        <el-tag v-for="item in saleGoodsHintList" closable @close="handleCloseTag(item)" :key="item">{{
                            item }}</el-tag>
                    </div>
                </div>
                <div class="sg_icon">
                    <el-icon>
                        <More />
                    </el-icon>
                </div>
            </div>
        </div>
    </div>
    <DialogChooseSaleGoods ref="refGoodsModal" @callback="cbGoodsModal" />
</template>

<script setup>
import { More } from '@element-plus/icons-vue'
import DialogChooseSaleGoods from "@/components/SalesRelated/DiaChooseSaleGoods.vue"

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
})
const saleGoodsData = ref([])
const saleGoodsHintList = ref([])
const refGoodsModal = ref()
const localParam = ref(props.modelValue)
const emit = defineEmits(['update:modelValue', 'callback'])
const tags = ref([])
const currItem = ref({ salesRelated: false })

watch(localParam, (newValue) => {
    emit('update:modelValue', newValue)
}, { deep: true })

const getClass2 = (item) => {
    let cname = 'txt';
    if (localParam.value && localParam.value.salesGoodsCategories) {
        const temp = localParam.value.salesGoodsCategories.split(',').filter(x => !!x);
        if (temp.includes(item)) {
            cname += " choose";
        }
    }
    return cname;
}

const onClickMutli = (label) => {
    const datas = localParam.value.salesGoodsCategories.split(',').filter(x => !!x)
    const isHas = datas.includes(label)
    if (isHas) {
        datas.splice(datas.indexOf(label), 1)
    } else {
        datas.push(label)
    }
    localParam.value.salesGoodsCategories = datas.join(',');
    emit('update:modelValue', localParam.value)
}

const onShowSelectGoods = () => {
    const categories = localParam.value.salesGoodsCategories.split(',').filter(x => !!x)
    const param = { topicId: currItem.value.id, preGoodsIds: categories }
    if (g.config.isElectron) {
        g.electronStore.openWin('choose_salegoods', param)
    } else {
        refGoodsModal.value.show(param);
    }
}

const cbGoodsModal = (action, data) => {
    if (action == 'confirm') {
        if (Array.isArray(data)) {
            saleGoodsData.value = data;
            localParam.value.salesGoodsCategories = data.map(x => x.id).join(',');
            if (data.length > 0) {
                saleGoodsHintList.value = data.map(x => x._label)
            } else {
                saleGoodsHintList.value = [];
            }
            emit('callback', 'saleGoodsHintList', toRaw(saleGoodsHintList.value))
        } else {
            console.log('wrong data type?', data)
        }
    } else {
        console.log('new action?', action)
    }
}

const handleCloseTag = (tag) => {
    saleGoodsData.value = saleGoodsData.value.filter(x => x._label != tag);
    saleGoodsHintList.value = saleGoodsHintList.value.filter(x => x !== tag);
}

const onChangeGoodsType = () => {
    localParam.value.salesGoodsCategories = '';
    saleGoodsData.value = [];
    saleGoodsHintList.value = [];
    emit('update:modelValue', localParam.value)
}

const setTags = (v) => {
    tags.value = v;
}

const setCurrItem = (x) => {
    currItem.value = x;
}

const setModelValue = (x) => {
    localParam.value = x;
}

// 添加 setSelectedTags 方法
const setSelectedTags = (tags) => {
    if (localParam.value.salesRelatedType === 2 && tags?.length > 0) {
        saleGoodsData.value = tags;
        saleGoodsHintList.value = tags.map(x => x.label || x._label);
        emit('callback', 'saleGoodsHintList', toRaw(saleGoodsHintList.value));
    }
}

onMounted(() => {
    if (g.config.isElectron) {
        g.ipcRenderer.on('forward_message', (_, { action, data }) => {
            cbGoodsModal(action, data)
        })
    }
})

defineExpose({
    tags, setTags, currItem, setCurrItem, refGoodsModal, cbGoodsModal, DialogChooseSaleGoods,
    setModelValue, setSelectedTags, saleGoodsData
})
</script>

<style lang="scss">
.vbp_value {
    ul.tags_item {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 0;

        li {
            list-style: none;
        }

        li.txt {
            min-width: 96px;
            padding: 0 18px;
            height: 32px;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #D9D9D9;
            text-align: center;
            line-height: 32px;
            margin: 10px 5px 0 5px;
            cursor: pointer;
        }

        .choose {
            border: 1px solid #436BFF;
            color: #436BFF;
        }
    }

    .vbp1 {
        margin-top: 12px;
    }

    .sale_goods_box {
        width: 94%;
        border: 1px solid #D9D9D9;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 4px 10px;
        border-radius: 4px;
        margin-top: 12px;
        cursor: pointer;
        min-height: 40px;

        .sg_left {
            flex: 1;
            overflow: hidden;

            .onlytxt {
                color: #bbb;
                line-height: 32px;
            }

            .el-tag {
                margin: 4px 5px 4px 0;
                max-width: 250px;

                .el-tag__content {
                    max-width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }

        .sg_icon {
            width: 20px;
            display: flex;
            align-items: center;
        }
    }
}
</style>
