import { defineStore } from 'pinia'
import { Base64 } from "js-base64";
import { getUser, openWindow, getStore, setStore, randomId, clearStore, jsOpenNewWindow } from "@/js/utils.js"
import { defaultSettings } from "@/app_electron/tools/config.js"
import { ConstValue } from "@/js/const_value"
import { toRaw } from "vue"
import customProtocolCheck from "custom-protocol-check";
import electronRouter from '@/app_electron/tools/router'
import { getWinSizeFromRouter } from "@/app_electron/tools/utils";

const ElectronStore = defineStore('electron', {
  state: () => ({
    settings: { ...defaultSettings, ...getStore(ConstValue.keySettings, {}) },
    isNetworkConnected: true,
    appVersion: '',
  }),
  actions: {
    getStore(key, defaultValue = '') {
      return new Promise((resolve) => {
        g.ipcRenderer.invoke('getStore', key).then((res) => {
          resolve(JSON.parse(res));
        }).catch(() => {
          resolve(defaultValue);
        });
      });
    },
    setIsNetworkConnected(isConnected) {
      this.isNetworkConnected = isConnected;
    },
    openUrl(url, newPageData = {}, id = '') {
      const user = getUser();
      const urlParam = {
        id: id || url,
      }
      if (url.indexOf('http') > -1) {
        urlParam.url = url
      } else if (url.indexOf('electron') > -1) {
        urlParam.url = url
        urlParam.id = url.split('/')[2]
      } else {
        if (user && user.token) {
          const token = user.token.replace("Bearer2 ", '');
          urlParam.url = `${g.config.publicPath}/#auth?id=${token}&to=${url}`
        } else {
          urlParam.url = `${g.config.publicPath}/#${url}`
        }
      }
      console.log('urlParam', urlParam)
      openWindow({ urlParam, newPageData })
    },
    createWindows(openParam) {
      g.ipcRenderer.send('create_window', JSON.stringify(openParam));
    },
    setStore(key, value) {
      g.ipcRenderer.send('setStore', key, value)
    },
    openWin(winName, newPageData = {}) {
      this.openUrl('/electron/' + winName, newPageData, winName)
    },
    closeWin(winName) {
      g.ipcRenderer.send('close_window', winName);
    },
    hideWin(winName) {
      g.ipcRenderer.send('hide_window', winName);
    },
    minimizeWin(winName) {
      g.ipcRenderer.send('minimize_window', winName);
    },
    sendMessage(winName, action, data = {}) {
      g.ipcRenderer.send('forward_message', { to: winName, message: { action, data } });
    },
    updateSetting(itemkey, value) {
      this.settings[itemkey] = value
      setStore(ConstValue.keySettings, this.settings)
    },
    toggleDevTools() {
      return g.ipcRenderer.invoke('toggle_dev_tools');
    },
    getProdIsProd() {
      const prodApiHost = g.appStore.getStore(g.cv.keyProdApihost, import.meta.env.VITE_WEB_URL);
      return prodApiHost.indexOf('blue') == -1;
    },
    toggleProdHost() {
      //切换产线的正式环境或blue环境
      return new Promise((resolve, reject) => {
        const preIsProd = this.getProdIsProd()
        const url = import.meta.env.VITE_WEB_URL;
        const web_url = preIsProd ? url.replace('www', 'blue') : url;
        const targetIsProd = !preIsProd;
        this.setStore("isBlue", preIsProd);
        g.appStore.setStore(g.cv.keyProdApihost, web_url);
        resolve(targetIsProd)
      })
    },
    joinMeeting(plan) {
      if (window.nodeRequire != undefined) {
        this.openWin('meet', plan)
      } else {
        console.log('openUrl')
        const url = `yxtnovaguide://action=meet&data=${Base64.encode(JSON.stringify(plan))}`
        const userAgent = navigator.userAgent
        const isEdge = userAgent.indexOf("Edg") > -1;
        if (isEdge) {
          location.href = url;
          this.showDownAppDialog();
        } else {
          customProtocolCheck(url, () => {
            console.log("Custom protocol not found.");
            this.showDownAppDialog();
          }, () => {
            console.log("Custom protocol found and opened the file successfully.");
          }, 2000, () => {
            console.log("Custom protocol found but failed to open the file.");
            this.showDownAppDialog();
          });
        }
      }
    },
    showDownAppDialog() {
      ElMessageBox.confirm(
        '如绚星销售助手客户端启动失败，请先下载安装。',
        '提示',
        {
          confirmButtonText: '去下载',
          cancelButtonText: '关闭',
          type: 'warning',
        }
      )
        .then(() => {
          jsOpenNewWindow('/#/download');
        })
        .catch(() => {
        })
    },
    waitPageReady(cmdName = 'pageReady') {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Page not ready'))
        }, 5000)
        const rid = randomId()
        g.ipcRenderer.once('page-opened', (_, { id, data }) => {
          console.log('premeet Received data:', id, JSON.stringify(toRaw(data)));
          if (id === rid) {
            timeout && clearTimeout(timeout)
            resolve(data)
          }
        });
        g.ipcRenderer.send(cmdName, rid);
      })
    },
    checkUpdateBase(isForce) {
      return new Promise((resolve, reject) => {
        g.ipcRenderer.invoke("update-check").then((res) => {
          console.log("checkUpdateBase res", JSON.stringify(res))
          if (res.code === 0) {
            if (res.data.needUpdate) {
              const hasSkipVersion = g.appStore.getStore(g.cv.keySkipVersion);
              if (!isForce && hasSkipVersion == res.data.newVersion) {
                console.log('hasSkipVersion', hasSkipVersion)
                resolve(false)
              } else {
                this.openWin('upgrade', res.data)
                resolve(true)
              }
            } else {
              resolve(false)
            }
          } else {
            reject(new Error('检查更新出错，请稍后再试'))
          }
        })
      })
    },
    checkRecordingPath() {
      return new Promise((resolve, reject) => {
        const path = this.settings.recording.localRecordingPath
        g.ipcRenderer.invoke("check-recording-path", path).then((res) => {
          console.log("checkRecordingPath res", res)
          this.settings.recording.localRecordingPath = res
          setStore(ConstValue.keySettings, this.settings)
          resolve(true)
        }).catch(() => {
          resolve(false)
        })
      })
    },
    getAppVersion() {
      return new Promise(async (resolve, reject) => {
        if (!this.appVersion) {
          try {
            this.appVersion = await g.ipcRenderer.invoke('get_app_version')
          } catch (e) {
            this.appVersion = g.config.version;
          }
        }
        resolve(this.appVersion)
      })
    },
    getMainScreenInfo() {
      return new Promise(async (resolve, reject) => {
        const info = await g.ipcRenderer.invoke('get-main-screen-info')
        setStore(ConstValue.keyMainScreenInfo, info)
        // {x: 0, y: 0, width: 1920, height: 1040}
        resolve(info)
      })
    },
    logout() {
      console.log('logout343')
      this.user = null;
      g.ipcRenderer.send('removeStore', 'userInfo');
      clearStore();
      g.ipcRenderer.send('close_all_windows');
      this.openWin('login')
    },
    updateWindowSize() {
      try {
        const formated = getWinSizeFromRouter(electronRouter)
        g.ipcRenderer.send('updateWindowSize', formated)
      } catch (e) {
        console.log('updateWindowSize error', e)
      }
    },
    getImageCache: (imageUrl) => g.ipcRenderer.invoke('get-image-cache', imageUrl),
    getLocalVersion() {
      return new Promise(async (resolve, reject) => {
        try {
          const url = location.href;
          if (url.startsWith('file://')) {
            const novaguideIndex = url.indexOf('novaguidepc');
            if (novaguideIndex > -1) {
              const basePath = url.substring(0, url.indexOf('/', novaguideIndex + 'novaguidepc'.length + 1));
              const versionUrl = `${basePath}/version.json`;
              console.log('versionUrl', versionUrl);
              const response = await fetch(versionUrl);
              const versionInfo = await response.json();
              versionInfo['code'] = 0;
              console.log('versionInfo', versionInfo);
              resolve(versionInfo);
            } else {
              resolve({ code: -1, error: '无法找到版本信息路径' });
            }
          } else {
            resolve({ code: -2, error: '开发环境不需要' });
          }
        } catch (error) {
          resolve({ code: -3, error });
        }
      });
    }
  },

})

export default ElectronStore;
