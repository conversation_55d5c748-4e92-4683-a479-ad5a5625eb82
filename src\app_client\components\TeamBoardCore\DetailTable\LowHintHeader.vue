<template>
  <div class="dept_header flex-row">
    <div>{{ title }}</div>
    <div class="vh_refer">
      <el-tooltip class="box-item" effect="dark" content="低于直属团队平均值" placement="top">
        <img :src="getOssUrl('client_board_low_hint.png')" />
      </el-tooltip>
    </div>
  </div>
</template>
<script setup>
import { getOssUrl } from "@/js/utils";

const props = defineProps(['data', 'title']);

defineExpose({
});
</script>

<style lang="scss">
.dept_header {
  justify-content: space-between;
  min-width: 200px;

  .vh_refer {
    margin-top: 2px;
    margin-left: 29px;
    cursor: pointer;
  }
}

.dept_popper {
  .vnh_pop_main {
    ol {
      list-style: decimal !important;
      padding-left: 20px;
      margin: 0;

      li {
        list-style: decimal !important;
        padding: 8px 0;

        .vp2 {
          .vp2_title {
            display: contents;
            align-items: baseline;
            gap: 4px;
            flex-wrap: wrap;
          }

          .vp2_name {
            font-family: "PingFang SC";
            font-weight: 600;
            font-size: 14px;
            color: #333;
          }

          .vp2_cn_name {
            font-family: "PingFang SC";
            font-weight: 600;
            font-size: 14px;
            color: #333;
          }

          .vp2_desc {
            font-size: 14px;
            color: #666;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
