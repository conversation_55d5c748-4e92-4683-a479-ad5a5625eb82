<template>
  <div>
    <div class="setuprole_wrap" v-if="isLoading || isGenReporting || isShowSetup">
      <div v-show="isLoading || isGenReporting">
        <loadingStep ref="refLoading" />
      </div>
      <div v-show="!isLoading && !isGenReporting" customClass="ai_load">
        <nodata v-if="lessData" />
        <div v-else-if="isHost">
          <div class="setup_note" v-if="show_hint">
            <div class="wran_icon">
              <WarningIcon />
            </div>
            <div class="setup_txt">
              拜访记录中所有发言人均需要标注身份，需标注后才可生成对应内容
            </div>
          </div>
          <Table ref="refTable" :cfg="tableCfg" class="role_table">
            <template #col_spokesman="{ row }">
              <div @click="onClickName(row)" class="link_txt">
                {{ row.name }}
              </div>
            </template>

            <template #col_type="{ row }">
              {{ getTypeName(row.type) }}
            </template>

            <template #col_operation="{ row }">
              <div @click="handleClick(row)" class="link_txt">
                {{ !!row.type ? "修改" : "标注" }}
              </div>
            </template>
          </Table>
          <el-button type="primary" size="default" class="gen_now_btn" @click="onGen('')"
            :disabled="disableGen || tableData.length == 0">
            立即生成
          </el-button>
        </div>
        <div v-else>
          <el-empty description="暂无内容"> </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from "vue";
import { generateSaleReport, getSaleReports } from "@/app_postmeet/tools/api";
import nodata from "@/app_postmeet/components/nodata.vue";
import WarningIcon from "@/app_postmeet/icons/warning.vue";
import loadingStep from "@/app_postmeet/components/loadingStep.vue";
import Table from "@/components/Table.vue";

const props = defineProps({
  show_hint: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
});

const lessData = ref(false);
const isShowSetup = ref(false);
const timer = ref(null);
const isHost = ref(false);
const disableGen = ref(true);
const tableData = ref([]);
const timeInterval = ref(1);
const refTable = ref(null);
const isLoading = ref(false);
const isGenReporting = ref(false);
const emit = defineEmits(["callback"]);

const tableCfg = reactive({
  show_pager: false,
  columns: ["spokesman", "type", "operation"],
  template: ["spokesman", "type", "operation"],
  data: [],
  need_header: false,
  show_link_column: false,
});

const init = () => {
  isShowSetup.value = g.postmeetStore.getIsShowSetup();
  isHost.value = !g.postmeetStore.isReadonly();
  tableData.value = g.postmeetStore.getRoleList();
  if (refTable.value) {
    refTable.value.update_data(tableData.value, tableData.value.length);
  }
  tableCfg.data = tableData.value;
  lessData.value =
    g.postmeetStore.data.words_count < g.postmeetStore.data.min_words_count;
  disableGen.value = tableData.value.filter((x) => !x.type).length > 0;
  const status = g.postmeetStore.data.saleReportStatus;
  const allowShowHint = status && !["SUCCESS", "IN_PROCESS"].includes(status);

  if (isHost.value && !disableGen.value && allowShowHint && !g.postmeetStore.lessData()) {
    g.emitter.emit("show_index_hint", [
      "setup_role_complete",
      "发言人身份已标注完成，是否立即生成？",
    ]);
  }
};

const onClickName = (row) => {
  g.emitter.emit("clickTag", [row.name, "username"]);
};

const getTypeName = (type) => {
  const cmap = {
    customer: "客户角色",
    internal: "内部伙伴",
  };
  return Object.keys(cmap).includes(type) ? cmap[type] : "- -";
};

const handleClick = (item) => {
  g.emitter.emit("showSaleRename", toRaw(item));
};

const _monitorReportStatus = () => {
  function fn() {
    !!timer.value && clearInterval(timer.value);
  }
  fn();
  isLoading.value = true;
  emit("callback", "loading", true);
  timer.value = setInterval(() => {
    timeInterval.value += 1;
    getSaleReports(g.postmeetStore.data.confId).then((resp) => {
      if (resp.code == 0) {
        const { status, complete } = resp.data;
        if (status == "SUCCESS" || status == "FAIL") {
          g.postmeetStore.setValue("isGenReporting", false);
          g.emitter.emit("is_re_gen_sale_report", false);
          isLoading.value = false;
          isShowSetup.value = false;
          emit("callback", "loading", false);
          if (status == "SUCCESS") {

            location.reload();
          }
          fn();
        } else {
          g.emitter.emit("update_report_complete_process", complete);
          _monitorReportStatus();
        }
      }
    });
  }, 10000);
};

const onGen = (dimensionId = "") => {
  if (disableGen.value) {
    return;
  }
  disableGen.value = true;
  const sentences = g.postmeetStore.data.subtitles.map((x) => {
    return {
      content: x.txt,
      startTime: x.time,
      username: x.name,
    };
  });
  const param = { dimensionId, sentences, forceRenew: true };
  generateSaleReport(g.postmeetStore.data.confId, param).then((resp) => {
    if (resp.code == 0) {
      g.postmeetStore.setValue("allowSaleReGen", false);
      g.emitter.emit("is_re_gen_sale_report", true);
      _monitorReportStatus();
    }
  });
};

onMounted(() => {
  init();
  g.emitter.on("updatedAsrContent", () => {
    init();
  });
  g.emitter.on("after_update_sale", () => {
    init();
  });
  g.emitter.on("is_re_gen_sale_report", (status) => {
    isGenReporting.value = status;
  });
  g.emitter.on("re_gen_sale_report_" + props.type, (id = "") => {
    // 当前组件在页面多有多次注册，为了防止收到消息后，同时触发多次
    const time = Math.round(100 * Math.random());
    setTimeout(() => {
      if (g.postmeetStore.data.isGenReporting) {
        return;
      }
      g.postmeetStore.setValue("isGenReporting", true);
      g.emitter.emit("is_re_gen_sale_report", true);
      isLoading.value = true;
      isShowSetup.value = true;
      nextTick(() => {
        onGen(id);
      });
    }, time);
  });
  g.emitter.on("monitor_sale_report", () => {
    isLoading.value = true;
    isShowSetup.value = true;
    nextTick(() => {
      _monitorReportStatus();
    });
  });
});
defineExpose({
  init,
  refTable,
});
</script>

<style lang="scss">
@use './style.scss';
</style>
