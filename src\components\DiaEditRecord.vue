<template>
  <el-dialog v-model="dialogVisible" title="修改沟通信息" width="500" :before-close="handleClose" class="vis_batch_edit_dia">
    <div class="bve_main">
      <div class="vbp_box">
        <div class="vbp_title">
          沟通关联客户<span>*</span>
        </div>
        <div class="vbp_value">
          <CustomerNamePicker v-model="param.salesMateCustomerName" v-model:customerId="param.customerId" />
        </div>
        <div class="vbp_title">
          主题标签<span>*</span>
        </div>
        <TagPicker v-model="param" ref="refTagPicker" @change="onTagChange" />
        <div class="vbp_title">
          沟通主题<span>*</span>
        </div>
        <div class="vbp_value">
          <el-input type="text" v-model="param.subject" placeholder="请输入" />
        </div>
        <div class="vbp_title">
          沟通目标<span>*</span>
        </div>
        <div class="vbp_value">
          <el-input type="textarea" v-model="param.salesGoal" :autosize="{ minRows: 3, maxRows: 6 }"
            placeholder="请输入" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateRecordInfo } from "@/js/api.js"
import CustomerNamePicker from "@/components/CustomerNamePicker";
import TagPicker from "@/components/ArrangeVisitCore/TagPicker.vue";
import { trimObject } from "@/js/utils.js"
const refTagPicker = ref()
const hasInit = ref(false)

const param = ref({
  salesGoal: '',
  subject: '',
  salesMateType: 2,//1 interval,2 customer
  salesRelatedType: 1,
  salesMateTags: '',
  salesGoodsCategories: '',
  customerId: '',
  salesMateCustomerName: ''
})
const choosed = ref({ customer: '', internal: '', categories: [] });

const emit = defineEmits(["callback"]);
const dialogVisible = ref();

const show = (p) => {
  let conferenceIds = p.conferenceIds;
  if (!Array.isArray(conferenceIds)) {
    conferenceIds = conferenceIds.split(',');
  }

  param.value = {
    conferenceIds,
    salesMateCustomerName: p.salesMateCustomerName || '',
    customerId: p.customerId || '',
    salesRelatedType: p.salesRelatedType || 1,
    salesMateTags: p.salesMateTags || '',
    salesGoodsCategories: p.salesGoodsCategories || '',
    salesMateType: p.salesMateType || 2,
    salesGoal: p.salesGoal || '',
    subject: p.subject || '',
  };
  dialogVisible.value = true;

  nextTick(() => {
    refTagPicker.value.init(toRaw(param.value))
    nextTick(() => {
      hasInit.value = true
    })
  })
}


const _updateTheme = () => {
  if (param.value.salesMateTags && param.value.salesMateCustomerName) {
    if (!param.value.subject) {
      param.value.subject = param.value.salesMateCustomerName + ' - ' + param.value.salesMateTags;
    }
  }
}

watch(() => param.value.salesMateCustomerName, (val) => {
  if (val) {
    _updateTheme()
  }
}, { immediate: true })

const onTagChange = (val) => {
  _updateTheme()
}

const handleClose = () => {
  dialogVisible.value = false;
};

const onConfirm = () => {
  let data = toRaw(param.value);

  data = trimObject(data);

  const { salesMateCustomerName, salesGoal } = data;

  if (!salesGoal) {
    ElMessage.error(`请输入沟通目标`)
    return
  }
  if (!salesMateCustomerName) {
    ElMessage.error(`请输入沟通关联客户`)
    return
  }
  const loading = ElLoading.service({
    lock: true,
    text: '正在提交公司信息...',
    background: 'rgba(255,255,255, 0.7)',
  })

  updateRecordInfo(data).then(resp => {
    if (resp.code == 0) {
      // 不要问为什么，问后台开发人员
      setTimeout(() => {
        ElMessage.success("更新成功");
        emit('callback', 'reload')
        loading.close()
        handleClose()
      }, 2000)
    } else {
      ElMessage.error(resp.message || '更新失败');
      loading.close()
    }
  })
}

defineExpose({
  handleClose, dialogVisible, onConfirm, show, param, refTagPicker, choosed, CustomerNamePicker,
  TagPicker
});
</script>

<style lang="scss">
.vis_batch_edit_dia {
  .vbp_box {
    margin-bottom: 12px;

    .vbp_title {
      margin-bottom: 12px;

      span {
        color: #F56C6C;
      }
    }

    .vbp_value {
      margin-bottom: 12px;
    }
  }
}
</style>
