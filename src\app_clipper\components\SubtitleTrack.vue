<template>
  <div class="subtitle-track">
    <div class="rb_item" v-for="(item, index) in subtitles" :key="index" :style="getItemStyle(item, 0)"
      @click="handleSubtitleClick(item)">
      <p>
        {{ item.content }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { getItemStyle } from "@/app_clipper/tools/utils.js";

const subtitles = ref([]);

// 添加字幕点击处理函数
const handleSubtitleClick = (item) => {
  // 更新当前时间为字幕的开始时间
  g.emitter.emit("set_audio_time", item.startSeconds);
};

const init = async () => {
  subtitles.value = g.clipperStore.subtitles;
};

onMounted(() => {
  g.emitter.on("audio_ready", (duration) => {
    init();
  });
});

onUnmounted(() => {
  g.emitter.off("audio_ready");
});

defineExpose({
  init,
  handleSubtitleClick,
  subtitles,
  getItemStyle,
});
</script>

<style lang="scss" scoped>
.subtitle-track {
  height: 50px;
  position: relative; // 添加这行，作为定位父容器

  .rb_item {
    position: absolute; // 改为绝对定位
    background: #fff2e0;
    border-radius: 4px;
    padding: 6px 0;
    height: 20px;
    font-size: 12px;
    color: #d98601;
    line-height: 20px;
    white-space: nowrap; // 防止文字换行
    overflow: hidden; // 超出部分隐藏
    text-overflow: ellipsis; // 显示省略号
    cursor: pointer; // 添加鼠标手型

    p {
      margin: 0 10px;
    }

    &:hover {
      background: #ffe4c4; // 添加悬停效果
    }
  }
}
</style>
