<template>
  <div class="participants prepare_wrap">
    <PrepareHeader title="参会人" :show_btn="false" />
    <div class="participants-card">
      <!-- 客户参会人 -->
      <div class="participant-group" v-if="customerAttendees.length > 0">
        <div class="group-title">客户</div>
        <div class="participant-list">
          <div class="participant-item" v-for="(item, index) in customerAttendees" :key="index">
            <div class="participant-info">
              <div class="name">{{ item.name }}</div>
              <div class="role" v-if="item.role">({{ item.role }})</div>
              <!-- <div class="detail-link" @click="onDetail(item)">详情</div> -->
            </div>
            <div class="par_summary" v-html="md2html((item.coreRequirement && item.coreRequirement['总结']) || '')
              "></div>
          </div>
        </div>
      </div>
      <!-- 伙伴参会人 -->
      <div class="participant-group" v-if="partnerAttendees.length > 0">
        <div class="group-title">伙伴</div>
        <div class="participant-list">
          <div class="participant-item simple" v-for="(item, index) in partnerAttendees" :key="index">
            <div class="participant-info">
              <div class="name">{{ item.name }}</div>
              <div class="role" v-if="item.role">({{ item.role }})</div>
            </div>
            <ul class="responsibility-list" v-if="item.keyPoints && item.keyPoints.length > 0">
              <li v-for="(point, pIndex) in item.keyPoints" :key="pIndex">{{ point }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PrepareHeader from "./PrepareHeader.vue";
import { ref } from "vue";
import { md2html } from "@/js/md.js"

const emit = defineEmits(["callback"]);

const customerAttendees = ref([]);
const partnerAttendees = ref([]);

const onDetail = () => {
  emit("callback", "show_customer", "customerAttend");
};

const init = (data) => {
  if (data.attendees) {
    customerAttendees.value = data.attendees.filter(
      (item) => item.attendeeType === "CUSTOMER_ATTENDEE"
    );

    partnerAttendees.value = data.attendees.filter(
      (item) => item.attendeeType === "PARTNER_ATTENDEE"
    );
  }
};

defineExpose({
  init,
  onDetail,
});
</script>

<style lang="scss" scoped>
.participants {
  .participants-card {
    background: #f9fafc;
    border-radius: 8px;
    padding: 0;

    .participant-group {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .group-title {
        display: inline-block;
        padding: 2px 6px;
        background: #e6ebfd;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        color: #436bff;
        line-height: 18px;
        margin-bottom: 12px;
      }

      .participant-list {
        .participant-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          &.simple {
            margin-bottom: 8px;
          }

          .participant-info {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .name {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }

            .role {
              font-size: 14px;
              color: #666;
              margin-left: 8px;
            }

            .detail-link {
              margin-left: auto;
              color: #4080ff;
              font-size: 14px;
            }
          }

          .par_summary {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
          }
        }
      }
    }
  }
}
</style>
