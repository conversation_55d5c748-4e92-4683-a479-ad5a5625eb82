<template>
    <div class="analysis">
        <!-- 标题区域 -->
        <div class="header" @click.stop="toggleContent">
            <span class="header__title">
                <img :src="getOssUrl('start1.png', 2)" alt="">
                <span class="title pdf_change_style">{{ title }}</span>
                <slot name="button"></slot>
            </span>
            <el-button link class="header__toggle">
                <span style="margin-right: 4px;">{{ isExpanded ? '收起' : '展开' }}</span>
                <el-icon>
                    <ArrowUpBold v-if="isExpanded" />
                    <ArrowDownBold v-else />
                </el-icon>
            </el-button>
        </div>
        <!-- 内容区域 -->
        <transition name="slide-fade">
            <div class="content" v-show="isExpanded" v-loading="loading" element-loading-text="数据加载中... 请稍等">

                <template v-for="item in localAIAnalysis">
                    <div class="md_body" v-if="item.content" :key="item.title">
                        <div class="title-1">
                            <img :src="getOssUrl(item.img, 2)" alt="">
                            {{ item.title }}
                        </div>
                        <div class="content" v-html="md2html(item.content)"></div>
                    </div>
                </template>
                <div v-if="localAIAnalysis.length === 0" class="no_data">
                    <el-empty description="暂无数据" :image="getOssUrl('no-data.png', 3)" />
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup lang="js">

import {
    ArrowUpBold,
    ArrowDownBold
} from '@element-plus/icons-vue'
import { getOssUrl } from '@/js/utils.js';
import { md2html } from "@/js/md.js"

// 接收外部传入的AI分析对象
const props = defineProps({
    data: {
        type: Array,
        default: () => ([])
    },
    title: {
        type: String,
        default: 'AI分析'
    },
    loading: {
        type: Boolean,
        default: false
    },
    defaultExpanded: {
        type: Boolean,
        default: false
    }
});

// 使用单个本地变量替代多个计算属性
const localAIAnalysis = computed(() => props.data.filter(x => x.content));

// 是否展开内容
const isExpanded = ref(props.defaultExpanded)

// 切换内容显示状态
const toggleContent = () => {
    isExpanded.value = !isExpanded.value
}
</script>

<style lang="scss" scoped>
.analysis {
    background: #fff;
    border-radius: 8px;
    color: #262626;

    width: 100%;

    img {
        width: 16px;
        height: 16px;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        padding: 12px 24px;
        border-bottom: 1px solid #ebeef5;
        background: #F1F3FB;
        border-radius: 8px 8px 0px 0px;

        &__title {
            display: flex;
            align-items: center;
            justify-content: space-between;

            span {
                margin-left: 4px;
            }
        }

        &__toggle {
            color: #436BFF;
            font-size: 14px;
        }
    }

    .title {
        font-size: 16px;
        font-weight: bold;
        background: linear-gradient(45deg, #6D1BFF 0%, #1D65FF 50%, #00BCE6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

    .content {
        border-radius: 0px 0px 4px 4px;
        background: #F9FAFC;
        min-height: 120px;
        padding: 0 24px;

        .md_body {
            padding: 20px 0;
            border-bottom: 1px solid #ebeef5;
            font-weight: 400;
            font-size: 14px;
            color: #262626;
            line-height: 22px;

            // white-space: pre-line;
            &:last-child {
                border-bottom: none;
            }
        }
    }

    .title-1 {
        display: flex;
        align-items: center;
        // margin-bottom: 16px;

        font-weight: 600;
        font-size: 14px;
        color: #262626;
        line-height: 24px;

        img {
            margin-right: 4px;
        }

    }

    .slide-fade-enter-active,
    .slide-fade-leave-active {
        transition: all 0.3s ease;
    }

    .slide-fade-enter-from,
    .slide-fade-leave-to {
        transform: translateY(10px);
        opacity: 0;
    }
}
</style>